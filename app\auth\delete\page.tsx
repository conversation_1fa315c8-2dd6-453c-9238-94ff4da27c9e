"use client";

import Image from "next/image"
import { useEffect, useState } from "react"
import { getSession } from "next-auth/react"
import { ExclamationTriangleIcon } from "@heroicons/react/20/solid"

export default function DeletePage() {
  const [email, setEmail] = useState<string | null>(null);

  useEffect(() => {
    async function fetchSession() {
      const session = await getSession();
      if (session && session.user && session.user.email) {
        setEmail(session.user.email);
      }
    }
    fetchSession();
  }, []);

  return (
    <div className="relative">
      <div className="w-full text-[#232F3F]">
        <div
          className="relative w-full h-screen bg-center bg-cover"
          style={{ backgroundImage: "url(/ui/auth-section-bg.png)" }}
        >
          <div className="absolute inset-0 w-full h-full flex items-center justify-center">
            <div className="max-w-full sm:max-w-[600px] p-6 mx-auto sm:border border-gray-200 bg-white rounded-lg">
              <Image
                src="/logo/optapex-logo-gray.svg"
                alt="Optapex Logo"
                className="w-auto h-4"
                width={197}
                height={60}
              />
              <div className="mt-8">
                <div className="text-gray-700">
                  <div className="text-2xl font-bold">Account closed</div>
                  <div className="mt-4 text-sm font-normal leading-relaxed">
                    Closure request for your account<span className="font-bold">{" (" + email + ")" || ""}</span> has been successfully processed. We&apos;re sorry to see you go!
                  </div>
                  <div className="mt-4 p-4 text-sm font-normal text-gray-500 bg-gray-100 rounded-md leading-relaxed">
                    <div className="w-full flex items-center gap-x-1 text-base text-gray-500 font-semibold">
                      <ExclamationTriangleIcon className="w-5 h-5 text-orange-500" />
                      Important notice:
                    </div>
                    Your account and all associated data have been <span className="text-red-500 font-semibold">permanently deleted</span>. Any active subscriptions have been cancelled, and you will not be billed further.
                  </div>
                  <div className="mt-4 text-sm font-normal leading-relaxed">
                    If you have any questions or need assistance in the future, please feel free to reach out to our support team. Thank you for being a part of LG Optapex, and we wish you all the best!
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}