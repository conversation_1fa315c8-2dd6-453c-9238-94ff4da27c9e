import React from 'react'
import { render, screen } from '@testing-library/react'
import '@testing-library/jest-dom'
import { describe, test, expect } from '@jest/globals'
import { Avatar, AvatarFallback } from '@/components/ui/avatar'

describe('components/ui/Avatar', () => {
  test('renders fallback content', () => {
    render(
      <Avatar>
        <AvatarFallback>AB</AvatarFallback>
      </Avatar>
    )
    expect(screen.getByText('AB')).toBeInTheDocument()
  })
}) 