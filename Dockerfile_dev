FROM public.ecr.aws/docker/library/node:18-alpine

ARG USER_NAME=mopapp
ARG USER_UID=1001
ARG USER_GID=1001

RUN addgroup -S -g ${USER_GID} ${USER_NAME} \
 && adduser  -S -u ${USER_UID} -G ${USER_NAME} ${USER_NAME}

WORKDIR /app

COPY package.json ./
RUN npm install

COPY --chown=${USER_NAME}:${USER_NAME} .next   ./.next
COPY --chown=${USER_NAME}:${USER_NAME} .env    ./.env

COPY --chown=${USER_NAME}:${USER_NAME} public         public
COPY --chown=${USER_NAME}:${USER_NAME} next.config.js ./
COPY --chown=${USER_NAME}:${USER_NAME} entrypoint.sh  ./
RUN chmod +x ./entrypoint.sh

EXPOSE 3000

USER ${USER_NAME}
ENTRYPOINT ["/app/entrypoint.sh"]
CMD ["npm", "start"]