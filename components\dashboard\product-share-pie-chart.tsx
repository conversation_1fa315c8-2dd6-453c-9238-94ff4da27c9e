"use client"

import Plot from "react-plotly.js"
import { currencyFormat } from "@/utils/msc"
import { useEffect } from "react"

type PieChartData = {
  id: string;
  name: string;
  share_percentage: number;
  value?: number;
  border: string;
  bg: string;
  text: string;
}

interface ProductSharePieChartProps {
  data: PieChartData[];
  metricName?: string;
  selectedLegendType?: string;
}

export default function ProductSharePieChart({ data, metricName = "Share Percentage", selectedLegendType }: ProductSharePieChartProps) {
  const dataSet = []
  const filteredData = data.filter(d => d.share_percentage > 0)
  
  // 값 포맷팅 함수
  const formatValue = (value: number) => {
    if (['revenue', 'ad-sales', 'ad-spends'].includes(selectedLegendType || '')) {
      return currencyFormat.format(value)
    }
    return value.toLocaleString(undefined, {maximumFractionDigits: 0})
  }

  useEffect(() => {
    // Plotly hover 요소들의 z-index를 적절한 수준으로 설정
    const style = document.createElement('style')
    style.textContent = `
      .js-plotly-plot .plotly .hoverlayer {
        z-index: 100 !important;
      }
      .js-plotly-plot .plotly .hovertext {
        z-index: 100 !important;
      }
    `
    document.head.appendChild(style)
    
    return () => {
      if (style && style.parentNode) {
        style.parentNode.removeChild(style)
      }
    }
  }, [])
  
  dataSet.push({
    values: filteredData.map((d) => d.share_percentage),
    labels: filteredData.map((d) => d.id),
    text: filteredData.map((d) => `${d.share_percentage.toFixed(1)}%`),
    hovertext: filteredData.map((d) => `${d.id}<br>${metricName}: ${formatValue(d.value || d.share_percentage)}`),
    hole: 0.7,
    pull: 0.015,
    type: 'pie',
    marker: {
      colors: filteredData.map((d) => d.bg),
      line: {
        color: filteredData.map((d) => d.border),
        width: 1,
      },
    },
    textfont: {
      color: filteredData.map((d) => d.text),
      size: 10,
      weight: 'bold',
    },
    hovertemplate: '%{hovertext}',
    textinfo: 'text',
    textposition: 'inside',
    hoverinfo: 'text',
    name: '',
  })
  
  return (
    <div className="w-full h-full relative z-2">
      <Plot
        data={dataSet as any}
        layout={{
          margin: {
            l: 1,
            r: 1,
            b: 1,
            t: 1,
            pad: 0
          },
          paper_bgcolor: 'rgba(0,0,0,0)',
          plot_bgcolor: 'rgba(0,0,0,0)',
          autosize: true,
          showlegend: false,
          dragmode: false,
          hovermode: 'closest',
          hoverlabel: {
            bgcolor: 'rgba(17, 24, 39, 0.9)',
            font: {
              size: 12,
              color: '#e5e7eb'
            },
            bordercolor: 'rgba(17, 24, 39, 0.9)',
          },
        }}
        config={{
          displayModeBar: false,
        }}
        useResizeHandler={true}
        className="w-full h-full"
        style={{ 
          position: 'relative',
          zIndex: 2
        }}
      />
    </div>
  )
}
