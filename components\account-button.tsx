import { Avatar, AvatarFallback, AvatarImage, AvatarSmileAnimation } from "./ui/avatar"
import { Button } from "./ui/button"
import { auth } from "auth"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuTrigger,
} from "./ui/dropdown-menu"
import { SignInButton } from "./ui/sign-in-button"
import { SignOutButton } from "./ui/sign-out-button"
import { ChevronRightIcon } from "@heroicons/react/20/solid"
import { hexToRgbA, stringToColor } from "@/utils/msc"
import { WithdrawButton } from "./ui/withdraw-button"
import { ManageProfileButton } from "./ui/manage-profile-button"

export default async function AccountButton() {
  const session = await auth()
  if (!session?.user) return <SignInButton provider="cognito"/>
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <div className="relative flex items-center justify-center gap-x-2 p-2 rounded-lg cursor-pointer hover:bg-blue-900/80 border border-gray-100/20">
          {/* <Avatar className="w-8 h-8">
            {session.user.image && (
              <AvatarImage
                src={session.user.image}
                alt={session.user.name ?? ""}
              />
            )}
            <AvatarFallback>{session.user.email}</AvatarFallback>
          </Avatar> */}
          {/* <div className="relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full">
            <AvatarSmileAnimation/>
          </div> */}
					<div className="w-5 h-5 rounded-full flex items-center justify-center border border-gray-100 bg-white">
						<div
							className="w-5 h-5 rounded-full flex items-center justify-center text-[10px] text-gray-500 font-bold"
							style={{
								backgroundColor: `${hexToRgbA(stringToColor(session.user.email || "abc"), 0.1 )}`,
								// color: `${hexToComplimentary(stringToColor(option.lwa_user.profile_user_id))}`,
							}}
						>
							{session.user.email?.charAt(0).toUpperCase()}
						</div>
					</div>
					<div className="text-xs text-gray-200 font-semibold truncate">
						{session.user.email}
					</div>
					<ChevronRightIcon className="h-6 w-6 text-gray-300"/>
        </div>
      </DropdownMenuTrigger>
      <DropdownMenuContent className="min-w-[160px] p-0 shadow-md" align="start" forceMount>
        <div className="flex items-center justify-center bg-gray-50 py-3 px-4 text-xs text-gray-600 font-semibold border-b border-gray-100">
          {session.user.email}
        </div>
        <div className="p-1 space-y-1">
          {/* <CustomLink
            href="/dashboard?tab=manage-profile"
            className="flex items-center justify-start w-full h-6 py-4 px-4 text-gray-600 text-xs hover:bg-accent hover:text-accent-foreground rounded-md font-medium"
          >
            <UserPlusIcon className="w-4 h-4 mr-2"/>
            Account Settings
          </CustomLink> */}
          {/* manage-profile page */}
          <ManageProfileButton />
          <SignOutButton />
          {/* <WithdrawButton /> */}
        </div>
      </DropdownMenuContent>
    </DropdownMenu>
  )
}
