/// <reference types="cypress" />

import BasePage from './BasePage';

class ReportPage extends BasePage {
    DROPDOWN_BOX = '.justify-between > .gap-x-1';
    FILTER = '.mt-3.flex'
    HEADER_1 =':nth-child(1) > .pb-1 > .flex-shrink-0';
    HEADER_2 =':nth-child(2) > .pb-1 > .flex-shrink-0';
    GRAPH_CONTENT = '.mt-4 > .p-3';
    TABLE_CONTENT = '.border-t > .h-full';
    EXCEL_BUTTON = 'a > .flex';

    assertDropDown(){
        cy.get(this.DROPDOWN_BOX).should('be.visible');
    }
    assertFilter(){
        cy.get(this.FILTER).should('be.visible');
    }

    assertReport(){
        cy.get(this.HEADER_1).contains('Estimated Margin')
        cy.get(this.HEADER_2).contains('ROAS')
        cy.get(this.GRAPH_CONTENT).should('be.visible');
        cy.get(this.TABLE_CONTENT).should('be.visible')
    }
    assertExcelButton(){
        cy.get(this.EXCEL_BUTTON).should('be.visible');
    }
}
export default ReportPage;   