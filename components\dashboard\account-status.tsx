"use client"

import { cn } from "@/utils/msc"
import { useTranslations } from "next-intl"


interface AccountStatusProps extends React.HTMLAttributes<HTMLDivElement> {
    accountItem: any;
}
export default function AccountStatus({
  accountItem
}: AccountStatusProps) {
  const t = useTranslations('component')
  return (
    <div className="flex items-center justify-center gap-x-2 text-gray-500 text-sm">
      <span className="relative flex h-2 w-2">
        <span className={cn(
          "absolute inline-flex h-full w-full rounded-full opacity-75",
          accountItem.status === "PAUSED"
            ? "bg-red-500"
            : accountItem.status === "IN_COLLECT"
              ? "animate-ping bg-yellow-500"
              : accountItem.status === "DONE"
                ? "animate-ping bg-green-500"
                : accountItem.status === "EXPIRED"
                  ? "bg-gray-300"
                  : "bg-gray-300"
        )}></span>
        <span className={cn(
          "relative inline-flex rounded-full h-2 w-2",
          accountItem.status === "PAUSED"
            ? "bg-red-500"
            : accountItem.status === "IN_COLLECT"
              ? "bg-yellow-500"
              : accountItem.status === "DONE"
                ? "bg-green-500"
                : accountItem.status === "EXPIRED"
                  ? "bg-gray-300"
                  : "bg-gray-300"
        )}></span>
      </span>
      {accountItem.status === "IN_COLLECT"
        ? t("accountStatus.initializing")
        : accountItem.status === "PROCESSING"
          ? t("accountStatus.processing")
          : accountItem.status === "PAUSED"
            ? t("accountStatus.paused")
            : accountItem.status === "DONE"
							? t("accountStatus.active")
							: accountItem.request_status
      }
    </div>
  )
}
