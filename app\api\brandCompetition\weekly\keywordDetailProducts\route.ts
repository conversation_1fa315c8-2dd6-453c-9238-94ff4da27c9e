import { ErrorResponse } from "@/app/api/_models";
import { getServerApiHostUrl } from "@/utils/host";
import { NextRequest, NextResponse } from "next/server";

export type BrandCompetitionKeywordDetailProductsResponse = any;

export async function GET(
  request: NextRequest
): Promise<NextResponse<BrandCompetitionKeywordDetailProductsResponse | ErrorResponse>> {
  const accessToken = request.headers.get("Authorization");
  if (!accessToken) {
    return NextResponse.json(
      { message: "Authorization header is missing" },
      { status: 400 }
    );
  }
  const accountId = request.nextUrl.searchParams.get("account_id")
  if (!accountId) {
    return NextResponse.json(
      { message: "account_id query is missing" },
      { status: 400 }
    );
  }
  const marketplaceId = request.nextUrl.searchParams.get("marketplace_id")
  if (!marketplaceId) {
    return NextResponse.json(
      { message: "marketplace_id query is missing" },
      { status: 400 }
    );
  }
  const date = request.nextUrl.searchParams.get("date")
  const searchTerm = request.nextUrl.searchParams.get("search_term")
  const brandCompetitionKeywordDetailProductsResponse = await fetch(
    `${await getServerApiHostUrl()}/api/brand-competition/weekly/keyword-detail/products?account_id=${accountId}&marketplace_id=${marketplaceId}&date=${date}&search_term=${searchTerm}`,
    {
      method: "GET",
      headers: {
        "Authorization": `Bearer ${accessToken}`,
        "Content-Type": "application/json",
      },
      cache: "force-cache",
      next: { revalidate: 600 }, // 10 minutes
    }
  ).then((res) => res.json());

  return NextResponse.json(brandCompetitionKeywordDetailProductsResponse, { status: 200 });
}
