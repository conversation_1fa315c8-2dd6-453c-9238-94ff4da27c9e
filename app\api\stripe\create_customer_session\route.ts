import { ErrorResponse } from "@/app/api/_models";
import { getServerApiHostUrl } from "@/utils/host";
import { NextRequest, NextResponse } from "next/server";

export type CreateStripeUserSessionResponse = any;

export async function POST(
  request: NextRequest
): Promise<NextResponse<CreateStripeUserSessionResponse | ErrorResponse>> {
  const body = await request.json();
  
  const accessToken = request.headers.get("Authorization");
  if (!accessToken) {
    return NextResponse.json(
      { message: "Authorization header is missing" },
      { status: 400 }
    );
  }

  const createStripeUserSessionsResponse = await fetch(
    `${await getServerApiHostUrl()}/api/stripe/create-customer-session`,
    {
      method: "POST",
      headers: {
        "Authorization": `Bearer ${accessToken}`,
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        "customer_id": body?.customer_id,
      }),
      cache: "no-cache",
    }
  ).then((res) => res.json());

  return NextResponse.json(createStripeUserSessionsResponse, { status: 200 });
}
