import React from 'react'
import { render, screen } from '@testing-library/react'
import { describe, test, expect } from '@jest/globals'
import { Button } from '@/components/ui/button'

describe('components/ui/Button', () => {
  test('renders with children and role button', () => {
    render(<Button>Click me</Button>)
    const el = screen.getByRole('button', { name: /click me/i })
    expect(el).toBeInTheDocument()
  })
}) 