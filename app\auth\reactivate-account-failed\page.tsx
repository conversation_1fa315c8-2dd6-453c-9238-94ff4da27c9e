"use client";

import Image from "next/image"

export default function ReactivateAccountFailedPageLayout() {

  return (
    <div className="relative">
      <div className="w-full text-[#232F3F]">
        <div
          className="relative w-full h-screen bg-center bg-cover"
          style={{ backgroundImage: "url(/ui/auth-section-bg.png)" }}
        >
          <div className="absolute inset-0 w-full h-full flex items-center justify-center">
            <div className="max-w-full sm:max-w-[402px] p-6 mx-auto sm:border border-gray-200 bg-white rounded-lg">
              <Image
                src="/logo/optapex-logo-gray.svg"
                alt="Optapex Logo"
                className="w-auto h-4"
                width={197}
                height={60}
              />
              
              <div className="mt-8">
                <div className="text-gray-700">
                  <div className="text-2xl font-bold">Reactivation failed</div>
                  <div className="mt-4 text-sm font-normal">
                    Your account reactivation has failed. Please contact support for assistance.
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}