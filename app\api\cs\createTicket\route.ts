import { ErrorResponse } from "@/app/api/_models";
import { getServerApiHostUrl } from "@/utils/host";
import { NextRequest, NextResponse } from "next/server";

export type CreateTicketResponse = any;

export async function POST(
  request: NextRequest
): Promise<NextResponse<CreateTicketResponse | ErrorResponse>> {
  const accessToken = request.headers.get("Authorization");
  const body = await request.json();

  if (!accessToken) {
    return NextResponse.json(
      { message: "Authorization header is missing" },
      { status: 400 }
    );
  }
  const createTicketResponse = await fetch(
    `${await getServerApiHostUrl()}/api/cs/tickets`,
    {
      method: "POST",
      headers: {
        "Authorization": `Bearer ${accessToken}`,
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        "title": body?.title,
        "category": body?.category,
        "message": body?.message,
        "file_infos": body?.file_infos,
      }),
      cache: "no-cache",
    }
  ).then((res) => res.json());

  return NextResponse.json(createTicketResponse, { status: 200 });
}
