"use client"

import { api } from "@/utils/api"
import { useSession } from "next-auth/react"
import { Fragment, useEffect, useRef, useState } from 'react'
import { Disclosure, DisclosureButton, DisclosurePanel, Transition } from '@headlessui/react'
import "react-datepicker/dist/react-datepicker.css"
import { cn, currencyFormat, formatDate, getDateDifference, integerCurrencyFormat } from "@/utils/msc"
import { ProfileOption } from "@/components/dashboard/profile-select"
import PortfolioStatus from './portfolio-status'
import { MarketplaceOption } from "@/components/dashboard/marketplace-select"
import { ChevronDownIcon, CheckBadgeIcon, ClockIcon, ExclamationCircleIcon, ExclamationTriangleIcon, PlusIcon, MagnifyingGlassIcon } from "@heroicons/react/20/solid"
import ProductStatus from "./product-status"
import { useTranslations } from "next-intl"
import Link from "next/link"
import { useCurrencyFormatter, getCurrencyCodeFromMarketplace } from "@/utils/currency"
import { useMemo } from "react"

interface AdSettingsLayoutProps {
  mopUserData: any;
  selectedProfile: ProfileOption;
  selectedMarketplace: MarketplaceOption;
}
export interface PortfolioListItem {
  id: number;
  account_id: string;
  account_type: string;
  selling_partner_id: string;
  marketplace_id: string;
  portfolio_id: string;
  created_by: number;
  creation_datetime: string;
  updated_by: number;
  last_update_datetime: string;
  ad_budget_amount: number | null;
  ad_budget_end_date: string | null;
  ad_budget_start_date: string | null;
  ad_budget_type: string;
  total_cost: any[];
  use_yn: string; // "Y" or "N"
  bid_yn: string; // "Y" or "N"
  display_yn: string; // "Y" or "N"
  optimization_goal: string;
  optimization_name: string;
  optimization_option: string;
  optimization_range: string;
  optimization_target_type: string;
  optimization_target_value: number;
  request_status: string;
  target_products: any[];
  prediction: any;
  competition_option: string;
  target_same_sku_only_yn: string; // "Y" or "N"
  limit_cpc: number;
}
export interface ProductListItem {
  optimization_set: any;
  selling_partner_id: string;
  account_id: string;
  account_type: string;
  creation_datetime: string;
  request_status: string;
  afn_inventory_quantity: number;
  asin: string;
  parent_asin: string;
  available_quantity: number;
  campaigns: any[];
  classification_rank: number;
  classification_rank_title: string;
  condition: string;
  currency: string;
  eligibility_status: string;
  fba_fee: number;
  fulfillment_channel: string;
  id: number;
  image: string;
  inbound_quantity: number;
  item_name: string;
  item_volume: number;
  listing_price: number | null;
  marketplace_id: string;
  merchant_inventory_quantity: number | null;
  mfn_inventory_quantity: number | null;
  product_group: string;
  product_size_tier: string;
  referral_fee: number;
  reserved_quantity: number | null;
  return_fee: number;
  refund_fee: number;
  shipping_price: number;
  shipping_weight: number;
  sku: string;
  target_status: string;
  status: string;
  unfulfillable_quantity: number;
  open_purchase_order_units: number | null;
  state: string;
}

export default function InCollectAdPortfolioLayoutComponent({
  mopUserData,
  selectedProfile,
  selectedMarketplace
}: AdSettingsLayoutProps) {
  const t = useTranslations('component')
  const tos = useTranslations('optimizationSets')
  const { data: session, status } = useSession()
  
  // Currency formatting
  const { formatCurrency } = useCurrencyFormatter()
  const currencyCode = useMemo(() => getCurrencyCodeFromMarketplace(selectedMarketplace), [selectedMarketplace])
  const [isPortfolioViewSliderOpen, setIsPortfolioViewSliderOpen] = useState(false)
  const fetchCounter = useRef(0)
  const [isProductViewSliderOpen, setIsProductViewSliderOpen] = useState(false)
  const [isEditSliderOpen, setIsEditSliderOpen] = useState(false)
  const portfolioListItems: PortfolioListItem[] = [
    {
      "id": 34,
      "account_id": "ARJ8LH78FZTCT",
      "marketplace_id": "ATVPDKIKX0DER",
      "optimization_name": "opt set test 6",
      "ad_budget_type": "DATERANGE",
      "ad_budget_amount": 100,
      "ad_budget_start_date": "2024-06-03",
      "ad_budget_end_date": "2024-07-31",
      "optimization_range": "SPPLUS",
      "optimization_goal": "ROI",
      "optimization_option": "NONE",
      "optimization_target_type": "NONE",
      "optimization_target_value": 0,
      "bid_yn": "Y",
      "use_yn": "Y",
      "created_by": 4,
      "creation_datetime": "2024-06-03T08:10:05",
      "updated_by": 4,
      "last_update_datetime": "2024-08-27T05:28:11",
      "request_status": "DONE",
      "portfolio_id": "***************",
      "account_type": "seller",
      "selling_partner_id": "ARJ8LH78FZTCT",
      "display_yn": "N",
      "target_products": [
        {
          "id": 54463,
          "account_id": "ARJ8LH78FZTCT",
          "marketplace_id": "ATVPDKIKX0DER",
          "asin": "B0B7BNSXVR",
          "sku": "Matt See Through case_iP13_Mint",
          "status": "Active",
          "fulfillment_channel": "AMAZON_NA",
          "parent_asin": "B0B7BB4MCC",
          "item_name": "baditude Rugged Phone Case Compatible with iPhone 13_Mint, Heavy Duty Military Grade Drop and Shock Protection, Ultra-Slim with Magnetic Charging Support",
          "image": "https://m.media-amazon.com/images/I/511UTSc51IL.jpg",
          "classification_rank": 121415,
          "classification_rank_title": "Cell Phone Basic Cases",
          "condition": "New",
          "available_quantity": 42,
          "unfulfillable_quantity": 0,
          "reserved_quantity": null,
          "inbound_quantity": 0,
          "currency": "USD",
          "listing_price": 9,
          "shipping_price": 0,
          "fba_fee": 2.76,
          "referral_fee": 1.35,
          "return_fee": 2.08,
          "refund_fee": 0.189,
          "eligibility_status": "ELIGIBLE",
          "product_group": "gl_wireless",
          "product_size_tier": "Small standard-size",
          "shipping_weight": 0.66,
          "item_volume": 0.0139514,
          "merchant_inventory_quantity": null,
          "mfn_inventory_quantity": null,
          "afn_inventory_quantity": 42,
          "account_type": "seller",
          "selling_partner_id": "ARJ8LH78FZTCT",
          "open_purchase_order_units": null,
          "state": "ACTIVE",
          "target_status": "OPTIMIZER_BID",
          "optimization_set": {
            "optimization_id": 34,
            "optimization_name": "opt set test 6"
          },
          "optimization_target_warnings": [],
          "campaigns": [
            {
              "campaign_id": "***************",
              "campaign_target_status": "OPTIMIZER_BID",
              "campaign_targeting_settings": "AUTO",
              "campaign_name": "mop_B0B7BNSXVR_campaign_auto_20240603081051160",
              "campaign_state": "ENABLED",
              "campaign_start_date": "2024-06-03",
              "campaign_mop_yn": "Y",
              "campaign_budget": 1.01,
              "campaign_budget_usage_percent": 0,
              "campaign_total_cost": [
                {
                  "date": "2024-06-03",
                  "value": 0
                },
                {
                  "date": "2024-06-04",
                  "value": 0
                },
                {
                  "date": "2024-06-05",
                  "value": 0
                },
                {
                  "date": "2024-06-06",
                  "value": 0
                },
                {
                  "date": "2024-06-07",
                  "value": 0
                },
                {
                  "date": "2024-06-08",
                  "value": 0
                },
                {
                  "date": "2024-06-09",
                  "value": 0
                },
                {
                  "date": "2024-06-10",
                  "value": 0
                },
                {
                  "date": "2024-06-11",
                  "value": 0
                },
                {
                  "date": "2024-06-12",
                  "value": 0
                },
                {
                  "date": "2024-06-13",
                  "value": 0.5600000023841858
                },
                {
                  "date": "2024-06-14",
                  "value": 0.5
                },
                {
                  "date": "2024-06-15",
                  "value": 1.0699999928474426
                },
                {
                  "date": "2024-06-16",
                  "value": 1.8600000143051147
                },
                {
                  "date": "2024-06-17",
                  "value": 7.150000095367432
                },
                {
                  "date": "2024-06-18",
                  "value": 1.6799999475479126
                },
                {
                  "date": "2024-06-19",
                  "value": 0
                },
                {
                  "date": "2024-06-20",
                  "value": 0
                },
                {
                  "date": "2024-06-21",
                  "value": 0
                },
                {
                  "date": "2024-06-22",
                  "value": 0.3700000047683716
                },
                {
                  "date": "2024-06-23",
                  "value": 0
                },
                {
                  "date": "2024-06-24",
                  "value": 0
                },
                {
                  "date": "2024-06-25",
                  "value": 0.3100000023841858
                },
                {
                  "date": "2024-06-26",
                  "value": 0
                },
                {
                  "date": "2024-06-27",
                  "value": 0.23999999463558197
                },
                {
                  "date": "2024-06-28",
                  "value": 0
                },
                {
                  "date": "2024-06-29",
                  "value": 0
                },
                {
                  "date": "2024-06-30",
                  "value": 0
                },
                {
                  "date": "2024-07-01",
                  "value": 0.12999999523162842
                },
                {
                  "date": "2024-07-02",
                  "value": 0.49000000953674316
                },
                {
                  "date": "2024-07-03",
                  "value": 0
                },
                {
                  "date": "2024-07-04",
                  "value": 0
                },
                {
                  "date": "2024-07-05",
                  "value": 0
                },
                {
                  "date": "2024-07-06",
                  "value": 0
                },
                {
                  "date": "2024-07-07",
                  "value": 0
                },
                {
                  "date": "2024-07-08",
                  "value": 0
                },
                {
                  "date": "2024-07-09",
                  "value": 0.49000000953674316
                },
                {
                  "date": "2024-07-10",
                  "value": 0
                },
                {
                  "date": "2024-07-11",
                  "value": 0.23999999463558197
                },
                {
                  "date": "2024-07-12",
                  "value": 0
                },
                {
                  "date": "2024-07-13",
                  "value": 0
                },
                {
                  "date": "2024-07-14",
                  "value": 0
                },
                {
                  "date": "2024-07-15",
                  "value": 0
                },
                {
                  "date": "2024-07-16",
                  "value": 0.23999999463558197
                },
                {
                  "date": "2024-07-17",
                  "value": 0.12999999523162842
                },
                {
                  "date": "2024-07-18",
                  "value": 0
                },
                {
                  "date": "2024-07-19",
                  "value": 0
                },
                {
                  "date": "2024-07-20",
                  "value": 0
                },
                {
                  "date": "2024-07-21",
                  "value": 0
                },
                {
                  "date": "2024-07-22",
                  "value": 0
                },
                {
                  "date": "2024-07-23",
                  "value": 0
                },
                {
                  "date": "2024-07-24",
                  "value": 0
                },
                {
                  "date": "2024-07-25",
                  "value": 0
                },
                {
                  "date": "2024-07-26",
                  "value": 0
                }
              ],
              "ad_groups": []
            },
            {
              "campaign_id": "481452774530766",
              "campaign_target_status": "OPTIMIZER_BID",
              "campaign_targeting_settings": "MANUAL",
              "campaign_name": "mop_B0B7BNSXVR_campaign_manual_keyword_20240603081107541",
              "campaign_state": "ENABLED",
              "campaign_start_date": "2024-06-03",
              "campaign_mop_yn": "Y",
              "campaign_budget": 1,
              "campaign_budget_usage_percent": 0,
              "campaign_total_cost": [
                {
                  "date": "2024-06-15",
                  "value": 0
                },
                {
                  "date": "2024-06-16",
                  "value": 1.149999976158142
                },
                {
                  "date": "2024-06-17",
                  "value": 1.149999976158142
                },
                {
                  "date": "2024-06-18",
                  "value": 1.8799999952316284
                },
                {
                  "date": "2024-06-19",
                  "value": 0.8700000047683716
                },
                {
                  "date": "2024-06-20",
                  "value": 0
                },
                {
                  "date": "2024-06-21",
                  "value": 0
                },
                {
                  "date": "2024-06-22",
                  "value": 0
                },
                {
                  "date": "2024-06-28",
                  "value": 2.1800000071525574
                },
                {
                  "date": "2024-06-29",
                  "value": 0
                },
                {
                  "date": "2024-07-02",
                  "value": 0
                },
                {
                  "date": "2024-07-03",
                  "value": 0
                },
                {
                  "date": "2024-07-05",
                  "value": 0
                },
                {
                  "date": "2024-07-06",
                  "value": 0
                },
                {
                  "date": "2024-07-07",
                  "value": 0
                },
                {
                  "date": "2024-07-08",
                  "value": 1
                },
                {
                  "date": "2024-07-09",
                  "value": 0
                },
                {
                  "date": "2024-07-11",
                  "value": 0
                },
                {
                  "date": "2024-07-12",
                  "value": 0
                },
                {
                  "date": "2024-07-17",
                  "value": 0
                },
                {
                  "date": "2024-07-18",
                  "value": 1.7000000476837158
                },
                {
                  "date": "2024-07-19",
                  "value": 1.2400000095367432
                },
                {
                  "date": "2024-07-20",
                  "value": 2.009999990463257
                },
                {
                  "date": "2024-07-21",
                  "value": 2.509999990463257
                },
                {
                  "date": "2024-07-22",
                  "value": 1.440000057220459
                },
                {
                  "date": "2024-07-26",
                  "value": 0
                }
              ],
              "ad_groups": []
            },
            {
              "campaign_id": "335634981538200",
              "campaign_target_status": "OPTIMIZER_BID",
              "campaign_targeting_settings": "MANUAL",
              "campaign_name": "mop_B0B7BNSXVR_campaign_manual_product_20240603081107541",
              "campaign_state": "ENABLED",
              "campaign_start_date": "2024-06-03",
              "campaign_mop_yn": "Y",
              "campaign_budget": 1,
              "campaign_budget_usage_percent": 0,
              "campaign_total_cost": [
                {
                  "date": "2025-03-28",
                  "value": 0
                }
              ],
              "ad_groups": []
            }
          ]
        },
        {
          "id": 54462,
          "account_id": "ARJ8LH78FZTCT",
          "marketplace_id": "ATVPDKIKX0DER",
          "asin": "B0B7BGHXSY",
          "sku": "Matt See Through case_iP13_Black",
          "status": "Active",
          "fulfillment_channel": "AMAZON_NA",
          "parent_asin": "B0B7BB4MCC",
          "item_name": "baditude Rugged Phone Case Compatible with iPhone 13_Black, Heavy Duty Military Grade Drop and Shock Protection, Ultra-Slim with Magnetic Charging Support",
          "image": "https://m.media-amazon.com/images/I/51OExDbNPOL.jpg",
          "classification_rank": 121415,
          "classification_rank_title": "Cell Phone Basic Cases",
          "condition": "New",
          "available_quantity": 40,
          "unfulfillable_quantity": 0,
          "reserved_quantity": null,
          "inbound_quantity": 0,
          "currency": "USD",
          "listing_price": 9,
          "shipping_price": 0,
          "fba_fee": 2.29,
          "referral_fee": 1.35,
          "return_fee": 1.78,
          "refund_fee": 0.189,
          "eligibility_status": "ELIGIBLE",
          "product_group": "gl_wireless",
          "product_size_tier": "Small standard-size",
          "shipping_weight": 0.110231,
          "item_volume": 0.0126338,
          "merchant_inventory_quantity": null,
          "mfn_inventory_quantity": null,
          "afn_inventory_quantity": 40,
          "account_type": "seller",
          "selling_partner_id": "ARJ8LH78FZTCT",
          "open_purchase_order_units": null,
          "state": "ACTIVE",
          "target_status": "OPTIMIZER_BID",
          "optimization_set": {
            "optimization_id": 34,
            "optimization_name": "opt set test 6"
          },
          "optimization_target_warnings": [],
          "campaigns": [
            {
              "campaign_id": "***************",
              "campaign_target_status": "OPTIMIZER_BID",
              "campaign_targeting_settings": "AUTO",
              "campaign_name": "mop_B0B7BGHXSY_campaign_auto_20240603081051656",
              "campaign_state": "ENABLED",
              "campaign_start_date": "2024-06-03",
              "campaign_mop_yn": "Y",
              "campaign_budget": 1,
              "campaign_budget_usage_percent": 0,
              "campaign_total_cost": [
                {
                  "date": "2024-06-03",
                  "value": 0
                },
                {
                  "date": "2024-06-04",
                  "value": 0
                },
                {
                  "date": "2024-06-05",
                  "value": 0
                },
                {
                  "date": "2024-06-06",
                  "value": 0
                },
                {
                  "date": "2024-06-07",
                  "value": 0
                },
                {
                  "date": "2024-06-08",
                  "value": 0
                },
                {
                  "date": "2024-06-09",
                  "value": 0
                },
                {
                  "date": "2024-06-10",
                  "value": 2.519999921321869
                },
                {
                  "date": "2024-06-11",
                  "value": 0
                },
                {
                  "date": "2024-06-12",
                  "value": 0.8199999928474426
                },
                {
                  "date": "2024-06-13",
                  "value": 0
                },
                {
                  "date": "2024-06-14",
                  "value": 0.8500000238418579
                },
                {
                  "date": "2024-06-15",
                  "value": 0.75
                },
                {
                  "date": "2024-06-16",
                  "value": 0
                },
                {
                  "date": "2024-06-17",
                  "value": 2.349999964237213
                },
                {
                  "date": "2024-06-18",
                  "value": 0
                },
                {
                  "date": "2024-06-19",
                  "value": 0
                },
                {
                  "date": "2024-06-20",
                  "value": 0
                },
                {
                  "date": "2024-06-21",
                  "value": 0
                },
                {
                  "date": "2024-06-22",
                  "value": 0.8999999761581421
                },
                {
                  "date": "2024-06-23",
                  "value": 0.8999999761581421
                },
                {
                  "date": "2024-06-24",
                  "value": 0
                },
                {
                  "date": "2024-06-25",
                  "value": 0
                },
                {
                  "date": "2024-06-26",
                  "value": 0
                },
                {
                  "date": "2024-06-27",
                  "value": 0
                },
                {
                  "date": "2024-06-28",
                  "value": 0.7899999916553497
                },
                {
                  "date": "2024-06-29",
                  "value": 0
                },
                {
                  "date": "2024-06-30",
                  "value": 0
                },
                {
                  "date": "2024-07-01",
                  "value": 0
                },
                {
                  "date": "2024-07-02",
                  "value": 0
                },
                {
                  "date": "2024-07-03",
                  "value": 0
                },
                {
                  "date": "2024-07-04",
                  "value": 0
                },
                {
                  "date": "2024-07-05",
                  "value": 0
                },
                {
                  "date": "2024-07-06",
                  "value": 0
                },
                {
                  "date": "2024-07-07",
                  "value": 0
                },
                {
                  "date": "2024-07-08",
                  "value": 0
                },
                {
                  "date": "2024-07-10",
                  "value": 0
                },
                {
                  "date": "2024-07-11",
                  "value": 0.8999999761581421
                },
                {
                  "date": "2024-07-12",
                  "value": 0
                },
                {
                  "date": "2024-07-13",
                  "value": 0
                },
                {
                  "date": "2024-07-14",
                  "value": 0
                },
                {
                  "date": "2024-07-15",
                  "value": 0
                },
                {
                  "date": "2024-07-16",
                  "value": 0
                },
                {
                  "date": "2024-07-17",
                  "value": 0
                },
                {
                  "date": "2024-07-18",
                  "value": 0
                },
                {
                  "date": "2024-07-19",
                  "value": 0
                },
                {
                  "date": "2024-07-20",
                  "value": 0
                },
                {
                  "date": "2024-07-21",
                  "value": 0
                },
                {
                  "date": "2024-07-22",
                  "value": 0
                },
                {
                  "date": "2024-07-23",
                  "value": 0
                },
                {
                  "date": "2024-07-24",
                  "value": 0
                },
                {
                  "date": "2024-07-25",
                  "value": 0
                },
                {
                  "date": "2024-07-26",
                  "value": 2.119999945163727
                }
              ],
              "ad_groups": []
            },
            {
              "campaign_id": "485378791679159",
              "campaign_target_status": "OPTIMIZER_BID",
              "campaign_targeting_settings": "MANUAL",
              "campaign_name": "mop_B0B7BGHXSY_campaign_manual_keyword_20240603081107541",
              "campaign_state": "ENABLED",
              "campaign_start_date": "2024-06-03",
              "campaign_mop_yn": "Y",
              "campaign_budget": 1,
              "campaign_budget_usage_percent": 0,
              "campaign_total_cost": [
                {
                  "date": "2024-06-13",
                  "value": 0
                },
                {
                  "date": "2024-06-14",
                  "value": 0
                },
                {
                  "date": "2024-06-15",
                  "value": 0
                },
                {
                  "date": "2024-06-16",
                  "value": 0
                },
                {
                  "date": "2024-06-17",
                  "value": 0
                },
                {
                  "date": "2024-06-18",
                  "value": 0
                },
                {
                  "date": "2024-06-19",
                  "value": 0
                },
                {
                  "date": "2024-06-22",
                  "value": 0
                },
                {
                  "date": "2024-06-23",
                  "value": 0
                },
                {
                  "date": "2024-06-24",
                  "value": 0
                },
                {
                  "date": "2024-06-25",
                  "value": 0.9700000286102295
                },
                {
                  "date": "2024-06-26",
                  "value": 0
                },
                {
                  "date": "2024-06-28",
                  "value": 0
                },
                {
                  "date": "2024-07-01",
                  "value": 0
                },
                {
                  "date": "2024-07-02",
                  "value": 0.8700000047683716
                },
                {
                  "date": "2024-07-03",
                  "value": 0
                },
                {
                  "date": "2024-07-05",
                  "value": 0
                },
                {
                  "date": "2024-07-06",
                  "value": 0
                },
                {
                  "date": "2024-07-08",
                  "value": 0
                },
                {
                  "date": "2024-07-09",
                  "value": 0
                },
                {
                  "date": "2024-07-11",
                  "value": 0
                },
                {
                  "date": "2024-07-12",
                  "value": 0
                },
                {
                  "date": "2024-07-13",
                  "value": 0
                },
                {
                  "date": "2024-07-15",
                  "value": 0.5
                },
                {
                  "date": "2024-07-16",
                  "value": 0
                },
                {
                  "date": "2024-07-17",
                  "value": 0
                },
                {
                  "date": "2024-07-20",
                  "value": 0
                },
                {
                  "date": "2024-07-21",
                  "value": 0
                },
                {
                  "date": "2024-07-22",
                  "value": 0
                },
                {
                  "date": "2024-07-23",
                  "value": 0
                },
                {
                  "date": "2024-07-24",
                  "value": 0
                },
                {
                  "date": "2024-07-26",
                  "value": 0
                }
              ],
              "ad_groups": []
            },
            {
              "campaign_id": "397122204127116",
              "campaign_target_status": "OPTIMIZER_BID",
              "campaign_targeting_settings": "MANUAL",
              "campaign_name": "mop_B0B7BGHXSY_campaign_manual_product_20240603081107541",
              "campaign_state": "ENABLED",
              "campaign_start_date": "2024-06-03",
              "campaign_mop_yn": "Y",
              "campaign_budget": 1,
              "campaign_budget_usage_percent": 0,
              "campaign_total_cost": [
                {
                  "date": "2025-03-28",
                  "value": 0
                }
              ],
              "ad_groups": []
            }
          ]
        },
        {
          "id": 54460,
          "account_id": "ARJ8LH78FZTCT",
          "marketplace_id": "ATVPDKIKX0DER",
          "asin": "B0B7BQ6KHC",
          "sku": "Matt See Through case_iP13Promax_Black",
          "status": "Active",
          "fulfillment_channel": "AMAZON_NA",
          "parent_asin": "B0B7BB4MCC",
          "item_name": "baditude Rugged Phone Case Compatible with iPhone 13Promax_Black, Heavy Duty Military Grade Drop and Shock Protection, Ultra-Slim with Magnetic Charging Support",
          "image": "https://m.media-amazon.com/images/I/51OExDbNPOL.jpg",
          "classification_rank": 121415,
          "classification_rank_title": "Cell Phone Basic Cases",
          "condition": "New",
          "available_quantity": 49,
          "unfulfillable_quantity": 0,
          "reserved_quantity": null,
          "inbound_quantity": 0,
          "currency": "USD",
          "listing_price": 9,
          "shipping_price": 0,
          "fba_fee": 2.38,
          "referral_fee": 1.35,
          "return_fee": 1.84,
          "refund_fee": 0.189,
          "eligibility_status": "ELIGIBLE",
          "product_group": "gl_wireless",
          "product_size_tier": "Small standard-size",
          "shipping_weight": 0.1433,
          "item_volume": 0.0126386,
          "merchant_inventory_quantity": null,
          "mfn_inventory_quantity": null,
          "afn_inventory_quantity": 49,
          "account_type": "seller",
          "selling_partner_id": "ARJ8LH78FZTCT",
          "open_purchase_order_units": null,
          "state": "ACTIVE",
          "target_status": "OPTIMIZER_BID",
          "optimization_set": {
            "optimization_id": 34,
            "optimization_name": "opt set test 6"
          },
          "optimization_target_warnings": [],
          "campaigns": [
            {
              "campaign_id": "***************",
              "campaign_target_status": "OPTIMIZER_BID",
              "campaign_targeting_settings": "AUTO",
              "campaign_name": "mop_B0B7BQ6KHC_campaign_auto_20240603081052074",
              "campaign_state": "ENABLED",
              "campaign_start_date": "2024-06-03",
              "campaign_mop_yn": "Y",
              "campaign_budget": 1.01,
              "campaign_budget_usage_percent": 0,
              "campaign_total_cost": [
                {
                  "date": "2024-06-03",
                  "value": 0
                },
                {
                  "date": "2024-06-04",
                  "value": 0
                },
                {
                  "date": "2024-06-05",
                  "value": 0
                },
                {
                  "date": "2024-06-06",
                  "value": 0
                },
                {
                  "date": "2024-06-07",
                  "value": 0
                },
                {
                  "date": "2024-06-09",
                  "value": 0
                },
                {
                  "date": "2024-06-10",
                  "value": 0
                },
                {
                  "date": "2024-06-11",
                  "value": 0
                },
                {
                  "date": "2024-06-12",
                  "value": 0
                },
                {
                  "date": "2024-06-13",
                  "value": 0
                },
                {
                  "date": "2024-06-14",
                  "value": 0
                },
                {
                  "date": "2024-06-15",
                  "value": 0
                },
                {
                  "date": "2024-06-16",
                  "value": 0
                },
                {
                  "date": "2024-06-17",
                  "value": 0
                },
                {
                  "date": "2024-06-18",
                  "value": 0
                },
                {
                  "date": "2024-06-19",
                  "value": 0.9300000071525574
                },
                {
                  "date": "2024-06-20",
                  "value": 0
                },
                {
                  "date": "2024-06-21",
                  "value": 0
                },
                {
                  "date": "2024-06-22",
                  "value": 0
                },
                {
                  "date": "2024-06-23",
                  "value": 0
                },
                {
                  "date": "2024-06-25",
                  "value": 0
                },
                {
                  "date": "2024-06-26",
                  "value": 0
                },
                {
                  "date": "2024-06-27",
                  "value": 0
                },
                {
                  "date": "2024-06-29",
                  "value": 0
                },
                {
                  "date": "2024-06-30",
                  "value": 0
                },
                {
                  "date": "2024-07-01",
                  "value": 0
                },
                {
                  "date": "2024-07-02",
                  "value": 0
                },
                {
                  "date": "2024-07-03",
                  "value": 0
                },
                {
                  "date": "2024-07-04",
                  "value": 0
                },
                {
                  "date": "2024-07-05",
                  "value": 0
                },
                {
                  "date": "2024-07-06",
                  "value": 0
                },
                {
                  "date": "2024-07-07",
                  "value": 0.7699999809265137
                },
                {
                  "date": "2024-07-08",
                  "value": 0
                },
                {
                  "date": "2024-07-09",
                  "value": 0
                },
                {
                  "date": "2024-07-10",
                  "value": 0
                },
                {
                  "date": "2024-07-11",
                  "value": 0
                },
                {
                  "date": "2024-07-12",
                  "value": 0
                },
                {
                  "date": "2024-07-13",
                  "value": 0
                },
                {
                  "date": "2024-07-14",
                  "value": 0
                },
                {
                  "date": "2024-07-15",
                  "value": 0.6899999976158142
                },
                {
                  "date": "2024-07-16",
                  "value": 0
                },
                {
                  "date": "2024-07-17",
                  "value": 0
                },
                {
                  "date": "2024-07-18",
                  "value": 0
                },
                {
                  "date": "2024-07-19",
                  "value": 0
                },
                {
                  "date": "2024-07-20",
                  "value": 0
                },
                {
                  "date": "2024-07-21",
                  "value": 0
                },
                {
                  "date": "2024-07-22",
                  "value": 3.1600000262260437
                },
                {
                  "date": "2024-07-23",
                  "value": 0
                },
                {
                  "date": "2024-07-24",
                  "value": 0
                },
                {
                  "date": "2024-07-26",
                  "value": 0
                }
              ],
              "ad_groups": []
            },
            {
              "campaign_id": "363265608995165",
              "campaign_target_status": "OPTIMIZER_BID",
              "campaign_targeting_settings": "MANUAL",
              "campaign_name": "mop_B0B7BQ6KHC_campaign_manual_keyword_20240603081107541",
              "campaign_state": "ENABLED",
              "campaign_start_date": "2024-06-03",
              "campaign_mop_yn": "Y",
              "campaign_budget": 1,
              "campaign_budget_usage_percent": 0,
              "campaign_total_cost": [
                {
                  "date": "2024-07-03",
                  "value": 0
                },
                {
                  "date": "2024-07-10",
                  "value": 0
                },
                {
                  "date": "2024-07-11",
                  "value": 0
                },
                {
                  "date": "2024-07-19",
                  "value": 0
                },
                {
                  "date": "2024-07-20",
                  "value": 0
                },
                {
                  "date": "2024-07-21",
                  "value": 0
                },
                {
                  "date": "2024-07-22",
                  "value": 0
                },
                {
                  "date": "2024-07-26",
                  "value": 0
                }
              ],
              "ad_groups": []
            },
            {
              "campaign_id": "435203735252558",
              "campaign_target_status": "OPTIMIZER_BID",
              "campaign_targeting_settings": "MANUAL",
              "campaign_name": "mop_B0B7BQ6KHC_campaign_manual_product_20240603081107541",
              "campaign_state": "ENABLED",
              "campaign_start_date": "2024-06-03",
              "campaign_mop_yn": "Y",
              "campaign_budget": 1,
              "campaign_budget_usage_percent": 0,
              "campaign_total_cost": [
                {
                  "date": "2025-03-28",
                  "value": 0
                }
              ],
              "ad_groups": []
            }
          ]
        },
        {
          "id": 54468,
          "account_id": "ARJ8LH78FZTCT",
          "marketplace_id": "ATVPDKIKX0DER",
          "asin": "B0BBG5KGG4",
          "sku": "Tempered glass_14Promax",
          "status": "Active",
          "fulfillment_channel": "AMAZON_NA",
          "parent_asin": "B0BBG43Y46",
          "item_name": "Baditude Tempered Glass Screen Protector for iPhone 14Promax(2022) - 2 Pack with Baditude's Special allighment tool",
          "image": "https://m.media-amazon.com/images/I/511tGE6u3pL.jpg",
          "classification_rank": 20680,
          "classification_rank_title": "Cell Phone Screen Protectors",
          "condition": "New",
          "available_quantity": 117,
          "unfulfillable_quantity": 0,
          "reserved_quantity": null,
          "inbound_quantity": 0,
          "currency": "USD",
          "listing_price": 7,
          "shipping_price": 0,
          "fba_fee": 2.42,
          "referral_fee": 1.05,
          "return_fee": 1.9,
          "refund_fee": 0.147,
          "eligibility_status": "ELIGIBLE",
          "product_group": "gl_wireless",
          "product_size_tier": "Small standard-size",
          "shipping_weight": 0.29101,
          "item_volume": 0.0140068,
          "merchant_inventory_quantity": null,
          "mfn_inventory_quantity": null,
          "afn_inventory_quantity": 117,
          "account_type": "seller",
          "selling_partner_id": "ARJ8LH78FZTCT",
          "open_purchase_order_units": null,
          "state": "ACTIVE",
          "target_status": "OPTIMIZER_BID",
          "optimization_set": {
            "optimization_id": 34,
            "optimization_name": "opt set test 6"
          },
          "optimization_target_warnings": [],
          "campaigns": [
            {
              "campaign_id": "***************",
              "campaign_target_status": "OPTIMIZER_BID",
              "campaign_targeting_settings": "AUTO",
              "campaign_name": "mop_B0BBG5KGG4_campaign_auto_20240603081052479",
              "campaign_state": "ENABLED",
              "campaign_start_date": "2024-06-03",
              "campaign_mop_yn": "Y",
              "campaign_budget": 1,
              "campaign_budget_usage_percent": 0,
              "campaign_total_cost": [
                {
                  "date": "2024-06-03",
                  "value": 0
                },
                {
                  "date": "2024-06-04",
                  "value": 0
                },
                {
                  "date": "2024-06-05",
                  "value": 0
                },
                {
                  "date": "2024-06-06",
                  "value": 0
                },
                {
                  "date": "2024-06-07",
                  "value": 0
                },
                {
                  "date": "2024-06-08",
                  "value": 0
                },
                {
                  "date": "2024-06-09",
                  "value": 0
                },
                {
                  "date": "2024-06-10",
                  "value": 0
                },
                {
                  "date": "2024-06-11",
                  "value": 0
                },
                {
                  "date": "2024-06-12",
                  "value": 0
                },
                {
                  "date": "2024-06-13",
                  "value": 0
                },
                {
                  "date": "2024-06-14",
                  "value": 0
                },
                {
                  "date": "2024-06-15",
                  "value": 0
                },
                {
                  "date": "2024-06-16",
                  "value": 0
                },
                {
                  "date": "2024-06-17",
                  "value": 0
                },
                {
                  "date": "2024-06-18",
                  "value": 0
                },
                {
                  "date": "2024-06-19",
                  "value": 0
                },
                {
                  "date": "2024-06-20",
                  "value": 0
                },
                {
                  "date": "2024-06-21",
                  "value": 0
                },
                {
                  "date": "2024-06-22",
                  "value": 0
                },
                {
                  "date": "2024-06-23",
                  "value": 0.6399999856948853
                },
                {
                  "date": "2024-06-24",
                  "value": 0
                },
                {
                  "date": "2024-06-25",
                  "value": 0
                },
                {
                  "date": "2024-06-26",
                  "value": 0
                },
                {
                  "date": "2024-06-27",
                  "value": 0
                },
                {
                  "date": "2024-06-28",
                  "value": 0
                },
                {
                  "date": "2024-06-29",
                  "value": 0
                },
                {
                  "date": "2024-06-30",
                  "value": 1.059999942779541
                },
                {
                  "date": "2024-07-01",
                  "value": 0
                },
                {
                  "date": "2024-07-02",
                  "value": 0
                },
                {
                  "date": "2024-07-03",
                  "value": 0
                },
                {
                  "date": "2024-07-04",
                  "value": 1.940000057220459
                },
                {
                  "date": "2024-07-05",
                  "value": 0
                },
                {
                  "date": "2024-07-06",
                  "value": 0
                },
                {
                  "date": "2024-07-07",
                  "value": 0
                },
                {
                  "date": "2024-07-08",
                  "value": 2.5799999237060547
                },
                {
                  "date": "2024-07-09",
                  "value": 0
                },
                {
                  "date": "2024-07-10",
                  "value": 0
                },
                {
                  "date": "2024-07-11",
                  "value": 0
                },
                {
                  "date": "2024-07-12",
                  "value": 2.8299999237060547
                },
                {
                  "date": "2024-07-13",
                  "value": 0.7599999904632568
                },
                {
                  "date": "2024-07-14",
                  "value": 0.8399999737739563
                },
                {
                  "date": "2024-07-15",
                  "value": 0
                },
                {
                  "date": "2024-07-16",
                  "value": 5
                },
                {
                  "date": "2024-07-17",
                  "value": 4.199999809265137
                },
                {
                  "date": "2024-07-18",
                  "value": 0
                },
                {
                  "date": "2024-07-19",
                  "value": 0
                },
                {
                  "date": "2024-07-20",
                  "value": 0
                },
                {
                  "date": "2024-07-21",
                  "value": 4.539999961853027
                },
                {
                  "date": "2024-07-22",
                  "value": 1.7400000095367432
                },
                {
                  "date": "2024-07-23",
                  "value": 0
                },
                {
                  "date": "2024-07-24",
                  "value": 0
                },
                {
                  "date": "2024-07-25",
                  "value": 0
                },
                {
                  "date": "2024-07-26",
                  "value": 0
                }
              ],
              "ad_groups": []
            },
            {
              "campaign_id": "552122647866396",
              "campaign_target_status": "OPTIMIZER_BID",
              "campaign_targeting_settings": "MANUAL",
              "campaign_name": "mop_B0BBG5KGG4_campaign_manual_keyword_20240603081107541",
              "campaign_state": "ENABLED",
              "campaign_start_date": "2024-06-03",
              "campaign_mop_yn": "Y",
              "campaign_budget": 1.01,
              "campaign_budget_usage_percent": 0,
              "campaign_total_cost": [
                {
                  "date": "2024-07-01",
                  "value": 0
                },
                {
                  "date": "2024-07-15",
                  "value": 0
                },
                {
                  "date": "2024-07-16",
                  "value": 1.5700000524520874
                },
                {
                  "date": "2024-07-17",
                  "value": 0
                },
                {
                  "date": "2024-07-18",
                  "value": 1.0499999523162842
                },
                {
                  "date": "2024-07-19",
                  "value": 1.090000033378601
                },
                {
                  "date": "2024-07-20",
                  "value": 0
                },
                {
                  "date": "2024-07-21",
                  "value": 3.259999990463257
                },
                {
                  "date": "2024-07-22",
                  "value": 10.109999895095825
                },
                {
                  "date": "2024-07-23",
                  "value": 0
                },
                {
                  "date": "2024-07-24",
                  "value": 0
                },
                {
                  "date": "2024-07-25",
                  "value": 0
                },
                {
                  "date": "2024-07-26",
                  "value": 0
                }
              ],
              "ad_groups": []
            },
            {
              "campaign_id": "403826389001023",
              "campaign_target_status": "OPTIMIZER_BID",
              "campaign_targeting_settings": "MANUAL",
              "campaign_name": "mop_B0BBG5KGG4_campaign_manual_product_20240603081107541",
              "campaign_state": "ENABLED",
              "campaign_start_date": "2024-06-03",
              "campaign_mop_yn": "Y",
              "campaign_budget": 1,
              "campaign_budget_usage_percent": 0,
              "campaign_total_cost": [
                {
                  "date": "2025-03-28",
                  "value": 0
                }
              ],
              "ad_groups": []
            }
          ]
        },
        {
          "id": 54469,
          "account_id": "ARJ8LH78FZTCT",
          "marketplace_id": "ATVPDKIKX0DER",
          "asin": "B0BBG4862P",
          "sku": "Tempered glass_14max",
          "status": "Active",
          "fulfillment_channel": "AMAZON_NA",
          "parent_asin": "B0BBG43Y46",
          "item_name": "Baditude Tempered Glass Screen Protector for iPhone 14Max(2022) - 2 Pack with Baditude's Special allighment tool",
          "image": "https://m.media-amazon.com/images/I/511tGE6u3pL.jpg",
          "classification_rank": 20680,
          "classification_rank_title": "Cell Phone Screen Protectors",
          "condition": "New",
          "available_quantity": 36,
          "unfulfillable_quantity": 0,
          "reserved_quantity": null,
          "inbound_quantity": 0,
          "currency": "USD",
          "listing_price": 7,
          "shipping_price": 0,
          "fba_fee": 3.09,
          "referral_fee": 1.05,
          "return_fee": 2.7,
          "refund_fee": 0.147,
          "eligibility_status": "ELIGIBLE",
          "product_group": "gl_wireless",
          "product_size_tier": "Large standard-size",
          "shipping_weight": 0.29101,
          "item_volume": 0.0185004,
          "merchant_inventory_quantity": null,
          "mfn_inventory_quantity": null,
          "afn_inventory_quantity": 36,
          "account_type": "seller",
          "selling_partner_id": "ARJ8LH78FZTCT",
          "open_purchase_order_units": null,
          "state": "ACTIVE",
          "target_status": "OPTIMIZER_BID",
          "optimization_set": {
            "optimization_id": 34,
            "optimization_name": "opt set test 6"
          },
          "optimization_target_warnings": [],
          "campaigns": [
            {
              "campaign_id": "***************",
              "campaign_target_status": "OPTIMIZER_BID",
              "campaign_targeting_settings": "AUTO",
              "campaign_name": "mop_B0BBG4862P_campaign_auto_20240603081052851",
              "campaign_state": "ENABLED",
              "campaign_start_date": "2024-06-03",
              "campaign_mop_yn": "Y",
              "campaign_budget": 1,
              "campaign_budget_usage_percent": 0,
              "campaign_total_cost": [
                {
                  "date": "2024-06-04",
                  "value": 0
                },
                {
                  "date": "2024-06-05",
                  "value": 0
                },
                {
                  "date": "2024-06-06",
                  "value": 0
                },
                {
                  "date": "2024-06-07",
                  "value": 0
                },
                {
                  "date": "2024-06-08",
                  "value": 0
                },
                {
                  "date": "2024-06-09",
                  "value": 0
                },
                {
                  "date": "2024-06-10",
                  "value": 0
                },
                {
                  "date": "2024-06-11",
                  "value": 0
                },
                {
                  "date": "2024-06-12",
                  "value": 0
                },
                {
                  "date": "2024-06-13",
                  "value": 1.5900000035762787
                },
                {
                  "date": "2024-06-14",
                  "value": 0.5799999833106995
                },
                {
                  "date": "2024-06-15",
                  "value": 0
                },
                {
                  "date": "2024-06-16",
                  "value": 0.6899999976158142
                },
                {
                  "date": "2024-06-17",
                  "value": 0.5799999833106995
                },
                {
                  "date": "2024-06-18",
                  "value": 0
                },
                {
                  "date": "2024-06-19",
                  "value": 0
                },
                {
                  "date": "2024-06-20",
                  "value": 0
                },
                {
                  "date": "2024-06-21",
                  "value": 0
                },
                {
                  "date": "2024-06-22",
                  "value": 0
                },
                {
                  "date": "2024-06-23",
                  "value": 0
                },
                {
                  "date": "2024-06-24",
                  "value": 0
                },
                {
                  "date": "2024-06-25",
                  "value": 0
                },
                {
                  "date": "2024-06-26",
                  "value": 0
                },
                {
                  "date": "2024-06-27",
                  "value": 0
                },
                {
                  "date": "2024-06-28",
                  "value": 0
                },
                {
                  "date": "2024-06-29",
                  "value": 0
                },
                {
                  "date": "2024-06-30",
                  "value": 0
                },
                {
                  "date": "2024-07-01",
                  "value": 0
                },
                {
                  "date": "2024-07-02",
                  "value": 0.49000000953674316
                },
                {
                  "date": "2024-07-03",
                  "value": 0.4099999964237213
                },
                {
                  "date": "2024-07-04",
                  "value": 0.46000000834465027
                },
                {
                  "date": "2024-07-05",
                  "value": 0
                },
                {
                  "date": "2024-07-06",
                  "value": 0
                },
                {
                  "date": "2024-07-07",
                  "value": 0.3700000047683716
                },
                {
                  "date": "2024-07-08",
                  "value": 0
                },
                {
                  "date": "2024-07-09",
                  "value": 0
                },
                {
                  "date": "2024-07-10",
                  "value": 0
                },
                {
                  "date": "2024-07-11",
                  "value": 0
                },
                {
                  "date": "2024-07-12",
                  "value": 0
                },
                {
                  "date": "2024-07-13",
                  "value": 0
                },
                {
                  "date": "2024-07-14",
                  "value": 0.47999998927116394
                },
                {
                  "date": "2024-07-15",
                  "value": 0
                },
                {
                  "date": "2024-07-16",
                  "value": 0
                },
                {
                  "date": "2024-07-17",
                  "value": 0
                },
                {
                  "date": "2024-07-18",
                  "value": 0
                },
                {
                  "date": "2024-07-19",
                  "value": 0
                },
                {
                  "date": "2024-07-20",
                  "value": 0
                },
                {
                  "date": "2024-07-21",
                  "value": 0
                },
                {
                  "date": "2024-07-22",
                  "value": 0
                },
                {
                  "date": "2024-07-23",
                  "value": 0
                },
                {
                  "date": "2024-07-24",
                  "value": 0
                },
                {
                  "date": "2024-07-25",
                  "value": 0
                },
                {
                  "date": "2024-07-26",
                  "value": 0
                }
              ],
              "ad_groups": []
            },
            {
              "campaign_id": "363266317420554",
              "campaign_target_status": "OPTIMIZER_BID",
              "campaign_targeting_settings": "MANUAL",
              "campaign_name": "mop_B0BBG4862P_campaign_manual_product_20240603081107541",
              "campaign_state": "ENABLED",
              "campaign_start_date": "2024-06-03",
              "campaign_mop_yn": "Y",
              "campaign_budget": 1,
              "campaign_budget_usage_percent": 0,
              "campaign_total_cost": [
                {
                  "date": "2025-03-28",
                  "value": 0
                }
              ],
              "ad_groups": []
            },
            {
              "campaign_id": "351065765501919",
              "campaign_target_status": "OPTIMIZER_BID",
              "campaign_targeting_settings": "MANUAL",
              "campaign_name": "mop_B0BBG4862P_campaign_manual_keyword_20240603081107541",
              "campaign_state": "ENABLED",
              "campaign_start_date": "2024-06-03",
              "campaign_mop_yn": "Y",
              "campaign_budget": 1,
              "campaign_budget_usage_percent": 0,
              "campaign_total_cost": [
                {
                  "date": "2025-03-28",
                  "value": 0
                }
              ],
              "ad_groups": []
            }
          ]
        }
      ],
      "total_cost": [
        {
          "date": "2024-06-03",
          "value": 0
        },
        {
          "date": "2024-06-04",
          "value": 0
        },
        {
          "date": "2024-06-05",
          "value": 0
        },
        {
          "date": "2024-06-06",
          "value": 0
        },
        {
          "date": "2024-06-07",
          "value": 0
        },
        {
          "date": "2024-06-08",
          "value": 0
        },
        {
          "date": "2024-06-09",
          "value": 0
        },
        {
          "date": "2024-06-10",
          "value": 2.519999921321869
        },
        {
          "date": "2024-06-11",
          "value": 0
        },
        {
          "date": "2024-06-12",
          "value": 0.8199999928474426
        },
        {
          "date": "2024-06-13",
          "value": 2.1500000059604645
        },
        {
          "date": "2024-06-14",
          "value": 1.9300000071525574
        },
        {
          "date": "2024-06-15",
          "value": 1.8199999928474426
        },
        {
          "date": "2024-06-16",
          "value": 3.699999988079071
        },
        {
          "date": "2024-06-17",
          "value": 11.230000019073486
        },
        {
          "date": "2024-06-18",
          "value": 3.559999942779541
        },
        {
          "date": "2024-06-19",
          "value": 1.800000011920929
        },
        {
          "date": "2024-06-20",
          "value": 0
        },
        {
          "date": "2024-06-21",
          "value": 0
        },
        {
          "date": "2024-06-22",
          "value": 1.2699999809265137
        },
        {
          "date": "2024-06-23",
          "value": 1.5399999618530273
        },
        {
          "date": "2024-06-24",
          "value": 0
        },
        {
          "date": "2024-06-25",
          "value": 1.2800000309944153
        },
        {
          "date": "2024-06-26",
          "value": 0
        },
        {
          "date": "2024-06-27",
          "value": 0.23999999463558197
        },
        {
          "date": "2024-06-28",
          "value": 2.969999998807907
        },
        {
          "date": "2024-06-29",
          "value": 0
        },
        {
          "date": "2024-06-30",
          "value": 1.059999942779541
        },
        {
          "date": "2024-07-01",
          "value": 0.12999999523162842
        },
        {
          "date": "2024-07-02",
          "value": 1.850000023841858
        },
        {
          "date": "2024-07-03",
          "value": 0.4099999964237213
        },
        {
          "date": "2024-07-04",
          "value": 2.4000000655651093
        },
        {
          "date": "2024-07-05",
          "value": 0
        },
        {
          "date": "2024-07-06",
          "value": 0
        },
        {
          "date": "2024-07-07",
          "value": 1.1399999856948853
        },
        {
          "date": "2024-07-08",
          "value": 3.5799999237060547
        },
        {
          "date": "2024-07-09",
          "value": 0.49000000953674316
        },
        {
          "date": "2024-07-10",
          "value": 0
        },
        {
          "date": "2024-07-11",
          "value": 1.139999970793724
        },
        {
          "date": "2024-07-12",
          "value": 2.8299999237060547
        },
        {
          "date": "2024-07-13",
          "value": 0.7599999904632568
        },
        {
          "date": "2024-07-14",
          "value": 1.3199999630451202
        },
        {
          "date": "2024-07-15",
          "value": 1.1899999976158142
        },
        {
          "date": "2024-07-16",
          "value": 6.810000047087669
        },
        {
          "date": "2024-07-17",
          "value": 4.329999804496765
        },
        {
          "date": "2024-07-18",
          "value": 2.75
        },
        {
          "date": "2024-07-19",
          "value": 2.3300000429153442
        },
        {
          "date": "2024-07-20",
          "value": 2.009999990463257
        },
        {
          "date": "2024-07-21",
          "value": 10.309999942779541
        },
        {
          "date": "2024-07-22",
          "value": 16.44999998807907
        },
        {
          "date": "2024-07-23",
          "value": 0
        },
        {
          "date": "2024-07-24",
          "value": 0
        },
        {
          "date": "2024-07-25",
          "value": 0
        },
        {
          "date": "2024-07-26",
          "value": 2.119999945163727
        }
      ],
      "prediction": {
        "budget_usage": {
          "MAX": 1.63124,
          "estimated_budget_state": "BUDGET_LACK",
          "MIN": 0.312415,
          "TARGET": 0.075615
        },
        "inventory": [
          {
            "asin": "B0B7BGHXSY",
            "sku": "Matt See Through case_iP13_Black",
            "estimated_daily_units_sold": 0.0175395,
            "available_quantity": 41,
            "estimated_inventory_state": "SAFE"
          },
          {
            "asin": "B0B7BNSXVR",
            "sku": "Matt See Through case_iP13_Mint",
            "estimated_daily_units_sold": 0,
            "available_quantity": 42,
            "estimated_inventory_state": "SAFE"
          },
          {
            "asin": "B0B7BQ6KHC",
            "sku": "Matt See Through case_iP13Promax_Black",
            "estimated_daily_units_sold": 0,
            "available_quantity": 49,
            "estimated_inventory_state": "SAFE"
          },
          {
            "asin": "B0BBG4862P",
            "sku": "Tempered glass_14max",
            "estimated_daily_units_sold": 0,
            "available_quantity": 36,
            "estimated_inventory_state": "SAFE"
          },
          {
            "asin": "B0BBG5KGG4",
            "sku": "Tempered glass_14Promax",
            "estimated_daily_units_sold": 0.00653857,
            "available_quantity": 118,
            "estimated_inventory_state": "SAFE"
          }
        ]
      },
      competition_option: "none",
      target_same_sku_only_yn: "N",
      limit_cpc: 0
    }
  ]
  const [selectedPortfolioItem, setSelectedPortfolioItem] = useState<PortfolioListItem | null>(null)
  const [selectedProductItem, setSelectedProductItem] = useState<any | null>(null)
  const [parentPortfolioItem, setParentPortfolioItem] = useState<PortfolioListItem | null>(null)
  const [portfolioSearchText, setPortfolioSearchText] = useState('')

  const [collectionStatus, setCollectionStatus] = useState({
    overall_progress: 0
  })

  const fetchCollectionStatus = async () => {
    const collectionStatusResponse = await api.getCollectionStatus(
      selectedProfile.account_id,
      selectedMarketplace.marketplace_id,
      (session?.user as any).access_token
    )
    collectionStatusResponse && setCollectionStatus(collectionStatusResponse)
  }
  useEffect(() => {
    selectedProfile && selectedMarketplace && fetchCollectionStatus()
  }, [selectedProfile, selectedMarketplace])

  return (
    <div className="relative flex items-center justify-center w-full h-full divide-x divide-gray-200">
      <div className="flex-shrink-0 flex flex-col w-full h-full bg-white py-4 sm:py-6 px-8 sm:px-12">
        <div className="flex items-center justify-between">
          <h1 className="text-2xl text-gray-800 font-medium">{tos("title")}</h1>
          <button
            className="flex items-center gap-x-2 p-3 cursor-pointer rounded-md focus:outline-none text-sm overflow-hidden bg-blue-100 hover:bg-blue-200 text-blue-500 font-semibold"
            onClick={() => { }}>
            <PlusIcon
              className="flex-shrink-0 h-5 w-5"
              aria-hidden="true"
            />
            <div>{tos("topButton.addNew")}</div>
          </button>
        </div>
        {/* search box */}
        <div className="relative flex-shrink-0 mt-3 rounded-lg overflow-hidden cursor-pointer">
          <MagnifyingGlassIcon
            className={cn(
              "h-5 w-5 absolute top-1/2 left-3 transform -translate-y-1/2",
              portfolioSearchText ? "text-gray-500" : "text-gray-300"
            )}
          />
          <input
            type="text"
            className="w-full pl-10 pr-4 py-2 bg-white text-sm text-gray-500 placeholder:text-gray-300 border border-gray-100 focus:border-transparent focus:outline-blue-200 rounded-lg"
            value={portfolioSearchText}
            onChange={(e) => setPortfolioSearchText(e.target.value)}
            placeholder={tos("searchBar.placeholder")}
          />
        </div>
        {/* portfolio list */}
        <div
          className={cn(
            'grow relative w-full mt-3 rounded-lg bg-white border border-gray-100 overflow-y-scroll',
          )}
        >
          {/* List */}
          <div className="sticky inset-x-0 top-0 z-[1] flex items-center gap-x-3 py-3 text-center text-sm text-gray-400 font-bold bg-gray-100/40 backdrop-blur-md">
            <div className="w-[210px] pr-4">
              {tos("optSetList.header.status")}
            </div>
            <div className="grow px-4">
              {tos("optSetList.header.name")}
            </div>
            <div className="flex-shrink-0 w-[200px] px-4">
              {tos("optSetList.header.period")}
            </div>
            <div className="flex-shrink-0 w-[200px] px-4">
              {tos("optSetList.header.budgetPacing")}
            </div>
            <div className="flex-shrink-0 w-[160px] pr-4">
              {tos("optSetList.header.creationDate")}
            </div>
          </div>
          {fetchCounter.current > 0
            ? <ul className="animate-pulse p-6 space-y-3">
              <li className="w-full flex items-center gap-x-3">
                <div className="flex-shrink-0 w-40 h-20 rounded-md bg-gray-100"></div>
                <div className="flex-1 h-20 rounded-md bg-gray-100"></div>
                <div className="flex-shrink-0 w-[200px] h-20 rounded-md bg-gray-100"></div>
                <div className="flex-shrink-0 w-[200px] h-20 rounded-md bg-gray-100"></div>
                <div className="flex-shrink-0 w-40 h-20 rounded-md bg-gray-100"></div>
              </li>
              <li className="w-full flex items-center gap-x-3">
                <div className="flex-shrink-0 w-40 h-20 rounded-md bg-gray-100"></div>
                <div className="flex-1 h-20 rounded-md bg-gray-100"></div>
                <div className="flex-shrink-0 w-[200px] h-20 rounded-md bg-gray-100"></div>
                <div className="flex-shrink-0 w-[200px] h-20 rounded-md bg-gray-100"></div>
                <div className="flex-shrink-0 w-40 h-20 rounded-md bg-gray-100"></div>
              </li>
              <li className="w-full flex items-center gap-x-3">
                <div className="flex-shrink-0 w-40 h-20 rounded-md bg-gray-100"></div>
                <div className="flex-1 h-20 rounded-md bg-gray-100"></div>
                <div className="flex-shrink-0 w-[200px] h-20 rounded-md bg-gray-100"></div>
                <div className="flex-shrink-0 w-[200px] h-20 rounded-md bg-gray-100"></div>
                <div className="flex-shrink-0 w-40 h-20 rounded-md bg-gray-100"></div>
              </li>
              <li className="w-full flex items-center gap-x-3">
                <div className="flex-shrink-0 w-40 h-20 rounded-md bg-gray-100"></div>
                <div className="flex-1 h-20 rounded-md bg-gray-100"></div>
                <div className="flex-shrink-0 w-[200px] h-20 rounded-md bg-gray-100"></div>
                <div className="flex-shrink-0 w-[200px] h-20 rounded-md bg-gray-100"></div>
                <div className="flex-shrink-0 w-40 h-20 rounded-md bg-gray-100"></div>
              </li>
              <li className="w-full flex items-center gap-x-3">
                <div className="flex-shrink-0 w-40 h-20 rounded-md bg-gray-100"></div>
                <div className="flex-1 h-20 rounded-md bg-gray-100"></div>
                <div className="flex-shrink-0 w-[200px] h-20 rounded-md bg-gray-100"></div>
                <div className="flex-shrink-0 w-[200px] h-20 rounded-md bg-gray-100"></div>
                <div className="flex-shrink-0 w-40 h-20 rounded-md bg-gray-100"></div>
              </li>
              <li className="w-full flex items-center gap-x-3">
                <div className="flex-shrink-0 w-40 h-20 rounded-md bg-gray-100"></div>
                <div className="flex-1 h-20 rounded-md bg-gray-100"></div>
                <div className="flex-shrink-0 w-[200px] h-20 rounded-md bg-gray-100"></div>
                <div className="flex-shrink-0 w-[200px] h-20 rounded-md bg-gray-100"></div>
                <div className="flex-shrink-0 w-40 h-20 rounded-md bg-gray-100"></div>
              </li>
              <li className="w-full flex items-center gap-x-3">
                <div className="flex-shrink-0 w-40 h-20 rounded-md bg-gray-100"></div>
                <div className="flex-1 h-20 rounded-md bg-gray-100"></div>
                <div className="flex-shrink-0 w-[200px] h-20 rounded-md bg-gray-100"></div>
                <div className="flex-shrink-0 w-[200px] h-20 rounded-md bg-gray-100"></div>
                <div className="flex-shrink-0 w-40 h-20 rounded-md bg-gray-100"></div>
              </li>
              <li className="w-full flex items-center gap-x-3">
                <div className="flex-shrink-0 w-40 h-20 rounded-md bg-gray-100"></div>
                <div className="flex-1 h-20 rounded-md bg-gray-100"></div>
                <div className="flex-shrink-0 w-[200px] h-20 rounded-md bg-gray-100"></div>
                <div className="flex-shrink-0 w-[200px] h-20 rounded-md bg-gray-100"></div>
                <div className="flex-shrink-0 w-40 h-20 rounded-md bg-gray-100"></div>
              </li>
              <li className="w-full flex items-center gap-x-3">
                <div className="flex-shrink-0 w-40 h-20 rounded-md bg-gray-100"></div>
                <div className="flex-1 h-20 rounded-md bg-gray-100"></div>
                <div className="flex-shrink-0 w-[200px] h-20 rounded-md bg-gray-100"></div>
                <div className="flex-shrink-0 w-[200px] h-20 rounded-md bg-gray-100"></div>
                <div className="flex-shrink-0 w-40 h-20 rounded-md bg-gray-100"></div>
              </li>
              <li className="w-full flex items-center gap-x-3">
                <div className="flex-shrink-0 w-40 h-20 rounded-md bg-gray-100"></div>
                <div className="flex-1 h-20 rounded-md bg-gray-100"></div>
                <div className="flex-shrink-0 w-[200px] h-20 rounded-md bg-gray-100"></div>
                <div className="flex-shrink-0 w-[200px] h-20 rounded-md bg-gray-100"></div>
                <div className="flex-shrink-0 w-40 h-20 rounded-md bg-gray-100"></div>
              </li>
              <li className="w-full flex items-center gap-x-3">
                <div className="flex-shrink-0 w-40 h-20 rounded-md bg-gray-100"></div>
                <div className="flex-1 h-20 rounded-md bg-gray-100"></div>
                <div className="flex-shrink-0 w-[200px] h-20 rounded-md bg-gray-100"></div>
                <div className="flex-shrink-0 w-[200px] h-20 rounded-md bg-gray-100"></div>
                <div className="flex-shrink-0 w-40 h-20 rounded-md bg-gray-100"></div>
              </li>
              <li className="w-full flex items-center gap-x-3">
                <div className="flex-shrink-0 w-40 h-20 rounded-md bg-gray-100"></div>
                <div className="flex-1 h-20 rounded-md bg-gray-100"></div>
                <div className="flex-shrink-0 w-[200px] h-20 rounded-md bg-gray-100"></div>
                <div className="flex-shrink-0 w-[200px] h-20 rounded-md bg-gray-100"></div>
                <div className="flex-shrink-0 w-40 h-20 rounded-md bg-gray-100"></div>
              </li>
            </ul>
            : <ul className="divide-y divide-gray-100">
              {(() => {
                const searchResult = portfolioListItems.filter((item) => {
                  return portfolioSearchText
                    ? item.optimization_name?.toLowerCase().includes(portfolioSearchText.toLowerCase())
                    : true
                })
                return searchResult.length === 0
                  ? <div className="absolute inset-0 flex items-center justify-center w-full text-gray-300 text-sm font-normal">
                    {portfolioSearchText
                      ? tos("optSetList.content.optSetRow.searchResultNull")
                      : tos("optSetList.content.optSetRow.optSetNull")
                    }
                  </div>
                  : searchResult.map((item, index) => (
                    <li className="" key={index}>
                      <Disclosure defaultOpen={true}>
                        {({ open }) => (
                          <>
                            <div className="relative flex items-center cursor-pointer hover:bg-gray-100/40 text-center text-gray-500 text-sm">
                              <DisclosureButton className="flex-shrink-0 w-6 h-6 ml-6 mr-2 flex items-center justify-center rounded-full hover:bg-gray-200">
                                <ChevronDownIcon
                                  className={cn(
                                    "w-5 h-5 text-gray-400 transition duration-200 ease-in-out",
                                    open ? "rotate-180" : ""
                                  )}
                                  aria-hidden="true"
                                />
                              </DisclosureButton>
                              <div className="flex-1 flex items-center py-8 gap-x-3 overflow-hidden" onClick={() => { }}>
                                <div
                                  className={cn(
                                    'absolute inset-y-0 left-0 w-1 h-full',
                                    selectedPortfolioItem && item.id === selectedPortfolioItem.id ? 'bg-blue-400' : 'bg-transparent'
                                  )}
                                ></div>
                                <div className="relative flex-shrink-0 flex flex-col items-start justify-center gap-x-2 gap-y-2 w-[140px] pl-4">
                                  {(() => {
                                    const ineligibleProducts = item.target_products.filter((productItem) => productItem.eligibility_status === "INELIGIBLE")

                                    const currentDate = new Date()
                                    const endOfMonth = new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, 0).getDate()
                                    const budgetEndDate = item.ad_budget_end_date
                                      ? new Date(item.ad_budget_end_date)
                                      : new Date(currentDate.getFullYear(), currentDate.getMonth(), endOfMonth)
                                    const lastSumValue = item.total_cost.reduce((acc, cur) => acc + cur.value, 0) || 0
                                    const lastDate = new Date(item.total_cost[item.total_cost.length - 1].date)
                                    const expectedUsage = lastSumValue + item.prediction.budget_usage.TARGET * getDateDifference(lastDate, budgetEndDate)
                                    // const budgetAbnormality = item.ad_budget_amount && (expectedUsage * 0.9 > item.ad_budget_amount || expectedUsage * 1.1 < item.ad_budget_amount)
                                    const budgetAbnormality = item.prediction?.budget_usage?.estimated_budget_state === "BUDGET_OVER" || item.prediction?.budget_usage?.estimated_budget_state === "BUDGET_LACK"
                                    return (
                                      (budgetAbnormality || ineligibleProducts.length > 0) &&
                                      <div className={cn(
                                        "absolute left-3.5 -top-4 flex items-center gap-x-1 text-[10px] font-semibold text-red-400 rounded-md",
                                      )}>
                                        <ExclamationTriangleIcon className="h-3 w-3" />
                                        {tos("optSetList.content.optSetRow.statusAlert.abnormal")}
                                      </div>
                                    )
                                  })()}
                                  <PortfolioStatus portfolioItem={item} />
                                </div>
                                <div className="grow relative flex flex-col gap-y-0.5 px-4 overflow-hidden">
                                  <div className="w-full text-left">
                                    {(() => {
                                      const productsWithUsage = item.target_products.map((productItem, index) => {
                                        const total_product_usage = productItem.campaigns.reduce((acc: any, campaign: { campaign_total_cost: any[] }) => {
                                          return acc + (campaign.campaign_total_cost?.reduce((sum: any, current: { value: any }) => { return (sum + current.value) }, 0) ?? 0)
                                        }, 0)
                                        return { usage: total_product_usage, item: productItem }
                                      })
                                      const sortedProducts = productsWithUsage.sort((a, b) => b.usage - a.usage)
                                      const topProduct = sortedProducts.length > 0
                                        ? sortedProducts[0].item
                                        : {
                                          item_name: "No Title",
                                          image: ""
                                        }
                                      return (
                                        <div className="grow relative flex items-center gap-x-4 truncate">
                                          {topProduct.image
                                            ? (<img src={topProduct.image} alt="Item Image" className="flex-shrink-0 w-14 h-14 rounded" />)
                                            : (<div className="flex-shrink-0 flex items-center justify-center w-14 h-14 bg-gray-100 rounded">
                                              <ExclamationTriangleIcon className="h-6 w-6 text-gray-300" />
                                            </div>)
                                          }
                                          <div className="flex-1 flex flex-col gap-y-0.5 overflow-hidden">
                                            <div className="text-base text-gray-500 text-left font-semibold truncate">
                                              {item.optimization_name}
                                            </div>
                                            <div className="flex items-center gap-x-2 divide-x divide-gray-100">
                                              <div className="text-[10px] text-gray-300 font-semibold rounded-full break-normal hover:no-underline">
                                                {tos("optSetList.content.optSetRow.name.topProduct")}
                                              </div>
                                              <div className="pl-2 text-gray-400 text-xs truncate">
                                                {topProduct.item_name
                                                  ? topProduct.item_name
                                                  : "No Title"
                                                }
                                              </div>
                                            </div>
                                          </div>
                                        </div>
                                      )
                                    })()}
                                  </div>
                                </div>
                                <div className="flex-shrink-0 w-[200px] flex flex-col gap-y-0.5 px-4 overflow-hidden">
                                  <div className="text-[10px] text-blue-400 font-semibold text-left leading-tight">
                                    {item.optimization_goal === "SALES"
                                      ? (selectedProfile.account_type === "vendor" ? tos("optSetList.content.optSetRow.period.optObjective.vendorMaxSales") : tos("optSetList.content.optSetRow.period.optObjective.maxSales"))
                                      : item.optimization_goal === "ROI"
                                        ? (selectedProfile.account_type === "vendor" ? tos("optSetList.content.optSetRow.period.optObjective.vendorMaxProfit") : tos("optSetList.content.optSetRow.period.optObjective.maxProfit"))
                                        : item.optimization_goal === "REVENUE"
                                          ? tos("optSetList.content.optSetRow.period.optObjective.maxAdSales")
                                          : tos("optSetList.content.optSetRow.period.optObjective.maxRoas")
                                    }
                                  </div>
                                  <div className="text-xs font-semibold text-left leading-tight">
                                    {item.ad_budget_type === "DATERANGE"
                                      ? item.ad_budget_end_date && item.ad_budget_start_date
                                        ? formatDate(item.ad_budget_start_date, ".") + " - " + formatDate(item.ad_budget_end_date, ".")
                                        : item.ad_budget_start_date
                                          ? "Starting from " + formatDate(item.ad_budget_start_date, ".")
                                          : ""
                                      : "Monthly recurring" + (item.ad_budget_end_date ? " until " + formatDate(item.ad_budget_end_date, ".") : "")
                                    }
                                  </div>
                                </div>
                                <div className="flex-shrink-0 relative w-[200px] flex flex-col items-center justify-start gap-y-0.5 px-4 overflow-hidden">
                                  <div className="w-full flex items-center justify-between text-xs text-gray-500 font-semibold">
                                    <div className="">{item.ad_budget_type === "DATERANGE" ? tos("optSetList.content.optSetRow.budgetPacing.totalBudget") : tos("optSetList.content.optSetRow.budgetPacing.monthlyBudget")}</div>
                                    <div className="">
                                      {item.ad_budget_amount
                                        ? (item.total_cost.map(cost => cost.value).reduce((sum, current) => { return (sum + current) }, 0) * 100 / item.ad_budget_amount).toFixed(2)
                                        : 0
                                      }%
                                    </div>
                                  </div>
                                  <div className="relative w-full h-1 bg-gray-200 rounded-sm overflow-hidden">
                                    <div
                                      className="h-full bg-blue-400"
                                      style={{
                                        width: `${item.ad_budget_amount
                                          ? (item.total_cost.map(cost => cost.value).reduce((sum, current) => { return (sum + current) }, 0) * 100 / item.ad_budget_amount).toFixed(2)
                                          : 0}%`
                                      }}
                                    ></div>
                                  </div>
                                  <div className="w-full flex items-center justify-between text-xs">
                                    <div className="text-gray-400">0</div>
                                    <div className="text-gray-400">{formatCurrency(item.ad_budget_amount || 0, currencyCode, { maximumFractionDigits: 0, minimumFractionDigits: 0 })}</div>
                                  </div>
                                </div>
                                <div className="flex-shrink-0 w-[160px] pr-4">
                                  {formatDate(item.creation_datetime, ".")}
                                </div>
                              </div>
                            </div>
                            <Transition
                              show={open}
                              enter="transition-all duration-200"
                              enterFrom="max-h-0 opacity-0"
                              enterTo="max-h-screen opacity-100"
                              leave="transition-all duration-50"
                              leaveFrom="max-h-screen opacity-100"
                              leaveTo="max-h-0 opacity-0"
                            >
                              <DisclosurePanel static>
                                <div className="grow relative w-full overflow-y-scroll">
                                  {/* List */}
                                  <ul className="divide-y divide-gray-200">
                                    {item.target_products.map((product) => {
                                      return { ...product, creation_datetime: item.creation_datetime, request_status: item.request_status }
                                    }).map((productItem, index) =>
                                      <li
                                        className="relative flex items-center gap-x-3 py-6 cursor-pointer bg-gray-100/80 hover:bg-gray-200/60 text-center text-gray-500 text-sm"
                                        key={index}
                                        onClick={() => { }}
                                      >
                                        <div
                                          className={cn(
                                            'absolute inset-y-0 left-0 w-1 h-full',
                                            selectedProductItem && productItem.asin === selectedProductItem.asin ? 'bg-purple-400' : 'bg-transparent'
                                          )}
                                        ></div>
                                        <div className="flex-shrink-0 w-[60px]"></div>
                                        <div className="relative flex-shrink-0 flex flex-col items-start justify-center gap-x-2 gap-y-2 w-[140px]">
                                          {(() => {
                                            const targetProductInventory = item.prediction?.inventory?.find((inventory: any) => inventory.asin === productItem.asin)
                                            const eligibilityAbnormality = targetProductInventory
                                              ? targetProductInventory.estimated_inventory_state === "DANGER"
                                              : false
                                            return (
                                              productItem.eligibility_status === "INELIGIBLE"
                                                ? <div className={cn(
                                                  "absolute -left-0.5 -top-4 flex items-center gap-x-1 text-[10px] font-semibold text-red-400 rounded-md",
                                                )}>
                                                  <ExclamationTriangleIcon className="h-3 w-3" />
                                                  {tos("optSetList.content.productRow.statusAlert.abnormal")}
                                                </div>
                                                : eligibilityAbnormality
                                                  ? <div className={cn(
                                                    "absolute -left-0.5 -top-4 flex items-center gap-x-1 text-[10px] font-semibold text-orange-400 rounded-md",
                                                  )}>
                                                    <ExclamationTriangleIcon className="h-3 w-3" />
                                                    {tos("optSetList.content.productRow.statusAlert.warning")}
                                                  </div>
                                                  : ""
                                            )
                                          })()}
                                          <ProductStatus productItem={productItem} />
                                        </div>
                                        <div className="grow relative flex items-center gap-x-4 pr-8 overflow-hidden">
                                          {productItem.image
                                            ? (<img src={productItem.image} alt="Item Image" className="flex-shrink-0 w-10 h-10 rounded" />)
                                            : (<div className="flex-shrink-0 flex items-center justify-center w-10 h-10 bg-gray-100 rounded">
                                              <ExclamationTriangleIcon className="h-5 w-5 text-gray-300" />
                                            </div>)
                                          }
                                          <div className="flex-1 flex flex-col gap-y-0.5 overflow-hidden">
                                            <div className="text-xs text-gray-500 text-left font-semibold truncate">
                                              {productItem.item_name
                                                ? productItem.item_name
                                                : "No Title"
                                              }
                                            </div>
                                            <div className="flex items-center gap-x-2 divide-x divide-gray-200">
                                              <div className="text-xs text-red-400 font-semibold">{formatCurrency(productItem.listing_price || 0, currencyCode)}</div>
                                              {productItem.eligibility_status &&
                                                productItem.eligibility_status === "ELIGIBLE"
                                                ? <div className="pl-2 text-xs text-blue-400 font-semibold">{t("eligibility.eligible")}</div>
                                                : productItem.eligibility_status === "INELIGIBLE"
                                                  ? <div className="pl-2 text-xs text-gray-400 font-semibold">{t("eligibility.ineligible")}</div>
                                                  : <div className="pl-2 text-xs text-gray-400 font-semibold">{t("eligibility.unknown")}</div>
                                              }
                                            </div>
                                            <div className="flex items-center gap-x-2 divide-x divide-gray-200">
                                              <div className="text-[10px] text-gray-400">{tos("optSetList.content.productRow.asin")}: {productItem.asin}</div>
                                              <div className="pl-2 text-[10px] text-gray-400">{tos("optSetList.content.productRow.sku")}: {productItem.sku}</div>
                                            </div>
                                          </div>
                                        </div>
                                      </li>
                                    )}
                                  </ul>
                                </div>
                              </DisclosurePanel>
                            </Transition>
                          </>
                        )}
                      </Disclosure>
                    </li>
                  ))
              })()}
            </ul>
          }
        </div>
      </div>
      <div className="absolute inset-0 size-full px-4 flex items-center justify-center bg-gray-100/60 backdrop-blur-sm z-[5]">
        <div className="w-full max-w-lg flex flex-col items-center">
          <div className="text-center text-gray-800 font-semibold text-lg">
            We are collecting the essential data to initialize Optapex.
            <br />
            Please wait 1 to 2 days after subscribing to the service.
            <div className="mt-8 mb-4 flex items-center justify-center gap-x-4">
              <div className="relative w-full h-3 bg-gray-300 rounded-full overflow-hidden">
                <div
                  className="absolute top-0 left-0 h-full bg-gradient-to-r from-purple-500 to-blue-500 rounded-full animate-pulse"
                  style={{
                    width: `${collectionStatus?.overall_progress * 100}%`
                  }}></div>
              </div>
              <div className="text-sm text-gray-600 text-right">
                {(collectionStatus?.overall_progress * 100).toFixed(2)}%
              </div>
            </div>
            <ul className="w-full text-left text-gray-700 divide-y divide-gray-200">
              {Object.entries(collectionStatus)
                .filter(([key]) => key !== "overall_status" && key !== "overall_progress")
                .map(([key, value]: [string, any]) => (
                <li key={key} className="py-4 w-full flex items-center justify-between">
                  <div className="">
                    <div className="text-sm font-semibold capitalize">{key.replace(/_/g, ' ')}</div>
                    <div className="mt-1 text-xs text-gray-500 font-normal">
                      {value.message}
                    </div>
                  </div>
                  {value.status === "COMPLETED"
                    ? <div className="flex items-center gap-x-1 text-xs text-gray-600">
                        <CheckBadgeIcon className="h-4 w-4 text-green-500" />
                        COMPLETED
                      </div>
                    : value.status === "IN_PROGRESS"
                      ? <div className="flex items-center gap-x-1 text-xs text-gray-600">
                          <ClockIcon className="h-4 w-4 text-blue-500" />
                          IN PROGRESS
                        </div>
                      : value.status === "PENDING"
                        ? <div className="flex items-center gap-x-1 text-xs text-gray-600">
                            <ExclamationCircleIcon className="h-4 w-4 text-gray-400" />
                            PENDING
                          </div>
                        : <div className="flex items-center gap-x-1 text-xs text-gray-600">
                            <ExclamationCircleIcon className="h-4 w-4 text-gray-400" />
                            {value.status}
                          </div>
                  }
                </li>
              ))}
            </ul>
          </div>
        </div>
      </div>
    </div>
  )
}
