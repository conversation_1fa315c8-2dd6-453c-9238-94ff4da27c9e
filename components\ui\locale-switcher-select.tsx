'use client';

import { LanguageIcon } from '@heroicons/react/24/solid'
import { Listbox, Transition } from "@headlessui/react"
import { cn } from "@/utils/msc"
import { Fragment, useTransition } from "react"
import { Locale } from '@/config'
import { setUserLocale } from '@/utils/locale'
import { Check } from "@/components/ui/check"
import { useRouter } from 'next/navigation';

type Props = {
  defaultValue: string;
  items: Array<{value: string; label: string}>;
  label: string;
};

export default function LocaleSwitcherSelect({
  defaultValue,
  items,
  label
}: Props) {
  const router = useRouter();
  const [isPending, startTransition] = useTransition();

  function onChange(value: string) {
    const locale = value as Locale;
    startTransition(() => {
      setUserLocale(locale).then(() => {
        router.refresh();
      });
    });
  }

  return (
    <div className="relative z-[1] flex h-8 w-8">
      <Listbox value={defaultValue} onChange={onChange}>
        {({ open }) => (
        <div className="relative">
          <Listbox.Button
            aria-label={label}
            className={cn(
              "group rounded-lg p-2 transition-colors hover:bg-accent",
              isPending && "pointer-events-none opacity-60"
            )}
          >
            <LanguageIcon className="h-4 w-4 text-slate-600 transition-colors group-hover:text-slate-900" />
          </Listbox.Button>
          <Transition
            as={Fragment}
            leave="transition ease-in duration-100"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
            <Listbox.Options
              className={cn(
                "absolute right-0 mt-1 max-h-60 w-[120px] overflow-auto rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black/5 focus:outline-none sm:text-sm",
              )}
            >
              {items.map((item) => (
                <Listbox.Option
                  key={item.value}
                  className={({ active }) =>
                    `relative cursor-pointer select-none py-2 pl-4 pr-10 ${
                      active ? 'bg-blue-100/40 text-blue-900' : 'text-gray-600'
                    }`
                  }
                  value={item.value}
                >
                  {({ selected }) => (
                    <>
                      <span
                        className={`block truncate ${
                          selected ? 'font-medium' : 'font-normal'
                        }`}
                      >
                        {item.label}
                      </span>
                      {item.value === defaultValue ? (
                        <div className="absolute inset-y-0 right-0 flex items-center pr-3">
                          <div className="shrink-0 flex items-center justify-center w-5 h-5 rounded-full">
                            <Check className="w-4 h-4 stroke-blue-400" aria-hidden="true" />
                          </div>
                        </div>
                      ) : null}
                    </>
                  )}
                </Listbox.Option>
              ))}
            </Listbox.Options>
          </Transition>
        </div>
        )}
      </Listbox>
    </div>
  );
}