"use client";

import Image from "next/image"
import { useSession } from "next-auth/react"
import { redirect } from "next/navigation"
import { useState } from "react"
import { api } from "@/utils/api"

const validatePassword = (password: string) => {
  const minLength = 8;
  const hasUpperCase = /[A-Z]/.test(password);
  const hasLowerCase = /[a-z]/.test(password);
  const hasNumber = /[0-9]/.test(password);
  const hasSpecialChar = /[!@#$%^&*(),.?":{}|<>]/.test(password);

  if (password.length < minLength) {
    return "Password must be at least 8 characters long";
  }
  if (!hasUpperCase) {
    return "Password must contain at least one uppercase letter";
  }
  if (!hasLowerCase) {
    return "Password must contain at least one lowercase letter";
  }
  if (!hasNumber) {
    return "Password must contain at least one number";
  }
  if (!hasSpecialChar) {
    return "Password must contain at least one special character";
  }
  return "";
};

export default function PasswordExpiredPageLayout() {
  const { data: session, status } = useSession()
  const [passwordType, setPasswordType] = useState("password");
  const [errorMessage, setErrorMessage] = useState("");

  const handleSkipForNow = async () => {
    try {
      if (!(session?.user as any).access_token) {
        console.log('access token is missing in the session.')
      }
      let response = await api.skipPasswordChange((session?.user as any).access_token)
      if (response.status !== "success") {
        throw new Error("Network response was not ok");
      } else {
        // Redirect to the dashboard
        redirect("/dashboard?tab=reports")
      }
    } catch (error) {
      // Handle error response
      console.error("Failed to skip password change:", error);
    }
  };

  const handleSubmit = async (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault();
    const formData = new FormData(event.currentTarget);
    const newPassword = formData.get("new-password") as string | null;
    const confirmPassword = formData.get("confirm-password") as string | null;
    const validationError = validatePassword(newPassword || "");
    
    if (validationError) {
      setErrorMessage(validationError);
      return;
    }
    
    if (newPassword !== confirmPassword) {
      setErrorMessage("Passwords do not match");
      return;
    } else {
      setErrorMessage("");
    }

    try {
      if (!(session?.user as any).access_token) {
        console.log('access token is missing in the session.')
      }
      let response = await api.changePassword(
        newPassword || "",
        (session?.user as any).access_token
      )
      if (response.status !== "success") {
        throw new Error("Network response was not ok");
      } else {
        // Redirect to the dashboard
        redirect("/dashboard?tab=reports");
      }
    } catch (error) {
      // Handle error response
      console.error("Failed to change password:", error);
    }
  };

  return (
    <div className="relative">
      <div className="w-full text-[#232F3F]">
        <div
          className="relative w-full h-screen bg-center bg-cover"
          style={{ backgroundImage: "url(/ui/auth-section-bg.png)" }}
        >
          <div className="absolute inset-0 w-full h-full flex items-center justify-center">
            <div className="max-w-full sm:max-w-[402px] p-6 mx-auto sm:border border-gray-200 bg-white rounded-lg">
              <Image
                src="/logo/optapex-logo-gray.svg"
                alt="Optapex Logo"
                className="w-auto h-4"
                width={197}
                height={60}
              />
              
              <div className="mt-8">
                <div className="text-gray-700">
                  <div className="text-2xl font-bold">Change password</div>
                  <div className="mt-4 text-sm font-normal">It&apos;s been 90 days since your last password update. Please create a new password for security.</div>
                </div>
                <form className="mt-8" onSubmit={handleSubmit}>
                  <div className="">
                    <label htmlFor="new-password" className="block text-sm font-semibold text-gray-500">
                      New Password
                    </label>
                    <input
                      type={passwordType}
                      id="new-password"
                      name="new-password"
                      className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                      required
                    />
                  </div>
                  <div className="mt-4">
                    <label htmlFor="confirm-password" className="block text-sm font-semibold text-gray-500">
                      Confirm New Password
                    </label>
                    <input
                      type={passwordType}
                      id="confirm-password"
                      name="confirm-password"
                      className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                      required
                    />
                  </div>
                  {errorMessage && <div className="mt-2 text-red-400 text-xs font-semibold">{errorMessage}</div>}
                  <div className="mt-4 flex items-center">
                    <input
                      type="checkbox"
                      id="show-passwords"
                      name="show-passwords"
                      className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                      onChange={(e) => {
                        setPasswordType(e.target.checked ? 'text' : 'password');
                      }}
                    />
                    <label htmlFor="show-passwords" className="ml-2 block text-sm text-gray-500">
                      Show passwords
                    </label>
                  </div>
                  <div className="mt-6 flex items-center gap-x-4">
                    <button
                      type="button"
                      className="w-1/2 flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-gray-700 bg-gray-200 hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500"
                      onClick={handleSkipForNow}
                    >
                      Skip for Now
                    </button>
                    <button
                      type="submit"
                      className="w-1/2 flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                    >
                      Update Password
                    </button>
                  </div>
                </form>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}