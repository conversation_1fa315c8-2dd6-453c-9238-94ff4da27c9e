{"name": "mop-global-web", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "test": "jest", "test:watch": "jest --watch", "test:ci": "jest --ci --coverage", "test:module": "yarn run cypress:run:module", "test:module:default": "yarn run cypress:run:module:default", "test:smoke": "export CYPRESS_BASE_URL=http://127.0.0.1:3000 && wait-on http://127.0.0.1:3000 && cypress run --config-file cypress-module.config.ts --config supportFile=tests/support/index.module.js --spec tests/module/specs/Smoke.module.cy.js", "test:smoke:remote": "cypress run --config-file cypress-module.config.ts --config supportFile=tests/support/index.module.js --spec tests/module/specs/Smoke.module.cy.js", "cypress:version": "cypress version", "cypress:open": "export CYPRESS_BASE_URL=http://localhost:3000 && cypress open --config-file cypress-module.config.ts --config supportFile=tests/support/index.local.js", "cypress:open:delayMode": "export CYPRESS_BASE_URL=http://localhost:3000 && cypress open --config-file cypress-module.config.ts --config supportFile=tests/support/index.delayMode.js", "cypress:open:window": "set CYPRESS_BASE_URL=http://localhost:3000 && wait-on http://localhost:3000 && cypress open --config-file cypress-module.config.ts --config supportFile=tests/support/index.local.js", "cypress:open:delayMode:window": "cypress open --config-file cypress-module.config.ts --config supportFile=tests/support/index.delayMode.js", "cypress:run:module": "export CYPRESS_BASE_URL=http://127.0.0.1:3000 && wait-on http://127.0.0.1:3000 && cypress run --config-file cypress-module.config.ts --config supportFile=tests/support/index.module.js", "cypress:run:module:default": "cypress run --config-file cypress-module.config.ts --config supportFile=tests/support/index.module.js"}, "dependencies": {"@emotion/react": "^11.11.3", "@emotion/styled": "^11.11.0", "@headlessui/react": "^2.2.0", "@heroicons/react": "^2.1.5", "@mui/icons-material": "^5.15.6", "@mui/material": "^5.15.6", "@mui/x-date-pickers": "^6.19.2", "@next/third-parties": "^15.0.3", "@radix-ui/react-avatar": "^1.0.3", "@radix-ui/react-collapsible": "^1.0.3", "@radix-ui/react-dropdown-menu": "^2.0.5", "@radix-ui/react-navigation-menu": "^1.1.3", "@radix-ui/react-slot": "^1.0.2", "@sentry/nextjs": "^7.105.0", "@types/react-csv": "^1.1.10", "@types/react-datepicker": "^4.19.5", "@types/react-plotly.js": "^2.6.3", "@types/react-simple-maps": "^3.0.6", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "cookies-next": "^4.1.1", "dotenv": "^10.0.0", "env-cmd": "^10.1.0", "formik": "^2.4.5", "framer-motion": "^12.0.6", "lottie-react": "^2.4.0", "lucide-react": "^0.274.0", "material-react-table": "^2.9.2", "next": "14.2.21", "next-auth": "^5.0.0-beta.4", "next-intl": "^3.17.2", "plotly.js": "^2.32.0", "react": "^18.2.0", "react-circle-flags": "^0.0.20", "react-csv": "^2.2.2", "react-datepicker": "^6.1.0", "react-dom": "^18.2.0", "react-plotly.js": "^2.6.0", "react-simple-maps": "^3.0.0", "react-type-animation": "^3.2.0", "tailwind-merge": "^1.14.0", "tailwindcss-animate": "^1.0.7"}, "devDependencies": {"@babel/core": "^7.28.3", "@babel/preset-env": "^7.28.3", "@babel/preset-react": "^7.27.1", "@babel/preset-typescript": "^7.27.1", "@testing-library/dom": "^10.4.1", "@testing-library/jest-dom": "^6.8.0", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/jest": "^30.0.0", "@types/node": "^18", "@types/react": "^18.2.23", "@types/react-dom": "^18.2.8", "autoprefixer": "^10.4.15", "babel-jest": "^30.1.1", "cypress": "12.17.4", "eslint": "^8", "eslint-config-next": "14.0.4", "identity-obj-proxy": "^3.0.0", "jest": "^30.1.1", "jest-environment-jsdom": "^30.1.1", "postcss": "^8.4.29", "tailwindcss": "^3.3.3", "typescript": "^5.2.2", "wait-on": "^8.0.4"}, "resolutions": {"d3-color": "^3.1.0", "axios": "^1.7.4", "@cypress/request": "^3.0.0", "debug": "^4.4.0"}}