"use client"

import { api } from "@/utils/api"
import { useSession } from "next-auth/react"
import { forwardRef, Fragment, useEffect, useRef, useState } from 'react'
import DatePicker, { registerLocale } from "react-datepicker"
import { ko } from "date-fns/locale"
import { Dialog, DialogPanel, Popover, PopoverButton, PopoverPanel, Transition, TransitionChild } from '@headlessui/react'
import { ProfileOption } from "@/components/dashboard/profile-select"
import { MarketplaceOption } from "@/components/dashboard/marketplace-select"
import { cn, currencyFormat, formatDate } from "@/utils/msc"
import { ExclamationTriangleIcon, QuestionMarkCircleIcon, XMarkIcon, ChevronUpDownIcon } from "@heroicons/react/20/solid"
import { useLocale, useTranslations } from 'next-intl'
import dynamic from "next/dynamic"

registerLocale('ko', ko)

const KeywordBrandShareGraph = dynamic(() => import('@/components/dashboard/keyword-brand-share-graph'), { ssr: false })

interface CompetitionWeeklyKeywordViewSliderProps {
  isOpen: boolean;
  onClose: () => void;
  selectedProfile: ProfileOption;
  selectedMarketplace: MarketplaceOption;
  selectedKeywordItem?: any;
  selectedDate: Date;
  brandCompetitionWeeklyOverview: any[];
}

export default function CompetitionWeeklyKeywordViewSlider({
  isOpen,
  onClose,
  selectedProfile,
  selectedMarketplace,
  selectedKeywordItem,
  selectedDate,
  brandCompetitionWeeklyOverview
}: CompetitionWeeklyKeywordViewSliderProps) {
  const t = useTranslations("component")
  const tos = useTranslations('optimizationSets')
  const tk = useTranslations('CompetitionPage')
  const locale = useLocale()
  const { data: session, status } = useSession()  
  const keywordDetailFetchCounter = useRef(0)
  const [orgKeywordDetail, setOrgKeywordDetail] = useState<any>(null)
  const [selectedKeywordDetail, setSelectedKeywordDetail] = useState<any>(null)
  const [selectedKeywordDetailProducts, setSelectedKeywordDetailProducts] = useState<any>(null)
  const [targetDate,  setTargetDate] = useState<Date>(selectedDate)
  const DateRangeInput = forwardRef(({ value, onClick }: { value: string, onClick: () => void }, ref) => {
    // Get the date from value (format is "yyyy.MM.dd")
    const parts = value.split('.');
    if (parts.length === 3) {
      const date = new Date(parseInt(parts[0]), parseInt(parts[1]) - 1, parseInt(parts[2]));
      // Calculate end date (7 days from selected date)
      const endDate = new Date(date);
      endDate.setDate(endDate.getDate() + 6);
      
      // Format end date
      const endDateFormatted = formatDate(endDate, ".");
      
      return (
        // @ts-ignore
        <button className="min-w-[200px] overflow-hidden cursor-pointer rounded-lg bg-gray-100/10 hover:bg-gray-100/30 text-gray-300 py-2 px-3 text-left shadow-sm focus:outline-none focus-visible:border-indigo-500 focus-visible:ring-2 focus-visible:ring-white/75 focus-visible:ring-offset-2 focus-visible:ring-offset-orange-300 sm:text-sm" onClick={onClick} ref={ref}>
          <div className="inline-flex">
            {value} - {endDateFormatted}
          </div>
        </button>
      );
    }
    
    return (
      // @ts-ignore
      <button className="min-w-[200px] overflow-hidden cursor-pointer rounded-lg bg-gray-100/10 hover:bg-gray-100/30 text-gray-300 py-2 px-3 text-left shadow-sm focus:outline-none focus-visible:border-indigo-500 focus-visible:ring-2 focus-visible:ring-white/75 focus-visible:ring-offset-2 focus-visible:ring-offset-orange-300 sm:text-sm" onClick={onClick} ref={ref}>
        <div className="inline-flex">
          {value}
        </div>
      </button>
    );
  });
  DateRangeInput.displayName = 'DateRangeInput'

  const productListItemCard = (item: any, mode: string) => {
    return (
      <div className="grow relative flex items-center gap-x-4 px-4 overflow-hidden">
        <div className="flex-1 flex flex-col gap-y-0.5 overflow-hidden">
          <div
            className={cn(
              "text-xs text-left font-semibold truncate",
              mode === "dark" ? "text-gray-200" : "text-gray-500"
            )}
            title={item.product || "No Title"}
          >
            {item.product
              ? item.product
              : "No Title"
            }
          </div>
          <div className={cn(
            "flex items-center gap-x-2 divide-x divide-gray-100",
            mode === "dark" ? "divide-gray-600" : "divide-gray-100"
          )}>
            <div className="text-xs text-red-400 font-semibold">{currencyFormat.format(item.price || 0)}</div>
            {item.competitor_type &&
              <div className="pl-2 text-xs text-blue-400 font-semibold">{item.competitor_type}</div>
            }
          </div>
          <div className={cn(
            "mt-1 flex items-center gap-x-2 divide-x",
            mode === "dark" ? "divide-gray-600" : "divide-gray-100"
          )}>
            <div className="text-[10px] text-gray-400 truncate">{tos("detailModal.optSet.budgetTab.table.content.productInfo.asin")}: {item.asin}</div>
          </div>
          <div className={cn(
            "flex items-center gap-x-2 divide-x",
            mode === "dark" ? "divide-gray-600" : "divide-gray-100"
          )}>
            <div className="text-[10px] text-gray-400 truncate">
              Click Share: {Array.isArray(item.click_share) ? item.click_share[0] : item.click_share}
            </div>
            <div className="pl-2 text-[10px] text-gray-400 truncate">
              Click Share Rank: {Array.isArray(item.click_share_rank) ? item.click_share_rank[0] : item.click_share_rank}
            </div>
          </div>
          <div className={cn(
            "flex items-center gap-x-2 divide-x",
            mode === "dark" ? "divide-gray-600" : "divide-gray-100"
          )}>
            
            {Array.isArray(item.classification_title)
              ? (<Popover className="relative inline-block ml-auto">
                <PopoverButton className="inline-flex items-center gap-x-0.5 text-gray-400 hover:text-gray-200 text-xs font-semibold focus:outline-none">
                  <span>Show more</span>
                  <ChevronUpDownIcon className="w-4 h-4" />
                </PopoverButton>
                <PopoverPanel
                  anchor={{ to: 'bottom end' }}
                  className="z-10 max-h-60 min-w-[200px] overflow-auto rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black/5 focus:outline-none sm:text-xs divide-y divide-gray-200"
                >
                  {item.classification_title.map((title: any, idx: number) => (
                    <div key={idx} className="px-3 py-2 space-y-2">
                      <div>
                        <div className="text-[10px] text-gray-400">Classification Title</div>
                        <div className="text-xs text-gray-600">{title}</div>
                      </div>
                      <div>
                        <div className="text-[10px] text-gray-400">Sales Rank</div>
                        <div className="text-xs text-gray-600">{typeof item.sales_rank[idx] === "number" ? item.sales_rank[idx] : "N/A"}</div>
                      </div>
                    </div>
                  ))}
                </PopoverPanel>
              </Popover>)
              : <div className="relative pl-2 text-[10px] text-gray-400 truncate">
                Sales Rank: {item.sales_rank}
              </div>
            }
          </div>
        </div>
      </div>
    )
  }
  
  const fetchBrandCompetitionKeywordDetail = async (signal: AbortSignal, searchTerm: string, date: Date, onLoad: boolean) => {
    if (!(session?.user as any).access_token) {
      console.log('access token is missing in the session.')
    }
    keywordDetailFetchCounter.current += 1
    let brandCompetitionKeywordDetailResponse = await api.getBrandCompetitionWeeklyKeywordDetail(
      selectedProfile.account_id,
      selectedMarketplace.marketplace_id,
      formatDate(date, "-"),
      searchTerm,
      (session?.user as any).access_token,
      signal
    )
    keywordDetailFetchCounter.current -= 1
    brandCompetitionKeywordDetailResponse && (onLoad ? setOrgKeywordDetail(brandCompetitionKeywordDetailResponse) : setSelectedKeywordDetail(brandCompetitionKeywordDetailResponse))
  }
  const fetchBrandCompetitionKeywordDetailProducts = async (signal: AbortSignal, searchTerm: string, date: Date) => {
    if (!(session?.user as any).access_token) {
      console.log('access token is missing in the session.')
    }
    keywordDetailFetchCounter.current += 1
    let brandCompetitionKeywordDetailProductsResponse = await api.getBrandCompetitionWeeklyKeywordDetailProducts(
      selectedProfile.account_id,
      selectedMarketplace.marketplace_id,
      formatDate(date, "-"),
      searchTerm,
      (session?.user as any).access_token,
      signal
    )
    keywordDetailFetchCounter.current -= 1
    brandCompetitionKeywordDetailProductsResponse && setSelectedKeywordDetailProducts(brandCompetitionKeywordDetailProductsResponse)
  }

  useEffect(() => {
    if (isOpen && selectedKeywordItem) {
      const controller = new AbortController()
      fetchBrandCompetitionKeywordDetail(controller.signal, selectedKeywordItem.search_term, targetDate, false)
      fetchBrandCompetitionKeywordDetailProducts(controller.signal, selectedKeywordItem.search_term, targetDate)
      return () => controller.abort()
    } else {
      setSelectedKeywordDetail(null)
      setSelectedKeywordDetailProducts(null)
    }
  }, [isOpen, selectedKeywordItem, targetDate])

  useEffect(() => {
    if (isOpen && selectedKeywordItem && selectedDate) {
      const controller = new AbortController()
      fetchBrandCompetitionKeywordDetail(controller.signal, selectedKeywordItem.search_term, targetDate, true)
      return () => controller.abort()
    }
  }, [])

  return (
    <Transition appear show={isOpen}>
      <Dialog as="div" className="relative z-10 focus:outline-none" onClose={() => onClose()}>
        <div className="fixed inset-0 bg-black/30 backdrop-blur-sm" aria-hidden="true" />
        <div className="fixed inset-0 z-10 w-screen overflow-y-auto">
          <div className="flex min-h-full items-center justify-center p-4">
            <TransitionChild
              enter="ease-out duration-300"
              enterFrom="opacity-0 transform-[scale(95%)]"
              enterTo="opacity-100 transform-[scale(100%)]"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 transform-[scale(100%)]"
              leaveTo="opacity-0 transform-[scale(95%)]"
            >
              <DialogPanel className="relative w-[960px] h-[785px] flex flex-col p-6 bg-gray-900/90 rounded-xl">
                <div className="flex-shrink-0 w-full">
                  <div className="flex items-center justify-end">
                    <button onClick={() => onClose()}>
                      <XMarkIcon className="h-5 w-5 text-gray-500 hover:text-gray-200" />
                    </button>
                  </div>
                </div>
                {keywordDetailFetchCounter.current > 0
                  ? (<div className="animate-pulse flex-1 flex items-center gap-x-6 pt-4">
                    <div className="relative w-2/3 h-full rounded-md bg-gray-100/10 shadow-sm overflow-hidden"></div>
                    <div className="relative w-1/3 h-full rounded-md bg-gray-100/10 shadow-sm overflow-hidden"></div>
                  </div>)
                  : orgKeywordDetail && selectedKeywordDetail
                    ? (<div className="relative grow min-h-0 flex items-center gap-x-6">
                        <div className="relative w-2/3 h-full pt-10">
                          <KeywordBrandShareGraph
                            myData={orgKeywordDetail?.click_share_chart
                              ? orgKeywordDetail.click_share_chart.map((item:any) => ({
                                  date: item.date,
                                  value: item.brands?.my_brand / 100
                                }))
                              : []
                            }
                            otherData={orgKeywordDetail?.click_share_chart
                              ? orgKeywordDetail.click_share_chart.map((item:any) => ({
                                  date: item.date,
                                  value: item.brands
                                    ? Object.entries(item.brands)
                                        .filter(([key]) => key !== "my_brand")
                                        .reduce((sum, [, val]) => sum + (typeof val === "number" ? val : 0), 0) / 100
                                    : 0
                                }))
                              : []
                            }
                            targetDate={targetDate}
                            setTargetDate={setTargetDate}
                            isWeekly={true}
                          />
                          <div className="absolute inset-0 flex flex-col justify-between h-min">
                            <div className="pl-6 pb-6">
                              <h1 className="flex items-center gap-x-4 text-2xl text-gray-200 font-medium">
                                <div className="flex items-center gap-x-1">
                                  {tk("keywordModalTitle")}
                                  <Popover className="relative flex items-center justify-center">
                                    {({ open }) => (
                                      <>
                                        <PopoverButton
                                          className={cn(
                                            "inline-flex items-center justify-center rounded-full text-gray-300 hover:text-gray-400 focus:outline-none"
                                          )}
                                        >
                                          <QuestionMarkCircleIcon className="w-5 h-5"/>
                                        </PopoverButton>
                                        <Transition
                                          as={Fragment}
                                          enter="transition ease-out duration-200"
                                          enterFrom="opacity-0 translate-y-1"
                                          enterTo="opacity-100 translate-y-0"
                                          leave="transition ease-in duration-150"
                                          leaveFrom="opacity-100 translate-y-0"
                                          leaveTo="opacity-0 translate-y-1"
                                        >
                                          <PopoverPanel className="absolute left-full top-0 z-10 ml-2 w-screen max-w-xs translate-y-0 transform px-4 sm:px-0">
                                            <div className="overflow-hidden rounded-lg shadow-lg">
                                              <div className="relative p-4 bg-gray-900/90 text-xs text-white font-normal text-left">
                                                {tk.rich("tooltip.keywordModalTitle",{
                                                  enter: () =>  <br/>
                                                })}
                                              </div>
                                            </div>
                                          </PopoverPanel>
                                        </Transition>
                                      </>
                                    )}
                                  </Popover>
                                </div>
                                {brandCompetitionWeeklyOverview && brandCompetitionWeeklyOverview.length > 0 && (
                                  <span className="flex items-center gap-x-1 text-[10px] text-gray-400 font-normal">
                                    <ExclamationTriangleIcon className="h-3 w-3 text-gray-400" />
                                    Last updated at {formatDate(new Date(brandCompetitionWeeklyOverview[brandCompetitionWeeklyOverview.length - 1].date), ".")}
                                  </span>
                                )}
                              </h1>
                              {selectedKeywordItem && (
                                <div className="flex items-center justify-between gap-x-4 mt-2">
                                  <div className="relative flex-shrink-0 z-[2]">
                                    <DatePicker
                                      selectsRange={false}
                                      // @ts-ignore
                                      includeDateIntervals={
                                        brandCompetitionWeeklyOverview && brandCompetitionWeeklyOverview.length > 0
                                          ? brandCompetitionWeeklyOverview.map((item: any) => {
                                              const date = new Date(item.date);
                                              const endDate = new Date(date);
                                              endDate.setDate(endDate.getDate() + 6);
                                              // { start: "2021/02/08", end: "2021/02/14" },
                                              return { start: formatDate(date, "/"), end: formatDate(endDate, "/") };
                                            })
                                          : []
                                      }
                                      selected={targetDate}
                                      showWeekPicker
                                      onChange={(update) => {
                                        setTargetDate(update as Date)
                                      }}
                                      dateFormat="yyyy.MM.dd"
                                      calendarClassName="dashboard-date-range"
                                      // @ts-ignore
                                      customInput={<DateRangeInput minWidth={'110'}/>}
                                      locale={locale}
                                    />
                                  </div>
                                  <div className="flex items-center gap-x-2 text-sm text-gray-400 font-semibold">
                                    <div className="flex-shrink-0 py-0.25 px-1.5 border border-gray-400 text-gray-400 text-[10px] rounded-md font-semibold">keyword</div>
                                    {selectedKeywordItem.search_term}
                                  </div>
                                </div>
                              )}
                            </div>
                          </div>
                        </div>
                        <div className="relative w-1/3 h-full flex items-end justify-between">
                          <div className="w-full flex flex-col gap-y-4">
                            <div className="p-3 rounded-md bg-gray-100/10 shadow-sm backdrop-blur-sm">
                              <div className="flex items-center gap-x-1.5 text-gray-400 text-xs font-semibold">
                                {tk("table.header.queryVolumeRank")}
                              </div>
                              <div className="mt-1 flex items-center gap-x-2">
                                <div className="">
                                  <span className="mt-1 text-lg text-gray-100 font-semibold">
                                    {selectedKeywordItem.search_frequency_rank.toLocaleString()}
                                  </span>
                                  <span className="text-xs text-gray-200"> / </span>
                                  <span className="text-xs text-gray-200">
                                    {((1 / selectedKeywordItem.relative_search_frequency_rank) * selectedKeywordItem.search_frequency_rank).toLocaleString(undefined, { maximumFractionDigits: 0 })}
                                  </span>
                                </div>
                                {selectedKeywordItem.relative_search_frequency_rank && selectedKeywordItem.relative_search_frequency_rank <= 0.4 && (
                                  <div className="flex-shrink-0 py-0.5 px-1.5 border border-orange-300 text-orange-300 text-[10px] rounded-md font-semibold">
                                    Top {(selectedKeywordItem.relative_search_frequency_rank * 100).toFixed(0)}%
                                  </div>
                                )}
                              </div>
                            </div>
                            <div className="p-3 rounded-md bg-gray-100/10 shadow-sm backdrop-blur-sm">
                              <div className="flex items-center gap-x-1.5 text-gray-400 text-xs font-semibold">
                                {tk("keywordModal.myBrandTotalClickShare")}
                                <div className="w-2 h-2 rounded-full bg-blue-400"></div>
                              </div>
                              <div className="mt-1 text-lg text-gray-100 font-semibold">
                                {selectedKeywordDetail.my_brand_click_share
                                  ? (selectedKeywordDetail.my_brand_click_share / 100).toFixed(2)
                                  : 0
                                }
                              </div>
                              {(() => {
                                const myBrands = selectedKeywordDetailProducts.items.filter((item: any) => item.competitor_type === "my third party")
                                const groupedByAsin = myBrands.reduce((acc: Record<string, any[]>, item: any) => {
                                  if (!acc[item.asin]) acc[item.asin] = []
                                  acc[item.asin].push(item)
                                  return acc
                                }, {})
                                return selectedKeywordDetailProducts?.items?.length > 0 && myBrands.length > 0
                                  ? (
                                    <div className="flex-shrink-0 w-full space-y-3 mt-2">
                                      {Object.entries(groupedByAsin).map(([asin, competitors], index) => (
                                        <div key={asin} className="w-full py-3 bg-gray-100/10 shadow-lg rounded-lg">
                                          <div className="flex items-center gap-x-2 px-4 pb-1">
                                            <div className="text-[10px] text-yellow-400 font-semibold">
                                              Top {Number(index) + 1}
                                            </div>
                                            <div className="text-xs text-gray-400 font-semibold">
                                              {(competitors as any[])[0]?.brand}
                                            </div>
                                          </div>
                                          <div>
                                            {productListItemCard(
                                              {
                                                ...(competitors as any[])[0],
                                                classification_title: (competitors as any[]).map((c) => c.classification_title),
                                                sales_rank: (competitors as any[]).map((c) => c.sales_rank),
                                                click_share: (competitors as any[]).map((c) => c.click_share),
                                                click_share_rank: (competitors as any[]).map((c) => c.click_share_rank),
                                              },
                                              "dark"
                                            )}
                                          </div>
                                        </div>
                                      ))}
                                    </div>
                                  )
                                  : ""
                              })()}
                            </div>
                            <div className="p-3 rounded-md bg-gray-100/10 shadow-sm backdrop-blur-sm">
                              <div className="flex items-center gap-x-1.5 text-gray-400 text-xs font-semibold">
                                {tk("keywordModal.keyCompetitorsTotalClickShare")}
                                <div className="w-2 h-2 rounded-full bg-yellow-400"></div>
                              </div>
                              <div className="mt-1 text-lg text-gray-100 font-semibold">
                                {selectedKeywordDetailProducts?.items?.length > 0
                                  ? (selectedKeywordDetailProducts.items.filter((item: any) => item.competitor_type !== "my third party").reduce((acc: number, curr: any) => acc + (curr.click_share || 0), 0) / 100).toFixed(2)
                                  : "0"
                                }
                              </div>
                              {(() => {
                                const competitors = selectedKeywordDetailProducts.items.filter((item: any) => item.competitor_type !== "my third party")
                                const groupedByAsin = competitors.reduce((acc: Record<string, any[]>, item: any) => {
                                  if (!acc[item.asin]) acc[item.asin] = []
                                  acc[item.asin].push(item)
                                  return acc
                                }, {})
                                return selectedKeywordDetailProducts?.items?.length > 0 && competitors.length > 0
                                  ? (
                                    <div className="flex-shrink-0 w-full space-y-3 mt-2">
                                      {Object.entries(groupedByAsin).slice(0, 3).map(([asin, competitors], index) => (
                                        <div key={asin} className="w-full py-3 bg-gray-100/10 shadow-lg rounded-lg">
                                          <div className="flex items-center gap-x-2 px-4 pb-1">
                                            <div className="text-[10px] text-yellow-400 font-semibold">
                                              Top {Number(index) + 1}
                                            </div>
                                            <div className="text-xs text-gray-400 font-semibold">
                                              {(competitors as any[])[0]?.brand}
                                            </div>
                                          </div>
                                          <div>
                                            {productListItemCard(
                                              {
                                                ...(competitors as any[])[0],
                                                classification_title: (competitors as any[]).map((c) => c.classification_title),
                                                sales_rank: (competitors as any[]).map((c) => c.sales_rank),
                                                click_share: (competitors as any[]).map((c) => c.click_share),
                                                click_share_rank: (competitors as any[]).map((c) => c.click_share_rank),
                                              },
                                              "dark"
                                            )}
                                          </div>
                                        </div>
                                      ))}
                                    </div>
                                  )
                                  : ""
                                })()}
                            </div>
                          </div>
                        </div>
                      </div>
                    )
                    : ""
                }
              </DialogPanel>
            </TransitionChild>
          </div>
        </div>
      </Dialog>
    </Transition>
  )
}
