import { auth } from "auth"
import DashboardLayoutComponent from "@/components/dashboard/dashboard-layout-component"
import { SessionProvider } from "next-auth/react"
import { getServerApiHostUrl } from "@/utils/host"
import AccountButton from "@/components/account-button"

export default async function Page() {
  const session = await auth()
  if (session?.user) {
    session.user = {
      ...session.user,
    }
  }
  const memberResponse = await fetch(
    `${await getServerApiHostUrl()}/api/member`,
    {
      method: "GET",
      headers: {
        "Authorization": `Bearer ${(session?.user as any).access_token}`,
      },
      cache: "no-cache",
    }
  ).then((res) => res.json())

  return (
    <SessionProvider session={session}>
      <DashboardLayoutComponent mopUserData={memberResponse} accountButton={<AccountButton />}/>
    </SessionProvider>
  )
}
