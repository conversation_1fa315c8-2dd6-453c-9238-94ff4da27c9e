import { describe, test, expect } from '@jest/globals'
import { getDateDifference, formatDate, formatDateTime, formatUTCDate, formatISODateTime, formatLocalDateAsISO, formatHour, stringToColor, hexToComplimentary, hexToRgbA, getRegionName } from '@/utils/msc'

describe('utils/msc', () => {
  test('getDateDifference returns absolute day difference', () => {
    const d1 = new Date('2024-01-10')
    const d2 = new Date('2024-01-15')
    expect(getDateDifference(d1, d2)).toBe(5)
    expect(getDateDifference(d2, d1)).toBe(5)
  })

  test('formatDate returns YYYY-MM-DD', () => {
    const d = new Date('2024-03-05T10:20:30Z')
    expect(formatDate(d)).toBe('2024-03-05')
    expect(formatDate(undefined as any)).toBe('-')
  })

  test('formatUTCDate formats with UTC components', () => {
    const d = new Date('2024-03-05T10:20:30Z')
    expect(formatUTCDate(d)).toBe('2024-03-05')
  })

  test('formatDateTime returns full date-time', () => {
    const d = new Date('2024-03-05T10:02:03')
    expect(formatDateTime(d)).toBe('2024-03-05 10:02:03')
    expect(formatDateTime(undefined as any)).toBe('-')
  })

  test('formatISODateTime converts Z to +00:00', () => {
    const d = new Date('2024-03-05T01:02:03Z')
    expect(formatISODateTime(d)).toMatch(/\+00:00$/)
  })

  test('formatLocalDateAsISO returns local date YYYY-MM-DD', () => {
    const d = new Date('2024-03-05T10:20:30Z')
    expect(formatLocalDateAsISO(d)).toMatch(/^\d{4}-\d{2}-\d{2}$/)
  })

  test('formatHour returns HH:00:00 and warns on invalid', () => {
    expect(formatHour(9)).toBe('09:00:00')
    expect(formatHour(23)).toBe('23:00:00')
  })

  test('stringToColor returns a hex color', () => {
    const color = stringToColor('hello')
    expect(color).toMatch(/^#[0-9a-fA-F]{6}$/)
  })

  test('hexToComplimentary returns hex', () => {
    const comp = hexToComplimentary('#336699')
    expect(comp).toMatch(/^#[0-9a-fA-F]{6}$/)
  })

  test('hexToRgbA converts to rgba string', () => {
    expect(hexToRgbA('#ffffff', 0.5)).toBe('rgba(255,255,255,0.5)')
  })

  test('getRegionName maps codes', () => {
    expect(getRegionName('EU')).toBe('Europe')
    expect(getRegionName('NA')).toBe('North America')
  })
}) 