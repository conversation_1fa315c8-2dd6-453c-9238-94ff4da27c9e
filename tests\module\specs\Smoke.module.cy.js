describe('Smoke - Redirect and Basic Availability', () => {
  beforeEach(() => {
    cy.clearCookies();
    cy.clearLocalStorage();
    cy.clearSession();
  });

  it('Unauthenticated visit to root redirects to login (auth flow trigger)', () => {
    cy.visit('/');
    // middleware -> auth -> unauth user should end up initiating login flow
    // login page triggers next-auth signIn on mount, we assert we are not stuck on /
    cy.location('pathname', { timeout: 10000 }).should('not.equal', '/');
  });

  it('Public assets are accessible (robots.txt)', () => {
    cy.request({ url: '/robots.txt', failOnStatusCode: false }).its('status').should('be.oneOf', [200, 304]);
  });
});

describe('Smoke - Test Utilities API', () => {
  it('POST /api/test/mock-session without body returns 400', () => {
    cy.request({
      method: 'POST',
      url: '/api/test/mock-session',
      body: {},
      failOnStatusCode: false,
    }).its('status').should('eq', 400);
  });

  it('POST /api/test/mock-session with token returns 200 and sets cookies', () => {
    cy.request({
      method: 'POST',
      url: '/api/test/mock-session',
      body: {
        token: {
          name: 'test',
          email: '<EMAIL>',
          sub: 'cypress-smoke-sub',
          access_token: 'mock_access_token_for_cypress_test',
          is_cypress_test: true,
        },
      },
      failOnStatusCode: false,
    }).then((res) => {
      expect(res.status).to.eq(200);
      expect(res.headers).to.have.property('set-cookie');
    });
  });
}); 