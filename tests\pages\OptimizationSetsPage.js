/// <reference types="cypress" />

import BasePage from './BasePage';

class OptimizationSetsPage extends BasePage {
    DROPDOWN_BOX = '.gap-x-1';
    ADD_BUTTON = '.flex-col > .justify-between > .flex';
    ITEM_1 = ':nth-child(1) > .cursor-pointer > .py-8';
    NAME_INPUT = '.pb-6 > div.w-full > .relative > .w-full';
    SEARCH_INPUT = '[data-testid="product-search-input"]';
    TOGGLE_BUTTON = 'button[role="switch"]';
    SAVE_BUTTON = '.flex-shrink-0.w-full > .gap-x-4 > .flex';
    PERIOD = '.px-6 > :nth-child(5)';
    BUDGET = '.bg-red-100 > .grow';
    USAGE = '.items-start > :nth-child(1) > .justify-start';
    EDIT_BUTTON = '[data-testid="edit-button"]';
    PAUSE_BUTTON = '[data-testid="pause-button"], [data-testid="resume-button"]';


    assertDropDown(){
        cy.get(this.DROPDOWN_BOX).should('be.visible');
    }
    
    assertAddButton(){
        cy.get(this.ADD_BUTTON).should('be.visible');
    }

    assertFirstItem(){
        // Click first row by opening the disclosure to ensure DOM rendered
        cy.get('.divide-y > li').first().should('exist');
    }

    assertNameInput(){
        cy.get(this.NAME_INPUT).should('be.visible');
    }

    assertSearchInput(){
        cy.get(this.SEARCH_INPUT).should('be.visible');
    }

    assertToggleButton(){
        cy.get(this.TOGGLE_BUTTON).should('be.visible');
    }

    assertSaveButton(){
        cy.get(this.SAVE_BUTTON).should('be.visible');
    }

    assertPeriod(){
        cy.get('[data-testid="period"]').should('be.visible');
    }

    assertBudget(){
        cy.get(this.BUDGET).should('be.visible');
    }

    assertUsage(){
        cy.get('[data-testid="usage"]').should('be.visible');
    }

    assertEditButton(){
        cy.get(this.EDIT_BUTTON).should('be.visible');
    }

    assertPauseButton(){
        cy.get(this.PAUSE_BUTTON).should('be.visible');
    }

    clickAddButton(){
        cy.get(this.ADD_BUTTON).click();
    }

    clickFirstItem(){
        // Click the first list row's content area
        cy.get('.divide-y > li').first().find('.py-8').click();
    }
    
    typeNameInput(value){
        cy.get(this.NAME_INPUT).type(value);
    }

    typeSearchInput(value){
        cy.get(this.SEARCH_INPUT).type(value);
    }

    clickToggleButton(){
        cy.get(this.TOGGLE_BUTTON).click();
    }



}
export default OptimizationSetsPage;   