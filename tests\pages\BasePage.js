/// <reference types="cypress" />

class BasePage {
    TOAST = '#CommonToast';
    CONFIRM_BUTTON = '[data-qa="action-button"]';
  
    fakeSession(sessionInfo) {
      cy.window().then((win) => {
        Object.keys(sessionInfo).forEach(function (key) {
          win.sessionStorage.setItem(key, sessionInfo[key]);
        });
      });
    }
  
    visit(url) {
      cy.visit(url);
    }
  
    assertDisplayedPage(url) {
      cy.location('href').should('include', url);
    }
  
    assertToastMessage(message) {
      cy.get(this.TOAST).should('have.text', message);
    }
  
    scrollToBottom() {
      cy.scrollTo('bottom');
    }
  }
  
  export default BasePage;
  