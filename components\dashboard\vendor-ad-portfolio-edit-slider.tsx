"use client"

import { api } from "@/utils/api"
import { useSession } from "next-auth/react"
import { forwardRef, Fragment, useEffect, useState, useMemo, useCallback } from 'react'
import DatePicker, { registerLocale } from "react-datepicker"
import { ko } from "date-fns/locale"
import { Button, Dialog, DialogPanel, DialogTitle, Disclosure, Popover, PopoverButton, PopoverPanel, RadioGroup, Switch, Transition, TransitionChild, Listbox, ListboxOption, ListboxOptions } from '@headlessui/react'
import { ProfileOption } from "@/components/dashboard/profile-select"
import { MarketplaceOption } from "@/components/dashboard/marketplace-select"
import { PortfolioListItem, ProductListItem } from './ad-portfolio-layout-component'
import { cn, formatDate } from "@/utils/msc"
import { useCurrencyFormatter, getCurrencyCodeFromMarketplace } from "@/utils/currency"
import { CloudArrowUpIcon, ExclamationTriangleIcon, MagnifyingGlassIcon, RocketLaunchIcon, XMarkIcon, ChevronUpDownIcon } from "@heroicons/react/20/solid"
import { Check } from "@/components/ui/check"
import { useLocale, useTranslations } from 'next-intl'
import { ChevronUpIcon } from "lucide-react"

// 가상화를 위한 상수들
const ITEM_HEIGHT = 100; // 각 아이템의 높이 (px)
const CONTAINER_HEIGHT = 400; // 컨테이너 높이 (px)
const VISIBLE_ITEMS = Math.ceil(CONTAINER_HEIGHT / ITEM_HEIGHT) + 2; // 보이는 아이템 수 + 버퍼

registerLocale('ko', ko)

interface VendorAdPortfolioEditSliderProps {
  selectedProfile: ProfileOption;
  selectedMarketplace: MarketplaceOption;
  optimizationSetNames: string[];
  handleEditCloseClick: (refresh: boolean) => void;
  selectedPortfolioItem: PortfolioListItem | null;
}
const stepOptions = [
  {
    type: 1,
    name: 'addEditModal.objective.label'
  },
  {
    type: 2,
    name: 'addEditModal.adType.label'
  },
  {
    type: 3,
    name: 'addEditModal.scheduleBudget.label'
  }
]
const policyOptions = [
  {
    type: 'DATERANGE',
    name: 'addEditModal.scheduleBudget.schedule.checkbox.daterange.label'
  },
  {
    type: 'MONTHLYRECURRING',
    name: 'addEditModal.scheduleBudget.schedule.checkbox.monthly.label'
  }
]
const objectiveOptions = [
  {
    type: 'SALES',
    name: 'addEditModal.objective.optObjectiveType.maxSales.vendorLabel'
  },
  {
    type: 'ROI',
    name: 'addEditModal.objective.optObjectiveType.maxProfit.vendorLabel'
  },
  {
    type: 'REVENUE',
    name: 'addEditModal.objective.optObjectiveType.maxAdSales.label'
  },
  {
    type: 'ROAS',
    name: 'addEditModal.objective.optObjectiveType.maxRoas.label'
  }
]
const adTypeOptions = [
  {
    type: 'DISPLAYN',
    name: 'addEditModal.adType.optAdType.displayN.label'
  },
  {
    type: 'DISPLAYY',
    name: 'addEditModal.adType.optAdType.displayY.label'
  },
]

export interface NewPortfolioItem {
  name: string;
  creation_datetime: string;
  ad_budget_amount: string;
  target_products: ProductListItem[];
  limit_cpc: string;
}

export default function VendorAdPortfolioEditSlider({
  selectedProfile,
  selectedMarketplace,
  optimizationSetNames,
  handleEditCloseClick,
  selectedPortfolioItem
}: VendorAdPortfolioEditSliderProps) {
  const t = useTranslations("component")
  const tos = useTranslations("optimizationSets")
  const locale = useLocale()
  const { data: session, status } = useSession()  
  const isAdvancedOptionsEnabled = selectedMarketplace?.subscription_features?.optimization_set_advanced_options === "enabled"
  const { formatCurrency } = useCurrencyFormatter()
  const currencyCode = useMemo(() => getCurrencyCodeFromMarketplace(selectedMarketplace), [selectedMarketplace])
  const [isLoading, setIsLoading] = useState(false)
  const [isSaveLoading, setIsSaveLoading] = useState(false)
  const [isIntegrateLoading, setIsIntegrateLoading] = useState(false)
  const [isDontIntegrateLoading, setIsDontIntegrateLoading] = useState(false)
  const [isIntegrateModalOpen, setIsIntegrateModalOpen] = useState(false)
  const [integrateInfo, setIntegrateInfo] = useState({
    new_products: [],
    paused_ads: []
  })
  const [productListItems, setProductListItems] = useState<ProductListItem[]>([])
  const [newPortfolioItem, setNewPortfolioItem] = useState<NewPortfolioItem>(selectedPortfolioItem
    ? {
      "name": selectedPortfolioItem.optimization_name,
      "creation_datetime": selectedPortfolioItem.creation_datetime,
      "ad_budget_amount": selectedPortfolioItem.ad_budget_amount ? selectedPortfolioItem.ad_budget_amount.toString() : "",
      "target_products": selectedPortfolioItem.target_products,
      "limit_cpc": selectedPortfolioItem.limit_cpc ? selectedPortfolioItem.limit_cpc.toString() : "",
    } : {
      "name": "",
      "creation_datetime": "",
      "ad_budget_amount": "",
      "target_products": [],
      "limit_cpc": "",
    }
  )
  const [filteredProductItems, setFilteredProductItems] = useState<any[]>([])
  const [productSearchText, setProductSearchText] = useState('')
  const [selectedStepOption, setSelectedStepOption] = useState(stepOptions[0])
  const [selectedScheduleOption, setSelectedScheduleOption] = useState(selectedPortfolioItem
    ? selectedPortfolioItem.ad_budget_type === "DATERANGE"
      ? policyOptions[0]
      : selectedPortfolioItem.ad_budget_type === "MONTHLYRECURRING"
        ? policyOptions[1]
        : policyOptions[0]
    : policyOptions[0]
  )
  const [selectedObjectiveOption, setSelectedObjectiveOption] = useState(selectedPortfolioItem
    ? selectedPortfolioItem.optimization_goal === "SALES"
      ? objectiveOptions[0]
      : selectedPortfolioItem.optimization_goal === "ROI"
        ? objectiveOptions[1]
        : selectedPortfolioItem.optimization_goal === "REVENUE"
          ? objectiveOptions[2]
          : objectiveOptions[3]
    : objectiveOptions[0]
  )
  const [minimizeInventory, setMinimizeInventory] = useState(selectedPortfolioItem
    ? selectedPortfolioItem.optimization_option === "INVENTORY"
      ? true
      : false
    : false
  )
  const [boostImpression, setBoostImpression] = useState(selectedPortfolioItem
    ? selectedPortfolioItem.optimization_option === "IMPRESSIONS"
      ? true
      : false
    : false
  )
  const [selectedAdTypeOption, setSelectedAdTypeOption] = useState(selectedPortfolioItem
      ? selectedPortfolioItem.display_yn === "N"
        ? adTypeOptions[0]
        : adTypeOptions[1]
      : adTypeOptions[0]
    )
  const [enableEndDate, setEnableEndDate] = useState(selectedPortfolioItem
    ? selectedPortfolioItem.ad_budget_end_date
      ? true
      : false
    : false
  )

  const [targetSameSkuOnly, setTargetSameSkuOnly] = useState(selectedPortfolioItem
    ? selectedPortfolioItem.target_same_sku_only_yn === "Y"
      ? true
      : false
    : false
  )
  const [optimizationSetNameError, setOptimizationSetNameError] = useState(false)
  const [dateRange, setDateRange] = useState<Date[]>(
    selectedPortfolioItem && selectedPortfolioItem.ad_budget_start_date && selectedPortfolioItem.ad_budget_end_date
      ? [new Date(selectedPortfolioItem.ad_budget_start_date), new Date(selectedPortfolioItem.ad_budget_end_date)]
      : [new Date(), new Date(new Date().setMonth(new Date().getMonth() + 1))]
  )
  const [dateRangeStartDate, dateRangeEndDate] = dateRange
  const [monthlyEndDate, setMonthlyEndDate] = useState<Date>(dateRangeEndDate)
  
  const [selectedCompetitionTacticValue, setSelectedCompetitionTacticValue] = useState(
    selectedPortfolioItem?.competition_option || "none"
  )
  const [limitCpc, setLimitCpc] = useState<string>(selectedPortfolioItem && selectedPortfolioItem.limit_cpc !== undefined && selectedPortfolioItem.limit_cpc !== null ? selectedPortfolioItem.limit_cpc.toString() : "")

  const competitionTacticOptions = [
    { value: "NONE", label: tos("addEditModal.objective.advancedOptions.competitionTactic.option.none") },
    { value: "BRAND_DEFENSE", label: tos("addEditModal.objective.advancedOptions.competitionTactic.option.brandDefense") },
    { value: "COMPETITOR_CONQUESTING", label: tos("addEditModal.objective.advancedOptions.competitionTactic.option.competitorConquesting") },     
  ]

  const DateRangeInput = forwardRef(({ value, onClick, minWidth }: { value: string, onClick: () => void, minWidth: string }, ref) => (
    // @ts-ignore
    <button className="w-auto mt-1 bg-white overflow-hidden cursor-pointer text-sm text-gray-600 focus:outline-none" onClick={onClick} ref={ref}>
      <div className={`min-w-[${minWidth}px] inline-flex py-2 px-4 rounded-lg bg-white hover:bg-gray-100/20 border border-gray-100 hover:border0-gray-200 text-gray-500`}>
        {value}
      </div>
    </button>
  ));
  DateRangeInput.displayName = 'DateRangeInput'
  const fetchTargetCandidateAsins = async (signal: AbortSignal) => {
    if (!(session?.user as any).access_token) {
      console.log('access token is missing in the session.')
    }
    setIsLoading(true)
    let targetCandidateAsinsResponse = await api.getListTargetCandidateAsins(selectedProfile.account_id, selectedMarketplace.marketplace_id, (session?.user as any).access_token, signal)
    setIsLoading(false)
    setProductListItems(selectedPortfolioItem
      ? selectedPortfolioItem.target_products.concat(
          targetCandidateAsinsResponse?.length
            ? targetCandidateAsinsResponse.filter(
              (candidate: any) =>
                !selectedPortfolioItem.target_products.some(
                  (prod: any) => prod.id === candidate.id
                )
            )
            : []
        )
      : targetCandidateAsinsResponse
    )
    setFilteredProductItems(selectedPortfolioItem ? selectedPortfolioItem.target_products : [])
  }
  const createOptimizationSet = async (integrateExistingTargets: boolean) => {
    if (!(session?.user as any).access_token) {
      console.log('access token is missing in the session.')
    }
    setIsSaveLoading(true)
    let createOptimizationResponse = await api.createOptimizationSet(
      {
        optimization_name: newPortfolioItem.name,
        ad_budget_type: selectedScheduleOption.type,
        ad_budget_amount: parseInt(newPortfolioItem.ad_budget_amount),
        ad_budget_start_date: selectedScheduleOption.type === "DATERANGE" ? formatDate(dateRangeStartDate, "-") : null,
        ad_budget_end_date: selectedScheduleOption.type === "DATERANGE" && dateRangeEndDate
          ? formatDate(dateRangeEndDate, "-")
          : selectedScheduleOption.type === "MONTHLYRECURRING" && enableEndDate
            ? formatDate(monthlyEndDate, "-")
            : null,
        optimization_range: (selectedObjectiveOption.type === "SALES" || selectedObjectiveOption.type === "ROI") ? "SPPLUS" : "ADONLY",
        optimization_goal: selectedObjectiveOption.type,
        optimization_option: (selectedObjectiveOption.type === "SALES" || selectedObjectiveOption.type === "ROI") && minimizeInventory
          ? "INVENTORY"
          : (selectedObjectiveOption.type === "REVENUE" || selectedObjectiveOption.type === "ROAS") && boostImpression
            ? "IMPRESSIONS"
            : "NONE",
        optimization_target_type: "NONE",
        optimization_target_value: "0",
        display_yn: selectedAdTypeOption.type === "DISPLAYN" ? "N" : "Y",
        competition_option: selectedCompetitionTacticValue,
        target_same_sku_only_yn: targetSameSkuOnly ? 'Y' : 'N',
        limit_cpc: limitCpc !== "" && limitCpc !== "-1" ? parseFloat(limitCpc.replace(/[^0-9.]/g, "")) : -1,
      },
      selectedProfile.account_id,
      selectedMarketplace.marketplace_id,
      (session?.user as any).access_token
    )
    if (createOptimizationResponse?.id) {
      await api.addOptimizationTargets(
        {
          optimization_id: createOptimizationResponse.id,
          target_products: filteredProductItems.map((product) => {
            return {asin: product.asin, sku: product.sku}
          }),
          integrate_existing_targets: integrateExistingTargets
        },
        selectedProfile.account_id,
        selectedMarketplace.marketplace_id,
        (session?.user as any).access_token
      )
    }
    setIsSaveLoading(false)
    handleEditCloseClick(true)
  }
  const editOptimizationSet = async (optimization_id: number, integrateExistingTargets: boolean) => {
    if (!(session?.user as any).access_token) {
      console.log('access token is missing in the session.')
    }
    setIsSaveLoading(true)
    let editOptimizationResponse = await api.editOptimizationSet(
      {
        optimization_id: optimization_id,
        optimization_name: newPortfolioItem.name,
        ad_budget_type: selectedScheduleOption.type,
        ad_budget_amount: parseInt(newPortfolioItem.ad_budget_amount),
        ad_budget_start_date: selectedScheduleOption.type === "DATERANGE" ? formatDate(dateRangeStartDate, "-") : null,
        ad_budget_end_date: selectedScheduleOption.type === "DATERANGE" && dateRangeEndDate
          ? formatDate(dateRangeEndDate, "-")
          : selectedScheduleOption.type === "MONTHLYRECURRING" && enableEndDate
            ? formatDate(monthlyEndDate, "-")
            : null,
        optimization_range: (selectedObjectiveOption.type === "SALES" || selectedObjectiveOption.type === "ROI") ? "SPPLUS" : "ADONLY",
        optimization_goal: selectedObjectiveOption.type,
        optimization_option: (selectedObjectiveOption.type === "SALES" || selectedObjectiveOption.type === "ROI") && minimizeInventory
          ? "INVENTORY"
          : (selectedObjectiveOption.type === "REVENUE" || selectedObjectiveOption.type === "ROAS") && boostImpression
            ? "IMPRESSIONS"
            : "NONE",
        optimization_target_type: "NONE",
        optimization_target_value: "0",
        display_yn: selectedAdTypeOption.type === "DISPLAYN" ? "N" : "Y",
        competition_option: selectedCompetitionTacticValue,
        target_same_sku_only_yn: targetSameSkuOnly ? 'Y' : 'N',
        limit_cpc: limitCpc !== "" && limitCpc !== "-1" ? parseFloat(limitCpc.replace(/[^0-9.]/g, "")) : -1,
      },
      selectedProfile.account_id,
      selectedMarketplace.marketplace_id,
      (session?.user as any).access_token
    )
    if (editOptimizationResponse?.id) {
      await api.addOptimizationTargets(
        {
          optimization_id: editOptimizationResponse.id,
          target_products: filteredProductItems.map((product) => {
            return {asin: product.asin, sku: product.sku}
          }),
          integrate_existing_targets: integrateExistingTargets
        },
        selectedProfile.account_id,
        selectedMarketplace.marketplace_id,
        (session?.user as any).access_token
      )
    }
    setIsSaveLoading(false)
    handleEditCloseClick(true)
  }
  const handleProductCheckChange = useCallback((item: any) => {
    if (filteredProductItems.some((i: any) => i.asin === item.asin && i.sku === item.sku)) {
      setFilteredProductItems(filteredProductItems.filter((i: any) => !(i.asin === item.asin && i.sku === item.sku)))
    } else {
      setFilteredProductItems([...filteredProductItems, item])
    }
  }, [filteredProductItems]);

  // 선택된 제품들의 Set을 메모이제이션 (O(1) 조회를 위해)
  const selectedProductsSet = useMemo(() => {
    const set = new Set<string>();
    filteredProductItems.forEach(item => {
      set.add(`${item.asin}-${item.sku}`);
    });
    return set;
  }, [filteredProductItems]);

  // 필터링된 제품 목록을 메모이제이션
  const filteredCandidateProducts = useMemo(() => {
    // productListItems가 undefined이거나 null인 경우 빈 배열 반환
    if (!productListItems || !Array.isArray(productListItems)) return [];
    
    if (!productSearchText) return productListItems;
    
    const searchLower = productSearchText.toLowerCase();
    return productListItems.filter((item) => 
      item.item_name?.toLowerCase().includes(searchLower) || 
      item.asin?.toLowerCase().includes(searchLower)
    );
  }, [productListItems, productSearchText]);

  // limitCpc 변경 핸들러를 메모이제이션
  const handleLimitCpcChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    let value = e.target.value;

    // Allow only numbers and a single dot
    value = value.replace(/[^0-9.]/g, '');

    // Only allow one dot
    const parts = value.split('.');
    if (parts.length > 2) {
      value = parts[0] + '.' + parts.slice(1).join('');
    }

    // Limit to 2 decimal places
    if (value.includes('.')) {
      const [intPart, decPart] = value.split('.');
      value = intPart + '.' + (decPart ? decPart.slice(0, 2) : '');
    }

    setLimitCpc(value);
  }, []);

  const keyPressHanlder = (event: any) => {
    const { key } = event
      setNewPortfolioItem({ ...newPortfolioItem,
      ad_budget_amount: key !== "Backspace"
        ? !Number.isNaN(parseInt(key))
          ? newPortfolioItem.ad_budget_amount + key
          : newPortfolioItem.ad_budget_amount
        : newPortfolioItem.ad_budget_amount.substring(0, newPortfolioItem.ad_budget_amount.length - 1)
    })
  }

  const step1Label = () => {
    return (
      <div className="flex items-center gap-x-2 divide-x divide-gray-100 text-xs text-gray-400 font-normal">
        <div className="flex-shrink-0">{tos(selectedObjectiveOption.name)}</div>
        { (selectedObjectiveOption.type === "SALES" || selectedObjectiveOption.type === "ROI") && minimizeInventory &&
          <div className="pl-2 flex-shrink-0">
            {minimizeInventory
              ? <span>{tos("addEditModal.objective.optObjectiveType.maxSales.option")}</span>
              : ""
            }
          </div>
        }
        { (selectedObjectiveOption.type === "REVENUE" || selectedObjectiveOption.type === "ROAS") && boostImpression &&
          <div className="pl-2 flex-shrink-0">
            {boostImpression
              ? <span>{tos("addEditModal.objective.optObjectiveType.maxAdSales.option")}</span>
              : ""
            }
          </div>
        }
      </div>
    )
  }
  const step2Label = () => {
    return (
      <div className="flex items-center gap-x-2 divide-x divide-gray-100 text-xs text-gray-400 font-normal">
        <div className="flex-shrink-0">{tos(selectedAdTypeOption.name)}</div>
      </div>
    )
  }
  const step3Label = () => {
    return (
      <div className="flex items-center gap-x-2 divide-x divide-gray-100 text-xs text-gray-400 font-normal">
        <div className="flex-shrink-0">
          {selectedScheduleOption.type === "DATERANGE"
            ? dateRangeEndDate
              ? formatDate(dateRangeStartDate, ".") + " - " + formatDate(dateRangeEndDate, ".")
              // : "from " + formatDate(dateRangeStartDate, ".")
              : tos.rich("addEditModal.scheduleBudget.schedule.overview.daterangeInfinite", {
                value: formatDate(dateRangeStartDate, ".")
              })
            // : "Monthly recurring" + (enableEndDate ? " until " + formatDate(monthlyEndDate, ".") : "")
            : enableEndDate
              ? tos.rich("addEditModal.scheduleBudget.schedule.overview.monthlyEnd",{
                value: formatDate(monthlyEndDate, ".") 
              }) 
              : tos("addEditModal.scheduleBudget.schedule.overview.monthlyInfinite")
          }
        </div>
        <div className="pl-2 flex-shrink-0">
          <span>{selectedScheduleOption.type === "DATERANGE" ? tos("addEditModal.scheduleBudget.budget.daterange.label") : tos("addEditModal.scheduleBudget.budget.monthly.label")} {formatCurrency(parseInt(newPortfolioItem.ad_budget_amount || "0"), currencyCode, { maximumFractionDigits: 0, minimumFractionDigits: 0 })}</span>
        </div>
      </div>
    )
  }
  const step1Content = () => {
    return (
      <div className="py-3 h-full overflow-y-auto">
        <div className="w-full">
          <div className="text-gray-400 text-xs">
            {tos("addEditModal.objective.subLabel")} <span className="text-red-500 text-sm">*</span>
          </div>
          <RadioGroup className="w-full" value={selectedObjectiveOption} onChange={setSelectedObjectiveOption}>
            <div className="mt-1 space-y-2">
              {objectiveOptions.map((option) => (
                <RadioGroup.Option
                  key={option.name}
                  value={option}
                  className={({ active, checked }) =>
                    `${checked ? 'text-gray-500 border-blue-300' : 'border-gray-100 hover:border-blue-100 hover:bg-blue-100/40'}
                      peer relative flex w-full cursor-pointer rounded-lg px-3 py-4 bg-white border-2 focus:outline-none`
                  }
                >
                  {({ active, checked }) => (
                    <>
                      <div className="flex w-full items-center justify-between gap-x-4">
                        <div className="flex w-full items-center">
                          <div className="w-full text-xs">
                            <RadioGroup.Label as="p">
                              <div
                                className={`flex items-center text-sm font-semibold ${
                                  checked ? '' : 'peer-hover:text-gray-500 text-gray-400'
                                }`}
                              >
                                {tos(option.name)}
                                {(option.type === "SALES" || option.type === "ROI")
                                  ? <span
                                      className={`pl-3 flex items-center gap-x-1 text-xs font-bold ${
                                        checked ? 'text-red-400' : 'text-red-300'
                                      }`}
                                    >
                                      <RocketLaunchIcon className="h-4 w-4" />
                                      {tos("addEditModal.objective.optObjectiveType.maxSales.vendorDataSource")}
                                    </span>
                                  : <span
                                      className={`pl-3 flex items-center gap-x-1 text-xs font-bold ${
                                        checked ? 'text-gray-400' : 'text-gray-300'
                                      }`}
                                    >
                                      <RocketLaunchIcon className="h-4 w-4" />
                                      {tos("addEditModal.objective.optObjectiveType.maxAdSales.dataSource")}
                                    </span>
                                }
                              </div>
                              <div
                                className={`mt-2 text-xs  ${
                                  checked ? 'text-gray-400' : 'text-gray-300'
                                }`}
                              >
                                {(option.type === "SALES" || option.type === "ROI")
                                  ? tos("addEditModal.objective.optObjectiveType.maxSales.vendorDetails")
                                  : tos("addEditModal.objective.optObjectiveType.maxAdSales.details")
                                }
                              </div>
                            </RadioGroup.Label>
                            <RadioGroup.Description
                              as="span"
                              className={`inline ${
                                checked ? '' : ''
                              }`}
                            >
                              <Transition
                                show={checked}
                                enter="transition-all duration-200"
                                enterFrom="max-h-0 opacity-0"
                                enterTo="max-h-screen opacity-100"
                                leave="transition-all duration-50"
                                leaveFrom="max-h-screen opacity-100"
                                leaveTo="max-h-0 opacity-0"
                              >
                                {(option.type === "SALES" || option.type === "ROI")
                                  ? <div className="mt-3 flex items-center gap-x-3 text-xs text-gray-400 font-semibold">
                                      <Switch checked={minimizeInventory} onChange={setMinimizeInventory} as={Fragment}>
                                        {({ checked }) => (
                                          <button
                                            className={`${
                                              checked ? 'bg-blue-400' : 'bg-gray-200'
                                            } relative inline-flex h-6 w-11 items-center rounded-full`}
                                          >
                                            <span className="sr-only">Minimize inventory</span>
                                            <span
                                              className={`${
                                                checked ? 'translate-x-6' : 'translate-x-1'
                                              } inline-block h-4 w-4 transform rounded-full bg-white transition`}
                                            />
                                          </button>
                                        )}
                                      </Switch>
                                      {tos("addEditModal.objective.optObjectiveType.maxSales.option")}
                                    </div>
                                  : <div className="mt-3 flex items-center gap-x-3 text-xs text-gray-400 font-semibold">
                                      <Switch checked={boostImpression} onChange={setBoostImpression} as={Fragment}>
                                        {({ checked }) => (
                                          <button
                                            className={`${
                                              checked ? 'bg-blue-400' : 'bg-gray-200'
                                            } relative inline-flex h-6 w-11 items-center rounded-full`}
                                          >
                                            <span className="sr-only">Boost impression</span>
                                            <span
                                              className={`${
                                                checked ? 'translate-x-6' : 'translate-x-1'
                                              } inline-block h-4 w-4 transform rounded-full bg-white transition`}
                                            />
                                          </button>
                                        )}
                                      </Switch>
                                      {tos("addEditModal.objective.optObjectiveType.maxAdSales.option")}
                                    </div>
                                }
                              </Transition>
                            </RadioGroup.Description>
                          </div>
                        </div>
                        {checked
                          ? <div className="shrink-0 flex items-center justify-center w-5 h-5 bg-blue-100 rounded-full">
                              <Check className="w-4 h-4 stroke-blue-500" />
                            </div>
                          : <div className="shrink-0 w-5 h-5"></div>
                        }
                      </div>
                    </>
                  )}
                </RadioGroup.Option>
              ))}
            </div>
          </RadioGroup>
          {isAdvancedOptionsEnabled && <div className="mt-2 space-y-0">            
            <div className={cn(
              "bg-gray-100 px-4 py-2 text-sm text-gray-900",
              showAdvancedOptions 
                ? "rounded-t-lg" 
                : "rounded-lg"
            )}>
              <button 
                type="button"
                className="flex items-center text-gray-400 text-xs w-full" 
                onClick={() => setShowAdvancedOptions(!showAdvancedOptions)}
              >
                <ChevronUpIcon
                  className={cn("h-5 w-5 text-gray-500 mr-2 transition duration-200 ease-in-out", 
                    showAdvancedOptions ? "rotate-180" : "")}
                />
                {tos("addEditModal.objective.advancedOptions.label")}
              </button>
            </div>
            {showAdvancedOptions && (
              <div className="bg-gray-100 rounded-b-lg border-t border-gray-200/50 px-4 py-4 text-xs text-gray-500">               
                <div className="mb-6">
                  {tos("addEditModal.objective.advancedOptions.competitionTactic.label")}
                  <Listbox 
                    value={selectedCompetitionTacticValue} 
                    onChange={setSelectedCompetitionTacticValue}
                  >
                    <div className="relative mt-2">
                      <Listbox.Button className="group relative w-full h-9 py-2 pl-3 pr-6 cursor-pointer rounded-lg text-left focus:outline-none text-xs border border-gray-100 hover:border-gray-300 bg-white">
                        <span className="block truncate text-gray-500 group-hover:text-gray-600">
                          {competitionTacticOptions.find(opt => opt.value === selectedCompetitionTacticValue)?.label || "None"}
                        </span>
                        <span className="pointer-events-none absolute right-0 inset-y-0 flex items-center px-2">
                          <ChevronUpDownIcon
                            className="h-4 w-4 text-gray-300 group-hover:text-gray-400"
                            aria-hidden="true"
                          />
                        </span>
                      </Listbox.Button>
                      <Transition
                        as={Fragment}
                        leave="transition ease-in duration-100"
                        leaveFrom="opacity-100"
                        leaveTo="opacity-0"
                      >
                        <Listbox.Options className="absolute z-10 mt-1 max-h-60 w-full overflow-auto rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black/5 focus:outline-none sm:text-xs">
                          {competitionTacticOptions.map((option) => (
                            <Listbox.Option
                              key={option.value}
                              value={option.value}
                              className={({ active, selected }) =>
                                `relative cursor-pointer select-none py-2 pl-4 pr-10 ${
                                  selected ? 'bg-blue-100/40 text-blue-900 font-medium' : 'text-gray-600 font-normal'
                                } ${active ? 'bg-blue-50' : ''}`
                              }
                            >
                              {({ selected }) => (
                                <>
                                  <span className="block truncate">
                                    {option.label}
                                  </span>
                                  {selected && (
                                    <span className="absolute inset-y-0 right-0 flex items-center pr-3">
                                      <Check className="w-4 h-4 stroke-blue-400" aria-hidden="true" />
                                    </span>
                                  )}
                                </>
                              )}
                            </Listbox.Option>
                          ))}
                        </Listbox.Options>
                      </Transition>
                    </div>
                  </Listbox>
                </div>
                
                {/* Sales Targeting Switch */}
                <div>
                  {tos("addEditModal.objective.advancedOptions.salesTargeting.label")}
                  <div className="mt-3 flex items-center gap-x-3 text-xs text-gray-400 font-semibold">
                    <Switch
                      checked={targetSameSkuOnly}
                      onChange={setTargetSameSkuOnly}
                      className={`${
                        targetSameSkuOnly ? 'bg-blue-400' : 'bg-gray-200'
                      } relative inline-flex h-6 w-11 items-center rounded-full`}
                    >
                      <span className="sr-only">Target Same SKU Only</span>
                      <span
                        className={`${
                          targetSameSkuOnly ? 'translate-x-6' : 'translate-x-1'
                        } inline-block h-4 w-4 transform rounded-full bg-white transition`}
                      />
                    </Switch>
                    <span className="text-gray-400 text-xs">{tos("addEditModal.objective.advancedOptions.salesTargeting.option")}</span>
                  </div>
                </div>
              </div>
            )}
          </div>}
        </div>
      </div>
    )
  }
  const step2Content = () => {
      return (
        <div className="py-3 h-full overflow-y-auto" >
          <div className="w-full">
            <div className="text-gray-400 text-xs">
              {tos("addEditModal.adType.subLabel")} <span className="text-red-500 text-sm">*</span>
            </div>
            <RadioGroup className="w-full" value={selectedAdTypeOption} onChange={setSelectedAdTypeOption}>
              <div className="mt-1 space-y-2">
                {adTypeOptions.map((option) => (
                  <RadioGroup.Option
                    key={option.name}
                    value={option}
                    className={({ active, checked }) =>
                      `${checked ? 'text-gray-500 border-blue-300' : 'border-gray-100 hover:border-blue-100 hover:bg-blue-100/40'}
                        peer relative flex w-full cursor-pointer rounded-lg px-3 py-4 bg-white border-2 focus:outline-none`
                    }
                  >
                    {({ active, checked }) => (
                      <>
                        <div className="flex w-full items-center justify-between gap-x-4">
                          <div className="flex w-full items-center">
                            <div className="w-full text-xs">
                              <RadioGroup.Label as="p">
                                <div
                                  className={`flex items-center text-sm font-semibold ${
                                    checked ? '' : 'peer-hover:text-gray-500 text-gray-400'
                                  }`}
                                >
                                  {tos(option.name)}
                                  {option.type === "DISPLAYN"
                                    ? <span
                                        className={`pl-3 flex items-center gap-x-1 text-xs font-bold ${
                                          checked ? 'text-gray-400' : 'text-gray-300'
                                        }`}
                                      >
                                        <RocketLaunchIcon className="h-4 w-4" />
                                        {tos("addEditModal.adType.optAdType.displayN.target")}
                                      </span>
                                    : <span
                                        className={`pl-3 flex items-center gap-x-1 text-xs font-bold ${
                                          checked ? 'text-red-400' : 'text-red-300'
                                        }`}
                                      >
                                        <RocketLaunchIcon className="h-4 w-4" />
                                        {tos("addEditModal.adType.optAdType.displayY.target")}
                                      </span>
                                  }
                                </div>
                                <div
                                  className={`mt-2 text-xs  ${
                                    checked ? 'text-gray-400' : 'text-gray-300'
                                  }`}
                                >
                                  {option.type === "DISPLAYN"
                                    ? tos("addEditModal.adType.optAdType.displayN.details")
                                    : tos("addEditModal.adType.optAdType.displayY.details")
                                  }
                                </div>
                              </RadioGroup.Label>
                            </div>
                          </div>
                          {checked
                            ? <div className="shrink-0 flex items-center justify-center w-5 h-5 bg-blue-100 rounded-full">
                                <Check className="w-4 h-4 stroke-blue-500" />
                              </div>
                            : <div className="shrink-0 w-5 h-5"></div>
                          }
                        </div>
                      </>
                    )}
                  </RadioGroup.Option>
                ))}
              </div>
            </RadioGroup>
          </div>
        </div>
      )
    }
  const step3Content = () => {
    return (
      <div className="min-h-[210px] flex flex-col gap-y-4 py-3 items-start justify-start h-full overflow-y-auto">
        <div className="w-full">
          { selectedPortfolioItem &&
          <div className="w-full mb-4">
            <div className="text-gray-400 text-xs">
              Creation Date
            </div>
            <div className="w-full mt-1 px-4 py-2 bg-gray-100/50 text-sm text-gray-500 border border-gray-100 rounded-lg cursor-not-allowed">
              {newPortfolioItem.creation_datetime}
            </div>
          </div>
          }
          <div className="w-full">
            <div className="text-gray-400 text-xs">
              {tos("addEditModal.scheduleBudget.schedule.label")} <span className="text-red-500 text-sm">*</span>
            </div>
            <RadioGroup className="w-full" value={selectedScheduleOption} onChange={setSelectedScheduleOption}>
              <div className="mt-1 space-y-2">
                {policyOptions.map((option) => (
                  <RadioGroup.Option
                    key={option.name}
                    value={option}
                    className={({ active, checked }) =>
                      `${checked ? 'text-gray-500 border-blue-300' : 'border-gray-100 hover:border-blue-100 hover:bg-blue-100/40'}
                        peer relative flex w-full cursor-pointer rounded-lg px-3 py-4 bg-white border-2 focus:outline-none`
                    }
                  >
                    {({ active, checked }) => (
                      <>
                        <div className="flex w-full items-center justify-between gap-x-4">
                          <div className="flex items-center">
                            <div className="text-xs">
                              <RadioGroup.Label as="p">
                                <div
                                  className={`font-semibold text-sm ${
                                    checked ? '' : 'peer-hover:text-gray-500 text-gray-400'
                                  }`}
                                >
                                  {tos(option.name)}
                                </div>
                                <div
                                  className={`mt-2 text-xs  ${
                                    checked ? 'text-gray-400' : 'text-gray-300'
                                  }`}
                                >
                                  {(option.type === "DATERANGE")
                                    ? tos("addEditModal.scheduleBudget.schedule.checkbox.daterange.details")
                                    : tos("addEditModal.scheduleBudget.schedule.checkbox.monthly.details")
                                  }
                                </div>
                              </RadioGroup.Label>
                              <RadioGroup.Description
                                as="span"
                                className={`inline ${
                                  checked ? '' : ''
                                }`}
                              >
                                <Transition
                                  show={option.type === "DATERANGE" && checked}
                                  enter="transition-all duration-100"
                                  enterFrom="max-h-0 opacity-0"
                                  enterTo="max-h-screen opacity-100"
                                  leave="transition-all duration-100"
                                  leaveFrom="max-h-screen opacity-100"
                                  leaveTo="max-h-0 opacity-0"
                                >
                                  <div className="mt-2 flex flex-wrap items-center gap-2">
                                    <DatePicker
                                      selectsRange={true}
                                      minDate={new Date('2023-01-01')}
                                      startDate={dateRangeStartDate}
                                      endDate={dateRangeEndDate}
                                      onChange={(update) => {
                                        setDateRange(update as Date[])
                                      }}
                                      dateFormat="yyyy.MM.dd"
                                      calendarClassName="dashboard-date-range"
                                      // @ts-ignore
                                      customInput={<DateRangeInput minWidth={'200'} />}
                                      locale={locale}
                                    />
                                  </div>
                                </Transition>
                                <Transition
                                  show={option.type === "MONTHLYRECURRING" && checked}
                                  enter="transition-all duration-100"
                                  enterFrom="max-h-0 opacity-0"
                                  enterTo="max-h-screen opacity-100"
                                  leave="transition-all duration-100"
                                  leaveFrom="max-h-screen opacity-100"
                                  leaveTo="max-h-0 opacity-0"
                                >
                                  <div>
                                    <div className="mt-3 flex items-center gap-x-3 text-xs text-gray-400 font-semibold">
                                      <Switch checked={enableEndDate} onChange={setEnableEndDate} as={Fragment}>
                                        {({ checked }) => (
                                          <button
                                            className={`${
                                              checked ? 'bg-blue-400' : 'bg-gray-200'
                                            } relative inline-flex h-6 w-11 items-center rounded-full`}
                                          >
                                            <span className="sr-only">Set an end date</span>
                                            <span
                                              className={`${
                                                checked ? 'translate-x-6' : 'translate-x-1'
                                              } inline-block h-4 w-4 transform rounded-full bg-white transition`}
                                            />
                                          </button>
                                        )}
                                      </Switch>
                                      {tos("addEditModal.scheduleBudget.schedule.checkbox.monthly.toggle.label")}
                                    </div>
                                    <Transition
                                      show={enableEndDate && checked}
                                      enter="transition-all duration-100"
                                      enterFrom="max-h-0 opacity-0"
                                      enterTo="max-h-screen opacity-100"
                                      leave="transition-all duration-100"
                                      leaveFrom="max-h-screen opacity-100"
                                      leaveTo="max-h-0 opacity-0"
                                    >
                                      <div className="mt-2 flex flex-wrap items-center gap-2">
                                        <DatePicker
                                          selectsRange={false}
                                          minDate={new Date('2023-01-01')}
                                          selected={monthlyEndDate}
                                          onChange={(update) => {
                                            setMonthlyEndDate(update as Date)
                                          }}
                                          dateFormat="yyyy.MM.dd"
                                          calendarClassName="dashboard-date-range"
                                          // @ts-ignore
                                          customInput={<DateRangeInput minWidth={'110'}/>}
                                          locale={locale}
                                        />
                                      </div>
                                    </Transition>
                                  </div>
                                </Transition>
                              </RadioGroup.Description>
                            </div>
                          </div>
                          {checked
                            ? <div className="shrink-0 flex items-center justify-center w-5 h-5 bg-blue-100 rounded-full">
                                <Check className="w-4 h-4 stroke-blue-500" />
                              </div>
                            : <div className="shrink-0 w-5 h-5"></div>
                          }
                        </div>
                      </>
                    )}
                  </RadioGroup.Option>
                ))}
              </div>
            </RadioGroup>
          </div>
          <div className="w-full mt-4">
            <div className="text-gray-400 text-xs">
              {selectedScheduleOption.type === "DATERANGE" ? tos("addEditModal.scheduleBudget.budget.daterange.label") : tos("addEditModal.scheduleBudget.budget.monthly.label")} <span className="text-red-500 text-sm">*</span>
            </div>
            <div className="relative flex-shrink-0 mt-1 rounded-lg overflow-hidden cursor-pointer">
              <input
                className="w-full px-4 py-2 bg-white text-sm text-gray-500 placeholder:text-gray-300 border border-gray-100 focus:border-transparent focus:outline-blue-200 rounded-lg"
                onKeyDown={keyPressHanlder}
                placeholder={selectedScheduleOption.type === "DATERANGE" ? tos("addEditModal.scheduleBudget.budget.daterange.input") : tos("addEditModal.scheduleBudget.budget.monthly.label")}
                value={newPortfolioItem.ad_budget_amount !== "" ? formatCurrency(parseInt(newPortfolioItem.ad_budget_amount), currencyCode, { maximumFractionDigits: 0, minimumFractionDigits: 0 }) : ""}
              />
            </div>
          </div>
          <div className="w-full mt-4">
            <div className="text-gray-400 text-xs">
              { tos("addEditModal.scheduleBudget.limitCpc.label")} 
            </div>
            <div className="relative flex-shrink-0 mt-1 rounded-lg overflow-hidden cursor-pointer">
              <input
                className="w-full px-4 py-2 bg-white text-sm text-gray-500 placeholder:text-gray-300 border border-gray-100 focus:border-transparent focus:outline-blue-200 rounded-lg"
                onChange={handleLimitCpcChange}
                placeholder={tos("addEditModal.scheduleBudget.limitCpc.input")}
                value={limitCpc !== "" && limitCpc !== "-1" ? limitCpc : ""}
                onBlur={() => {
                  // Sanitize and format value on blur
                  if (limitCpc !== "" && limitCpc !== "-1") {
                    // Remove all characters except digits and decimal point
                    const sanitized = limitCpc.replace(/[^0-9.]/g, '');
                    const num = parseFloat(sanitized);
                    if (!isNaN(num)) {
                      setLimitCpc(formatCurrency(num, currencyCode));
                    } else {
                      setLimitCpc("");
                    }
                  }
                }}
              />
            </div>
          </div>
        </div>
      </div>
    )
  }

  useEffect(() => {
    const abortController = new AbortController()
    !isLoading && selectedProfile && selectedMarketplace && fetchTargetCandidateAsins(abortController.signal)
    return () => {
      abortController.abort()
    }
  }, [selectedProfile, selectedMarketplace])
  
  // 고급 옵션 표시 여부를 제어하는 상태 변수 추가
  const [showAdvancedOptions, setShowAdvancedOptions] = useState(false);
  
  // 가상화를 위한 스크롤 상태
  const [scrollTop, setScrollTop] = useState(0);

  // 가상화된 아이템 계산
  const virtualizedItems = useMemo(() => {
    // filteredCandidateProducts가 안전한지 확인
    if (!filteredCandidateProducts || !Array.isArray(filteredCandidateProducts)) {
      return {
        startIndex: 0,
        endIndex: 0,
        items: [],
        totalHeight: 0,
        offsetY: 0
      };
    }
    
    const startIndex = Math.floor(scrollTop / ITEM_HEIGHT);
    const endIndex = Math.min(startIndex + VISIBLE_ITEMS, filteredCandidateProducts.length);
    
    return {
      startIndex: Math.max(0, startIndex),
      endIndex,
      items: filteredCandidateProducts.slice(Math.max(0, startIndex), endIndex),
      totalHeight: filteredCandidateProducts.length * ITEM_HEIGHT,
      offsetY: Math.max(0, startIndex) * ITEM_HEIGHT
    };
  }, [filteredCandidateProducts, scrollTop]);

  // 스크롤 핸들러
  const handleScroll = useCallback((e: React.UIEvent<HTMLDivElement>) => {
    setScrollTop(e.currentTarget.scrollTop);
  }, []);

  return (
    <>
      <Transition.Child
        as={Fragment}
        enter="ease-out duration-300"
        enterFrom="opacity-0"
        enterTo="opacity-100"
        leave="ease-in duration-200"
        leaveFrom="opacity-100"
        leaveTo="opacity-0"
      >
        <div className="w-full h-full bg-black/30" onClick={() => handleEditCloseClick(false)} />
      </Transition.Child>
      <Transition.Child
        as={Fragment}
        enter="ease-out duration-300"
        enterFrom="opacity-0 w-[0vw]"
        enterTo="opacity-100 w-[80vw]"
        leave="ease-in duration-200"
        leaveFrom="opacity-100 w-[80vw]"
        leaveTo="opacity-0 w-[0vw]"
      >
        <div className="absolute inset-y-0 right-0 w-[80vw] h-full overflow-hidden shadow-md">
          {/* add or edit portfolio item */}
          <div className="flex-shrink-0 flex flex-col w-[80vw] h-full p-6 bg-white">
              <div className="flex-shrink-0 w-full flex items-center justify-between">
                <button onClick={() => handleEditCloseClick(false)}>
                  <XMarkIcon className="h-5 w-5 text-gray-500 hover:text-gray-800" />
                </button>
                <div className="flex items-center gap-x-4">
                  <button
                    className={cn(
                      "flex items-center gap-x-2 p-3 cursor-pointer rounded-md focus:outline-none text-sm overflow-hidden font-semibold",
                      isLoading || !newPortfolioItem.name || optimizationSetNameError || !newPortfolioItem.ad_budget_amount ? "bg-gray-200 text-gray-500" : "bg-blue-100 hover:bg-blue-200 text-blue-500",
                      (isSaveLoading || isLoading || !newPortfolioItem.name || optimizationSetNameError || !newPortfolioItem.ad_budget_amount) && "cursor-not-allowed"
                      )}
                    disabled={isSaveLoading || isLoading || !newPortfolioItem.name || optimizationSetNameError || !newPortfolioItem.ad_budget_amount}
                    onClick={
                      async () => {
                        let newAddedProducts = selectedPortfolioItem
                          ? filteredProductItems.filter((product) => !selectedPortfolioItem.target_products.some((i) => i.asin === product.asin && i.sku === product.sku))
                          : filteredProductItems
                        if (newAddedProducts.length > 0) { // check if there is new products added (edit)
                          setIsSaveLoading(true)
                          let newCampaignsAndPausedAdsResponse = await api.listNewCampaignsAndPausedAds(
                            {
                              target_products: filteredProductItems.map((product) => {
                                return {asin: product.asin, sku: product.sku}
                              }),
                            },
                            selectedProfile.account_id,
                            selectedMarketplace.marketplace_id,
                            (session?.user as any).access_token
                          )
                          if (newCampaignsAndPausedAdsResponse.new_products.length === 0 && newCampaignsAndPausedAdsResponse.paused_ads.length === 0) {
                            selectedPortfolioItem
                              ? await editOptimizationSet(selectedPortfolioItem.id, false)
                              : await createOptimizationSet(false)
                            setIsSaveLoading(false)
                            handleEditCloseClick(true)
                          } else {
                            setIntegrateInfo(newCampaignsAndPausedAdsResponse)
                            setIsSaveLoading(false)
                            setIsIntegrateModalOpen(true)
                            return
                          }
                        } else {
                          selectedPortfolioItem
                            ? await editOptimizationSet(selectedPortfolioItem.id, false)
                            : await createOptimizationSet(false)
                          handleEditCloseClick(true)
                        }
                      }
                    }>
                      {isSaveLoading
                        ? <svg className="animate-spin h-4 w-4 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                          </svg>
                        : <CloudArrowUpIcon
                            className="flex-shrink-0 h-4 w-4"
                            aria-hidden="true"
                          />
                      }
                    <div>{tos("addEditModal.topButton.save")}</div>
                  </button>
                  <Transition appear show={isIntegrateModalOpen}>
                    <Dialog as="div" className="relative z-10 focus:outline-none" onClose={() => setIsIntegrateModalOpen(false)}>
                      <div className="fixed inset-0 bg-black/30 backdrop-blur-sm" aria-hidden="true" />
                      <div className="fixed inset-0 z-10 w-screen overflow-y-auto">
                        <div className="flex min-h-full items-center justify-center p-4">
                          <TransitionChild
                            enter="ease-out duration-300"
                            enterFrom="opacity-0 transform-[scale(95%)]"
                            enterTo="opacity-100 transform-[scale(100%)]"
                            leave="ease-in duration-200"
                            leaveFrom="opacity-100 transform-[scale(100%)]"
                            leaveTo="opacity-0 transform-[scale(95%)]"
                          >
                            <DialogPanel className="w-full max-w-xl rounded-xl bg-white p-6">
                              <DialogTitle as="h3" className="text-base/7 font-medium text-gray-600">
                                Integrate Keywords
                              </DialogTitle>
                              <p className="mt-2 text-sm/6 text-gray-500">
                                Optapex will reformulate your campaign structures to unlock your product&apos;s full potential. Integrate keywords to expedite the alogorithm training process.
                                <div className="mt-4 p-5 bg-gray-100 rounded-md">
                                  <div className="text-xs text-gray-400 font-semibold">
                                    New Campaigns
                                  </div>
                                  <div className="mt-1 text-sm text-gray-500">
                                    <span className="text-xl text-blue-500 font-semibold">{(integrateInfo?.new_products?.length || 0) * 5}</span> new campaigns will be created.
                                  </div>
                                  <div className="text-xs text-gray-400 font-semibold mt-4">
                                    Paused Ads
                                  </div>
                                  <div className="mt-1 text-sm text-gray-500">
                                  <span className="text-xl text-blue-500 font-semibold">{integrateInfo?.paused_ads?.length || 0}</span> ASINs will be paused across the entire account.
                                  </div>
                                </div>
                              </p>
                              <div className="mt-4 flex items-center justify-end gap-x-4">
                                <Button
                                  className="inline-flex items-center gap-2 rounded-md bg-white py-1.5 px-3 text-sm/6 font-semibold text-gray-500 shadow-inner shadow-white/10 focus:outline-none data-[hover]:bg-gray-100 data-[open]:bg-gray-700 data-[focus]:outline-1 data-[focus]:outline-white"
                                  onClick={() => setIsIntegrateModalOpen(false)}
                                >
                                  Cancel
                                </Button>
                                <Button
                                  className={cn(
                                    "inline-flex items-center gap-2 rounded-md bg-blue-100 py-1.5 px-3 text-sm/6 font-semibold text-blue-500 shadow-inner shadow-white/10 focus:outline-none data-[hover]:bg-blue-200 data-[open]:bg-gray-700 data-[focus]:outline-1 data-[focus]:outline-white cursor-pointer",
                                    isDontIntegrateLoading && "cursor-not-allowed"
                                    )}
                                  onClick={async () => {
                                    setIsDontIntegrateLoading(true)
                                    selectedPortfolioItem
                                      ? await editOptimizationSet(selectedPortfolioItem.id, false)
                                      : await createOptimizationSet(false)
                                    setIsDontIntegrateLoading(false)
                                    setIsIntegrateModalOpen(false)
                                  }}
                                  disabled={isDontIntegrateLoading}
                                >
                                  {isDontIntegrateLoading
                                    ? <svg className="animate-spin h-4 w-4 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                      </svg>
                                    : ""
                                  }
                                  <div>Don&apos;t Integrate Keywords</div>
                                </Button>
                                <Button
                                  className={cn(
                                    "inline-flex items-center gap-2 rounded-md bg-blue-600 py-1.5 px-3 text-sm/6 font-semibold text-white shadow-inner shadow-white/10 focus:outline-none data-[hover]:bg-blue-700 data-[open]:bg-gray-700 data-[focus]:outline-1 data-[focus]:outline-white cursor-pointer",
                                    isIntegrateLoading && "cursor-not-allowed"
                                  )}
                                  onClick={async () => {
                                    setIsIntegrateLoading(true)
                                    selectedPortfolioItem
                                      ? await editOptimizationSet(selectedPortfolioItem.id, true)
                                      : await createOptimizationSet(true)
                                    setIsIntegrateLoading(false)
                                    setIsIntegrateModalOpen(false)
                                  }}
                                  disabled={isIntegrateLoading}
                                >
                                  {isIntegrateLoading
                                    ? <svg className="animate-spin h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                      </svg>
                                    : ""
                                  }
                                  <div>Integrate Keywords</div>
                                </Button>
                              </div>
                            </DialogPanel>
                          </TransitionChild>
                        </div>
                      </div>
                    </Dialog>
                  </Transition>
                </div>
              </div>
              <div className="grow relative flex flex-col w-full overflow-hidden">
                <div className="grow relative flex gap-x-8 px-6 pb-6 overflow-hidden">
                  {/* Product List in a Portfolio */}
                  <div className="grow relative flex flex-col gap-y-6 w-1/2 overflow-hidden">
                    <div className="relative flex flex-col w-full h-2/3 overflow-hidden">
                      <div className="flex-shrink-0">
                        <div className="flex items-center pb-6 gap-x-6">
                          <div className="w-full">
                            <div className="text-gray-400 text-xs">
                              {tos("addEditModal.optSetName.label")} <span className="text-red-500 text-sm">*</span>
                            </div>
                            {/* portfolio name input */}
                            <div className="relative flex-shrink-0 mt-1 rounded-lg overflow-hidden cursor-pointer">
                              <input
                                type="text"
                                className="w-full px-4 py-2 bg-white text-sm text-gray-500 placeholder:text-gray-300 border border-gray-100 focus:border-transparent focus:outline-blue-200 rounded-lg"
                                value={newPortfolioItem.name}
                                onChange={(e) => {
                                  if (e.target.value.length <= 50) {
                                    setNewPortfolioItem({ ...newPortfolioItem, name: e.target.value })
                                  }
                                  if (optimizationSetNames.length > 0 && optimizationSetNames.filter(name => name === e.target.value && name !== selectedPortfolioItem?.optimization_name).length > 0) {
                                    setOptimizationSetNameError(true)
                                  } else {
                                    setOptimizationSetNameError(false)
                                  }
                                }}
                                placeholder={tos("addEditModal.optSetName.placeholder")}
                              />
                              <div className="absolute inset-y-0 right-0 flex items-center pr-4 text-xs text-gray-300 font-semibold">
                                {newPortfolioItem.name.length}/{50}
                              </div>
                            </div>
                            {optimizationSetNameError && (
                              <div className="px-4 text-red-400 text-xs font-semibold mt-1">
                                {tos("addEditModal.optSetName.duplicateError")}
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                      <div className="grow relative flex flex-col min-h-0 rounded-lg border border-gray-100">
                        <div className="p-3">
                          {/* search box */}
                          <div className="relative flex-shrink-0 rounded-lg overflow-hidden cursor-pointer">
                            <MagnifyingGlassIcon
                              className={cn(
                                "h-5 w-5 absolute top-1/2 left-3 transform -translate-y-1/2",
                                productSearchText ? "text-gray-500" : "text-gray-300"
                              )}
                            />
                            <input
                              data-testid="product-search-input"
                              type="text"
                              className="w-full max-w-[300px] pl-10 pr-4 py-2 bg-white text-sm text-gray-500 placeholder:text-gray-300 border border-gray-100 focus:border-transparent focus:outline-blue-200 rounded-lg"
                              value={productSearchText}
                              onChange={(e) => setProductSearchText(e.target.value)}
                              placeholder={tos("addEditModal.product.searchBar.placeholder")}
                            />
                          </div>
                        </div>
                        <div className="grow relative overflow-y-scroll" onScroll={handleScroll} style={{ height: CONTAINER_HEIGHT }}>
                          <div className="sticky inset-x-0 top-0 z-[1] flex items-center gap-x-3 px-6 py-3 text-left text-base text-gray-400 font-bold bg-gray-100/40 backdrop-blur-md">
                            <div className="w-full">
                              {tos("addEditModal.product.targetCandidates.label")}
                            </div>
                          </div>
                          {isLoading
                            ? <ul className="animate-pulse p-6 space-y-3">
                                <li className="w-full flex items-center gap-x-3">
                                  <div className="flex-shrink-0 w-24 h-16 rounded-md bg-gray-100"></div>
                                  <div className="flex-1 h-16 rounded-md bg-gray-100"></div>
                                </li>
                                <li className="w-full flex items-center gap-x-3">
                                  <div className="flex-shrink-0 w-24 h-16 rounded-md bg-gray-100"></div>
                                  <div className="flex-1 h-16 rounded-md bg-gray-100"></div>
                                </li>
                                <li className="w-full flex items-center gap-x-3">
                                  <div className="flex-shrink-0 w-24 h-16 rounded-md bg-gray-100"></div>
                                  <div className="flex-1 h-16 rounded-md bg-gray-100"></div>
                                </li>
                                <li className="w-full flex items-center gap-x-3">
                                  <div className="flex-shrink-0 w-24 h-16 rounded-md bg-gray-100"></div>
                                  <div className="flex-1 h-16 rounded-md bg-gray-100"></div>
                                </li>
                                <li className="w-full flex items-center gap-x-3">
                                  <div className="flex-shrink-0 w-24 h-16 rounded-md bg-gray-100"></div>
                                  <div className="flex-1 h-16 rounded-md bg-gray-100"></div>
                                </li>
                                <li className="w-full flex items-center gap-x-3">
                                  <div className="flex-shrink-0 w-24 h-16 rounded-md bg-gray-100"></div>
                                  <div className="flex-1 h-16 rounded-md bg-gray-100"></div>
                                </li>
                                <li className="w-full flex items-center gap-x-3">
                                  <div className="flex-shrink-0 w-24 h-16 rounded-md bg-gray-100"></div>
                                  <div className="flex-1 h-16 rounded-md bg-gray-100"></div>
                                </li>
                                <li className="w-full flex items-center gap-x-3">
                                  <div className="flex-shrink-0 w-24 h-16 rounded-md bg-gray-100"></div>
                                  <div className="flex-1 h-16 rounded-md bg-gray-100"></div>
                                </li>
                              </ul>
                            : <div style={{ height: virtualizedItems.totalHeight, position: 'relative' }}>
                                <ul className="divide-y divide-gray-100" style={{ transform: `translateY(${virtualizedItems.offsetY}px)` }}>
                                  {virtualizedItems.items.map((item, index) => {
                                    const alreadyRegistered = false
                                    const actualIndex = virtualizedItems.startIndex + index;
                                    return item && (
                                      <li className="relative flex items-center gap-x-3 py-6 cursor-pointer hover:bg-gray-100/50 text-center text-gray-500 text-sm" key={`${item.asin}-${item.sku}-${actualIndex}`} style={{ height: ITEM_HEIGHT }}>
                                        <div
                                          className="flex-shrink-0 w-[40px] pl-6"
                                          onClick={(e) => e.stopPropagation()}
                                        >
                                          <div
                                            className="grid items-center justify-center"
                                            onClick={() => {
                                              alreadyRegistered
                                                ? null
                                                : handleProductCheckChange(item)
                                            }}
                                          >
                                            <input
                                              type="checkbox"
                                              className={cn(
                                                "peer row-start-1 col-start-1 appearance-none w-4 h-4 border ring-transparent border-gray-300 hover:border-blue-300 hover:bg-blue-100/50 rounded",
                                                alreadyRegistered
                                                  ? "checked:bg-gray-400 checked:border-gray-400 cursor-not-allowed"
                                                  : "checked:bg-blue-400 checked:border-blue-400 cursor-pointer"
                                              )}
                                              disabled={alreadyRegistered}
                                              checked={selectedProductsSet.has(`${item.asin}-${item.sku}`)}
                                            />
                                            <Check className="invisible peer-checked:visible row-start-1 col-start-1 stroke-white" />
                                          </div>
                                        </div>
                                        <div className="grow relative flex items-center gap-x-4 px-4 overflow-hidden">
                                          { item.image
                                          ? (<img src={item.image} alt="Item Image" className="flex-shrink-0 w-10 h-10 rounded" />)
                                          : (<div className="flex-shrink-0 flex items-center justify-center w-10 h-10 bg-gray-100 rounded">
                                              <ExclamationTriangleIcon className="h-5 w-5 text-gray-300" />
                                            </div>)
                                          }
                                          <div className="flex-1 flex flex-col gap-y-0.5 overflow-hidden">
                                            <a
                                              href={`https://www.amazon.com/dp/${item.asin}`}
                                              target="_blank"
                                              rel="noopener noreferrer"
                                              className="flex items-center gap-x-1 focus:outline-none"
                                            >
                                              {item.recommend_type && (
                                                item.recommend_type === "GROWTH"
                                                  ? <div className="flex-shrink-0 py-0.25 px-1.5 text-white bg-purple-500 text-[10px] rounded-md">Growth</div>
                                                  : item.recommend_type === "EFFICIENCY"
                                                    ? <div className="flex-shrink-0 py-0.25 px-1.5 text-white bg-green-500 text-[10px] rounded-md">Efficiency</div>
                                                    : ""
                                              )}
                                              <span className="text-xs text-gray-500 text-left font-semibold truncate hover:underline">
                                                {item.item_name
                                                  ? item.item_name
                                                  : "No Title"
                                                }
                                              </span>
                                            </a>
                                            <div className="flex items-center gap-x-2 divide-x divide-gray-100">
                                              <div className="text-xs text-red-400 font-semibold">{formatCurrency(item.listing_price || 0, currencyCode)}</div>
                                              {item.eligibility_status &&
                                                item.eligibility_status === "ELIGIBLE"
                                                  ? <div className="pl-2 text-xs text-blue-400 font-semibold">{t("eligibility.eligible")}</div>
                                                  : item.eligibility_status === "INELIGIBLE"
                                                    ? <div className="pl-2 text-xs text-gray-400 font-semibold">{t("eligibility.ineligible")}</div>
                                                    : <div className="pl-2 text-xs text-gray-400 font-semibold">{t("eligibility.unknown")}</div>
                                              }
                                            </div>
                                            <div className="flex items-center gap-x-2 divide-x divide-gray-100">
                                              <div className="text-[10px] text-gray-400">{tos("addEditModal.product.targetCandidates.asin")}: {item.asin}</div>
                                              {item.sku &&
                                              <div className="pl-2 text-[10px] text-gray-400">SKU: {item.sku}</div>
                                              }
                                            </div>
                                          </div>
                                        </div>
                                      </li>
                                    )
                                  })}
                                </ul>
                                {(!filteredCandidateProducts || filteredCandidateProducts.length === 0) && (
                                  <div className="absolute inset-0 flex items-center justify-center w-full text-gray-300 text-sm font-normal" style={{ height: CONTAINER_HEIGHT - 50 }}>
                                    {tos("addEditModal.product.targetCandidates.null")}
                                  </div>
                                )}
                              </div>
                          }
                        </div>
                      </div>
                    </div>
                    <div className="relative w-full h-1/3 rounded-lg bg-blue-100/30 overflow-y-scroll">
                      <div className="sticky inset-x-0 top-0 z-[1] flex items-center gap-x-2 px-6 py-3 text-center text-md text-blue-400 font-bold backdrop-blur-md">
                        <div>{tos("addEditModal.product.targetProducts.label")}</div>
                        <div className="flex items-center justify-center w-4 h-4 rounded-full bg-blue-400 text-[10px] text-white font-semibold">{filteredProductItems.length}</div>
                      </div>
                      {filteredProductItems.length === 0 &&
                        <div className="absolute inset-0 flex items-center justify-center w-full text-gray-300 text-sm font-normal">
                          {tos("addEditModal.product.targetProducts.null")}
                        </div>
                      }
                      {filteredProductItems.length > 0 &&
                        <ul className="divide-y divide-gray-100">
                          {filteredProductItems.map((item, index) => {
                            const alreadyRegistered = false
                            return item && (
                              <li className="relative flex items-center gap-x-3 px-6 py-2.5 cursor-pointer hover:bg-blue-100/20 text-left text-gray-500 text-xs font-normal rounded-md" key={index}>
                                <div className="grow relative flex items-center gap-x-4 overflow-hidden">
                                  { item.image
                                  ? (<img src={item.image} alt="Item Image" className="flex-shrink-0 w-8 h-8 rounded-full" />)
                                  : (<div className="flex-shrink-0 flex items-center justify-center w-8 h-8 bg-gray-100 rounded-full">
                                      <ExclamationTriangleIcon className="h-3 w-3 text-gray-300" />
                                    </div>)
                                  }
                                  <div className="flex-1 flex flex-col gap-y-0.5 overflow-hidden">
                                    <div className="flex items-center gap-x-2">
                                      <div className="flex-shrink-0 text-xs text-red-400 font-semibold">{formatCurrency(item.listing_price || 0, currencyCode)}</div>
                                      <a
                                        href={`https://www.amazon.com/dp/${item.asin}`}
                                        target="_blank"
                                        rel="noopener noreferrer"
                                        className="flex items-center gap-x-1 focus:outline-none"
                                      >
                                        {item.recommend_type && (
                                          item.recommend_type === "GROWTH"
                                            ? <div className="flex-shrink-0 py-0.25 px-1.5 text-white bg-purple-500 text-[10px] rounded-md">Growth</div>
                                            : item.recommend_type === "EFFICIENCY"
                                              ? <div className="flex-shrink-0 py-0.25 px-1.5 text-white bg-green-500 text-[10px] rounded-md">Efficiency</div>
                                              : ""
                                        )}
                                        <span className="text-xs text-gray-500 text-left font-semibold truncate hover:underline">
                                          {item.item_name
                                            ? item.item_name
                                            : "No Title"
                                          }
                                        </span>
                                      </a>
                                    </div>
                                    <div className="flex items-center gap-x-2 divide-x divide-gray-100">
                                      <div className="text-[10px] text-gray-400">{tos("addEditModal.product.targetProducts.asin")}: {item.asin}</div>
                                      {item.sku &&
                                      <div className="pl-2 text-[10px] text-gray-400">SKU: {item.sku}</div>
                                      }
                                    </div>
                                  </div>
                                </div>
                                {alreadyRegistered
                                  ? ""
                                  : <button onClick={() => handleProductCheckChange(item)}>
                                      <XMarkIcon className="h-4 w-4 text-gray-400 hover:text-gray-500" />
                                    </button>
                                }
                              </li>
                            )})}
                        </ul>
                      }
                    </div>
                  </div>
                  {/* target setting for a Portfolio */}
                  <div className="w-1/2">
                    <RadioGroup className="flex flex-col w-full h-full" value={selectedStepOption} onChange={setSelectedStepOption}>
                      {stepOptions.map((option) => (
                        <RadioGroup.Option
                          key={option.name}
                          value={option}
                          className={({ active, checked }) =>
                            cn(
                              "group relative flex w-full cursor-pointer border-l-2 bg-white focus:outline-none",
                              stepOptions.length === option.type ? 'min-h-[40px]' : 'min-h-[80px]',
                              option.type < stepOptions.length ? 'border-gray-200' : 'border-transparent',
                              selectedStepOption.type > option.type ? 'border-blue-400' : '',
                              checked ? 'grow flex-1' : ''
                            )
                          }
                        >
                          {({ active, checked }) => (
                            <>
                              <div className="flex w-full items-start gap-x-6 h-full">
                                <div
                                  className={cn(
                                    "shrink-0 flex items-center justify-center w-5 h-5 ml-[-11px] bg-white rounded-full border-2 border-gray-200 group-hover:border-gray-500",
                                    selectedStepOption.type > option.type ? 'bg-blue-400 border-blue-400 group-hover:bg-blue-500 group-hover:border-blue-500' : '',
                                    checked ? 'border-blue-400 group-hover:border-blue-400 bg-white' : ''
                                  )}
                                >
                                  { selectedStepOption.type > option.type
                                    ? <Check className="w-4 h-4 stroke-white" />
                                    : <div
                                        className={cn(
                                          "w-2 h-2 bg-white rounded-full",
                                          selectedStepOption.type > option.type ? 'group-hover:bg-blue-500' : 'group-hover:bg-gray-500',
                                          checked ? 'bg-blue-400 group-hover:bg-blue-400': ''
                                        )}
                                      />
                                  }
                                </div>
                                <div className="flex items-start w-full h-full">
                                  <div className="w-full text-xs text-gray-400 h-full flex flex-col">
                                    <RadioGroup.Label
                                      as="p"
                                      className={cn(
                                        "mt-[-2px] text-base font-bold",
                                        checked ? 'text-blue-400' : 'group-hover:text-gray-500',
                                        selectedStepOption.type > option.type ? 'text-blue-400 group-hover:text-blue-500' : ''
                                      )}
                                    >
                                      {tos(option.name)}
                                      { !checked &&
                                        (<div className="mt-1">
                                          {option.type === 1
                                            ? step1Label()
                                            : option.type === 2
                                              ? step2Label()
                                              : step3Label()
                                          }
                                        </div>)
                                      }
                                      
                                    </RadioGroup.Label>
                                    <Transition
                                      show={option.type === 1 && checked}
                                      enter="transition-all duration-100"
                                      enterFrom="max-h-0 opacity-0"
                                      enterTo="max-h-screen opacity-100"
                                      leave="transition-all duration-100"
                                      leaveFrom="max-h-screen opacity-100"
                                      leaveTo="max-h-0 opacity-0"
                                    >
                                      <div className="flex-1 overflow-hidden pb-4">
                                        {/* schedule section */}
                                        {step1Content()}
                                      </div>
                                    </Transition>
                                    <Transition
                                      show={option.type === 2 && checked}
                                      enter="transition-all duration-100"
                                      enterFrom="max-h-0 opacity-0"
                                      enterTo="max-h-screen opacity-100"
                                      leave="transition-all duration-100"
                                      leaveFrom="max-h-screen opacity-100"
                                      leaveTo="max-h-0 opacity-0"
                                    >
                                      <div className="flex-1 overflow-hidden pb-4">
                                        {/* ad type section */}
                                        {step2Content()}
                                      </div>
                                    </Transition>
                                    <Transition
                                      show={option.type === 3 && checked}
                                      enter="transition-all duration-100"
                                      enterFrom="max-h-0 opacity-0"
                                      enterTo="max-h-screen opacity-100"
                                      leave="transition-all duration-100"
                                      leaveFrom="max-h-screen opacity-100"
                                      leaveTo="max-h-0 opacity-0"
                                    >
                                      <div className="flex-1 overflow-hidden pb-4">
                                        {/* target budget section */}
                                        {step3Content()}
                                      </div>
                                    </Transition>
                                  </div>
                                </div>
                              </div>
                            </>
                          )}
                        </RadioGroup.Option>
                      ))}
                    </RadioGroup>
                  </div>
                </div>
              </div>
          </div>
        </div>
      </Transition.Child>
    </>
  )
}
