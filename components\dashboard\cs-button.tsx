"use client"

import { api } from "@/utils/api"
import { cn, formatDateTime, hexToRgbA, stringToColor, useScrollToBottom } from "@/utils/msc"
import { useSession } from "next-auth/react"
import { Dialog, DialogPanel, DialogTitle, Disclosure, DisclosureButton, DisclosurePanel, Tab, TabGroup, TabList, TabPanel, TabPanels, Transition, TransitionChild } from '@headlessui/react'
import { Fragment, Key, useEffect, useRef, useState } from "react"
import { ChevronDownIcon, ChatBubbleOvalLeftEllipsisIcon, ChevronLeftIcon, PlusIcon, PaperAirplaneIcon, PaperClipIcon, PlusCircleIcon, XMarkIcon } from "@heroicons/react/20/solid"
import Image from "next/image"
import ProfileSelect, { ProfileOption } from "@/components/dashboard/profile-select"
import MarketplaceSelect, { MarketplaceOption } from "@/components/dashboard/marketplace-select"
import { useLocale, useTranslations } from "next-intl"
import { StaticImport } from "next/dist/shared/lib/get-img-props"
import SupportPageComponent from "./support-page-component"

interface CSButtonProps {
  mopUserData: any;
  selectedProfile: ProfileOption | null;
  setSelectedProfile: (option: ProfileOption | null) => void
  selectedMarketplace: MarketplaceOption | null;
  setSelectedMarketplace: (option: MarketplaceOption | null) => void
}

export default function CSButton({
  mopUserData,
  selectedProfile,
  setSelectedProfile,
  selectedMarketplace,
  setSelectedMarketplace,
}: CSButtonProps) {
  const { data: session, status } = useSession()
  const [showChat, setShowChat] = useState(false);
  const [maximizeWindow, setMaximizeWindow] = useState(false);
  const [tickets, setTickets] = useState<any[]>([]);
  const [faqs, setFaqs] = useState<any[]>([]);
  const [selectedTicket, setSelectedTicket] = useState<any | null>(null);
  const [showTitleModal, setShowTitleModal] = useState(false);
  const [newTitle, setNewTitle] = useState("");
  const [newMessage, setNewMessage] = useState("");
  const [newTicket, setNewTicket] = useState<any | null>(null);
  const [showWarningModal, setShowWarningModal] = useState(false);
  const [showWarningInChatModal, setShowWarningInChatModal] = useState(false);
  const [isTicketDetailLoading, setIsTicketDetailLoading] = useState(false);
  const [isCreateTicketLoading, setIsCreateTicketLoading] = useState(false);
  const [isSendMessageLoading, setIsSendMessageLoading] = useState(false);
  const [isFileUploadLoading, setIsFileUploadLoading] = useState(false);
  const faqListFetchCounter = useRef(0)
  const [selectedImage, setSelectedImage] = useState<string | null>(null);
  const ticketListFetchCounter = useRef(0)
  const conversationEndRef = useRef<HTMLDivElement>(null);
  const {scrollToBottom} = useScrollToBottom(conversationEndRef);
  const locale = useLocale()

  const parseTicketStatusStyle = (status: string) => {
    switch (status) {
      case "OPEN":
        return "bg-green-500";
      case "IN_PROGRESS":
        return "bg-blue-500";
      case "CLOSED":
        return "bg-gray-300";
      case "PENDING":
        return "bg-yellow-500";
      default:
        return "bg-gray-300";
    }
  }
  const parseTicketStatusString = (status: string) => {
    switch (status) {
      case "OPEN":
        return "Open";
      case "IN_PROGRESS":
        return "In Progress";
      case "CLOSED":
        return "Closed";
      case "PENDING":
        return "Pending";
      default:
        return "Unknown";
    }
  }
  const fetchFaqList = async (signal: AbortSignal) => {
      if (!(session?.user as any).access_token) {
        console.log('access token is missing in the session.');
        return;
      }
      faqListFetchCounter.current += 1;
      try {
        const faqListResponse: {
          id: number;
          created_by: number;
          updated_by: number;
          display_order: number;
          name: string;
          created_datetime: string;
          updated_datetime: string;
          faqs: {
            category_id: number;
            question: string;
            display_order: number;
            created_datetime: string;
            updated_datetime: string;
            id: number;
            answer: string;
            created_by: number;
            updated_by: number;
          }[];
        }[] = await api.getFaqList((session?.user as any).access_token, signal, locale);
  
        if (faqListResponse?.length > 0) {
          setFaqs(
            faqListResponse
              .sort((a, b) => a.display_order - b.display_order)
              .map((category) => ({
                category: category.name,
                faqs: category.faqs.map((faq) => ({
                  question: faq.question,
                  answer: faq.answer,
                })),
              }))
          );
        }
      } catch (error) {
        console.error("Failed to fetch FAQ list:", error);
      } finally {
        faqListFetchCounter.current -= 1;
      }
    };
  
  const fetchTicketList = async (signal: AbortSignal) => {
    if (!(session?.user as any).access_token) {
      console.log('access token is missing in the session.')
    }
    ticketListFetchCounter.current += 1
    let ticketListResponse = await api.getTicketList((session?.user as any).access_token, signal)
    // ticketListResponse = {
    //   limit: 100,
    //   offset: 0,
    //   tickets: [
    //     {
    //       category: "General",
    //       created_datetime: "2025-03-21T05:30:12",
    //       id: 2,
    //       last_message_datetime: "2025-03-21T05:30:12",
    //       latest_message: {
    //         content: "asdf",
    //         created_datetime: "2025-03-21T05:30:12",
    //         file_size: null,
    //         file_type: null,
    //         id: 3,
    //         message_status: "UNREAD",
    //         message_type: "TEXT",
    //         sender_id: 116,
    //         sender_staff: null,
    //         sender_type: "CUSTOMER",
    //         ticket_id: 2,
    //       },
    //       priority: "Medium",
    //       status: "OPEN",
    //       title: "asdf",
    //     }
    //   ]
    // }
    ticketListResponse?.tickets && setTickets(ticketListResponse?.tickets)
    ticketListFetchCounter.current -= 1
  }

  const fetchTicketDetail = async (ticketId: string) => {
    setIsTicketDetailLoading(true)
    try {
      const abortController = new AbortController()
      const response = await api.getTicketDetail(ticketId, (session?.user as any).access_token, abortController.signal)
      // response = {
      //   "id": 15,
      //   "title": "ticket open test 1",
      //   "category": "General",
      //   "priority": "Medium",
      //   "status": "OPEN",
      //   "created_datetime": "2025-03-27T08:01:32",
      //   "closed_datetime": null,
      //   "last_message_datetime": "2025-03-27T08:01:33",
      //   "assigned_staff": null,
      //   "messages": [
      //     {
      //       "id": 81,
      //       "message_type": "TEXT",
      //       "content": "ticket open test 1 ticket open test 1 ticket open test 1 ticket open test 1 ticket open test 1 ticket open test 1 ticket open test 1 ticket open test 1 ticket open test 1 ticket open test 1 ticket open test 1 ",
      //       "file_info": null,
      //       "sender_type": "CUSTOMER",
      //       "sender_id": 116,
      //       "sender_staff": null,
      //       "created_datetime": "2025-03-27T08:01:32",
      //       "message_status": "UNREAD"
      //     },
      //     {
      //       "id": 82,
      //       "message_type": "FILE",
      //       "content": "https://s3-us-east-1-dev-gmop-fe-admin-media-assets.s3.amazonaws.com/cs-attachments/116/4f2336e8-7d4a-47e3-9f17-1b53bf869ed3.png?AWSAccessKeyId=ASIAVVZON46RCAWJQVWI&Signature=5jDdB4oUCtymmXeDBnS5cg%2FVzGc%3D&x-amz-security-token=IQoJb3JpZ2luX2VjENj%2F%2F%2F%2F%2F%2F%2F%2F%2F%2FwEaCXVzLWVhc3QtMSJHMEUCIGzZeTYFjiaF6ZOU0o4Rtl2u8MWyM2UOvF1JUzggyxfYAiEA5xcVhmJbe1AbiiIMQ1HpVlRaGyvqYnveQMSTSTHHy2gq%2BwQIQRAAGgwzOTA0MDI1MzMyODIiDLSaneXhuwxFBxR36SrYBA34jp%2BKa51AEbHHiByTBM3Zx47y1Lr1muJSOZ4XZqvKTrRvng7BfM%2FcY6BEPIS7av8xOj9m29waSw52AWuHVVh6PdgV40ktaARgWMWucHo%2B7XggBEN6S19vVUA0I5yjzzabaDrT5vWeVpKETr0zMvkpi1iDJRHflYQ%2FfGx%2FQqQi0tos8QsxipQXHGQaSALteS9z0UX9cE9n6DHEBOiJmFuR2pF33j10WfMOWSUhFnss8nyZE3ZXZBXbSVEP427C2wB68Ob2%2F8VesllPePNfn3nNrhDwxuOkCBL7aH9g65UNgABczBcNFnyL1LS4Vhc5n8VohYbPRyY%2BjRHaTz%2B%2BYz7rc3zbwjRQbnYMI7vEwi2QGuXPJpUAXxPoSZD8Qjqij1p5JOcvNm%2BC18NzLiwMGbJeKB7r%2BgBCb0La7OJ14heyfzbBMFkSoIN5eO%2FSnvHEazrnr1D09eqkAssCcEzdK3j7qVmo0P7rAWQMGWQpcmKPACHGFn62Mbs%2FJ5jIinuwB%2FPhwd9iuZrhJJfeGIJzep0WDA42%2F9ZXEqgc0n5NRjjfjqqJ4XgfiAyWp6MWaje2BWNIL9RWFweyrJdgyfnfWzU%2Bv1RbKHmTGqzcstWAbVDsstyPoI3g4gizRcKe7TIFjwy%2Fp0ePLeOGTB%2BB9cugR3jogcU16Jg20URHqSEUCStATtgjdBhr65JomtUpkSKdDdWNZzo%2B9MnYlXEy3uw8WOkJp3pFaodNvybebLaMzRuemJGKkBpBgGKEv5W6loFpxo2GATVR2ZglzU%2BQmX5FGiCffSUEs88hFTDbi5S%2FBjqaASek%2FJkLUXV%2FOYPa7nIhjLl%2BJPQRH0RAoEQwLpR7iGVz8LwOzIUX6ovw4k%2FTv%2Bn2T8qI5zRa3sDuu1lTxxKXflFSyIQuVy%2BqhCVHD6xin4sr5huMjj2opoyfeRHVP4vsOdEqjrESipGeZZKli3Fp%2Fog5hnNJRjj7a%2FG8VllXbtn9vjXDnMvkxj3B3B1yF4SQqcUwC5yy8FkXyJI%3D&Expires=1743063306",
      //       "file_info": {
      //         "file_name": "after.png",
      //         "file_path": "cs-attachments/116/4f2336e8-7d4a-47e3-9f17-1b53bf869ed3.png",
      //         "file_size": 79521,
      //         "file_type": "image/png"
      //       },
      //       "sender_type": "CUSTOMER",
      //       "sender_id": 116,
      //       "sender_staff": null,
      //       "created_datetime": "2025-03-27T08:01:33",
      //       "message_status": "UNREAD"
      //     },
      //     {
      //       "id": 83,
      //       "message_type": "FILE",
      //       "content": "https://s3-us-east-1-dev-gmop-fe-admin-media-assets.s3.amazonaws.com/cs-attachments/116/ca577d3f-ae50-48ce-80d4-3fe6658ff5ab.jpg?AWSAccessKeyId=ASIAVVZON46RCAWJQVWI&Signature=HB8eUuEKV%2FO%2FGLEZF%2BdKw361Q58%3D&x-amz-security-token=IQoJb3JpZ2luX2VjENj%2F%2F%2F%2F%2F%2F%2F%2F%2F%2FwEaCXVzLWVhc3QtMSJHMEUCIGzZeTYFjiaF6ZOU0o4Rtl2u8MWyM2UOvF1JUzggyxfYAiEA5xcVhmJbe1AbiiIMQ1HpVlRaGyvqYnveQMSTSTHHy2gq%2BwQIQRAAGgwzOTA0MDI1MzMyODIiDLSaneXhuwxFBxR36SrYBA34jp%2BKa51AEbHHiByTBM3Zx47y1Lr1muJSOZ4XZqvKTrRvng7BfM%2FcY6BEPIS7av8xOj9m29waSw52AWuHVVh6PdgV40ktaARgWMWucHo%2B7XggBEN6S19vVUA0I5yjzzabaDrT5vWeVpKETr0zMvkpi1iDJRHflYQ%2FfGx%2FQqQi0tos8QsxipQXHGQaSALteS9z0UX9cE9n6DHEBOiJmFuR2pF33j10WfMOWSUhFnss8nyZE3ZXZBXbSVEP427C2wB68Ob2%2F8VesllPePNfn3nNrhDwxuOkCBL7aH9g65UNgABczBcNFnyL1LS4Vhc5n8VohYbPRyY%2BjRHaTz%2B%2BYz7rc3zbwjRQbnYMI7vEwi2QGuXPJpUAXxPoSZD8Qjqij1p5JOcvNm%2BC18NzLiwMGbJeKB7r%2BgBCb0La7OJ14heyfzbBMFkSoIN5eO%2FSnvHEazrnr1D09eqkAssCcEzdK3j7qVmo0P7rAWQMGWQpcmKPACHGFn62Mbs%2FJ5jIinuwB%2FPhwd9iuZrhJJfeGIJzep0WDA42%2F9ZXEqgc0n5NRjjfjqqJ4XgfiAyWp6MWaje2BWNIL9RWFweyrJdgyfnfWzU%2Bv1RbKHmTGqzcstWAbVDsstyPoI3g4gizRcKe7TIFjwy%2Fp0ePLeOGTB%2BB9cugR3jogcU16Jg20URHqSEUCStATtgjdBhr65JomtUpkSKdDdWNZzo%2B9MnYlXEy3uw8WOkJp3pFaodNvybebLaMzRuemJGKkBpBgGKEv5W6loFpxo2GATVR2ZglzU%2BQmX5FGiCffSUEs88hFTDbi5S%2FBjqaASek%2FJkLUXV%2FOYPa7nIhjLl%2BJPQRH0RAoEQwLpR7iGVz8LwOzIUX6ovw4k%2FTv%2Bn2T8qI5zRa3sDuu1lTxxKXflFSyIQuVy%2BqhCVHD6xin4sr5huMjj2opoyfeRHVP4vsOdEqjrESipGeZZKli3Fp%2Fog5hnNJRjj7a%2FG8VllXbtn9vjXDnMvkxj3B3B1yF4SQqcUwC5yy8FkXyJI%3D&Expires=1743063306",
      //       "file_info": {
      //         "file_name": "aceess-token-expiration.jpg",
      //         "file_path": "cs-attachments/116/ca577d3f-ae50-48ce-80d4-3fe6658ff5ab.jpg",
      //         "file_size": 399202,
      //         "file_type": "image/jpeg"
      //       },
      //       "sender_type": "CUSTOMER",
      //       "sender_id": 116,
      //       "sender_staff": null,
      //       "created_datetime": "2025-03-27T08:01:33",
      //       "message_status": "UNREAD"
      //     }
      //   ]
      // }
      setSelectedTicket({
        id: response.id,
        title: response.title,
        category: response.category,
        status: response.status,
        priority: response.priority,
        created_datetime: response.created_datetime,
        last_message_datetime: response.last_message_datetime,
        conversation: response.messages
          .filter((message: any) => message.message_type !== "SYSTEM")
          .map((message: any) => ({
            content: message.content,
            id: message.id,
            type: message.message_type === "TEXT" ? "text" : "image",
            sender: message.sender_type === "CUSTOMER" ? "user" : "support",
            datetime: message.created_datetime,
          })),
        scrollToBottom: !(selectedTicket && selectedTicket.id === response.id && selectedTicket.conversation.length === response.messages.length)
      });
    } catch (error) {
      console.error("Failed to fetch ticket details:", error);
    }
    setIsTicketDetailLoading(false)
  }

  useEffect(() => {
    if (selectedTicket?.scrollToBottom) {
      scrollToBottom()
    }
  }, [selectedTicket?.conversation]);

  useEffect(() => { // periodically fetch ticket list and ticket detail
    if (selectedTicket?.id) { // fetchTicketDetail every 5 seconds when there is a selected ticket
      const ticketDetailIntervalId = setInterval(() => {
        fetchTicketDetail(selectedTicket.id);
      }, 5000);

      return () => clearInterval(ticketDetailIntervalId);
    }
    if (!selectedTicket) { // fetchTicketList every 10 seconds when there is no selected ticket
      const ticketListIntervalId = setInterval(() => {
        const abortController = new AbortController();
        fetchTicketList(abortController.signal);
        return () => abortController.abort();
      }, 10000);

      return () => clearInterval(ticketListIntervalId);
    }
  }, [selectedTicket?.id]);

  useEffect(() => {
    const abortController = new AbortController()
    mopUserData && fetchTicketList(abortController.signal)
    mopUserData && fetchFaqList(abortController.signal)
    return () => {
      abortController.abort()
    }
  }, []);
  
  return (
    <div className="fixed bottom-4 right-4 z-10 gap-y-2">
      <button
        className="bg-gradient-to-br from-blue-500 to-purple-500 text-white p-3 rounded-full shadow-[0_3px_9px_-1px_rgba(0,0,0,0.6)] hover:from-blue-600 hover:to-purple-600 focus:outline-none"
        onClick={() => setShowChat(!showChat)}
      >
        <ChatBubbleOvalLeftEllipsisIcon className="h-8 w-8" />
      </button>
      <div
        className={`relative bg-white rounded-lg transition-all duration-300 ${
          showChat
            ? `opacity-100 mt-2 ${maximizeWindow ? "h-[90vh] w-[80vw] max-h-[calc(100vh-100px)]" : "h-[75vh] w-[30vw] min-w-[360px]"}`
            : "opacity-0 mt-0 h-0 w-0 overflow-hidden transition-all duration-300"
        }`}
      >
        <TabGroup className="relative size-full">
          {({ selectedIndex }) => (
            <div
              className={cn(
                "size-full p-4 border border-gray-300 rounded-lg shadow-lg overflow-hidden",
                selectedIndex === 0 ? "bg-gradient-to-b from-blue-500/20 via-purple-500/30 via-40% to-white to-70%" : "bg-white"
              )}
            >
              <button
                className="absolute top-4 right-4 p-1 text-gray-400 hover:text-gray-600 focus:outline-none z-10"
                onClick={() => setMaximizeWindow(!maximizeWindow)}
              >
                {maximizeWindow ? (
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 9V4.5M9 9H4.5M9 9L3.75 3.75M15 15v4.5M15 15h4.5M15 15l5.25 5.25" />
                  </svg>
                ) : (
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3.75 3.75v4.5m0-4.5h4.5m-4.5 0L9 9M3.75 20.25v-4.5m0 4.5h4.5m-4.5 0L9 15M20.25 3.75h-4.5m4.5 0v4.5m0-4.5L15 9m5.25 11.25h-4.5m4.5 0v-4.5m0 4.5L15 15" />
                  </svg>
                )}
              </button>
              <div className="flex flex-col h-full">
                <div className="flex-shrink-0 pb-2">
                  <TabList
                    className={cn(
                      "inline-flex gap-x-1 p-1 rounded-lg",
                      selectedIndex === 0 ? "bg-white/40" : "bg-gray-100"
                    )}
                  >
                    <Tab
                      className={cn(
                        "flex items-center gap-x-1 py-2 px-4 text-sm font-semibold focus:outline-none rounded-lg",
                        selectedIndex === 0
                          ? "text-gray-400 data-[selected]:text-blue-800/80 data-[selected]:bg-white/50 data-[selected]:shadow-md data-[selected]:data-[hover]:bg-white/30 data-[focus]:outline-1 data-[focus]:outline-white"
                          : "text-gray-400 data-[selected]:text-gray-500 data-[selected]:bg-white data-[selected]:shadow-md data-[selected]:data-[hover]:bg-white data-[hover]:bg-white/40 data-[focus]:outline-1 data-[focus]:outline-white"
                      )}
                    >
                      Home
                    </Tab>
                    <Tab
                      className="flex items-center gap-x-1 py-2 px-4 text-sm font-semibold text-gray-400 focus:outline-none data-[selected]:text-gray-500 data-[selected]:bg-white data-[selected]:shadow-md data-[selected]:data-[hover]:bg-white data-[hover]:bg-white/40 data-[focus]:outline-1 data-[focus]:outline-white rounded-lg"
                      onClick={() => setMaximizeWindow(false)}
                    >
                      FAQs
                    </Tab>
                    <Tab
                      className="flex items-center gap-x-1 py-2 px-4 text-sm font-semibold text-gray-400 focus:outline-none data-[selected]:text-gray-500 data-[selected]:bg-white data-[selected]:shadow-md data-[selected]:data-[hover]:bg-white data-[hover]:bg-white/40 data-[focus]:outline-1 data-[focus]:outline-white rounded-lg"
                      onClick={() => setMaximizeWindow(false)}
                    >
                      Tickets
                    </Tab>
                  </TabList>
                </div>
                <TabPanels className="grow relative flex flex-col min-h-0">
                  <TabPanel className="relative flex flex-col w-full h-full overflow-y-auto">
                    <SupportPageComponent locale={locale} setMaximizeWindow={setMaximizeWindow} />
                    <div className="absolute bottom-0 inset-x-0 w-full h-4 bg-gradient-to-b from-white/0 to-white"></div>
                  </TabPanel>
                  <TabPanel className="flex flex-col w-full h-full overflow-y-auto">
                    <div className="relative grow w-full min-h-0 py-4">
                      {faqListFetchCounter.current > 0
                        ? <ul className="animate-pulse space-y-4">
                            <li className="space-y-3">
                              <div className="flex-shrink-0 w-20 h-8 rounded-md bg-gray-100"></div>
                              <div className="flex-shrink-0 w-full h-8 rounded-md bg-gray-100"></div>
                              <div className="flex-shrink-0 w-full h-8 rounded-md bg-gray-100"></div>
                              <div className="flex-shrink-0 w-full h-8 rounded-md bg-gray-100"></div>
                              <div className="flex-shrink-0 w-full h-8 rounded-md bg-gray-100"></div>
                              <div className="flex-shrink-0 w-full h-8 rounded-md bg-gray-100"></div>
                            </li>
                            <li className="space-y-3">
                              <div className="flex-shrink-0 w-20 h-8 rounded-md bg-gray-100"></div>
                              <div className="flex-shrink-0 w-full h-8 rounded-md bg-gray-100"></div>
                              <div className="flex-shrink-0 w-full h-8 rounded-md bg-gray-100"></div>
                              <div className="flex-shrink-0 w-full h-8 rounded-md bg-gray-100"></div>
                              <div className="flex-shrink-0 w-full h-8 rounded-md bg-gray-100"></div>
                              <div className="flex-shrink-0 w-full h-8 rounded-md bg-gray-100"></div>
                            </li>
                            <li className="space-y-3">
                              <div className="flex-shrink-0 w-20 h-8 rounded-md bg-gray-100"></div>
                              <div className="flex-shrink-0 w-full h-8 rounded-md bg-gray-100"></div>
                              <div className="flex-shrink-0 w-full h-8 rounded-md bg-gray-100"></div>
                              <div className="flex-shrink-0 w-full h-8 rounded-md bg-gray-100"></div>
                              <div className="flex-shrink-0 w-full h-8 rounded-md bg-gray-100"></div>
                              <div className="flex-shrink-0 w-full h-8 rounded-md bg-gray-100"></div>
                            </li>
                            <li className="space-y-3">
                              <div className="flex-shrink-0 w-20 h-8 rounded-md bg-gray-100"></div>
                              <div className="flex-shrink-0 w-full h-8 rounded-md bg-gray-100"></div>
                              <div className="flex-shrink-0 w-full h-8 rounded-md bg-gray-100"></div>
                              <div className="flex-shrink-0 w-full h-8 rounded-md bg-gray-100"></div>
                              <div className="flex-shrink-0 w-full h-8 rounded-md bg-gray-100"></div>
                              <div className="flex-shrink-0 w-full h-8 rounded-md bg-gray-100"></div>
                            </li>
                          </ul>
                        : faqs.map((faqItem, index) => (
                        <div key={index} className="mb-4">
                          <div className="text-gray-600 text-sm font-semibold mb-2">
                            {faqItem.category}
                          </div>
                          <ul className="space-y-2">
                            {faqItem.faqs.map((faq: any, faqIndex: number) => (
                            <li key={faqIndex} className="mb-2">
                              <Disclosure>
                                {({ open }) => (
                                  <>
                                    <DisclosureButton
                                      className={`${
                                        open ? 'text-blue-500 bg-blue-100/50 hover:bg-blue-100' : 'text-gray-500 bg-gray-100/50 hover:bg-gray-100'
                                      } flex justify-between w-full px-4 py-2 rounded-lg text-sm font-medium text-left focus:outline-none focus-visible:ring focus-visible:ring-blue-500 focus-visible:ring-opacity-75`}
                                    >
                                      <span>{faq.question}</span>
                                      <ChevronDownIcon
                                        className={`${
                                          open ? 'transform rotate-180 text-blue-500' : 'text-gray-500'
                                        } w-5 h-5`}
                                      />
                                    </DisclosureButton>
                                    <DisclosurePanel className="px-4 pt-4 pb-2 text-sm text-gray-500">
                                      <p>{faq.answer}</p>
                                    </DisclosurePanel>
                                  </>
                                )}
                              </Disclosure>
                            </li>
                            ))}
                          </ul>
                        </div>
                        ))
                      }
                    </div>
                  </TabPanel>
                  <TabPanel className="flex flex-col w-full h-full">
                    <div className="relative grow w-full min-h-0">
                      {!selectedTicket ? (
                        <div className="relative size-full flex flex-col h-full">
                          {isTicketDetailLoading &&
                            <div className="absolute inset-0 flex items-center justify-center overflow-hidden w-full h-full cursor-not-allowed bg-white bg-opacity-80 z-[1]">
                              <svg className="animate-spin h-10 w-10 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                              </svg>
                            </div>
                          }
                          {ticketListFetchCounter.current > 0
                            ? <ul className="animate-pulse space-y-3">
                                <li className="w-full flex items-center gap-x-3">
                                  <div className="flex-shrink-0 w-full h-20 rounded-md bg-gray-100"></div>
                                </li>
                                <li className="w-full flex items-center gap-x-3">
                                  <div className="flex-shrink-0 w-full h-20 rounded-md bg-gray-100"></div>
                                </li>
                                <li className="w-full flex items-center gap-x-3">
                                  <div className="flex-shrink-0 w-full h-20 rounded-md bg-gray-100"></div>
                                </li>
                                <li className="w-full flex items-center gap-x-3">
                                  <div className="flex-shrink-0 w-full h-20 rounded-md bg-gray-100"></div>
                                </li>
                                <li className="w-full flex items-center gap-x-3">
                                  <div className="flex-shrink-0 w-full h-20 rounded-md bg-gray-100"></div>
                                </li>
                                <li className="w-full flex items-center gap-x-3">
                                  <div className="flex-shrink-0 w-full h-20 rounded-md bg-gray-100"></div>
                                </li>
                                <li className="w-full flex items-center gap-x-3">
                                  <div className="flex-shrink-0 w-full h-20 rounded-md bg-gray-100"></div>
                                </li>
                                <li className="w-full flex items-center gap-x-3">
                                  <div className="flex-shrink-0 w-full h-20 rounded-md bg-gray-100"></div>
                                </li>
                                <li className="w-full flex items-center gap-x-3">
                                  <div className="flex-shrink-0 w-full h-20 rounded-md bg-gray-100"></div>
                                </li>
                                <li className="w-full flex items-center gap-x-3">
                                  <div className="flex-shrink-0 w-full h-20 rounded-md bg-gray-100"></div>
                                </li>
                              </ul>
                            : <ul className="relative grow w-full min-h-0 flex flex-col divide-y divide-gray-100 overflow-y-scroll">
                                {tickets.length > 0
                                  ? tickets.map((ticket, index) => (
                                    <li key={index} className="py-4">
                                      <button
                                        className="group text-left w-full focus:outline-none"
                                        onClick={() => fetchTicketDetail(ticket.id)}
                                      >
                                        <div className="flex-shrink-0 flex items-center gap-x-1 text-xs text-gray-500">
                                          <div className={cn(
                                            "flex-shrink-0 w-2 h-2 rounded-full",
                                            parseTicketStatusStyle(ticket.status)
                                          )}></div>
                                          {parseTicketStatusString(ticket.status)}
                                        </div>
                                        <div className="mt-1 text-blue-500 group-hover:text-blue-700 text-base font-semibold truncate">
                                          {ticket.title}
                                        </div>
                                        <div className="mt-1 text-gray-500 text-sm truncate">
                                          {ticket.latest_message?.content || ""}
                                        </div>
                                        <div className="mt-1 text-gray-400 text-xs">
                                          updated at {formatDateTime(ticket.last_message_datetime)}
                                        </div>
                                      </button>
                                    </li>
                                  ))
                                  : (
                                    <div className="flex-grow flex items-center justify-center text-gray-400 text-sm">
                                      <div className="text-center">
                                        <div className="text-gray-400 text-lg font-semibold">
                                          No tickets found
                                        </div>
                                        <div className="text-gray-400 text-sm">
                                          Start a new conversation by creating a ticket
                                        </div>
                                      </div>
                                    </div>
                                  )
                                }
                              </ul>
                          }
                          <button
                            className={cn(
                              "mt-4 flex-shrink-0 flex items-center justify-center gap-x-1 py-1.5 px-3 text-sm/6 rounded-md focus:outline-none overflow-hidden bg-blue-600 hover:bg-blue-700 text-white font-semibold",
                              isTicketDetailLoading ? "cursor-not-allowed" : "cursor-pointer"
                            )}
                            disabled={isTicketDetailLoading}
                            onClick={() => {
                              setNewTicket({
                                title: "",
                                lastUpdatedTime: "",
                                conversation: [],
                              });
                              setShowTitleModal(true);
                            }}
                          >
                            <PlusIcon className="flex-shrink-0 h-4 w-4" />
                            New Ticket
                          </button>
                          {showTitleModal && (
                            <div className={`absolute inset-0 flex flex-col bg-white transition-transform transform ${showTitleModal ? 'translate-x-0' : 'translate-x-full'}`}>
                              <div className="relative grow w-full min-h-0 flex flex-col">
                                <div className="flex-shrink-0 py-4 bg-white">
                                  <button
                                    className="flex items-center text-blue-500 hover:text-blue-700 text-left text-xs font-semibold"
                                    onClick={() => setShowTitleModal(false)}
                                  >
                                    <ChevronLeftIcon className="h-4 w-4 inline-block" />
                                    <span>Back to Tickets</span>
                                  </button>
                                </div>

                                <div className="relative grow w-full min-h-0 flex flex-col">
                                  <div className="flex-shrink-0">
                                    <div className="w-full">
                                      <div className="text-gray-400 text-xs font-semibold ">
                                        Ticket Title
                                      </div>
                                      <div className="mt-2 relative flex-shrink-0 rounded-lg overflow-hidden cursor-pointer">
                                        <input
                                          type="text"
                                          className="w-full px-4 py-2 bg-white text-sm text-gray-500 placeholder:text-gray-300 border border-gray-100 focus:border-transparent focus:outline-blue-200 rounded-lg"
                                          value={newTitle}
                                          onChange={(e) => {
                                            if (e.target.value.length <= 50) {
                                              setNewTitle(e.target.value)
                                            }
                                          }}
                                          placeholder={"Enter a title"}
                                        />
                                        <div className="absolute inset-y-0 right-0 flex items-center pr-4 text-xs text-gray-300 font-semibold">
                                          <span className="bg-white py-2">{newTitle.length}/{50}</span>
                                        </div>
                                      </div>
                                    </div>
                                    <div className="mt-4 w-full">
                                      <div className="text-gray-400 text-xs font-semibold ">
                                        Images
                                      </div>
                                      <div className="mt-2 relative flex-shrink-0 border border-gray-100 rounded-lg overflow-hidden cursor-pointer">
                                        <input
                                          type="file"
                                          accept="image/*"
                                          multiple
                                          className="hidden"
                                          id="image-upload"
                                          onChange={(e) => {
                                            if (!newTicket) {
                                              console.error("New ticket is not initialized.");
                                              return;
                                            }
                                            if (newTicket.conversation.filter((msg: { type: string }) => msg.type === "image").length >= 3) {
                                              console.warn("Maximum of 3 images can be uploaded.");
                                              return;
                                            }
                                            const files = e.target.files;
                                            if (files && files.length > 0) {
                                              const selectedFiles = Array.from(files).slice(0, 3);
                                              const imagePreviews = selectedFiles.map(file => ({
                                                sender: "user",
                                                type: "image",
                                                content: URL.createObjectURL(file),
                                                datetime: new Date().toISOString(),
                                              }));
                                              const updatedTicket = {
                                                ...newTicket,
                                                conversation: [
                                                  ...(newTicket.conversation || []),
                                                  ...imagePreviews,
                                                ],
                                                files: [
                                                  ...(newTicket.files || []),
                                                  ...selectedFiles,
                                                ],
                                              };
                                              setNewTicket(updatedTicket);
                                              scrollToBottom();
                                            }
                                          }}
                                        />
                                        <label htmlFor="image-upload" className="flex items-center gap-x-1 py-2 pl-3 pr-4 text-sm text-gray-300 hover:text-gray-400 font-semibold cursor-pointer">
                                          <PlusCircleIcon className="w-4 h-4" />
                                          Upload Images
                                        </label>
                                        <div className="">
                                          {newTicket?.conversation
                                          .filter((msg: { type: string }) => msg.type === "image")
                                          .map((msg: { content: string | StaticImport }, index: Key | null | undefined) => (
                                            <div key={index} className="relative inline-block">
                                              <Image
                                                src={msg.content}
                                                alt={`Uploaded image ${(index as number) + 1}`}
                                                width={100}
                                                height={100}
                                                className="rounded-lg m-4 border border-gray-100"
                                              />
                                              <button
                                                className="absolute top-2 right-2 bg-gray-400/80 hover:bg-gray-500/80 text-white rounded-full p-1"
                                                onClick={() => {
                                                  const updatedConversation = newTicket.conversation.filter((_: any, i: number) => i !== index);
                                                  const updatedTicket = { ...newTicket, conversation: updatedConversation };
                                                  setNewTicket(updatedTicket);
                                                }}
                                              >
                                                <XMarkIcon className="h-4 w-4" />
                                              </button>
                                            </div>
                                          ))}
                                        </div>
                                        <div className="absolute top-0 right-0 flex items-center pt-2 pr-4 text-xs text-gray-300 font-semibold">
                                          {newTicket?.conversation.filter((msg: { type: string }) => msg.type === "image").length}/{3}
                                        </div>
                                      </div>
                                    </div>
                                  </div>
                                  <div className="mt-4 relative grow w-full min-h-0 flex flex-col">
                                    <div className="flex-shrink-0 text-gray-400 text-xs font-semibold ">
                                      Ticket Message
                                    </div>
                                    <textarea
                                      className="mt-2 relative grow w-full min-h-0 py-2 px-3 text-sm outline-none resize-none text-sm text-gray-500 placeholder:text-gray-300 border border-gray-100 focus:border-transparent focus:outline-blue-200 rounded-lg"
                                      placeholder="Type in your message"
                                      value={newMessage}
                                      onChange={(e) => {
                                        if (e.target.value.length <= 500) {
                                          setNewMessage(e.target.value)
                                        }
                                      }}
                                    />
                                    <div className="absolute right-0 bottom-0 flex items-center px-2 mb-2 mr-1 text-xs text-gray-300 font-semibold bg-white">
                                      {newMessage.length}/{500}
                                    </div>
                                  </div>
                                </div>
                              </div>
                              <div className="mt-4 flex-shrink-0 flex items-center justify-end gap-x-4">
                                <button
                                  className="inline-flex items-center gap-2 rounded-md bg-white py-1.5 px-3 text-sm/6 font-semibold text-gray-500 shadow-inner shadow-white/10 focus:outline-none data-[hover]:bg-gray-100 data-[open]:bg-gray-700 data-[focus]:outline-1 data-[focus]:outline-white"
                                  onClick={() => {
                                    setNewTitle("");
                                    setNewMessage("");
                                    setShowTitleModal(false);
                                  }}
                                >
                                  Cancel
                                </button>
                                <button
                                  className={cn(
                                    "flex items-center gap-x-2 p-3 cursor-pointer rounded-md focus:outline-none text-sm overflow-hidden bg-blue-100 hover:bg-blue-200 text-blue-500 font-semibold",
                                    (!newTitle || !newMessage) && "cursor-not-allowed"
                                  )}
                                  onClick={() => {
                                    if (newTitle && newMessage) {
                                      setShowWarningModal(true);
                                    }
                                  }}
                                >
                                  <PlusIcon className="flex-shrink-0 h-4 w-4" />
                                  Create
                                </button>
                                {showWarningModal && (
                                  <Transition appear show={showWarningModal} as={Fragment}>
                                    <Dialog as="div" className="relative z-10" onClose={() => setShowWarningModal(false)}>
                                      <TransitionChild
                                      as={Fragment}
                                      enter="ease-out duration-300"
                                      enterFrom="opacity-0"
                                      enterTo="opacity-100"
                                      leave="ease-in duration-200"
                                      leaveFrom="opacity-100"
                                      leaveTo="opacity-0"
                                      >
                                        <div className="fixed inset-0 bg-black bg-opacity-25" />
                                      </TransitionChild>

                                      <div className="fixed inset-0 overflow-y-auto">
                                        <div className="flex min-h-full items-center justify-center p-4 text-center">
                                          <TransitionChild
                                          as={Fragment}
                                          enter="ease-out duration-300"
                                          enterFrom="opacity-0 scale-95"
                                          enterTo="opacity-100 scale-100"
                                          leave="ease-in duration-200"
                                          leaveFrom="opacity-100 scale-100"
                                          leaveTo="opacity-0 scale-95"
                                          >
                                          <DialogPanel className="w-full max-w-md transform overflow-hidden rounded-2xl bg-white p-6 text-left align-middle shadow-xl transition-all">
                                            <DialogTitle as="h3" className="text-base/7 font-medium text-gray-600">
                                              24 Hour Response Time
                                            </DialogTitle>
                                            <div className="mt-2">
                                              <p className="text-sm text-gray-500">
                                                It may take up to 24 hours to receive a reply from the support team.
                                                It is recommended to give as much detail as possible in your message.
                                                Are you sure you want to send this message?
                                              </p>
                                            </div>

                                            <div className="mt-4 flex justify-end gap-x-4">
                                              <button
                                                className="inline-flex items-center gap-2 rounded-md bg-white py-1.5 px-3 text-sm/6 font-semibold text-gray-500 shadow-inner shadow-white/10 focus:outline-none data-[hover]:bg-gray-100 data-[open]:bg-gray-700 data-[focus]:outline-1 data-[focus]:outline-white"
                                                onClick={() => setShowWarningModal(false)}
                                              >
                                                Cancel
                                              </button>
                                              <button
                                                className="inline-flex items-center justify-center gap-x-2 rounded-md border border-transparent bg-blue-600 px-4 py-2 text-sm font-medium text-white hover:bg-blue-700 focus:outline-none focus-visible:ring-2 focus-visible:ring-offset-2"
                                                disabled={isCreateTicketLoading}
                                                onClick={async () => {
                                                  try {
                                                    setIsCreateTicketLoading(true)
                                                    if (session?.user?.access_token) {
                                                      const fileInfosList: any[] = [];
                                                      if (newTicket?.files) {
                                                        await Promise.all(
                                                          newTicket.files
                                                            .slice(0, 3)
                                                            .map(async (file: string | Blob) => {
                                                              const formData = new FormData();
                                                              formData.append("file", file);
                                                              try {
                                                                const uploadResponse = await api.uploadFile(formData, (session?.user as any).access_token);
                                                                // uploadResponse = {
                                                                //   "presigned_url": "https://s3-us-east-1-dev-gmop-fe-admin-media-assets.s3.amazonaws.com/cs-attachments/124/...",
                                                                //   "file_info": {
                                                                //     "file_path": "cs-attachments/124/d4ee26d1-8c39-4c4d-81ef-de99177bc272.jpeg",
                                                                //     "file_name": "lg_test_image.jpeg",
                                                                //     "file_type": "image/jpeg",
                                                                //     "file_size": 2601
                                                                //   }
                                                                // }
                                                                if (uploadResponse?.file_info) {
                                                                  fileInfosList.push(uploadResponse.file_info);
                                                                }
                                                              } catch (error) {
                                                                console.error("Failed to upload file:", error);
                                                              }
                                                            })
                                                        );
                                                      }
                                                      // {
                                                      //   "title": "파일 업로드 테스트 티켓",
                                                      //   "category": "기술적 지원",
                                                      //   "message": "파일 업로드 테스트입니다.",
                                                      //   "file_infos": [
                                                      //     {
                                                      //       "file_path": "cs-attachments/124/d4ee26d1-8c39-4c4d-81ef-de99177bc272.jpeg",
                                                      //       "file_name": "lg_test_image.jpeg",
                                                      //       "file_type": "image/jpeg",
                                                      //       "file_size": 2601
                                                      //     }
                                                      //   ]
                                                      // }
                                                      const response = await api.createTicket({
                                                        title: newTitle,
                                                        category: "General",
                                                        message: newMessage,
                                                        file_infos: fileInfosList,
                                                      }, session.user.access_token);
                                                      // response = {
                                                      //   "id": 14,
                                                      //   "title": "파일 업로드 테스트 티켓",
                                                      //   "category": "기술적 지원",
                                                      //   "priority": "Medium",
                                                      //   "status": "OPEN",
                                                      //   "created_datetime": "2025-03-25T09:22:14",
                                                      //   "closed_datetime": null,
                                                      //   "last_message_datetime": "2025-03-25T09:22:14",
                                                      //   "assigned_staff": null,
                                                      //   "messages": [
                                                      //     {
                                                      //       "id": 78,
                                                      //       "message_type": "TEXT",
                                                      //       "content": "파일 업로드 테스트입니다.",
                                                      //       "file_info": null,
                                                      //       "sender_type": "CUSTOMER",
                                                      //       "sender_id": 124,
                                                      //       "sender_staff": null,
                                                      //       "created_datetime": "2025-03-25T09:22:14",
                                                      //       "message_status": "UNREAD"
                                                      //     },
                                                      //     {
                                                      //       "id": 79,
                                                      //       "message_type": "FILE",
                                                      //       "content": "",
                                                      //       "file_info": {
                                                      //         "file_name": "lg_test_image.jpeg",
                                                      //         "file_path": "cs-attachments/124/d4ee26d1-8c39-4c4d-81ef-de99177bc272.jpeg",
                                                      //         "file_size": 2601,
                                                      //         "file_type": "image/jpeg"
                                                      //       },
                                                      //       "sender_type": "CUSTOMER",
                                                      //       "sender_id": 124,
                                                      //       "sender_staff": null,
                                                      //       "created_datetime": "2025-03-25T09:22:14",
                                                      //       "message_status": "UNREAD"
                                                      //     }
                                                      //   ]
                                                      // }

                                                      if (response?.id) {
                                                        fetchTicketDetail(response.id);
                                                        await fetchTicketList(new AbortController().signal);
                                                      }
                                                    } else {
                                                      console.error("Access token is missing in the session.");
                                                    }
                                                  } catch (error) {
                                                    console.error("Failed to create ticket:", error);
                                                  } finally {
                                                    setShowTitleModal(false);
                                                    setShowWarningModal(false);
                                                    setNewMessage("");
                                                    setNewTitle("");
                                                    setNewTicket({
                                                      title: "",
                                                      lastUpdatedTime: "",
                                                      conversation: [],
                                                    });
                                                    setIsCreateTicketLoading(false)
                                                  }
                                                }}
                                              >
                                                {isCreateTicketLoading
                                                  ? <svg className="animate-spin h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                                    </svg>
                                                  : ""
                                                }
                                                <div>Confirm</div>
                                              </button>
                                            </div>
                                          </DialogPanel>
                                          </TransitionChild>
                                        </div>
                                      </div>
                                    </Dialog>
                                  </Transition>
                                )}
                              </div>
                            </div>
                          )}
                        </div>
                      ) : (
                        <div className="flex flex-col size-full">
                          <div className="flex-shrink-0 py-4 bg-white">
                            <button
                              className="flex items-center text-blue-500 hover:text-blue-700 text-left text-xs font-semibold"
                              onClick={() => setSelectedTicket(null)}
                            >
                              <ChevronLeftIcon className="h-4 w-4 inline-block" />
                              <span>Back to Tickets</span>
                            </button>
                            <div className="mt-1 flex items-start justify-between gap-x-2">
                              <h4 className="text-lg text-gray-600 font-semibold">{selectedTicket.title}</h4>
                              <div className="mt-1.5 px-2 flex-shrink-0 flex items-center gap-x-2 text-sm text-gray-500">
                                <div className={cn(
                                  "flex-shrink-0 w-2 h-2 rounded-full",
                                  parseTicketStatusStyle(selectedTicket.status)
                                )}></div>
                                {parseTicketStatusString(selectedTicket.status)}
                              </div>
                            </div>
                          </div>
                          <div
                            ref={conversationEndRef}
                            className="relative grow w-full min-h-0 overflow-y-auto rounded-lg bg-gray-100/50 border border-gray-300 shadow-inner"
                          >
                            <div className="sticky inset-x-0 top-0 w-full h-4 bg-gradient-to-t from-transparent from-50% via-gray-100/20 to-gray-100/50 to-100% z-[1]"></div>
                            <div className="px-4 space-y-4">
                              {selectedTicket.conversation.map((message: any, index: number) => (
                                <div
                                  key={index}
                                  className={`flex items-start gap-2 ${
                                    message.sender === "user" ? "flex-row-reverse" : "flex-col"
                                  }`}
                                >
                                  {message.sender === "user" ? (
                                    <div
                                      className="flex-shrink-0 w-6 h-6 rounded-full flex items-center justify-center text-[10px] text-gray-500 font-bold"
                                      style={{
                                        backgroundColor: `${hexToRgbA(stringToColor(session?.user?.email || "abc"), 0.1 )}`,
                                      }}
                                    >
                                      {session?.user?.email?.charAt(0).toUpperCase()}
                                    </div>
                                  ) : (
                                    <div className="flex items-center gap-x-1.5">
                                      <div className="flex-shrink-0 p-[2px] rounded-full bg-gradient-to-br from-blue-500 to-purple-500 opacity-80">
                                        <Image
                                          src="/logo/optapex-favicon-black.png"
                                          alt="Optapex Favicon Logo"
                                          className="w-5 h-5 rounded-full"
                                          width={72}
                                          height={72}
                                        />
                                      </div>
                                      <Image
                                        src="/logo/optapex-logo.svg"
                                        alt="Optapex Logo"
                                        className="w-auto h-3 mt-1 opacity-80"
                                        width={197}
                                        height={60}
                                      />
                                    </div>
                                  )}
                                  <div>
                                    <div
                                      className={`relative p-3 text-sm ${
                                        message.sender === "user" ? "bg-blue-100/80 text-gray-700 self-end ml-8 rounded-tl-lg rounded-b-lg" : "bg-gray-700 text-gray-100 self-start mr-8 rounded-lg"
                                      }`}
                                    >
                                        {message.type === "image" ? (
                                        <>
                                          <Image
                                          src={message.content}
                                          alt="Uploaded image"
                                          width={100}
                                          height={100}
                                          className="rounded-lg border border-gray-100 cursor-pointer"
                                          onClick={() => setSelectedImage(message.content)}
                                          />
                                        </>
                                        ) : (
                                        <p>{message.content}</p>
                                        )}
                                      {message.sender === "user" && (
                                        <div className="absolute top-0 right-[-4px]">
                                          <div
                                            className="w-0 h-0"
                                            style={{
                                              borderStyle: "solid",
                                              borderWidth: "6px 4px 0 0",
                                              borderColor: "rgb(219 234 254 / 0.8) transparent transparent transparent",
                                              transform: "rotate(0deg)"
                                            }}
                                          />
                                        </div>
                                      )}
                                    </div>
                                    <div
                                      className={`text-gray-400 text-xs mt-1.5 ${
                                        message.sender === "user" ? "text-right" : ""
                                      }`}
                                    >
                                      {formatDateTime(message.datetime)}
                                    </div>
                                  </div>
                                </div>
                              ))}
                              {selectedImage && (
                                <Transition appear show={!!selectedImage} as={Fragment}>
                                  <Dialog as="div" className="relative z-10" onClose={() => setSelectedImage(null)}>
                                  <TransitionChild
                                    as={Fragment}
                                    enter="ease-out duration-300"
                                    enterFrom="opacity-0"
                                    enterTo="opacity-100"
                                    leave="ease-in duration-200"
                                    leaveFrom="opacity-100"
                                    leaveTo="opacity-0"
                                  >
                                    <div className="fixed inset-0 bg-black bg-opacity-50" />
                                  </TransitionChild>

                                  <div className="fixed inset-0 overflow-y-auto">
                                    <div className="flex min-h-full items-center justify-center p-4 text-center">
                                    <TransitionChild
                                      as={Fragment}
                                      enter="ease-out duration-300"
                                      enterFrom="opacity-0 scale-95"
                                      enterTo="opacity-100 scale-100"
                                      leave="ease-in duration-200"
                                      leaveFrom="opacity-100 scale-100"
                                      leaveTo="opacity-0 scale-95"
                                    >
                                      <DialogPanel className="w-full max-w-3xl transform overflow-hidden rounded-2xl bg-white p-6 text-left align-middle shadow-xl transition-all">
                                      <Image
                                        src={selectedImage || ""}
                                        alt="Large view"
                                        width={800}
                                        height={800}
                                        className="rounded-lg"
                                      />
                                      <button
                                        className="absolute top-4 right-4 bg-gray-400/80 hover:bg-gray-500/80 text-white rounded-full p-1"
                                        onClick={() => setSelectedImage(null)}
                                      >
                                        <XMarkIcon className="h-5 w-5" />
                                      </button>
                                      </DialogPanel>
                                    </TransitionChild>
                                    </div>
                                  </div>
                                  </Dialog>
                                </Transition>
                                )}
                            </div>
                            <div className="sticky inset-x-0 bottom-0 w-full h-4 bg-gradient-to-b from-transparent from-50% via-gray-100/20 to-gray-100/50 to-100%"></div>
                          </div>
                          <div className="mt-4 border border-gray-300 rounded-lg">
                            <textarea
                              className="w-full mt-1 py-2 px-3 outline-none resize-none text-sm text-gray-500 placeholder:text-gray-300"
                              placeholder="Type in your message"
                              value={newMessage}
                              onChange={(e) => {
                                if (e.target.value.length <= 500) {
                                  setNewMessage(e.target.value)
                                }
                              }}
                            />
                            <div className="flex items-center justify-between px-2 pb-2">
                              <div className="flex items-center px-1 text-xs text-gray-300 font-semibold bg-white">
                                {newMessage.length}/{500}
                              </div>
                              <div className="flex items-center gap-x-4">
                                <label htmlFor="image-upload-in-chat" className="cursor-pointer">
                                  <PaperClipIcon className="h-4 w-4 text-gray-400 hover:text-gray-500" />
                                </label>
                                <input
                                  type="file"
                                  accept="image/*"
                                  className="hidden"
                                  id="image-upload-in-chat"
                                  disabled={isFileUploadLoading}
                                  onChange={async (e) => {
                                    const file = e.target.files?.[0];
                                    if (file) {
                                      setIsFileUploadLoading(true);
                                      try {
                                        const formData = new FormData();
                                        formData.append("file", file);
                                        const uploadResponse = await api.uploadFile(
                                          formData,
                                          (session?.user as any).access_token
                                        );
                                        // uploadResponse = {
                                        //   "presigned_url": "https://s3-us-east-1-dev-gmop-fe-admin-media-assets.s3.amazonaws.com/cs-attachments/124/...",
                                        //   "file_info": {
                                        //     "file_path": "cs-attachments/124/d4ee26d1-8c39-4c4d-81ef-de99177bc272.jpeg",
                                        //     "file_name": "lg_test_image.jpeg",
                                        //     "file_type": "image/jpeg",
                                        //     "file_size": 2601
                                        //   }
                                        // }
                                        if (uploadResponse?.file_info) {
                                          const response = await api.sendMessage({
                                            ticket_id: selectedTicket.id,
                                            message_type: "FILE",
                                            content: "",
                                            file_info: uploadResponse.file_info,
                                          }, (session?.user as any).access_token)
                                          if (response) {
                                            await fetchTicketDetail(selectedTicket.id)
                                            scrollToBottom();
                                          }
                                        }
                                      } catch (error) {
                                        console.error("Failed to upload file:", error);
                                      }
                                      setIsFileUploadLoading(false);
                                    }
                                  }}
                                />
                                <button
                                  className="flex items-center justify-center gap-x-1 p-2 rounded-lg shadow text-xs text-white bg-blue-600 hover:bg-blue-700 focus:outline-none"
                                  disabled={isSendMessageLoading || isFileUploadLoading}
                                  onClick={() => {
                                    if (newMessage.trim()) {
                                      setShowWarningInChatModal(true);
                                    }
                                  }}
                                >
                                  {isFileUploadLoading
                                    ? <svg className="animate-spin h-3 w-3 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                      </svg>
                                    : <PaperAirplaneIcon className="h-3 w-3 inline-block" />
                                  }
                                  Send
                                </button>
                              </div>
                              {showWarningInChatModal && (
                                <Transition appear show={showWarningInChatModal} as={Fragment}>
                                  <Dialog as="div" className="relative z-10" onClose={() => setShowWarningInChatModal(false)}>
                                    <TransitionChild
                                      as={Fragment}
                                      enter="ease-out duration-300"
                                      enterFrom="opacity-0"
                                      enterTo="opacity-100"
                                      leave="ease-in duration-200"
                                      leaveFrom="opacity-100"
                                      leaveTo="opacity-0"
                                    >
                                      <div className="fixed inset-0 bg-black bg-opacity-25" />
                                    </TransitionChild>

                                    <div className="fixed inset-0 overflow-y-auto">
                                      <div className="flex min-h-full items-center justify-center p-4 text-center">
                                        <TransitionChild
                                          as={Fragment}
                                          enter="ease-out duration-300"
                                          enterFrom="opacity-0 scale-95"
                                          enterTo="opacity-100 scale-100"
                                          leave="ease-in duration-200"
                                          leaveFrom="opacity-100 scale-100"
                                          leaveTo="opacity-0 scale-95"
                                        >
                                          <DialogPanel className="w-full max-w-md transform overflow-hidden rounded-2xl bg-white p-6 text-left align-middle shadow-xl transition-all">
                                            <DialogTitle as="h3" className="text-base/7 font-medium text-gray-600">
                                              24 Hour Response Time
                                            </DialogTitle>
                                            <div className="mt-2">
                                              <p className="text-sm text-gray-500">
                                                It may take up to 24 hours to receive a reply from the support team.
                                                It is recommended to give as much detail as possible in your message.
                                                Are you sure you want to send this message?
                                              </p>
                                            </div>

                                            <div className="mt-4 flex justify-end gap-x-4">
                                              <button
                                                className="inline-flex items-center gap-2 rounded-md bg-white py-1.5 px-3 text-sm/6 font-semibold text-gray-500 shadow-inner shadow-white/10 focus:outline-none data-[hover]:bg-gray-100 data-[open]:bg-gray-700 data-[focus]:outline-1 data-[focus]:outline-white"
                                                onClick={() => setShowWarningInChatModal(false)}
                                              >
                                                Cancel
                                              </button>
                                              <button
                                                className="inline-flex items-center justify-center gap-x-2 rounded-md border border-transparent bg-blue-600 px-4 py-2 text-sm font-medium text-white hover:bg-blue-700 focus:outline-none focus-visible:ring-2 focus-visible:ring-offset-2"
                                                disabled={isSendMessageLoading}
                                                onClick={async () => {
                                                  if (newMessage.trim()) {
                                                    setIsSendMessageLoading(true)
                                                    try {
                                                      const response = await api.sendMessage({
                                                        ticket_id: selectedTicket.id,
                                                        message_type: "TEXT",
                                                        content: newMessage,
                                                      }, (session?.user as any).access_token)
                                                      if (response) {
                                                        await fetchTicketDetail(selectedTicket.id)
                                                        setNewMessage("")
                                                        setShowWarningInChatModal(false);
                                                        scrollToBottom();
                                                      }
                                                    } catch (error) {
                                                      console.error("Failed to send message:", error)
                                                    }
                                                    setIsSendMessageLoading(false)
                                                  }
                                                }}
                                              >
                                                {isSendMessageLoading
                                                  ? <svg className="animate-spin h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                                    </svg>
                                                  : ""
                                                }
                                                <div>Confirm</div>
                                              </button>
                                            </div>
                                          </DialogPanel>
                                        </TransitionChild>
                                      </div>
                                    </div>
                                  </Dialog>
                                </Transition>
                              )}
                            </div>
                          </div>
                        </div>
                      )}
                    </div>
                  </TabPanel>
                </TabPanels>
              </div>
            </div>
          )}
        </TabGroup>
      </div>
    </div>
  )
}
