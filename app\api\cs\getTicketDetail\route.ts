import { ErrorResponse } from "@/app/api/_models";
import { getServerApiHostUrl } from "@/utils/host";
import { NextRequest, NextResponse } from "next/server";

export type TicketDetailResponse = any;

export async function GET(
  request: NextRequest
): Promise<NextResponse<TicketDetailResponse | ErrorResponse>> {
  const accessToken = request.headers.get("Authorization");
  if (!accessToken) {
    return NextResponse.json(
      { message: "Authorization header is missing" },
      { status: 400 }
    );
  }
  const ticketId = request.nextUrl.searchParams.get("ticket_id")
  if (!ticketId) {
    return NextResponse.json(
      { message: "ticket_id query is missing" },
      { status: 400 }
    );
  }
  const ticketDetailResponse = await fetch(
    `${await getServerApiHostUrl()}/api/cs/tickets/${ticketId}`,
    {
      method: "GET",
      headers: {
        "Authorization": `Bearer ${accessToken}`,
        "Content-Type": "application/json",
      },
      cache: "no-cache",
    }
  ).then((res) => res.json());

  return NextResponse.json(ticketDetailResponse, { status: 200 });
}
