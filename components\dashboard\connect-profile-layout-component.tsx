"use client"

import { Fragment, useEffect, useState } from "react"
import { useTranslations } from "next-intl"
import { useSession } from "next-auth/react"
import { Tab, TabGroup, TabList, TabPanel, TabPanels, Transition } from '@headlessui/react'
import ConnectAdButton from "@/components/dashboard/connect-ad-button"
import ConnectSpButton from "@/components/dashboard/connect-sp-button"
import ConnectVendorButton from "@/components/dashboard/connect-vendor-button"
import "react-datepicker/dist/react-datepicker.css"
import { cn, formatDate } from "@/utils/msc"
import { api } from "@/utils/api"
import AccountStatus from "./account-status"
import AccountPairViewSlider from "./account-pair-view-slider"


interface AddProfileLayoutProps {
  mopUserData: any;
}

export default function ConnectProfileLayoutComponent({
  mopUserData,
}: AddProfileLayoutProps) {
  const t = useTranslations('component.accounts')
  const { data: session, status } = useSession()
	const [selectedNewProfile, setSelectedNewProfile] = useState<any>(null)
	const fetchNewProfile = async () => {
		const memberResponse = await api.getExMember((session?.user as any).access_token)
		setSelectedNewProfile(memberResponse)
	}
	useEffect(() => {
		if (session?.user) {
			fetchNewProfile()
		}
	}, [session])

	const [selectedAccountPairItem, setSelectedAccountPairItem] = useState<any | null>(null)
	const [isAccountPairViewSliderOpen, setIsAccountPairViewSliderOpen] = useState(false)
	const handleAccountItemClick = (accountItem: any) => {
    setSelectedAccountPairItem(accountItem)
    setIsAccountPairViewSliderOpen(true)
  };
	const handleAccountPairViewCloseClick = () => {
    setSelectedAccountPairItem(null)
    setIsAccountPairViewSliderOpen(false)
  }

  return (
    <div className="relative h-full flex flex-col items-center justify-center py-4 sm:py-6 px-8 sm:px-12 bg-white">
			{
				selectedNewProfile && (
					<div className="relative size-full flex flex-col">
						<h1 className="flex-shrink-0 text-2xl text-gray-800 font-medium">{t('title')}</h1>
						<div className="mt-4 relative grow w-full min-h-0 flex items-center gap-x-6">
							<div className="w-2/5 h-full relative rounded-lg border border-gray-100">
								<div className="relative size-full flex flex-col p-6">
									<div className="flex-shrink-0">
										<div className="flex items-center gap-x-2 text-gray-500 text-lg font-bold">
											<span>Amazon Accounts Pair</span>
											{(() => {
												const amazonAccounts = selectedNewProfile.amazon_accounts && selectedNewProfile.amazon_accounts.length > 0 && selectedNewProfile.amazon_accounts.filter((account: any) => account.ad_lwa_account_id && account.sp_lwa_account_id)
												return (amazonAccounts && amazonAccounts.length > 0
													? (
														<div className="flex items-center justify-center text-xs font-semibold text-blue-500 w-5 h-5 rounded-full bg-blue-100">
															{amazonAccounts.length}
														</div>
													)
													: ""
												)
											})()}
										</div>
										<div className="mt-2 mb-6 text-gray-500 text-sm">
											{t('description')}
										</div>
									</div>
									<div className="mt-2 relative grow w-full min-h-0 rounded-lg bg-white border border-gray-100 overflow-y-scroll">
										{/* List */}
										<div className="sticky inset-x-0 top-0 z-[1] flex items-center gap-x-3 py-3 text-center text-sm text-gray-400 font-bold bg-gray-100/40 backdrop-blur-md">
											<div className="flex-shrink-0 w-[120px] pl-6 text-center">
												Status
											</div>
											<div className="grow w-[200px] px-4">
												Accounts Pair
											</div>
											<div className="flex-shrink-0 w-[160px] pr-6">
												Updated Date
											</div>
										</div>
										<ul className="divide-y divide-gray-100">
											{(() => {
												const amazonAccounts = selectedNewProfile.amazon_accounts && selectedNewProfile.amazon_accounts.length > 0 && selectedNewProfile.amazon_accounts.filter((account: any) => account.ad_lwa_account_id && account.sp_lwa_account_id)
												return (amazonAccounts && amazonAccounts.length > 0
													? amazonAccounts.map((account: any) => {
														return (
															<li
																className="relative flex items-center gap-x-3 py-6 cursor-pointer hover:bg-gray-100/50 text-center text-gray-500 text-sm"
																key={account.id}
																onClick={() => handleAccountItemClick(account)}
															>
																<div
																	className={cn(
																		'absolute inset-y-0 left-0 w-1 h-full',
																		selectedAccountPairItem && account.id === selectedAccountPairItem.id ? 'bg-blue-400' : 'bg-transparent'
																	)}
																></div>
																<div className="flex-shrink-0 flex items-center jutify-start w-[120px] pl-6">
																	<AccountStatus accountItem={account}/>
																</div>
																<div className="grow relative px-4 overflow-hidden text-xs text-gray-500 font-semibold space-y-2">
																	<div className="flex items-center gap-x-2">
																		{account.sp_account_type === "vendor"
																			? <div className="border border-blue-600 bg-blue-100/40 text-blue-800 text-xs font-semibold px-2 py-1 rounded-md">
																					Vendor
																				</div>
																			: <div className="border border-blue-600 bg-blue-100/40 text-blue-800 text-xs font-semibold px-2 py-1 rounded-md">
																					Seller
																				</div>
																		}
																		<div className="truncate">{account.selling_partner_id}</div>
																	</div>
																	<div className="flex items-center gap-x-2">
																		<div className="border border-purple-600 bg-purple-100/40 text-purple-800 text-xs font-semibold px-2 py-1 rounded-md">
																			Ads
																		</div>
																		<div className="truncate">{account.ad_account_id}</div>
																	</div>
																</div>
																<div className="flex-shrink-0 w-[160px] pr-6">
																	<span className="text-xs text-gray-500 font-semibold">
																		{formatDate(account.updated_datetime, ".")}
																	</span>
																</div>
															</li>
														)})
													: (
														<li className="absolute inset-0 size-full flex items-center justify-center text-gray-400 text-sm">
															No Amazon account connected
														</li>
													)
												)
											})()}
										</ul>
									</div>
								</div>
							</div>
							<div className="w-3/5 h-full relative rounded-lg bg-white p-6 border border-gray-100">
								<TabGroup className="flex flex-col h-full">
									<div className="flex-shrink-0">
										<TabList className="inline-flex gap-x-1 bg-gray-100 p-1 rounded-lg">
											<Tab
												className="flex items-center gap-x-1 py-2 px-4 text-sm font-semibold text-gray-400 focus:outline-none data-[selected]:text-gray-500 data-[selected]:bg-white data-[selected]:shadow-md data-[selected]:data-[hover]:bg-white data-[hover]:bg-white/40 data-[focus]:outline-1 data-[focus]:outline-white rounded-lg"
											>
												SP Account
											</Tab>
											<Tab
												className="flex items-center gap-x-1 py-2 px-4 text-sm font-semibold text-gray-400 focus:outline-none data-[selected]:text-gray-500 data-[selected]:bg-white data-[selected]:shadow-md data-[selected]:data-[hover]:bg-white data-[hover]:bg-white/40 data-[focus]:outline-1 data-[focus]:outline-white rounded-lg"
											>
												Vendor Account
											</Tab>
											<Tab
												className="flex items-center gap-x-1 py-2 px-4 text-sm font-semibold text-gray-400 focus:outline-none data-[selected]:text-gray-500 data-[selected]:bg-white data-[selected]:shadow-md data-[selected]:data-[hover]:bg-white data-[hover]:bg-white/40 data-[focus]:outline-1 data-[focus]:outline-white rounded-lg"
											>
												Ads Account
											</Tab>
										</TabList>
									</div>
									<TabPanels className="mt-4 grow relative flex flex-col min-h-0 rounded-lg bg-white">
										<TabPanel className="flex flex-col w-full h-full">
											<div className="relative grow w-full min-h-0">
												<div className="relative size-full flex flex-col">
													<div className="flex-shrink-0 flex items-start justify-between gap-x-8">
														<div className="">
															<div className="flex items-center gap-x-2 text-gray-500 text-lg font-bold">
																<span>SP Account</span>
																{(() => {
																	const spAccounts = selectedNewProfile.amazon_accounts && selectedNewProfile.amazon_accounts.length > 0 && selectedNewProfile.amazon_accounts.filter((account: any) => account.sp_lwa_account_id && account.sp_account_type === "seller")
																	return (spAccounts && spAccounts.length > 0
																		? (
																			<div className="flex items-center justify-center text-xs font-semibold text-blue-500 w-5 h-5 rounded-full bg-blue-100">
																				{spAccounts.length}
																			</div>
																		)
																		: ""
																	)
																})()}
															</div>
															<div className="mt-2 mb-6 text-gray-500 text-sm">
																You have to connect to Seller Central to get your SP data.
															</div>
														</div>
														<div className="flex-shrink-0 -mt-[60px]">
															<ConnectSpButton
																buttonClassName={cn(
																		"group relative flex-shrink-0 flex items-center gap-x-2 p-3 cursor-pointer rounded-md focus:outline-none text-sm overflow-hidden bg-blue-100 hover:bg-blue-200 text-blue-500 font-semibold",
																		"transition-width ease-in-out delay-150"
																)}
																isLNBExpanded={true}
															/>
														</div>
													</div>
													<div className="mt-2 relative grow w-full min-h-0 rounded-lg bg-white border border-gray-100 overflow-y-scroll">
														{/* List */}
														<div className="sticky inset-x-0 top-0 z-[1] flex items-center gap-x-3 py-3 text-center text-sm text-gray-400 font-bold bg-gray-100/40 backdrop-blur-md">
															<div className="flex-shrink-0 w-[140px] pl-6 text-center">
																Account ID
															</div>
															<div className="grow px-4">
																Seller Partner ID
															</div>
															<div className="flex-shrink-0 w-[160px] pr-6">
																Updated Date
															</div>
														</div>
														<ul className="divide-y divide-gray-100">
															{(() => {
																const spAccounts = selectedNewProfile.amazon_accounts && selectedNewProfile.amazon_accounts.length > 0 && selectedNewProfile.amazon_accounts.filter((account: any) => account.sp_lwa_account_id && account.sp_account_type === "seller")
																return (spAccounts && spAccounts.length > 0
																	? spAccounts.map((account: any) => {
																		return (
																			<li
																				className="relative flex items-center gap-x-3 py-6 cursor-pointer hover:bg-gray-100/50 text-center text-gray-500 text-sm"
																				key={account.id}
																			>
																				<div className="flex-shrink-0 w-[140px] pl-6">
																					{account.sp_lwa_account_id}
																				</div>
																				<div className="grow relative px-4 overflow-hidden text-xs text-gray-500 font-semibold">
																					{account.selling_partner_id}
																				</div>
																				<div className="flex-shrink-0 w-[160px] pr-6">
																					<span className="text-xs text-gray-500 font-semibold">
																						{formatDate(account.updated_datetime, ".")}
																					</span>
																				</div>
																			</li>
																		)})
																	: (
																		<li className="absolute inset-0 size-full flex items-center justify-center text-gray-400 text-sm">
																			No SP account connected
																		</li>
																	)
																)
															})()}
														</ul>
													</div>
												</div>
											</div>
										</TabPanel>
										<TabPanel className="flex flex-col w-full h-full">
											<div className="relative grow w-full min-h-0">
												<div className="relative size-full flex flex-col">
													<div className="flex-shrink-0 flex items-start justify-between gap-x-8">
														<div className="">
															<div className="flex items-center gap-x-2 text-gray-500 text-lg font-bold">
																<span>Vendor Account</span>
																{(() => {
																	const vendorAccounts = selectedNewProfile.amazon_accounts && selectedNewProfile.amazon_accounts.length > 0 && selectedNewProfile.amazon_accounts.filter((account: any) => account.sp_lwa_account_id && account.sp_account_type === "vendor")
																	return (vendorAccounts && vendorAccounts.length > 0
																		? (
																			<div className="flex items-center justify-center text-xs font-semibold text-blue-500 w-5 h-5 rounded-full bg-blue-100">
																				{vendorAccounts.length}
																			</div>
																		)
																		: ""
																	)
																})()}
															</div>
															<div className="mt-2 mb-6 text-gray-500 text-sm">
																You have to connect to Vendor Central to get your Vendor data.
															</div>
														</div>
														<div className="flex-shrink-0 -mt-[60px]">
															<ConnectVendorButton
																buttonClassName={cn(
																		"group relative flex-shrink-0 flex items-center gap-x-2 p-3 cursor-pointer rounded-md focus:outline-none text-sm overflow-hidden bg-blue-100 hover:bg-blue-200 text-blue-500 font-semibold",
																		"transition-width ease-in-out delay-150"
																)}
																isLNBExpanded={true}
															/>
														</div>
													</div>
													<div className="mt-2 relative grow w-full min-h-0 rounded-lg bg-white border border-gray-100 overflow-y-scroll">
														{/* List */}
														<div className="sticky inset-x-0 top-0 z-[1] flex items-center gap-x-3 py-3 text-center text-sm text-gray-400 font-bold bg-gray-100/40 backdrop-blur-md">
															<div className="flex-shrink-0 w-[140px] pl-6 text-center">
																Account ID
															</div>
															<div className="grow px-4">
																Seller Partner ID
															</div>
															<div className="flex-shrink-0 w-[160px] pr-6">
																Updated Date
															</div>
														</div>
														<ul className="divide-y divide-gray-100">
															{(() => {
																const vendorAccounts = selectedNewProfile.amazon_accounts && selectedNewProfile.amazon_accounts.length > 0 && selectedNewProfile.amazon_accounts.filter((account: any) => account.sp_lwa_account_id && account.sp_account_type === "vendor")
																return (vendorAccounts && vendorAccounts.length > 0
																	? vendorAccounts.map((account: any) => {
																		return (
																			<li
																				className="relative flex items-center gap-x-3 py-6 cursor-pointer hover:bg-gray-100/50 text-center text-gray-500 text-sm"
																				key={account.id}
																			>
																				<div className="flex-shrink-0 w-[140px] pl-6">
																					{account.sp_lwa_account_id}
																				</div>
																				<div className="grow relative px-4 overflow-hidden text-xs text-gray-500 font-semibold">
																					{account.selling_partner_id}
																				</div>
																				<div className="flex-shrink-0 w-[160px] pr-6">
																					<span className="text-xs text-gray-500 font-semibold">
																						{formatDate(account.updated_datetime, ".")}
																					</span>
																				</div>
																			</li>
																		)})
																	: (
																		<li className="absolute inset-0 size-full flex items-center justify-center text-gray-400 text-sm">
																			No Vendor account connected
																		</li>
																	)
																)
															})()}
														</ul>
													</div>
												</div>
											</div>
										</TabPanel>
										<TabPanel className="flex flex-col w-full h-full">
											<div className="relative grow w-full min-h-0">
												<div className="relative size-full flex flex-col">
													<div className="flex-shrink-0 flex items-start justify-between gap-x-8">
														<div className="">
															<div className="flex items-center gap-x-2 text-gray-500 text-lg font-bold">
																<span>Ads Account</span>
																{(() => {
																	const adsAccounts = selectedNewProfile.amazon_accounts && selectedNewProfile.amazon_accounts.length > 0 && selectedNewProfile.amazon_accounts.filter((account: any) => account.ad_lwa_account_id)
																	return (adsAccounts && adsAccounts.length > 0
																		? (
																			<div className="flex items-center justify-center text-xs font-semibold text-purple-500 w-5 h-5 rounded-full bg-purple-100">
																				{adsAccounts.length}
																			</div>
																		)
																		: ""
																	)
																})()}
															</div>
															<div className="mt-2 mb-6 text-gray-500 text-sm">
																You can connect multiple Ads accounts to be connected to your SP or Vendor accounts.
															</div>
														</div>
														<div className="flex-shrink-0 -mt-[60px]">
															<ConnectAdButton
																buttonClassName={cn(
																		"group relative flex-shrink-0 flex items-center gap-x-2 p-3 cursor-pointer rounded-md focus:outline-none text-sm overflow-hidden bg-purple-100 hover:bg-purple-200 text-purple-500 font-semibold",
																		"transition-width ease-in-out delay-150"
																)}
																isLNBExpanded={true}
															/>
														</div>
													</div>
													<div className="mt-2 relative grow w-full min-h-0 rounded-lg bg-white border border-gray-100 overflow-y-scroll">
														{/* List */}
														<div className="sticky inset-x-0 top-0 z-[1] flex items-center gap-x-3 py-3 text-center text-sm text-gray-400 font-bold bg-gray-100/40 backdrop-blur-md">
															<div className="flex-shrink-0 w-[140px] pl-6 text-center">
																Account ID
															</div>
															<div className="grow px-4">
																Profile Email
															</div>
															<div className="flex-shrink-0 w-[160px] pr-6">
																Updated Date
															</div>
														</div>
														<ul className="divide-y divide-gray-100">
															{(() => {
																const adsAccounts = selectedNewProfile.amazon_accounts && selectedNewProfile.amazon_accounts.length > 0 && selectedNewProfile.amazon_accounts.filter((account: any) => account.ad_lwa_account_id)
																return (adsAccounts && adsAccounts.length > 0
																	? adsAccounts.map((account: any) => {
																		return (
																			<li
																				className="relative flex items-center gap-x-3 py-6 cursor-pointer hover:bg-gray-100/50 text-center text-gray-500 text-sm"
																				key={account.id}
																			>
																				<div className="flex-shrink-0 w-[140px] pl-6">
																					{account.ad_lwa_account_id}
																				</div>
																				<div className="grow relative px-4 overflow-hidden text-xs text-gray-500 font-semibold">
																					{account.ad_account_id}
																				</div>
																				<div className="flex-shrink-0 w-[160px] pr-6">
																					<span className="text-xs text-gray-500 font-semibold">
																						{formatDate(account.updated_datetime, ".")}
																					</span>
																				</div>
																			</li>
																		)})
																	: (
																		<li className="absolute inset-0 size-full flex items-center justify-center text-gray-400 text-sm">
																			No Ads account connected
																		</li>
																	)
																)
															})()}
														</ul>
													</div>
												</div>
											</div>
										</TabPanel>
									</TabPanels>
								</TabGroup>
							</div>
						</div>
					</div>
				)
			}
			{/* right side slider for portfolio view content */}
			<Transition show={isAccountPairViewSliderOpen} as={Fragment}>
				<div className="absolute inset-0 z-[1]">
					{isAccountPairViewSliderOpen && selectedAccountPairItem &&
						<AccountPairViewSlider
							handleAccountPairViewCloseClick={handleAccountPairViewCloseClick}	
							selectedAccountPairItem={selectedAccountPairItem}
						/>
					}
				</div>
			</Transition>
		</div>
  )
}
