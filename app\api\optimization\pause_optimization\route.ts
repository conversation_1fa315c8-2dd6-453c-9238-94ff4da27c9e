import { ErrorResponse } from "@/app/api/_models";
import { getServerApiHostUrl } from "@/utils/host";
import { NextRequest, NextResponse } from "next/server";

export type PauseOptimizationResponse = any[];

export async function PUT(
  request: NextRequest
): Promise<NextResponse<PauseOptimizationResponse | ErrorResponse>> {
  const accessToken = request.headers.get("Authorization");
  const body = await request.json();

  if (!accessToken) {
    return NextResponse.json(
      { message: "Authorization header is missing" },
      { status: 400 }
    );
  }
  const accountId = request.nextUrl.searchParams.get("account_id")
  if (!accountId) {
    return NextResponse.json(
      { message: "account_id query is missing" },
      { status: 400 }
    );
  }
  const marketplaceId = request.nextUrl.searchParams.get("marketplace_id")
  if (!marketplaceId) {
    return NextResponse.json(
      { message: "marketplace_id query is missing" },
      { status: 400 }
    );
  }
  const pauseOptimizationsResponse = await fetch(
    `${await getServerApiHostUrl()}/api/optimization/pause_optimization?account_id=${accountId}&marketplace_id=${marketplaceId}`,
    {
      method: "PUT",
      headers: {
        "Authorization": `Bearer ${accessToken}`,
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        "optimization_id": body?.optimization_id,
        "bid_yn": body?.bid_yn,
      }),
      cache: "no-cache",
    }
  ).then((res) => res.json());

  return NextResponse.json(pauseOptimizationsResponse, { status: 200 });
}
