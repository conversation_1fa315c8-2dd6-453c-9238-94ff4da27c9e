{"AppLayout": {"home": "Home", "logout": "Logout", "profile": "Profile"}, "HomePage": {"title": "Home"}, "LocaleSwitcher": {"ko": "한국어", "en": "English", "cn": "中文", "label": "Language"}, "LoginPage": {"description": "Please enter your details.", "email": "Email", "invalidCredentials": "Please check your credentials.", "invalidEmail": "Please enter a valid email address.", "invalidPassword": "Please enter a password.", "login": "Sign in", "password": "Password", "title": "Welcome back"}, "component": {"gnb": {"logo": "OptApex", "account": {"logout": "Sign Out", "withdraw": "delete my account"}}, "accounts": {"title": "Manage Your Amazon Accounts", "description": "You need a pair of Amazon accounts for the full functionality. A pair can be a combination of SP and Ads accounts or Vendor and Ads accounts.", "noProductData": "No product data connected", "productData": "Product Data", "authorizeAds": "Authorize Ads", "authorizeSP": "Authorize SP", "authorizeVendor": "Authorize <PERSON><PERSON><PERSON>", "selectRegion": "Select Region", "selectRegionDescription": "You have to select a region to authorize your Amazon Ad Account.", "chooseProductData": "Choose a product data to connect", "productDataAvailable": "product data available", "type": "Type", "actions": "Actions", "cancel": "Cancel", "confirm": "Confirm", "chooseProductType": "Choose the type of your product data to authorize", "authorizationDescription": "Once you authorize SP or Vendor data from Amazon, you can connect each marketplace with your ad profile later on without further authorization.", "areSeller": "Are you a seller?", "areVendor": "Are you a vendor?", "notConnected": "Not Connected"}, "lnb": {"profile": {"region": "region", "profileEmail": "account email", "profileName": "account name", "profileId": "profile id"}, "marketplace": {"country": {"usa": "US", "canada": "CA", "mexico": "MX", "japan": "JP", "china": "CN"}, "currency": {"USD": "USD", "CAD": "CAD", "MXN": "MXN", "JPY": "JPY", "CNY": "CNY"}, "title": {"usMarketTitle": "{{marketplace.country.usa}} {{marketplace.currency.USD}}", "caMarketTitle": "{{marketplace.country.canada}} {{marketplace.currency.CAD}}", "mxMarketTitle": "{{marketplace.country.mexico}} {{marketplace.currency.MXN}}", "jpMarketTitle": "{{marketplace.country.japan}} {{marketplace.currency.JPY}}", "cnMarketTitle": "{{marketplace.country.china}} {{marketplace.currency.CNY}}"}, "marketplaceName": {"label": "marketplace name", "content": {"usMarketplace": "Amazon.com", "caMarketplace": "Amazon.ca", "mxMarketplace": "Amazon.com.mx", "jpMarketplace": "Amazon.co.jp", "cnMarketplace": "Amazon.cn"}}, "marketplaceId": {"label": "marketplace id"}}, "menu": {"accounts": "Accounts", "home": "Home", "competition": "Brand Protection", "reports": "Reports", "optimizationSets": "Optimization Sets", "realtime": "Real-time", "downloads": "Downloads", "notifications": "Notifications", "manageAccounts": "Accounts", "manageBillings": "<PERSON><PERSON>"}}, "calendar": {"month": ["January", "February", "March", "April", "May", "June", "July", "August", "September", "October", "November", "December"], "days": ["Su", "Mo", "Tu", "We", "Th", "Fr", "Sa"]}, "eligibility": {"eligible": "Eligible", "ineligible": "Ineligible", "unknown": "Eligibility Unknown"}, "productStatus": {"mopAuto": "Optimizing", "amazonAuto": "Amazon Auto", "ineligible": "Ineligible", "initializing": "Initializing", "processing": "Processing", "paused": "Paused", "warning": "Warning", "error": "Error"}, "campaignType": {"auto": "AUTO", "manual": "MANUAL"}, "portfolioStatus": {"initializing": "Initializing", "processing": "Processing", "paused": "Paused", "active": "Active", "expired": "Expired", "warning": "Warning"}, "downloadStatus": {"creating": "Creating", "created": "Created", "error": "Error", "expired": "Expired"}, "adStatus": {"enabled": "ENABLED", "paused": "PAUSED"}, "accountStatus": {"initializing": "Initializing", "processing": "Processing", "paused": "Paused", "active": "Active"}, "filter": {"dateRange": {"label": "Date Range"}, "compareWith": {"label": "Compare With", "content": {"null": "None", "previousMonth": "Previous month", "previousYear": "Previous year", "customPeriod": "Custom period"}}}, "status": {"notAvailable": "Not Available", "connected": "connected", "activated": "Activated", "activate": "Activate"}, "billing": {"title": "Manage Your Billings", "table": {"header": {"status": "Status", "accountsPair": "Accounts Pair", "updatedDate": "Updated Date", "pairActions": "Pair Actions", "billing": "Billing"}}}}, "CompetitionPage": {"daily": "Daily", "weekly": "Weekly", "topBannerTitle": "Brand Protection Score", "tableTitle": "My Brand Keyword Protection Status", "weeklyFunnel": {"topButton": "Weekly Funnel", "modalTitle": "Weekly Funnel", "targetPeriod": "Target Period", "targetKeywords": "Target Keywords", "myBrandShare": "My Brand Share", "competitorShare": "Competitor Share", "table": {"header": {"keyword": "Keyword", "impressionShare": "Impression Share", "leadingProducts": "Leading Products"}}}, "keywordModalTitle": "Keyword Competition Status", "overview": {"weeklyAvgKeywordRank": "Weekly Avg. Keyword Rank", "weeklyAvgRelKeywordRank": "Weekly Avg. Relative Keyword Rank", "weeklyBrandKeywordLeadershipRate": "Weekly Brand Keyword Leadership Rate"}, "searchBar": {"placeholder": "Search by Keyword"}, "tooltip": {"brandProtectionScore": "Brand Protection Score provides insight into how well your brand keywords are being protected against competition. The score may scale up from 0 to 1, with higher score meaning stronger protection.<enter></enter><enter></enter>It is calculated based on the click share of brand keywords picked by LG Optapex's classification algorithm and the number of competitors.", "keywordModalTitle": "You can find your brand's click share leadership trend against your key competitors.<enter></enter><enter></enter>Key Competitors are picked based on LG Optapex's algorithm, which uses the product's rank, price, listing information and keyword relationship with my brand's products."}, "table": {"header": {"queryVolumeRank": "Query Volume Rank", "myBrandKeyword": "My Brand Keyword", "myBrandClickShare": "My Brand Click Share", "leadingProducts": "Leading Products"}}, "keywordModal": {"myBrandTotalClickShare": "My Brand Total Click Share", "keyCompetitorsTotalClickShare": "Key Competitors Total Click Share"}}, "DashboardPage": {"title": "Amazon Performance", "filter": {"dateRange": {"label": "Date Range"}, "attributionWindow": {"label": "Attribution Window", "content": {"1d": "1 day", "7d": "7 days", "14d": "14 days", "30d": "30 days"}}, "optimizationSet": {"label": "Optimization Set", "content": {"optSet": "opt set", "null": "None"}}, "productFilter": {"label": "Product Filter"}, "compareWith": {"label": "Compare With", "content": {"null": "None", "previousMonth": "Previous month", "previousYear": "Previous year", "customPeriod": "Custom period"}}, "customPeriod": {"label": "Custom period"}, "advancedFilter": {"label": "Advanced Filter", "subLabel": {"lowInventory": "Low Stock", "excessInventory": "Excess Stock", "recommendedAsin": "Recommended ASIN"}, "content": {"null": "None", "low30d": "Days of Supply < 30 days", "low60d": "Days of Supply < 60 days", "low90d": "Days of Supply < 90 days", "excess180d": "Days of Supply > 180 days", "excess270d": "Days of Supply > 270 days", "excess365d": "Days of Supply > 365 days", "growth": "Growth Potential", "efficiency": "Efficiency Potential"}}}, "keyMetrics": {"totalMetrics": {"totalSales": "Total Sales", "totalRevenue": "Revenue", "totalCogsRevenue": "Cogs Revenue", "estFee": "Estimated Fee", "estMargin": "Estimated <PERSON>"}, "adMetrics": {"adSales": "Ad Sales", "adSpend": "Ad Spend", "roas": "ROAS"}, "metricsTooltip": {"estimatedMargin": "(total sales - sales returned - estimated fee - ad cost) / total sales * 100", "vendorEstimatedMargin": "(cogs revenue - sales returned - estimated fee - ad cost) / cogs revenue * 100", "totalSales": "<first>${value} of sales returned is not accounted.</first>", "totalCogsRevenue": "<first>Cogs revenue is revenue from Amazon given to the vendor. {value} of shipped revenue is sum of actual prices sold to cutomers.</first>", "totalRevenue": "Revenue is the sum of prices customers actually paid for the products.", "estFee": "fba fee + referral fee + storage fee (according to the time span)", "roas": "(SP sales + SD sales) / (SP spend + SD spend) * 100"}}, "graph": {"tab": {"daily": "Daily", "hourly": "Hourly", "tabTooltip": {"hourly": "Shows the average value of a specific hour during the query period."}}, "totalMetrics": {"absolute": {"totalSales": "Total Sales", "estMargin": "Estimated <PERSON>", "pageViews": "Pageviews"}, "compared": {"totalSales": "Total Sales (compared)", "estMargin": "Estimated <PERSON> (compared)", "pageViews": "Pageviews (compared)"}}, "adMetrics": {"absolute": {"adSales": "Ad Sales", "roas": "ROAS", "adClicks": "<PERSON>"}, "compared": {"adSales": "Ad Sales (compared)", "roas": "ROAS (compared)", "adClicks": "<PERSON> (compared)"}}, "legendSelect": {"sales": "Total Sales", "adSales": "Ad Sales", "adSalesSameSku": "Ad Sales for Same SKU", "sdSalesPromotedClick": "SD Sales Promoted Clicks", "pageViews": "Pageviews", "clicks": "<PERSON>", "impressions": "Impressions", "sessions": "Sessions", "roas": "ROAS", "estimatedMargin": "Est. Profit", "comparedSales": "Total Sales (compared)", "comparedAdSales": "Ad Sales (compared)", "comparedAdSalesSameSku": "Ad Sales for Same SKU (compared)", "comparedSdSalesPromotedClick": "SD Sales Promoted Clicks (compared)", "comparedPageViews": "Pageviews (compared)", "comparedClicks": "<PERSON> (compared)", "comparedImpressions": "Impressions (compared)", "comparedSessions": "Sessions (compared)", "comparedRoas": "ROAS (compared)", "comparedEstimatedMargin": "Est. <PERSON>it (compared)", "revenue": "Revenue", "cogsRevenue": "Cogs Revenue", "adCost": "Ad Spend", "glanceViews": "Glance Views", "comparedRevenue": "Revenue (compared)", "comparedCogsRevenue": "Cogs Revenue (compared)", "comparedAdCost": "<PERSON>pend (compared)", "comparedGlanceViews": "Glance Views (compared)", "orderedUnits": "Ordered Units", "adOrders": "Ad Orders"}}, "table": {"header": {"products": "Products", "totalPerformance": "Total Performance", "adPerformance": "Ad Performance"}, "subHeader": {"productsAdInfo": "Product & Ad Info"}, "content": {"products": {"rank": "<first><second>#{value1}</second> in {value2}</first>", "fee": "FEE", "points": "POINTS", "inventory": "INVENTORY", "inventoryType": {"available": "available", "unfulfillable": "unfulfillable", "reserved": "reserved", "inbound": "inbound", "openPurchase": "open purchase order units"}, "asin": "ASIN", "sku": "SKU"}, "totalMetrics": {"cogsRevenue": "Cogs Revenue", "totalRevenue": "Shipped Revenue", "sales": "Sales", "estFee": "<PERSON><PERSON><PERSON>", "shippedUnits": "Shipped Units", "unitsSold": "Units Sold", "unitsReturned": "Units Returned", "salesReturned": "Sales Returned", "sessions": "Sessions", "buyBoxPercentage": "Buy Box %", "glanceViews": "Glance Views", "pageViews": "Pageviews", "cvrTotal": "CVR (Total)"}, "adMetrics": {"adSpend": "Ad Spend", "adSales": "Ad Sales", "adUnitsSold": "SP Units Sold", "attributedSalesSameSku": "SP Sales (Same SKU)", "unitsSoldSameSku": "SP Units Sold (Same SKU)", "tacos": "TACOS", "profit": "Profit", "acos": "ACOS", "spAcos": "SP ACOS", "roas": "ROAS", "impressions": "Impressions", "spImpressions": "SP Impressions", "clicks": "<PERSON>licks", "spClicks": "SP Clicks", "ctr": "CTR", "spCtr": "SP CTR", "avgCPC": "Avg.CPC", "spAvgCPC": "SP Avg.CPC", "cvrAds": "CVR (Ad)", "spCvr": "SP CVR", "spCost": "SP Spend", "spSales": "SP Sales", "spRoas": "SP ROAS", "sdSales": "SD Sales", "sdSalesPromotedClicks": "SD Promoted Clicks", "sdCost": "SD Spend", "sdUnitsSold": "SD Units Sold", "sdClicks": "SD Clicks", "sdImpressions": "SD Impressions", "sdImpressionsViews": "SD Impressions Views", "sdCumulativeReach": "SD Cumulative Reach", "sdDetailPageViews": "SD Detail Page Views", "sdNewToBrandDetailPageViews": "SD New to Brand Detail Page Views", "sdNewToBrandSales": "SD New to Brand Sales", "sdRoas": "SD ROAS", "sdAcos": "SD ACOS", "sdConvRate": "SD CVR", "sdViewClickThroughRate": "SD View CTR", "sdNewToBrandSalesRate": "SD New to Brand Sales Rate", "sdImpressionFrequencyAverage": "SD Impression Frequency Average", "sdViewabilityRate": "SD Viewability Rate", "sdNewToBrandDetailPageViewRate": "SD New to Brand Detail Page View Rate"}}, "adDetailRow": {"header": {"status": "STATUS", "runBy": "Run By", "campaign": "Campaign Name"}, "content": {"status": {"enabled": "ENABLED", "paused": "PAUSED", "archived": "ARCHIVED"}, "runBy": {"amazon": "Amazon", "mop": "OptApex"}, "campaign": {"id": "ID", "budget": "Budget", "type": {"auto": "AUTO", "manual": "MANUAL"}}}}}, "viewSetting": {"rowsPerPage": "Rows per page", "currentRow": "{value1} of {value2}"}}, "optimizationSets": {"title": "Optimization Set", "topButton": {"addNew": "Add New"}, "searchBar": {"placeholder": "Search by Optimization Set Name"}, "optSetList": {"header": {"status": "Status", "name": "Name", "period": "Period", "budgetPacing": "Budget Usage", "creationDate": "Creation Date"}, "content": {"optSetRow": {"status": {"active": "Active", "paused": "Paused"}, "statusAlert": {"abnormal": "Abnormal", "warning": "Warning"}, "name": {"optSet": "opt set", "topProduct": "Top product"}, "period": {"optObjective": {"maxSales": "Maximize Sales", "vendorMaxSales": "Maximize Vendor <PERSON>", "maxProfit": "Maximize Profit", "vendorMaxProfit": "<PERSON><PERSON>or <PERSON>", "maxAdSales": "Maximize Ad Sales", "maxRoas": "Maximize ROAS"}}, "budgetPacing": {"totalBudget": "Total Budget", "monthlyBudget": "Monthly Budget"}, "searchResultNull": "No search results found", "optSetNull": "No Optimization Sets Created"}, "productRow": {"statusAlert": {"abnormal": "Abnormal", "warning": "Warning"}, "asin": "ASIN", "sku": "SKU"}}}, "detailModal": {"accountPair": {"message": {"dateDifference": "<first>{value}</first> <second>days have passed since your accounts pair was created.</second>", "initialWarning": "It usually takes about 1~2 weeks or more to fully load data from Amazon."}}, "optSet": {"topButton": {"edit": "Edit", "resume": "Resume", "pause": "Pause", "delete": "Delete"}, "topStatus": {"active": "Active", "paused": "Paused"}, "message": {"ineligibleDetails": "<first>{value}</first> <second><red>Ineligible product(s)</red> exist</second>", "totalBudget": "<first>{value}</first> <second>of total budget</second>", "expectedUsage": "<first>{value}</first> <second>predicted to be used</second>", "feasibleBudgetRange": "<second>feasible budget range</second> <first>{value}</first>", "and": " and ", "ineligible": "Ineligible product(s) exist.", "budgetDecrease": "Budget must be decreased", "budgetIncrease": "Budget must be increased"}, "budgetSensitivityGraph": {"title": "Budget Simulator", "cta": "Find Your Optimal Budget", "tooltip": {"budgetGuideline": "The budget guideline is forecasted based on the current status of your optimization set, analyzing recent performance and trends to suggest an optimal budget range."}, "warning": "View your projected performance for the next 7 days.", "maximum": "Maximum", "minimum": "Minimum", "optimalBudget": "Optimal Budget", "optimizationTarget": "Optimization Target", "optimizationNotStarted": "Start the simulation to find your optimal budget.", "startSimulation": "Start Simulation", "totalProfit": {"label": "Total Profit"}, "totalSales": {"label": "Total Sales"}, "totalCost": {"label": "Total Cost"}, "adSpend": {"label": "Ad Spend"}, "adSales": {"label": "Ad Sales"}, "adSalesSameSku": {"label": "Ad Sales Same SKU"}}, "budgetPaceGraph": {"title": {"daterangeBudget": "Budget Pacing Status", "monthlyBudget": "Monthly Budget Pacing Status"}, "budgetType": {"noTracking": {"label": "Not tracked", "details": "Final usage not yet calculated"}, "abnormal": {"label": "Abnormal", "underAbnormal": {"details": "Below normal pace", "action": "Decrease Budget"}, "overAbnormal": {"details": "Above normal pace", "action": "Increase Budget"}}, "normal": {"label": "Normal", "details": "Budget usage in Normal pace"}}, "metricType": {"usage": {"daterange": "Budget Spent", "monthly": "Budget Spent"}, "usageExpected": "Expected Final Usage", "budget": {"daterange": "Total budget", "monthly": "Total budget"}}}, "optProfile": {"label": "Optimization Set", "optSetName": "opt set {value}", "optSetObjective": {"label": "Objective", "objectiveType": {"maxSales": "Maximize Sales", "vendorMaxSales": "Maximize Vendor <PERSON>", "maxProfit": "Maximize Profit", "vendorMaxProfit": "<PERSON><PERSON>or <PERSON>", "maxAdSales": "Maximize Ad Sales", "maxRoas": "Maximize ROAS"}, "option": {"default": "No Boost Selected", "minInventory": "Minimizing Inventory Option", "boostImpression": "Boosting Impression Option"}}, "optSetPeriod": {"label": "Period", "policy": {"daterange": "Date Range", "monthly": "Monthly Recurring"}, "createdDate": "created at"}}, "budgetTab": {"tabLabel": "Budget Optimization", "searchBar": {"placeholder": "Search by Product Title or ASIN"}, "table": {"header": {"status": "Status", "productInfo": "Product", "totalBudgetUsage": "Budget Usage"}, "content": {"statusAlert": {"warning": "Warning", "abnormal": "Abnormal"}, "productInfo": {"asin": "ASIN", "sku": "SKU"}, "searchResultNull": "No search results found", "optSetNull": "No Optimization Sets Created"}}}, "historyTab": {"tabLabel": "History", "filter": {"dateRange": {"label": "Date Range"}}, "table": {"header": {"actionType": "Action Type", "actionDetails": "Action Details", "dateTime": "Date Time"}, "content": {"actionType": {"create": "Optimization set created", "edit": "Optimization set edited", "pauseResume": "Optimization set paused/resumed"}, "actionDetails": {"budgetPolicy": "Budget Policy", "budgetAmount": "Budget Amount", "budgetStartDate": "Budget Start Date", "budgetEndDate": "Budget End Date"}, "null": "No history result found"}}}}, "product": {"message": {"abnormal": {"ineligibleAlert": "This product is ineligible.", "action": "<first>Please review the product and make necessary changes at <red>Amazon Seller Central</red>.</first>"}, "warning": {"lowStockAlert": "This product is running low stock.", "estimatedSold": "<orange1>{value}</orange1> <first><orange2>units</orange2> estimated to be sold daily</first>", "available": "<orange1>{value}</orange1> <first><orange2>units</orange2> available</first>", "estimatedDaysLeft": "<orange1>{value}</orange1> <first><orange2>day(s)</orange2> estimated until sold out</first>"}}, "productProfile": {"label": {"optSet": "opt set", "asin": "ASIN", "sku": "SKU"}}, "campaignTab": {"tabLabel": "Campaigns", "tabTooltip": "Run campaigns automated by Amazon for the first 2 weeks to soft land the product.<enter></enter><enter></enter>Based on the performance of campaigns run by Amazon for the last 2 weeks, the OptApex system will automatically optimize the bid to reach your objective within the budget.", "cumulatedBudgetUsage": "Budget Spent", "table": {"header": {"status": "Status", "campaignInfo": "Campaign Info", "dailyBudgetUsage": "Daily Budget Usage"}, "content": {"status": {"enabled": "Enabled", "paused": "Paused"}, "campaignInfo": {"id": "ID", "startDate": "Started at"}}}, "donutChart": {"dailyBudgetUsage": "Daily Budget Usage"}}, "productHistoryTab": {"tabLabel": "Product History", "filter": {"history": {"label": "History Filter", "type": {"operation": "Operation", "budget": "Budget"}}, "date": {"label": "Date Range"}}, "table": {"header": {"actionType": "Action Type", "from": "From", "to": "To", "datetime": "Date Time"}, "content": {"operation": {"actionType": {}, "itemChanged": {}}, "budget": {"actionType": {}, "itemChanged": {}}, "null": "No history result found"}}}, "targetTab": {"tabLabel": "Target", "filter": {"date": {"label": "Date Range"}}, "table": {"header": {"actionType": "Action Type", "from": "From", "to": "To", "datetime": "Date Time"}, "content": {"actionType": {}, "itemChanged": {}, "null": "No history result found"}}}, "bidHistoryTab": {"tabLabel": "Bid History", "filter": {"date": {"label": "Date Range"}}, "table": {"header": {"actionType": "Action Type", "from": "From", "to": "To", "datetime": "Date Time"}, "content": {"actionType": {}, "itemChanged": {}, "null": "No history result found"}}}}}, "addEditModal": {"topButton": {"save": "Save"}, "bulkSelection": {"openModal": "Bulk Selection", "description": "Paste csv or txt file with ASINs or SKUs to select products in bulk.", "select": "Select", "textarea": {"placeholder": "Enter ASINs separated by a comma, space or new line."}, "import": {"placeholder": "Import CSV or TXT file", "tooltip": "You can click to select a file here. A sample file is available for download.", "sampleFile": "Download Sample File"}, "matchedProducts": "Matched Products"}, "optSetName": {"label": "Optimization Set Name", "placeholder": "Enter Optimization Set Name", "duplicateError": "Optimization set name already exists", "limitReachedMessage": "In the LITE version, the maximum number of optimization sets is limited to 1."}, "product": {"searchBar": {"placeholder": "Search by Product Title or ASIN"}, "targetCandidates": {"label": "Target Candidates", "asin": "ASIN", "null": "No search result found"}, "targetProducts": {"label": "Target Products", "asin": "ASIN", "null": "No product selected"}}, "objective": {"label": "Objective", "subLabel": "Optimization Objectives", "optObjectiveType": {"maxSales": {"label": "Maximize Sales", "vendorLabel": "Maximize Vendor <PERSON>", "dataSource": "SP data + Ad data", "vendorDataSource": "Vendor data + Ads data", "details": "Use Organic data and Ad data altogether to maximize your total sales", "vendorDetails": "Use vendor data and Ads data altogether to maximize your objective", "option": "Minimize Inventory"}, "maxProfit": {"label": "Maximize Profit", "vendorLabel": "<PERSON><PERSON>or <PERSON>", "dataSource": "SP data + Ad data", "vendorDataSource": "Vendor data + Ads data", "details": "Use Organic data and Ad data altogether to maximize your total profit", "vendorDetails": "Use vendor data and Ads data altogether to maximize your objective", "option": "Minimize Inventory"}, "maxAdSales": {"label": "Maximize Ad Sales", "dataSource": "Ad data", "details": "Use Ad data to maximize your Ad Sales", "option": "Boost Impression"}, "maxRoas": {"label": "Maximize ROAS", "dataSource": "Ad data", "details": "Use Ad data to maximize your ROAS", "option": "Boost Impression"}}, "advancedOptions": {"label": "Advanced Options", "competitionTactic": {"label": "Competition Tactic", "option": {"none": "None", "brandDefense": "Brand Defense", "competitorConquesting": "Competitor Conquesting"}}, "salesTargeting": {"label": "Sales Targeting Option", "option": "Target Same SKU Only"}, "tooltip": {"competitionTactic": "Competition Tactic offers an advanced strategy that optimizes your ads based on your marketing objectives.<enter></enter><enter></enter>Brand Defense focuses on strengthening click share for brand keywords selected by LG Optapex's proprietary algorithm.<enter></enter><enter></enter>Competitor Conquesting focuses on increasing click share for key competitors identified by LG Optapex's proprietary algorithm."}}}, "adType": {"label": "Advertising Type", "subLabel": "Ad Coverage", "optAdType": {"displayN": {"label": "Sponsored Product", "target": "SP only", "details": "Optimize only Sponsored Product"}, "displayY": {"label": "Sponsored Product + Sponsored Display", "target": "SP + SD", "details": "Optimize both Sponsored Product and Sponsored Display"}}}, "scheduleBudget": {"label": "Schedule & Budget", "creationDateLabel": "Creation Date", "schedule": {"label": "Optimization schedule", "overview": {"monthlyInfinite": "Monthly recurring", "monthlyEnd": "Monthly recurring until {value}", "daterangeInfinite": "from {value}"}, "checkbox": {"daterange": {"label": "Date range", "details": "Set Budget for a specific period of time."}, "monthly": {"label": "Monthly recurring", "details": "Set budget that is automatically renewed at the beginning of each month.", "toggle": {"label": "Set an end date"}}}}, "budget": {"daterange": {"label": "Total Budget", "input": "Enter Total Budget"}, "monthly": {"label": "Monthly Budget", "input": "Enter Monthly Budget"}}, "limitCpc": {"label": "Smart CPC Guard", "input": "Enter Smart CPC Guard", "tooltip": "Smart CPC Guard controls CPC to stay close to your desired limit - without strict cutoffs - to allow smooth budget pacing."}}, "confirmModal": {"titleCreate": "Create Optimization Set", "titleEdit": "Edit Optimization Set", "descriptionCreate": "Are you sure you want to create this optimization set?", "descriptionEdit": "Are you sure you want to save the changes to this optimization set?", "cancel": "Cancel", "save": "Save"}, "closeConfirmModal": {"title": "Discard Unsaved Changes", "description": "You have unsaved changes. Are you sure you want to close without saving?", "cancel": "Cancel", "discard": "Discard Changes"}}}, "RealtimePage": {"title": {"todayOverview": "Today's Overview", "realTimePerformance": "Real-time Performance", "optsetMonitoring": "Optimization Set Monitoring"}, "filter": {"attributionWindow": {"label": "Attribution Window", "content": {"1d": "1 day", "7d": "7 days", "14d": "14 days", "30d": "30 days"}}, "optimizationSet": {"label": "Optimization Set", "content": {"optSet": "opt set", "null": "None"}}}, "keyMetrics": {"totalMetrics": {"totalSales": "Total Sales", "totalRevenue": "Revenue", "totalCogsRevenue": "Cogs Revenue", "estFee": "Estimated Fee", "estMargin": "Estimated <PERSON>"}, "adMetrics": {"adSales": "Ad Sales", "adSpend": "Ad Spend", "roas": "ROAS"}, "metricsTooltip": {"estimatedMargin": "(total sales - sales returned - estimated fee - ad cost) / total sales * 100", "vendorEstimatedMargin": "(cogs revenue - sales returned - estimated fee - ad cost) / cogs revenue * 100", "totalSales": "<first>${value} of sales returned is not accounted.</first>", "totalCogsRevenue": "<first>Cogs revenue is revenue from Amazon given to the vendor. {value} of shipped revenue is sum of actual prices sold to cutomers.</first>", "totalRevenue": "Revenue is the sum of prices customers actually paid for the products.", "estFee": "fba fee + referral fee + storage fee (according to the time span)", "roas": "(SP sales + SD sales) / (SP spend + SD spend) * 100"}}, "productShare": {"title": "Product Share", "searchBar": {"placeholder": "Search by Product Title or ASIN"}, "table": {"header": {"rank": "Rank", "productInfo": "Product", "share": "Share"}, "content": {"statusAlert": {"warning": "Warning", "abnormal": "Abnormal"}, "productInfo": {"asin": "ASIN", "sku": "SKU"}, "searchResultNull": "No search results found", "optSetNull": "No Optimization Sets Created"}}}, "graph": {"legendSelect": {"sales": "Total Sales", "adSales": "Ad Sales", "adSalesSameSku": "Ad Sales for Same SKU", "sdSalesPromotedClick": "SD Sales Promoted Clicks", "pageViews": "Pageviews", "clicks": "<PERSON>", "impressions": "Impressions", "sessions": "Sessions", "roas": "ROAS", "estimatedMargin": "Est. Profit", "comparedSales": "Total Sales (compared)", "comparedAdSales": "Ad Sales (compared)", "comparedAdSalesSameSku": "Ad Sales for Same SKU (compared)", "comparedSdSalesPromotedClick": "SD Sales Promoted Clicks (compared)", "comparedPageViews": "Pageviews (compared)", "comparedClicks": "<PERSON> (compared)", "comparedImpressions": "Impressions (compared)", "comparedSessions": "Sessions (compared)", "comparedRoas": "ROAS (compared)", "comparedEstimatedMargin": "Est. <PERSON>it (compared)", "revenue": "Revenue", "cogsRevenue": "Cogs Revenue", "adCost": "Ad Spend", "glanceViews": "Glance Views", "comparedRevenue": "Revenue (compared)", "comparedCogsRevenue": "Cogs Revenue (compared)", "comparedAdCost": "<PERSON>pend (compared)", "comparedGlanceViews": "Glance Views (compared)", "orderedUnits": "Ordered Units", "adOrders": "Ad Orders"}}}, "downloads": {"title": "Report Downloads", "topButton": {"addNew": "Add New", "remainingRequests": "Today Remaining Requests"}, "downloadList": {"header": {"status": "Status", "reportTitle": "Report Title", "reportType": "Report Type", "requestTime": "Request Time", "fileCreationTime": "File Creation Time", "download": "Download"}, "content": {"downloadRow": {"downloadNull": "No download result found"}}}, "addModal": {"topButton": {"request": "Request"}, "reportType": {"label": "Report Type", "subLabel": "Type", "options": {"sp": {"label": "Seller Central Report", "details": "Seller Central Report is a report that is generated from Seller Central. It includes all the data related to your products, including sales, inventory, and performance metrics."}, "vp": {"label": "Vendor Retail Analytics Report", "details": "Vendor Retail Analytics Report is a report that is generated from Vendor Retail Analytics. It includes all the data related to your products, including sales, inventory, and performance metrics."}, "ad": {"label": "Ads Console Report", "details": "Ads Console Report is a report that is generated from Ads Console. It includes all the data related to your advertising campaigns, including impressions, clicks, and sales."}, "optapex": {"label": "Optapex Report", "details": "Create and download reports only available in Optapex.", "featured": "Featured"}}}, "reportName": {"label": "Report Name", "category": {"subLabel": "Category"}, "name": {"subLabel": "Name"}}, "reportTitle": {"label": "Report Title", "subLabel": "Title"}, "advancedSetting": {"label": "Advanced Settings", "filter": {"dateAggregationFilter": {"label": "Date Aggregation Filter", "options": {"daily": "By Day", "weekly": "By Week", "monthly": "By Month"}}, "asinAggregationFilter": {"label": "ASIN Aggregation Filter", "options": {"parent": "By Parent ASIN", "child": "By Child ASIN", "sku": "By SKU"}}, "locationAggregationFilter": {"label": "Location Aggregation Filter", "options": {"country": "By Country", "fc": "By Fullfillment Center"}}, "eventTypeFilter": {"label": "Event Type Filter", "options": {"adjustments": "Adjustments", "customerReturns": "Customer Returns", "receipts": "Receipts", "shipments": "Shipments", "vendorReturns": "<PERSON><PERSON><PERSON> Returns", "whseTransfers": "Warehouse Transfers"}}, "timeUnitFilter": {"label": "Time Unit Filter", "options": {"daily": "Daily", "summary": "Summary"}}, "groupByFilter": {"label": "Group By Filter", "options": {"searchTerm": "Search Term", "targeting": "Targeting", "matchedTarget": "Matched Target", "advertiser": "Advertiser"}}, "distributorViewFilter": {"label": "Distributor View Filter", "options": {"vendor": "<PERSON><PERSON><PERSON>", "manufacturing": "Manufacturing", "sourcing": "Sourcing"}}, "sellingProgramFilter": {"label": "Selling Program Filter", "options": {"vendor": "<PERSON><PERSON><PERSON>", "retail": "Retail", "business": "Business", "fresh": "Fresh"}}, "reportPeriodFilter": {"label": "Report Period Filter", "options": {"day": "Day", "week": "Week", "month": "Month", "quarter": "Quarter", "year": "Year"}}}, "dateRestrictions": {"title": "Date Restrictions", "maxFromNow": "<PERSON> From Now", "maxRange": "Max Range"}}, "error": {"dateRangeRequired": "Date range is required", "startDateTooFar": "Start date is too far from today", "dateRangeTooWide": "Date range is too wide", "recent": "recent", "maximum": "maximum", "timeUnits": {"days": "days", "weeks": "weeks", "months": "months", "quarters": "quarters", "years": "years"}}}}}