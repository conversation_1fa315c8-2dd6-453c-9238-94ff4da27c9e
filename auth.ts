import NextAuth from "next-auth"
import Cognito from "next-auth/providers/cognito"
import type { NextAuthConfig, User } from "next-auth"
import { getServerApiHostUrl } from "./utils/host";
import { NextResponse } from "next/server";

declare module "next-auth" {
  interface User {
    sub?: string;
    access_token?: string;
    is_cypress_test?: boolean;
  }
}

export const config = {
  providers: [
    Cognito({
      clientId: process.env.COGNITO_CLIENT_ID,
      clientSecret: process.env.COGNITO_CLIENT_SECRET,
      issuer: process.env.COGNITO_ISSUER,
    })
  ],
  callbacks: {
    authorized: async ({ request, auth }) => {
      // For cypress test, bypass the authorization check.
      if (auth?.user?.is_cypress_test) {
        return true;
      }
      const { pathname } = request.nextUrl;
      if (pathname === "/dashboard" || pathname === "/auth/pw-expired" || pathname === "/auth/dormant" || pathname === "/auth/delete") { // authorized page
        const subInSession = auth?.user?.sub;
        const tokenInSession = auth?.user?.access_token;
        if (!subInSession || !tokenInSession) {
          return false;
        }
        const cognitoResponse = await fetch(
          `${await getServerApiHostUrl()}/api/cognito/user-attributes`,
          {
            method: "GET",
            headers: {
              "Authorization": `Bearer ${tokenInSession}`,
            },
            cache: "no-cache",
          }
        ).then((res) => res.json())
        
        console.log("@@@@@@")
        console.log(JSON.stringify(cognitoResponse))
        
        if (cognitoResponse?.sub) {
          // custom:optapex_status
          // `VERIFY_NEEDED`: 이메일 인증 필요
          // `INITIAL`: Amazon 계정 연동 필요
          // `NORMAL`: 정상 상태
          // `PW_EXPIRED`: 비밀번호 만료 (90일)
          // `DORMANT`: 휴면 상태 (90일 미접속)
          // `DELETE`: 탈퇴 상태
          // `LOCK`: 계정 잠금 상태
          if (cognitoResponse?.attributes && cognitoResponse?.attributes["custom:optapex_status"] === "PW_EXPIRED" && pathname !== "/auth/pw-expired") {
            return NextResponse.redirect(new URL("/auth/pw-expired", request.url));
          } else if (cognitoResponse?.attributes && cognitoResponse?.attributes["custom:optapex_status"] === "DORMANT" && pathname !== "/auth/dormant") {
            return NextResponse.redirect(new URL("/auth/dormant", request.url));
          } else if (cognitoResponse?.attributes && cognitoResponse?.attributes["custom:optapex_status"] === "DELETE" && pathname !== "/auth/delete") {
            return NextResponse.redirect(new URL("/auth/delete", request.url));
          }
          return true;
        } else {
          return NextResponse.redirect(new URL("/auth/logout", request.url));
        }
      }
      return true;
    },
    jwt({ token, account }) {
      if (account) {
          return {
            ...token,
            access_token: account.access_token,
            expires_at: account.expires_at,
            refresh_token: account.refresh_token,
          };
      }
      return token;
    },
    redirect: async ({ url, baseUrl }) => {
      const redirectUrl = new URL(url, baseUrl);
      const lang = redirectUrl.searchParams.get('lang');

      if (lang === 'zh-CN') {
        redirectUrl.searchParams.set('lang', 'cn');
      }

      return redirectUrl.toString();
    },
    // @ts-ignore
    async session({ session, token }) {
      // token에 sub, exp 등의 정보가 담겨있기 때문에 session에 추가해준다.
      if (session?.user && token) {
        return { ...session, user: { ...session.user, ...token } };
      }

      return session;
    },
  },
  pages: {
    signIn: "/auth/login",
    error: "/auth/error", // Custom error page
  }
} satisfies NextAuthConfig

export const { handlers, auth, signIn, signOut } = NextAuth(config)
