"use client"

import Plot from "react-plotly.js"
import { formatDate, getDateDifference } from "@/utils/msc"
import { useEffect, useMemo, useState } from "react"

type GraphData = {
  date: string;
  value: number;
}

type PredictionData = {
  budget_usage: any;
  inventory: any;
}

export default function DetailPaceGraph({ data, endDate, totalBudget, prediction, show }: { data: GraphData[], endDate: Date, totalBudget: number, prediction: PredictionData, show: boolean }) {
  const lastDate = new Date(data[data.length - 1].date)
  const lastSumValue =  data.reduce((acc, cur) => acc + cur.value, 0)
  const initialDataset: any[] = useMemo(() => {
    const dataset = []
    dataset.push({
      x: data.map((d) => new Date(d.date)),
      y: data.map((d) => data[0].value),
      customdata: data.map((d) => formatDate(d.date, '.')),
      type: 'scatter',
      mode: 'lines',
      line: { shape: 'spline', smoothing: 1.3 },
      xperiodalignment: 'start',
      fill: 'tozeroy',
      fillgradient: {
        colorscale: [
          [0, 'rgba(59, 130, 246, 0.05)'],
          [1, 'rgba(59, 130, 246, 0.4)'],
        ],
        type: 'vertical',
      },
      marker: {
        color: '#3B82F6',
      },
      name: 'Budget Usage',
    })

    dataset.push({
      // from lastDate to endDate
      x: [lastDate, endDate],
      y: [data[0].value, data[0].value],
      customdata: data.map((d) => formatDate(d.date, '.')),
      type: 'scatter',
      mode: 'lines',
      line: { shape: 'spline', smoothing: 1.3, dash: 'dot' },
      xperiodalignment: 'start',
      fill: 'tozeroy',
      fillgradient: {
        colorscale: [
          [0, 'rgba(234, 179, 8, 0.05)'],
          [1, 'rgba(234, 179, 8, 0.4)'],
        ],
        type: 'vertical',
      },
      marker: {
        color: '#eab308',
      },
      name: 'Expected Budget Usage',
    })
    return dataset
  }, [data])

  const changedDataset = useMemo(() => {
    const dataset = []
    dataset.push({
      x: data.map((d) => new Date(d.date)),
      y: data.map((d) => data.slice(0, data.indexOf(d) + 1).reduce((acc, cur) => acc + cur.value, 0)),
      customdata: data.map((d) => formatDate(d.date, '.')),
      type: 'scatter',
      mode: 'lines',
      line: { shape: 'spline', smoothing: 1.3 },
      xperiodalignment: 'start',
      fill: 'tozeroy',
      fillgradient: {
        colorscale: [
          [0, 'rgba(59, 130, 246, 0.05)'],
          [1, 'rgba(59, 130, 246, 0.4)'],
        ],
        type: 'vertical',
      },
      marker: {
        color: '#3B82F6',
      },
      name: 'Budget',
    })

    dataset.push({
      // from lastDate to endDate
      x: [lastDate, endDate],
      y: [lastSumValue, lastSumValue + prediction.budget_usage.TARGET * getDateDifference(lastDate, new Date(endDate))],
      type: 'scatter',
      mode: 'lines',
      line: { shape: 'spline', smoothing: 1.3, dash: 'dot' },
      xperiodalignment: 'start',
      fill: 'tozeroy',
      fillgradient: {
        colorscale: [
          [0, 'rgba(234, 179, 8, 0.05)'],
          [1, 'rgba(234, 179, 8, 0.4)'],
        ],
        type: 'vertical',
      },
      marker: {
        color: '#eab308',
      },
      name: 'Expected Budget',
    })

    dataset.push({
      x: [lastDate],
      y: [lastSumValue],
      type: 'scatter',
      mode: 'markers',
      xperiodalignment: 'start',
      marker: {
        size: 8,
        color: '#3B82F6',
      },
      name: 'Now',
    })

    dataset.push({
      x: [endDate],
      y: [lastSumValue + prediction.budget_usage.TARGET * getDateDifference(lastDate, new Date(endDate))],
      type: 'scatter',
      mode: 'markers',
      xperiodalignment: 'start',
      marker: {
        size: 8,
        color: '#eab308',
      },
      name: 'Final Prediction',
    })

    return dataset
  }, [data])

  const dataRange = useMemo(() => {
    return [
      data[0].value,
      totalBudget
    ]
  }, [data])

	const layout = {
		transition: {
			duration: 2000,
			easing: 'cubic-in-out',
		},
		margin: {
			l: 25,
			r: 25,
			b: 40,
			t: 25,
			pad: 0
		},
		paper_bgcolor: 'rgba(0,0,0,0)',
		plot_bgcolor: 'rgba(0,0,0,0)',
		autosize: true,
		showlegend: false,
		xaxis: {
			tickfont: {
				size: 10,
				color: '#4b5563'
			},
			tickformat: '%y.%m.%d',
			ticks: 'outside',
			tickcolor: 'rgba(0,0,0,0)',
			gridcolor: '#374151',
			zerolinecolor: '#374151',
			tickmode: 'auto',
			nticks: data.length > 10 ? 10 : data.length,
      range: [data[0].date, endDate],
		},
		yaxis: {
			tickformat: '$,',
			tickfont: {
				size: 10,
				color: '#4b5563'
			},
			ticks: 'outside',
			tickcolor: 'rgba(0,0,0,0)',
			gridcolor: '#374151',
			zerolinecolor: '#374151',
			automargin: true,
      range: dataRange,
		},
		hovermode: 'x unified',
		hoverlabel: {
			bgcolor: 'rgba(17, 24, 39, 0.9)',
			font: {
				size: 10,
				color: '#e5e7eb'
			},
		},
		dragmode: false,
	}
  const [graphInfo, setGraphInfo] = useState<any>({
    dataset: initialDataset,
    layout: layout,
  })
	useEffect(() => {
		if (!show) {
      setTimeout(() => {
        setGraphInfo({
          dataset: initialDataset,
          layout: {
            ...layout,
            transition: {
              duration: 100,
              easing: 'cubic-in-out',
            },
          },
        })
      }, 100)
		} else {
      setTimeout(() => {
        setGraphInfo({
          dataset: changedDataset,
          layout: {
            ...layout,
          }
        })
      }, 100)
    }
	}, [show])
  return (
    <Plot
      data={graphInfo.dataset}
      layout={graphInfo.layout}
      config={{
        displayModeBar: false,
      }}
      useResizeHandler={true}
      className="w-full h-full"
    />
  )
}
