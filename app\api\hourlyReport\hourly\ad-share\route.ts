import { ErrorResponse } from "@/app/api/_models";
import { getServerApiHostUrl } from "@/utils/host";
import { NextRequest, NextResponse } from "next/server";

export type HourlyAdShareResponse = any;

export async function GET(
  request: NextRequest
): Promise<NextResponse<HourlyAdShareResponse | ErrorResponse>> {
  const accessToken = request.headers.get("Authorization");
  if (!accessToken) {
    return NextResponse.json(
      { message: "Authorization header is missing" },
      { status: 400 }
    );
  }

  // Required parameters
  const targetMetric = request.nextUrl.searchParams.get("target_metric")
  if (!targetMetric) {
    return NextResponse.json(
      { message: "target_metric query is missing" },
      { status: 400 }
    );
  }

  const accountId = request.nextUrl.searchParams.get("account_id")
  if (!accountId) {
    return NextResponse.json(
      { message: "account_id query is missing" },
      { status: 400 }
    );
  }

  const marketplaceId = request.nextUrl.searchParams.get("marketplace_id")
  if (!marketplaceId) {
    return NextResponse.json(
      { message: "marketplace_id query is missing" },
      { status: 400 }
    );
  }

  // Optional parameters
  const optimizationId = request.nextUrl.searchParams.get("optimization_id")
  const attributionWindow = request.nextUrl.searchParams.get("attribution_window") || "7"

  // Build URL with parameters
  let apiUrl = `${await getServerApiHostUrl()}/api/hourly_report/hourly/ad-share?target_metric=${targetMetric}&account_id=${accountId}&marketplace_id=${marketplaceId}&attribution_window=${attributionWindow}`
  
  if (optimizationId) {
    apiUrl += `&optimization_id=${optimizationId}`
  }

  try {
    const adShareResponse = await fetch(apiUrl, {
      method: "GET",
      headers: {
        "Authorization": `Bearer ${accessToken}`,
        "Content-Type": "application/json",
      },
      cache: "force-cache",
      next: { revalidate: 300 }, // 5 minutes cache
    }).then((res) => res.json());

    return NextResponse.json(adShareResponse, { status: 200 });
  } catch (error) {
    console.error('Error fetching ad share data:', error);
    return NextResponse.json(
      { message: "Failed to fetch ad share data" },
      { status: 500 }
    );
  }
} 