import { defineConfig } from 'cypress'

export default defineConfig({
  screenshotOnRunFailure: true,
  screenshotsFolder: 'tests/module/screenshots',
  videosFolder: 'tests/module/videos',
  fixturesFolder: 'cypress/fixtures',
  numTestsKeptInMemory: 10,
  video: false,
  viewportWidth: 1920,
  viewportHeight: 1080,
  requestTimeout: 10000,
  chromeWebSecurity: false,
  e2e: {
    // We've imported your old cypress plugins here.
    // You may want to clean this up later by importing these.
    setupNodeEvents(on, config) {
      return require('./tests/plugins')(on, config)
    },
    supportFile: 'tests/support',
    specPattern: 'tests/module/specs//**/*.cy.{js,jsx,ts,tsx}',
  },
})
