import { ErrorResponse } from "@/app/api/_models";
import { getServerApiHostUrl } from "@/utils/host";
import { NextRequest, NextResponse } from "next/server";

export type AvailableProfilesResponse = any;

export async function GET(
  request: NextRequest
): Promise<NextResponse<AvailableProfilesResponse | ErrorResponse>> {
  const accessToken = request.headers.get("Authorization");
  if (!accessToken) {
    return NextResponse.json(
      { message: "Authorization header is missing" },
      { status: 400 }
    );
  }

  const availableProfilesResponse = await fetch(
    `${await getServerApiHostUrl()}/api/ex/oauth/available_profiles`,
    {
      method: "GET",
      headers: {
        "Authorization": `Bearer ${accessToken}`,
      },
      cache: "no-cache",
    }
  ).then((res) => res.json());

  return NextResponse.json(availableProfilesResponse, { status: 200 });
}
