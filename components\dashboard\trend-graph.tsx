"use client"

import Plot from "react-plotly.js"
import { formatDate, getDateDifference } from "@/utils/msc"
import { useEffect, useMemo, useState } from "react"

type GraphData = {
  date: string;
  value: number;
}

export default function TrendGraph({ data }: { data: GraphData[] }) {
  const initialDataset = useMemo(() => {
    const dataset = []
    dataset.push({
      x: data.map((d) => new Date(d.date)),
      y: data.map((d) => d.value),
      customdata: data.map((d) => formatDate(d.date, '.')),
      type: 'scatter',
      mode: 'lines',
      line: { shape: 'spline', smoothing: 1.3 },
      xperiodalignment: 'start',
      fill: 'tozeroy',
      fillgradient: {
        colorscale: [
          [0, 'rgba(234, 179, 8, 0)'],
          [1, 'rgba(234, 179, 8, 0.4)'],
        ],
        type: 'vertical',
      },
      marker: {
        color: '#eab308',
      },
      name: 'trend line',
    })

    return dataset
  }, [data])

  const layout = {
    transition: {
      duration: 2000,
      easing: 'cubic-in-out',
    },
    margin: {
      l: 0,
      r: 0,
      b: 0,
      t: 0,
      pad: 0
    },
    paper_bgcolor: 'rgba(0,0,0,0)',
    plot_bgcolor: 'rgba(0,0,0,0)',
    autosize: true,
    showlegend: false,
    xaxis: {
      visible: false,
    },
    yaxis: {
      visible: false,
    },
    hovermode: false,
    hoverlabel: {
      bgcolor: 'rgba(17, 24, 39, 0.9)',
      font: {
        size: 10,
        color: '#e5e7eb'
      },
    },
    dragmode: false,
  }
  const [graphInfo, setGraphInfo] = useState<any>({
    dataset: initialDataset,
    layout: layout,
  })
  // useEffect(() => {
  //   setTimeout(() => {
  //     setGraphInfo({
  //       dataset: changedDataset,
  //       layout: {
  //         ...layout,
  //       }
  //     })
  //   }, 100)
  // }, [])
  return (
    <Plot
      data={graphInfo.dataset}
      layout={graphInfo.layout}
      config={{
        displayModeBar: false,
      }}
      useResizeHandler={true}
      className="w-full h-full"
    />
  )
}
