import { ErrorResponse } from '@/app/api/_models';
import { getServerApiHostUrl } from '@/utils/host';
import { NextRequest, NextResponse } from 'next/server';

export type ChangeStatusTargetResponse = {
  success: boolean;
};

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
): Promise<NextResponse<ChangeStatusTargetResponse | ErrorResponse>> {
  const accessToken = request.headers.get('Authorization');

  if (!accessToken) {
    return NextResponse.json(
      { message: 'Authorization header is missing' },
      { status: 400 }
    );
  }
  const { id } = params;
  const body = await request.json();

  const changeStatusOfTargetResponse = await fetch(
    `${await getServerApiHostUrl()}/api/optimization/targets/${id}`,
    {
      method: 'PUT',
      headers: {
        Authorization: `Bearer ${accessToken}`,
        'Content-Type': 'application/json',
      },
      cache: 'no-cache',
      body: JSON.stringify({ status: body?.status }),
    }
  ).then((res) => res.json());

  return NextResponse.json(changeStatusOfTargetResponse, { status: 200 });
}
