import { NextRequest, NextResponse } from "next/server";
import { encode } from '@auth/core/jwt';
import Crypto from 'crypto';

const ALLOWED_COOKIE_SIZE = 4096;
const ESTIMATED_EMPTY_COOKIE_SIZE = 160;
const CHUNK_SIZE = ALLOWED_COOKIE_SIZE - ESTIMATED_EMPTY_COOKIE_SIZE;

export async function POST(request: NextRequest) {
    const body = await request.json();
    const authSecret = process.env.AUTH_SECRET ?? '';

    if (!body.token) {
        return NextResponse.json({ message: "Invalid request" }, { status: 400 });
    }

    const csrfCookieName = process.env.NODE_ENV !== 'development' ? '__Secure-authjs.csrf-token' : 'authjs.csrf-token';
    const csrfToken = Crypto.randomBytes(32).toString("hex");
    const csrfTokenHash = Crypto.createHash('sha256').update(csrfToken + authSecret).digest('hex');

    const tokenToEncode = {
        ...body.token,
        [csrfCookieName]: csrfTokenHash
    };

    const salt = process.env.NODE_ENV !== 'development' ? '__Secure-authjs.session-token' : 'authjs.session-token';
    const newToken = await encode({
        token : tokenToEncode,
        secret : authSecret,
        salt : salt
    });

    const res = NextResponse.json({ message: "Session created" });

    res.cookies.set(csrfCookieName, csrfToken, {
        httpOnly: true,
        path: '/',
        sameSite: 'lax',
        secure: process.env.NODE_ENV !== 'development',
    });

    const chunkCount = Math.ceil(newToken.length / CHUNK_SIZE);
    if (chunkCount === 1) {
        res.cookies.set(salt, newToken, {
            httpOnly: true,
            path: '/',
            sameSite: 'lax',
            secure: process.env.NODE_ENV !== 'development',
            expires: new Date(Date.now() + 60 * 60 * 1000)
        });
    } else {
        for (let i = 0; i < chunkCount; i++) {
            const name = `${salt}.${i}`;
            const value = newToken.substring(i * CHUNK_SIZE, (i+1) * CHUNK_SIZE);
            res.cookies.set(name, value, {
                httpOnly: true,
                path: '/',
                sameSite: 'lax',
                secure: process.env.NODE_ENV !== 'development',
                expires: new Date(Date.now() + 60 * 60 * 1000)
            });
        }
    }

    return res;
}