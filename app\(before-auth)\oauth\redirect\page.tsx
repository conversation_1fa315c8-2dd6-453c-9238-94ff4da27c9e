"use client";

import { api } from "@/utils/api";
import { useSearchParams } from "next/navigation";
import { useEffect, useState } from "react";

export default function OAuthRedirect() {
  const searchParams = useSearchParams();
  const [token, setToken] = useState<any>(null);
  const [userProfile, setUserProfile] = useState<any>(null);

  useEffect(() => {
    const fetchData = async () => {
      const code = searchParams.get("code");

      if (!code) {
        return;
      }

      const tokenData = await api.getTokenData(code);

      if (!tokenData?.access_token || !tokenData?.refresh_token) {
        return;
      }
      setToken(tokenData);

      const profile = await api.getProfileData(tokenData.access_token);

      if (profile) {
        setUserProfile(profile);
      }
    };

    fetchData();
  }, [searchParams]);

  return (
    <div className="h-screen flex flex-col items-center justify-center gap-4">
      {token && (
        <div className="w-2/3 text-sm grid grid-cols-2 font-semibold break-keep">
          <div>Access Token</div>
          <div>{token.access_token}</div>
          <div>Refresh Token</div>
          <div>{token.refresh_token}</div>
          <div>Expires In</div>
          <div>{token.expires_in}</div>
        </div>
      )}

      {userProfile && (
        <div className="w-2/3 text-xl grid grid-cols-2 font-semibold">
          <div>Account ID</div>
          <div>{userProfile.accountInfo.id}</div>
          <div>Account Name</div>
          <div>{userProfile.accountInfo.name}</div>
          <div>Account Type</div>
          <div>{userProfile.accountInfo.type}</div>
          <div>Profile ID</div>
          <div>{userProfile.profileId}</div>
          <div>Country Code</div>
          <div>{userProfile.countryCode}</div>
          <div>Currency Code</div>
          <div>{userProfile.currencyCode}</div>
        </div>
      )}
    </div>
  );
}
