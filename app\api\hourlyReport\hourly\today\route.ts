import { ErrorResponse } from "@/app/api/_models";
import { getServerApiHostUrl } from "@/utils/host";
import { NextRequest, NextResponse } from "next/server";

export type HourlyReportTodayResponse = {
  sales: Record<string, number>;
  ordered_units: Record<string, number>;
  glance_views: Record<string, number>;
  ad_spend: Record<string, number>;
  ad_impressions: Record<string, number>;
  ad_clicks: Record<string, number>;
  ad_orders: Record<string, number>;
  ad_sales: Record<string, number>;
  ad_sales_same_sku: Record<string, number>;
};

export async function GET(
  request: NextRequest
): Promise<NextResponse<HourlyReportTodayResponse | ErrorResponse>> {
  const accessToken = request.headers.get("Authorization");
  if (!accessToken) {
    return NextResponse.json(
      { message: "Authorization header is missing" },
      { status: 400 }
    );
  }

  const accountId = request.nextUrl.searchParams.get("account_id");
  if (!accountId) {
    return NextResponse.json(
      { message: "account_id query is missing" },
      { status: 400 }
    );
  }

  const marketplaceId = request.nextUrl.searchParams.get("marketplace_id");
  if (!marketplaceId) {
    return NextResponse.json(
      { message: "marketplace_id query is missing" },
      { status: 400 }
    );
  }

  const optimizationId = request.nextUrl.searchParams.get("optimization_id");
  const attributionWindow =
    request.nextUrl.searchParams.get("attribution_window") || "7";

  let apiUrl = `${await getServerApiHostUrl()}/api/hourly_report/hourly/today?account_id=${accountId}&marketplace_id=${marketplaceId}&attribution_window=${attributionWindow}`;

  if (optimizationId) {
    apiUrl += `&optimization_id=${optimizationId}`;
  }

  const hourlyReportResponse = await fetch(apiUrl, {
    method: "GET",
    headers: {
      Authorization: `Bearer ${accessToken}`,
      "Content-Type": "application/json",
    },
    cache: "no-store",
  }).then((res) => res.json());

  return NextResponse.json(hourlyReportResponse, { status: 200 });
} 