"use client"

import { cn } from "@/utils/msc"
import { useTranslations } from "next-intl"


interface DownloadStatusProps extends React.HTMLAttributes<HTMLDivElement> {
    downloadItem: any;
}
export default function DownloadStatus({
  downloadItem
}: DownloadStatusProps) {
  const t = useTranslations('component')    
 
  const isExpired = () => {
    if (!downloadItem.presigned_url_expired_at) return false;
    
    const expiredAt = new Date(downloadItem.presigned_url_expired_at);
    const now = new Date();
    
    return now > expiredAt;
  };
  
  
  const currentStatus = () => {
    if (downloadItem.status === "DONE" && isExpired()) {
      return "EXPIRED";
    }
    return downloadItem.status;
  };
  
  const status = currentStatus();
  
  return (
    <div className="flex items-center justify-center gap-x-2 text-gray-500 text-sm">
      <span className="relative flex h-2 w-2">
        <span className={cn(
          "absolute inline-flex h-full w-full rounded-full opacity-75",
          ["ERROR","FATAL","RETRY_REQUIRED", "CANCELED"].includes(status)
            ? "bg-red-500"            
            : status === "DONE"
              ? "bg-blue-500"
              : ["CREATED","PENDING","IN_QUEUE","IN_PROGRESS","DONE_REQUESTED"].includes(status)
                ? "animate-ping bg-green-500"
                : "bg-gray-300"
        )}></span>
        <span className={cn(
          "relative inline-flex rounded-full h-2 w-2",
          ["ERROR","RETRY_REQUIRED","FATAL","CANCELED"].includes(status)
            ? "bg-red-500"           
            : status === "DONE"
              ? "bg-blue-500"
            : ["CREATED","PENDING","IN_QUEUE","IN_PROGRESS","DONE_REQUESTED"].includes(status)
                ? "bg-green-500"
                : "bg-gray-300"
        )}></span>
      </span>
      {["ERROR","RETRY_REQUIRED","FATAL"].includes(status)
        ? t("downloadStatus.error")
        : status === "EXPIRED"
          ? t("downloadStatus.expired")
        : status === "DONE"
          ? t("downloadStatus.created")
          : ["CREATED","PENDING","IN_QUEUE","IN_PROGRESS","DONE_REQUESTED"].includes(status)
            ? t("downloadStatus.creating")
            : status
      }
    </div>
  )
}
