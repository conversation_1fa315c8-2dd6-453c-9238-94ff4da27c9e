"use client"

import { Fragment, useState } from "react"
import { cn, hexToComplimentary, hexToRgbA, stringToColor } from "@/utils/msc"
import { Listbox, Transition } from "@headlessui/react"
import { ChevronUpDownIcon, GlobeAmericasIcon, UsersIcon } from "@heroicons/react/20/solid"
import { Check } from "@/components/ui/check"
import { MarketplaceOption } from "@/components/dashboard/marketplace-select"
import { useTranslations } from "next-intl"

export type ProfileOption = {
  id: string;
  status: string;
  account_id: string;
  account_name: string;
  account_type: string;
  creation_datetime: string;
  deleted_datetime?: string;
  last_update_datetime?: string;
  lwa_user: any;
  marketplaces: MarketplaceOption[];
  region: string;
  selling_partner_id: string;
  validation_yn: string;
}
interface ProfileSelectProps extends React.HTMLAttributes<HTMLDivElement> {
    listboxClassName?: string;
    profileOptions: ProfileOption[];
    isLNBExpanded: boolean;
    selected: ProfileOption;
    setSelected: (option: ProfileOption | null) => void
}
export default function ProfileSelect({
    className,
    listboxClassName,
    profileOptions,
    isLNBExpanded,
    selected,
    setSelected,
    ...props
}: ProfileSelectProps) {
  const t = useTranslations('component')
  return (
    <div
      className={cn(
        "relative",
        className
      )}
      {...props}
    >
      <Listbox value={selected} onChange={setSelected}>
        {({ open }) => (
        <div className="relative">
          <Listbox.Button
            className={cn(
              "group relative w-full h-9 py-2 pl-3 pr-6 cursor-pointer rounded-lg text-left focus:outline-none text-sm border border-gray-200 hover:border-gray-300",
              open
                ? "bg-gray-100"
                : ""
            )}
          >
            <span className="block truncate text-gray-500 group-hover:text-gray-600">
              { isLNBExpanded
                ? selected.account_name || selected.account_id
                : ""
              }
            </span>
            <span
              className={cn(
                "pointer-events-none absolute inset-y-0 flex items-center px-2",
                isLNBExpanded ? "right-0" : "left-0"
              )}
            >
              { isLNBExpanded
                ? (
                  <ChevronUpDownIcon
                    className="h-5 w-5 text-gray-300 group-hover:text-gray-400"
                    aria-hidden="true"
                  />
                )
                : (
                  <UsersIcon
                    className="h-5 w-5 text-gray-500 group-hover:text-gray-600"
                    aria-hidden="true"
                  />
                )
              }
            </span>
          </Listbox.Button>
          <Transition
            as={Fragment}
            leave="transition ease-in duration-100"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
            <Listbox.Options
              className={cn(
                listboxClassName
              )}
              >
              {profileOptions.map((option, optionIdx) => (
                <Listbox.Option
                  key={optionIdx}
                  className={({ active }) =>
                    `group relative cursor-pointer select-none py-2 pl-4 pr-10 ${
                      active ? 'bg-blue-100/40 text-blue-900' : 'text-gray-600'
                    }`
                  }
                  value={option}
                >
                  {() => (
                    <>
                      <div>
                        <div className="py-3 px-2">
                          <div className="flex items-center gap-x-1">
                            <div
                                className={`inline-flex items-center gap-x-2 text-base truncate ${
                                selected.id === option.id ? 'font-medium' : 'font-normal'
                            }`}>
                              {option.account_type === "vendor" && (
                              <div className="border border-blue-600 bg-blue-100/40 text-blue-800 text-xs font-semibold px-2 py-1 rounded-md">
                                Vendor
                              </div>
                              )}
                              <span className="truncate">{option.account_name || option.account_id}</span>
                            </div>
                            <div className="flex-shrink-0 flex items-center gap-x-0.5 text-gray-300 group-hover:text-gray-400">
                              <div>·</div>
                              <GlobeAmericasIcon className="flex-shrink-0 h-3 w-3"/>
                              <div className="text-[10px]">
                                { option.lwa_user.region === "FE"
                                  ? "Far East"
                                  : option.lwa_user.region === "NA"
                                    ? "North America"
                                    : option.lwa_user.region
                                }
                              </div>
                            </div>
                          </div>
                          {option.lwa_user && (
                            <>
                              <div className="mt-2 flex items-center gap-x-2 text-xs text-gray-500">
                                <div
                                  className="w-[28px] h-[28px] rounded-full flex items-center justify-center border border-gray-100 text-[10px] text-gray-500 font-bold"
                                  style={{
                                    backgroundColor: `${hexToRgbA(stringToColor(option.lwa_user.profile_user_id), 0.1 )}`,
                                    // color: `${hexToComplimentary(stringToColor(option.lwa_user.profile_user_id))}`,
                                  }}
                                >
                                  {option.lwa_user.profile_name.charAt(0).toUpperCase()}
                                </div>
                                <div className="">
                                  <div className="text-xs text-gray-500">{option.lwa_user.profile_name}</div>
                                  <div className="text-[10px] text-gray-400">{option.lwa_user.profile_email}</div>
                                </div>
                              </div>
                              {/* <div className="mt-1 text-[10px] text-gray-300">{t("lnb.profile.profileId")} : {option.lwa_user.profile_user_id}</div> */}
                            </>
                          )}
                        </div>
                      </div>
                      {selected.id === option.id ? (
                        <div className="absolute inset-y-0 right-0 flex items-center pr-5">
                          <div className="shrink-0 flex items-center justify-center w-5 h-5 rounded-full bg-blue-400">
                            <Check className="w-4 h-4 stroke-white" aria-hidden="true" />
                          </div>
                        </div>
                      ) : null}
                    </>
                  )}
                </Listbox.Option>
              ))}
            </Listbox.Options>
          </Transition>
        </div>
        )}
      </Listbox>
    </div>
  )
}
