// ***********************************************************
// This example support/index.js is processed and
// loaded automatically before your test files.
//
// This is a great place to put global configuration and
// behavior that modifies Cypress.
//
// You can change the location of this file or turn off
// automatically serving support files with the
// 'supportFile' configuration option.
//
// You can read more here:
// https://on.cypress.io/configuration
// ***********************************************************

// Import commands.js using ES2015 syntax:
import './commands'

// Alternatively you can use CommonJS syntax:
// require('./commands')


Cypress.on('uncaught:exception', (err, runnable) => {
    return false;
});

it.guide = function (
    title,
    {
        mockFunc: mockFunction,
        actionFunc: actionFunction,
        waitFunc: waitFunction,
        assertFunc: assertFunction,
        afterFunc: afterFunction,
    }
) {
    it(title, () => {
        if (!actionFunction) expect('NO_ACTION_FUNCTION_ERROR').equal();
        if (!assertFunction) expect('NO_ASSERTION_FUNCTION_ERROR').equal();
        if (mockFunction) mockFunction();
        actionFunction();
        if (waitFunction) waitFunction();
        assertFunction();
        if (afterFunction) afterFunction();
    });
};

xit.guide = function (
    title,
    { mockFunc: mockFunction, actionFunc: actionFunction, waitFunc: waitFunction, assertFunc: assertFunction }
) {
    xit(title, () => {
        if (!actionFunction) expect('NO_ACTION_FUNCTION_ERROR').equal();
        if (!assertFunction) expect('NO_ASSERTION_FUNCTION_ERROR').equal();
        if (mockFunction) mockFunction();
        actionFunction();
        if (waitFunction) waitFunction();
        assertFunction();
        if (afterFunction) afterFunction();
    });
};