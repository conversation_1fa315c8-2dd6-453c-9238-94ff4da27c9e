"use client"

import { useTranslations } from 'next-intl'
import { Fragment, useEffect, useState, useMemo } from 'react'
import { Dialog, DialogPanel, Listbox, Popover, PopoverButton, PopoverPanel, Transition, TransitionChild } from '@headlessui/react'
import { cn } from "@/utils/msc"
import { useCurrencyFormatter, getCurrencyCodeFromMarketplace } from "@/utils/currency"
import { ChevronUpDownIcon, ExclamationTriangleIcon, QuestionMarkCircleIcon, XMarkIcon } from "@heroicons/react/20/solid"
import { Check } from "@/components/ui/check"
import dynamic from "next/dynamic"
import { use } from 'chai'

const BudgetSensitivityGraph = dynamic(() => import('@/components/dashboard/budget-sensitivity-graph'), { ssr: false })

interface BudgetSensitivityOptimizeSliderProps {
  handleCloseClick: () => void;
  optimizationBudgetSensitivityDetail?: any;
  selectedPortfolioItem?: any;
}

export default function BudgetSensitivityOptimizeSlider({
  handleCloseClick,
  optimizationBudgetSensitivityDetail,
  selectedPortfolioItem
}: BudgetSensitivityOptimizeSliderProps) {
  const t = useTranslations('component')
  const tos = useTranslations('optimizationSets')
  const { formatCurrency } = useCurrencyFormatter()
  const currencyCode = useMemo(() => {
    // selectedPortfolioItem의 marketplace_id를 사용하여 통화 코드 결정
    if (selectedPortfolioItem?.marketplace_id) {
      const mockMarketplace = { marketplace_id: selectedPortfolioItem.marketplace_id }
      return getCurrencyCodeFromMarketplace(mockMarketplace)
    }
    return 'USD' // 기본값
  }, [selectedPortfolioItem?.marketplace_id])
  interface BudgetSensitivityDataItem {
    budget_ratio: number;
    name?: string;
    metrics?: {
      total_profit?: number;
      total_sales?: number;
      total_cost?: number;
      ad_spending?: number;
      ad_sales?: number;
      ad_sales_same_sku?: number;
    };
    optimization_date?: string;
    [key: string]: any;
  }
  type BudgetOptTargetOption = {
    name: string;
    type: string;
  }
  const budgetOptTargetOptions: BudgetOptTargetOption[] = [
    {
      name: 'None',
      type: 'none'
    },
    {
      name: 'Total Sales',
      type: 'total-sales'
    },
    {
      name: 'Total Profit',
      type: 'total-profit'
    },
    {
      name: 'Ad Sales',
      type: 'ad-sales'
    },
    {
      name: 'Ad Sales Same SKU',
      type: 'ad-sales-same-sku'
    }
  ]
  const findOptimizedBudgetRatio = () => {
    if (optimizationBudgetSensitivityDetail?.data?.length > 0 && selectedBudgetOptTarget) {
      const metricType = selectedBudgetOptTarget.type;
      // Determine if we are maximizing or minimizing
      const maximizeTypes = [
        "total-sales",
        "total-profit",
        "ad-sales",
        "ad-sales-same-sku"
      ];
      const minimizeTypes = [
        "total-cost",
        "ad-spending",
      ];
      let targetValue: number;
      if (metricType === "none") {
        const baseItem = optimizationBudgetSensitivityDetail.data.find((item: any) => Number(item.budget_ratio) === 1);
        if (baseItem) {
          setOptimizedBudgetRatio(baseItem);
          setSelectedBudgetRatio(baseItem);
        }
        return;
      }
      if (maximizeTypes.includes(metricType)) {
        targetValue = Math.max(
          ...optimizationBudgetSensitivityDetail.data.map(
            (item: any) => item.metrics?.[metricType.replace(/-/g, "_")] ?? -Infinity
          )
        );
      } else if (minimizeTypes.includes(metricType)) {
        targetValue = Math.min(
          ...optimizationBudgetSensitivityDetail.data.map(
            (item: any) => item.metrics?.[metricType.replace(/-/g, "_")] ?? Infinity
          )
        );
      }
      // Filter all items with the target value
      const filteredItems = optimizationBudgetSensitivityDetail.data.filter(
        (item: any) =>
          (item.metrics?.[metricType.replace(/-/g, "_")] ?? null) === targetValue
      );
      // Pick the one with the lowest budget_ratio
      const minBudgetRatioItem = filteredItems.reduce((prev: any, curr: any) =>
        Number(curr.budget_ratio) < Number(prev.budget_ratio) ? curr : prev,
        filteredItems[0]
      );
      setOptimizedBudgetRatio(minBudgetRatioItem);
      setSelectedBudgetRatio(minBudgetRatioItem);
    }
  }
  const [optimizedBudgetRatio, setOptimizedBudgetRatio] = useState<BudgetSensitivityDataItem | null>(null)
  const [selectedBudgetRatio, setSelectedBudgetRatio] = useState<BudgetSensitivityDataItem | null>(null)
  const [selectedBudgetOptTarget, setSelectedBudgetOptTarget] = useState<BudgetOptTargetOption>(budgetOptTargetOptions[0])
  const [optimized, setOptimized] = useState<boolean>(false)

  useEffect(() => {
    if (optimizationBudgetSensitivityDetail?.data && optimizationBudgetSensitivityDetail.data.length > 0) {
      const defaultRatio = optimizationBudgetSensitivityDetail.data.find((item: any) => Number(item.budget_ratio) === 1)
      setSelectedBudgetRatio(defaultRatio)
    }
  }, [optimizationBudgetSensitivityDetail])

  useEffect(() => {
    if (optimizationBudgetSensitivityDetail?.data && optimizationBudgetSensitivityDetail.data.length > 0 && optimized) {
      findOptimizedBudgetRatio();
    }
  }, [optimizationBudgetSensitivityDetail, selectedBudgetOptTarget, optimized])

  return (
    <Dialog as="div" className="relative z-10 focus:outline-none" onClose={handleCloseClick}>
      <div className="fixed inset-0 bg-black/30 backdrop-blur-sm" aria-hidden="true" />
      <div className="fixed inset-0 z-10 w-screen overflow-y-auto">
        <div className="flex min-h-full items-center justify-center p-4">
          <TransitionChild
            enter="ease-out duration-300"
            enterFrom="opacity-0 transform-[scale(95%)]"
            enterTo="opacity-100 transform-[scale(100%)]"
            leave="ease-in duration-200"
            leaveFrom="opacity-100 transform-[scale(100%)]"
            leaveTo="opacity-0 transform-[scale(95%)]"
          >
            <DialogPanel className="relative w-[1024px] h-[800px] flex flex-col bg-gray-900/90 rounded-xl overflow-hidden">
              <div className="flex-shrink-0 w-full pt-6 px-6">
                <div className="flex items-center justify-end">
                  <button onClick={handleCloseClick} className="inline-flex items-center justify-center rounded-md p-2 text-gray-500 hover:text-gray-200 focus:outline-none">
                    <XMarkIcon className="h-5 w-5 text-gray-500 hover:text-gray-200" />
                  </button>
                </div>
              </div>
              {optimizationBudgetSensitivityDetail?.data && (
              <div className="relative grow min-h-0 flex flex-col gap-y-4">
                <div className="relative px-6 flex-shrink-0 w-full flex flex-col items-start justify-end gap-y-4">
                  <div className="w-full px-6">
                    <h1 className="flex items-center gap-x-4 text-2xl text-gray-200 font-medium">
                      <div className="flex items-center gap-x-1">
                        {tos("detailModal.optSet.budgetSensitivityGraph.title")}
                        <Popover className="relative flex items-center justify-center">
                          {({ open }) => (
                            <>
                              <PopoverButton
                                className={cn(
                                  "inline-flex items-center justify-center rounded-full text-gray-300 hover:text-gray-400 focus:outline-none"
                                )}
                              >
                                <QuestionMarkCircleIcon className="w-5 h-5"/>
                              </PopoverButton>
                              <Transition
                                as={Fragment}
                                enter="transition ease-out duration-200"
                                enterFrom="opacity-0 translate-y-1"
                                enterTo="opacity-100 translate-y-0"
                                leave="transition ease-in duration-150"
                                leaveFrom="opacity-100 translate-y-0"
                                leaveTo="opacity-0 translate-y-1"
                              >
                                <PopoverPanel className="absolute left-full top-0 z-10 ml-2 w-screen max-w-xs translate-y-0 transform px-4 sm:px-0">
                                  <div className="overflow-hidden rounded-lg shadow-lg">
                                    <div className="relative p-4 bg-gray-900/90 text-xs text-white font-normal text-left">
                                      {tos.rich("detailModal.optSet.budgetSensitivityGraph.tooltip.budgetGuideline",{
                                        enter: () =>  <br/>
                                      })}
                                    </div>
                                  </div>
                                </PopoverPanel>
                              </Transition>
                            </>
                          )}
                        </Popover>
                      </div>
                      {optimizationBudgetSensitivityDetail?.data && optimizationBudgetSensitivityDetail.data.length > 0 && (
                        <span className="flex items-center gap-x-1 text-[10px] text-gray-400 font-normal">
                          <ExclamationTriangleIcon className="h-3 w-3 text-gray-400" />
                          {tos("detailModal.optSet.budgetSensitivityGraph.warning")}
                        </span>
                      )}
                    </h1>
                    {optimizationBudgetSensitivityDetail?.data && selectedBudgetRatio && (
                    <div className="mt-4 w-full grid grid-cols-4 grid-rows-2 gap-4">
                      <div className="p-3 rounded-md bg-white shadow-sm backdrop-blur-sm">
                        <div className="flex items-center gap-x-1.5 text-gray-500 text-xs font-semibold">
                          Budget
                        </div>
                        <div className="mt-1 text-lg text-gray-700 font-semibold">
                          {selectedPortfolioItem.ad_budget_amount
                            ? formatCurrency(selectedPortfolioItem.ad_budget_amount * (selectedBudgetRatio?.budget_ratio ?? 1), currencyCode, { maximumFractionDigits: 0, minimumFractionDigits: 0 })
                            : ""}
                          {(() => {
                            // Show absolute change from base (budget_ratio = 1)
                            const baseBudget = selectedPortfolioItem.ad_budget_amount ?? 0;
                            const currentBudget = baseBudget * (selectedBudgetRatio?.budget_ratio ?? 1);
                            const baseItem = optimizationBudgetSensitivityDetail?.data?.find((item: any) => Number(item.budget_ratio) === 1);
                            const baseValue = baseBudget * (baseItem?.budget_ratio ?? 1);
                            const diff = currentBudget - baseValue;
                            if (selectedBudgetRatio?.budget_ratio !== 1 && baseBudget > 0) {
                              return (
                                <span className="ml-2 text-xs text-gray-400">
                                  {diff > 0 ? "+" : ""}
                                  {formatCurrency(diff, currencyCode, { maximumFractionDigits: 0, minimumFractionDigits: 0 })}
                                  {" "}
                                  ({(selectedBudgetRatio?.budget_ratio * 100).toFixed(2)}%)
                                </span>
                              );
                            }
                            return null;
                          })()}
                        </div>
                      </div>
                      <div className="p-3 rounded-md bg-gray-100/10 shadow-sm backdrop-blur-sm">
                        <div className="flex items-center gap-x-1.5 text-gray-400 text-xs font-semibold">
                          Total Sales
                          <div className="w-2 h-2 rounded-full bg-[#ec4899]"></div>
                        </div>
                        <div className="mt-1 text-lg text-gray-100 font-semibold">
                          {(() => {
                            const current = optimizationBudgetSensitivityDetail?.data.find((d: any) => d.budget_ratio === selectedBudgetRatio.budget_ratio);
                            const base = optimizationBudgetSensitivityDetail?.data.find((d: any) => Number(d.budget_ratio) === 1);
                            const currentSales = current?.metrics?.total_sales ?? 0;
                            const baseSales = base?.metrics?.total_sales ?? 0;
                            const percent =
                              baseSales && currentSales
                                ? ((currentSales - baseSales) / baseSales) * 100
                                : 0;
                            return (
                              <>
                                ${currentSales.toLocaleString(undefined, { maximumFractionDigits: 0 })}
                                {baseSales > 0 && selectedBudgetRatio.budget_ratio !== 1 && (
                                  <span
                                    className={
                                      percent > 0
                                        ? "ml-2 text-green-400 text-xs"
                                        : percent < 0
                                          ? "ml-2 text-red-400 text-xs"
                                          : "ml-2 text-gray-400 text-xs"
                                    }
                                  >
                                    {currentSales - baseSales > 0 ? "+" : ""}
                                    {(currentSales - baseSales).toLocaleString(undefined, { maximumFractionDigits: 0 })}
                                    {" "}
                                    ({percent > 0 ? "+" : ""}
                                    {percent.toFixed(2)}%)
                                  </span>
                                )}
                              </>
                            );
                          })()}
                        </div>
                      </div>
                      <div className="p-3 rounded-md bg-gray-100/10 shadow-sm backdrop-blur-sm">
                        <div className="flex items-center gap-x-1.5 text-gray-400 text-xs font-semibold">
                          Total Profit
                          <div className="w-2 h-2 rounded-full bg-[#a855f7]"></div>
                        </div>
                        <div className="mt-1 text-lg text-gray-100 font-semibold">
                          {(() => {
                            const current = optimizationBudgetSensitivityDetail?.data.find((d: any) => d.budget_ratio === selectedBudgetRatio.budget_ratio);
                            const base = optimizationBudgetSensitivityDetail?.data.find((d: any) => Number(d.budget_ratio) === 1);
                            const currentProfit = current?.metrics?.total_profit ?? 0;
                            const baseProfit = base?.metrics?.total_profit ?? 0;
                            const percent =
                              baseProfit && currentProfit
                                ? ((currentProfit - baseProfit) / baseProfit) * 100
                                : 0;
                            return (
                              <>
                                ${currentProfit.toLocaleString(undefined, { maximumFractionDigits: 0 })}
                                {baseProfit > 0 && selectedBudgetRatio.budget_ratio !== 1 && (
                                  <span
                                    className={
                                      percent > 0
                                        ? "ml-2 text-green-400 text-xs"
                                        : percent < 0
                                          ? "ml-2 text-red-400 text-xs"
                                          : "ml-2 text-gray-400 text-xs"
                                    }
                                  >
                                    {currentProfit - baseProfit > 0 ? "+" : ""}
                                    {(currentProfit - baseProfit).toLocaleString(undefined, { maximumFractionDigits: 0 })}
                                    {" "}
                                    ({percent > 0 ? "+" : ""}
                                    {percent.toFixed(2)}%)
                                  </span>
                                )}
                              </>
                            );
                          })()}
                        </div>
                      </div>
                      <div className="p-3 rounded-md bg-gray-100/10 shadow-sm backdrop-blur-sm">
                        <div className="flex items-center gap-x-1.5 text-gray-400 text-xs font-semibold">
                          Total Cost
                          <div className="w-2 h-2 rounded-full bg-[#dc2626]"></div>
                        </div>
                        <div className="mt-1 text-lg text-gray-100 font-semibold">
                          {(() => {
                            const current = optimizationBudgetSensitivityDetail?.data.find((d: any) => d.budget_ratio === selectedBudgetRatio.budget_ratio);
                            const base = optimizationBudgetSensitivityDetail?.data.find((d: any) => Number(d.budget_ratio) === 1);
                            const currentCost = current?.metrics?.total_cost ?? 0;
                            const baseCost = base?.metrics?.total_cost ?? 0;
                            const percent =
                              baseCost && currentCost
                                ? ((currentCost - baseCost) / baseCost) * 100
                                : 0;
                            return (
                              <>
                                ${currentCost.toLocaleString(undefined, { maximumFractionDigits: 0 })}
                                {baseCost > 0 && selectedBudgetRatio.budget_ratio !== 1 && (
                                  <span
                                    className={
                                      percent > 0
                                        ? "ml-2 text-red-400 text-xs"
                                        : percent < 0
                                          ? "ml-2 text-green-400 text-xs"
                                          : "ml-2 text-gray-400 text-xs"
                                    }
                                  >
                                    {currentCost - baseCost > 0 ? "+" : ""}
                                    {(currentCost - baseCost).toLocaleString(undefined, { maximumFractionDigits: 0 })}
                                    {" "}
                                    ({percent > 0 ? "+" : ""}
                                    {percent.toFixed(2)}%)
                                  </span>
                                )}
                              </>
                            );
                          })()}
                        </div>
                      </div>
                      <div className="p-3 rounded-md bg-gray-100/10 shadow-sm backdrop-blur-sm">
                        <div className="flex items-center gap-x-1.5 text-gray-400 text-xs font-semibold">
                          TACOS
                        </div>
                        <div className="mt-1 text-lg text-gray-100 font-semibold">
                          {(() => {
                            const current = optimizationBudgetSensitivityDetail?.data.find((d: any) => d.budget_ratio === selectedBudgetRatio.budget_ratio);
                            const base = optimizationBudgetSensitivityDetail?.data.find((d: any) => Number(d.budget_ratio) === 1);
                            const adSpend = current?.metrics?.ad_spending ?? 0;
                            const totalSales = current?.metrics?.total_sales ?? 0;
                            const baseAdSpend = base?.metrics?.ad_spending ?? 0;
                            const baseTotalSales = base?.metrics?.total_sales ?? 0;
                            if (totalSales > 0 && baseTotalSales > 0) {
                              const tacos = (adSpend / totalSales) * 100;
                              const baseTacos = (baseAdSpend / baseTotalSales) * 100;
                              const diff = tacos - baseTacos;
                              const percent = baseTacos !== 0 ? (diff / baseTacos) * 100 : 0;
                              return (
                                <>
                                  {tacos.toFixed(2)}%
                                  {selectedBudgetRatio.budget_ratio !== 1 && (
                                    <span
                                      className={
                                        diff > 0
                                          ? "ml-2 text-red-400 text-xs"
                                          : diff < 0
                                            ? "ml-2 text-green-400 text-xs"
                                            : "ml-2 text-gray-400 text-xs"
                                      }
                                    >
                                      {diff > 0 ? "+" : ""}
                                      {diff.toFixed(2)}%
                                      {" "}
                                      ({percent > 0 ? "+" : ""}
                                      {percent.toFixed(2)}%)
                                    </span>
                                  )}
                                </>
                              );
                            }
                            return "-";
                          })()}
                        </div>
                      </div>
                      <div className="p-3 rounded-md bg-gray-100/10 shadow-sm backdrop-blur-sm">
                        <div className="flex items-center gap-x-1.5 text-gray-400 text-xs font-semibold">
                          Ad Spend
                          <div className="w-2 h-2 rounded-full bg-[#f59e0b]"></div>
                        </div>
                        <div className="mt-1 text-lg text-gray-100 font-semibold">
                          {(() => {
                            const current = optimizationBudgetSensitivityDetail?.data.find((d: any) => d.budget_ratio === selectedBudgetRatio.budget_ratio);
                            const base = optimizationBudgetSensitivityDetail?.data.find((d: any) => Number(d.budget_ratio) === 1);
                            const currentAdSpend = current?.metrics?.ad_spending ?? 0;
                            const baseAdSpend = base?.metrics?.ad_spending ?? 0;
                            const percent =
                              baseAdSpend && currentAdSpend
                                ? ((currentAdSpend - baseAdSpend) / baseAdSpend) * 100
                                : 0;
                            return (
                              <>
                                ${currentAdSpend.toLocaleString(undefined, { maximumFractionDigits: 0 })}
                                {baseAdSpend > 0 && selectedBudgetRatio.budget_ratio !== 1 && (
                                  <span
                                    className={
                                      percent > 0
                                        ? "ml-2 text-red-400 text-xs"
                                        : percent < 0
                                          ? "ml-2 text-green-400 text-xs"
                                          : "ml-2 text-gray-400 text-xs"
                                    }
                                  >
                                    {currentAdSpend - baseAdSpend > 0 ? "+" : ""}
                                    {(currentAdSpend - baseAdSpend).toLocaleString(undefined, { maximumFractionDigits: 0 })}
                                    {" "}
                                    ({percent > 0 ? "+" : ""}
                                    {percent.toFixed(2)}%)
                                  </span>
                                )}
                              </>
                            );
                          })()}
                        </div>
                      </div>
                      <div className="p-3 rounded-md bg-gray-100/10 shadow-sm backdrop-blur-sm">
                        <div className="flex items-center gap-x-1.5 text-gray-400 text-xs font-semibold">
                          Ad Sales
                          <div className="w-2 h-2 rounded-full bg-[#6366f1]"></div>
                        </div>
                        <div className="mt-1 text-lg text-gray-100 font-semibold">
                          {(() => {
                            const current = optimizationBudgetSensitivityDetail?.data.find((d: any) => d.budget_ratio === selectedBudgetRatio.budget_ratio);
                            const base = optimizationBudgetSensitivityDetail?.data.find((d: any) => Number(d.budget_ratio) === 1);
                            const currentAdSales = current?.metrics?.ad_sales ?? 0;
                            const baseAdSales = base?.metrics?.ad_sales ?? 0;
                            const percent =
                              baseAdSales && currentAdSales
                                ? ((currentAdSales - baseAdSales) / baseAdSales) * 100
                                : 0;
                            return (
                              <>
                                ${currentAdSales.toLocaleString(undefined, { maximumFractionDigits: 0 })}
                                {baseAdSales > 0 && selectedBudgetRatio.budget_ratio !== 1 && (
                                  <span
                                    className={
                                      percent > 0
                                        ? "ml-2 text-green-400 text-xs"
                                        : percent < 0
                                          ? "ml-2 text-red-400 text-xs"
                                          : "ml-2 text-gray-400 text-xs"
                                    }
                                  >
                                    {currentAdSales - baseAdSales > 0 ? "+" : ""}
                                    {(currentAdSales - baseAdSales).toLocaleString(undefined, { maximumFractionDigits: 0 })}
                                    {" "}
                                    ({percent > 0 ? "+" : ""}
                                    {percent.toFixed(2)}%)
                                  </span>
                                )}
                              </>
                            );
                          })()}
                        </div>
                      </div>
                      <div className="p-3 rounded-md bg-gray-100/10 shadow-sm backdrop-blur-sm">
                        <div className="flex items-center gap-x-1.5 text-gray-400 text-xs font-semibold">
                          Ad Sales Same SKU
                          <div className="w-2 h-2 rounded-full bg-[#66d9e8]"></div>
                        </div>
                        <div className="mt-1 text-lg text-gray-100 font-semibold">
                          {(() => {
                            const current = optimizationBudgetSensitivityDetail?.data.find((d: any) => d.budget_ratio === selectedBudgetRatio.budget_ratio);
                            const base = optimizationBudgetSensitivityDetail?.data.find((d: any) => Number(d.budget_ratio) === 1);
                            const currentValue = current?.metrics?.ad_sales_same_sku ?? 0;
                            const baseValue = base?.metrics?.ad_sales_same_sku ?? 0;
                            const percent =
                              baseValue && currentValue
                                ? ((currentValue - baseValue) / baseValue) * 100
                                : 0;
                            return (
                              <>
                                ${currentValue.toLocaleString(undefined, { maximumFractionDigits: 0 })}
                                {baseValue > 0 && selectedBudgetRatio.budget_ratio !== 1 && (
                                  <span
                                    className={
                                      percent > 0
                                        ? "ml-2 text-green-400 text-xs"
                                        : percent < 0
                                          ? "ml-2 text-red-400 text-xs"
                                          : "ml-2 text-gray-400 text-xs"
                                    }
                                  >
                                    {currentValue - baseValue > 0 ? "+" : ""}
                                    {(currentValue - baseValue).toLocaleString(undefined, { maximumFractionDigits: 0 })}
                                    {" "}
                                    ({percent > 0 ? "+" : ""}
                                    {percent.toFixed(2)}%)
                                  </span>
                                )}
                              </>
                            );
                          })()}
                        </div>
                      </div>
                    </div>
                    )}
                    <div className="mt-6">
                      <div className="text-xs text-gray-400 font-semibold">{tos("detailModal.optSet.budgetSensitivityGraph.optimizationTarget")} *</div>
                      <div className="mt-1 relative w-[200px]">
                        <Listbox
                          value={selectedBudgetOptTarget}
                          onChange={setSelectedBudgetOptTarget}
                          by="type"
                          disabled={!optimized}
                        >
                          <div className="relative">
                            <Listbox.Button
                              className={cn(
                                "relative w-full rounded-lg py-2 pl-3 pr-10 text-left shadow-md focus:outline-none focus-visible:border-indigo-500 focus-visible:ring-2 focus-visible:ring-white/75 focus-visible:ring-offset-2 focus-visible:ring-offset-orange-300 sm:text-sm",
                                optimized
                                  ? "cursor-pointer bg-gray-100/10 hover:bg-gray-100/30 text-gray-300"
                                  : "cursor-not-allowed bg-gray-100/10 text-gray-500 opacity-60"
                              )}
                              disabled={!optimized}
                            >
                              <span className="block truncate">
                                {selectedBudgetOptTarget?.name}
                              </span>
                              <span className="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-2">
                                <ChevronUpDownIcon
                                  className="h-4 w-4 text-gray-300"
                                  aria-hidden="true"
                                />
                              </span>
                            </Listbox.Button>
                            <Transition
                              as={Fragment}
                              leave="transition ease-in duration-100"
                              leaveFrom="opacity-100"
                              leaveTo="opacity-0"
                            >
                              <Listbox.Options
                                className={cn(
                                  "absolute z-[3] mt-1 max-h-60 w-full overflow-auto rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black/5 focus:outline-none sm:text-sm",
                                )}
                              >
                                {budgetOptTargetOptions.map((option: any, optionIdx: number) => (
                                  <Listbox.Option
                                    key={option.type}
                                    className={({ active }) =>
                                      `relative cursor-pointer select-none py-2 pl-4 pr-10 ${
                                        active ? 'bg-blue-100/40 text-blue-900' : 'text-gray-600'
                                      }`
                                    }
                                    value={option}
                                  >
                                    {({ selected }) => (
                                      <>
                                        <span
                                          className={`block truncate ${
                                            selected ? 'font-medium' : 'font-normal'
                                          }`}
                                        >
                                          {option.name}
                                        </span>
                                        {selected ? (
                                          <div className="absolute inset-y-0 right-0 flex items-center pr-3">
                                            <div className="shrink-0 flex items-center justify-center w-5 h-5 rounded-full">
                                              <Check className="w-4 h-4 stroke-blue-400" aria-hidden="true" />
                                            </div>
                                          </div>
                                        ) : null}
                                      </>
                                    )}
                                  </Listbox.Option>
                                ))}
                              </Listbox.Options>
                            </Transition>
                          </div>
                        </Listbox>
                      </div>
                    </div>
                  </div>
                </div>
                <div className="relative grow min-h-0 flex flex-col gap-4">
                  <div className="px-6 relative grow min-h-0">
                    {!optimized
                      ? (
                        <div className="absolute inset-0 flex items-center justify-center px-12 pb-12">
                          <div className="flex items-center justify-center size-full text-gray-400 text-base p-3 rounded-md bg-gray-100/10 shadow-sm backdrop-blur-sm">
                            {tos("detailModal.optSet.budgetSensitivityGraph.optimizationNotStarted")}
                          </div>
                        </div>
                      )
                      : <BudgetSensitivityGraph
                          totalProfitData={optimizationBudgetSensitivityDetail?.data.sort((a: any, b: any) => a.budget_ratio - b.budget_ratio).map((item: any) => ({
                            budget_ratio: item?.budget_ratio,
                            value: item?.metrics?.total_profit || 0
                          })) || []}
                          totalSalesData={optimizationBudgetSensitivityDetail?.data.sort((a: any, b: any) => a.budget_ratio - b.budget_ratio).map((item: any) => ({
                            budget_ratio: item?.budget_ratio,
                            value: item?.metrics?.total_sales || 0
                          })) || []}
                          totalCostData={optimizationBudgetSensitivityDetail?.data.sort((a: any, b: any) => a.budget_ratio - b.budget_ratio).map((item: any) => ({
                            budget_ratio: item?.budget_ratio,
                            value: item?.metrics?.total_cost || 0
                          })) || []}
                          adSpendData={optimizationBudgetSensitivityDetail?.data.sort((a: any, b: any) => a.budget_ratio - b.budget_ratio).map((item: any) => ({
                            budget_ratio: item?.budget_ratio,
                            value: item?.metrics?.ad_spending || 0
                          })) || []}
                          adSalesData={optimizationBudgetSensitivityDetail?.data.sort((a: any, b: any) => a.budget_ratio - b.budget_ratio).map((item: any) => ({
                            budget_ratio: item?.budget_ratio,
                            value: item?.metrics?.ad_sales || 0
                          })) || []}
                          adSalesSameSkuData={optimizationBudgetSensitivityDetail?.data.sort((a: any, b: any) => a.budget_ratio - b.budget_ratio).map((item: any) => ({
                            budget_ratio: item?.budget_ratio,
                            value: item?.metrics?.ad_sales_same_sku || 0
                          })) || []}
                          optimized={optimized}
                        />
                    }
                  </div>
                  {optimizationBudgetSensitivityDetail?.data && selectedBudgetRatio && (
                    <div className="flex-shrink-0">
                      <div className="px-6 pb-12">
                        <div className="relative flex-shrink-0 w-full pr-6 pl-[90px] flex items-center flex-wrap gap-2 text-lg text-gray-400 font-semibold">
                          <button
                            className="absolute left-6 -top-[10px] inline-flex items-center gap-2 rounded-md py-1.5 px-3 text-sm/6 font-semibold text-gray-100 focus:outline-none hover:bg-gray-100/20 transition cursor-pointer"
                            onClick={() => {
                              setSelectedBudgetOptTarget(budgetOptTargetOptions[0]); // Set to "none"
                            }}
                            disabled={selectedBudgetRatio?.budget_ratio === 1 || !optimizationBudgetSensitivityDetail?.data.some((item: any) => Number(item.budget_ratio) === 1)}
                          >
                            Reset
                          </button>
                          <div className="flex flex-col w-full pr-[76px] pl-[8px]">
                            {(() => {
                              // Prepare sorted budget ratios for slider marks
                              const budgetRatios = optimizationBudgetSensitivityDetail?.data
                                ?.map((item: any) => Number(item.budget_ratio))
                                ?.sort((a: number, b: number) => a - b) || [];
                              const min = budgetRatios[0] ?? 0;
                              const max = budgetRatios[budgetRatios.length - 1] ?? 1;
                              // Find the closest ratio to the slider value
                              const getClosestRatio = (value: number) => {
                                return optimizationBudgetSensitivityDetail?.data.reduce(
                                  (prev: any, curr: any) =>
                                    Math.abs(Number(curr.budget_ratio) - value) < Math.abs(Number(prev.budget_ratio) - value)
                                      ? curr
                                      : prev,
                                  optimizationBudgetSensitivityDetail?.data[0]
                                );
                              };
                              return (
                                <>
                                  <input
                                    type="range"
                                    min={min}
                                    max={max}
                                    step={0.0001}
                                    value={selectedBudgetRatio?.budget_ratio ?? min}
                                    onChange={e => {
                                      const value = Number(e.target.value);
                                      const closest = getClosestRatio(value);
                                      setSelectedBudgetRatio(closest);
                                    }}
                                    className="w-full accent-gray-100 cursor-pointer"
                                    disabled={!optimized}
                                  />
                                    <div className="w-full relative flex justify-between text-xs text-gray-400 mt-1">
                                      <span>{(min * 100).toFixed(2)}%</span>
                                      {/* Vertical line for optimizedBudgetRatio */}
                                      {/* Optimized budget ratio line */}
                                      {optimizedBudgetRatio && (
                                      <Transition
                                        show={optimized && !!optimizedBudgetRatio && selectedBudgetOptTarget?.type !== "none"}
                                        as={Fragment}
                                        enter="transition-opacity duration-500"
                                        enterFrom="opacity-0"
                                        enterTo="opacity-100"
                                        leave="transition-opacity duration-300"
                                        leaveFrom="opacity-100"
                                        leaveTo="opacity-0"
                                      >
                                        <div
                                          className="absolute top-0 bottom-0 bg-gradient-to-br from-blue-500/60 to-purple-500/60 pointer-events-none"
                                          style={{
                                            left: `${
                                              ((optimizedBudgetRatio?.budget_ratio - min) / (max - min)) * 100
                                            }%`,
                                            width: "2px",
                                            height: "296px",
                                            marginTop: "-364px",
                                            borderRadius: "1px",
                                            zIndex: 2,
                                          }}
                                        >
                                          <div
                                            className="absolute left-1/2 -translate-x-1/2 -translate-y-full top-0 flex items-center gap-x-1 py-0.5 px-1.5 rounded-sm text-[10px] text-white bg-gradient-to-br from-blue-500/60 to-purple-500/60 whitespace-nowrap"
                                            style={{ whiteSpace: "nowrap" }}
                                          >
                                            <span className="relative flex h-1.5 w-1.5">
                                              <span className="absolute inline-flex h-full w-full rounded-full opacity-75 animate-ping bg-white"></span>
                                              <span className="relative inline-flex rounded-full h-1.5 w-1.5 bg-white"></span>
                                            </span>
                                            {selectedBudgetOptTarget?.name}
                                          </div>
                                        </div>
                                      </Transition>
                                      )}
                                      {/* Always show base budget_ratio=1 line */}
                                      <Transition
                                        show={optimized}
                                        as={Fragment}
                                        enter="transition-opacity duration-500"
                                        enterFrom="opacity-0"
                                        enterTo="opacity-100"
                                        leave="transition-opacity duration-300"
                                        leaveFrom="opacity-100"
                                        leaveTo="opacity-0"
                                      >
                                        {(() => {
                                          const baseItem = optimizationBudgetSensitivityDetail?.data?.find((item: any) => Number(item.budget_ratio) === 1);
                                          if (!baseItem) return null;
                                          return (
                                            <div
                                              className="absolute top-0 bottom-0 bg-white/70 pointer-events-none"
                                              style={{
                                                left: `${
                                                  ((baseItem.budget_ratio - min) / (max - min)) * 100
                                                }%`,
                                                width: "2px",
                                                height: "296px",
                                                marginTop: "-364px",
                                                borderRadius: "1px",
                                                zIndex: 1,
                                              }}
                                            >
                                              <div
                                                className="absolute left-1/2 -translate-x-1/2 -translate-y-full top-0 py-0.5 px-1.5 rounded-sm text-[10px] text-gray-900/90 bg-white/70 whitespace-nowrap"
                                                style={{ whiteSpace: "nowrap" }}
                                              >
                                                Current
                                              </div>
                                            </div>
                                          );
                                        })()}
                                      </Transition>
                                      <span>{(max * 100).toFixed(2)}%</span>
                                    </div>
                                </>
                              );
                            })()}
                          </div>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
                {!optimized && (
                  <div className="absolute inset-0 size-full py-10 px-10 z-[5]">
                    <div className="relative size-full flex items-center justify-center bg-gray-100/10 rounded-md backdrop-blur-md">
                      <div className="flex flex-col items-center">
                        <div className="text-center text-gray-200 font-semibold text-lg">
                          {tos("detailModal.optSet.budgetSensitivityGraph.optimizationNotStarted")}
                        </div>
                        <button
                          className={cn(
                            "mt-4 relative inline-flex items-center gap-2 rounded-md py-1.5 px-3 text-sm/6 font-semibold focus:outline-none transition",
                            optimized
                              ? "bg-gray-100/10 text-gray-500 opacity-60 cursor-not-allowed"
                              : "bg-gray-100 hover:bg-gray-100/80  text-gray-800 cursor-pointer"
                          )}
                          onClick={() => setOptimized(true)}
                          disabled={!optimizationBudgetSensitivityDetail?.data?.length || optimized}
                        >
                          <span className="relative flex-shrink-0 block">
                            {!optimized &&
                            <span className="block absolute -right-1.5 -top-0.5 w-1.5 h-1.5 rounded-full bg-red-400 group-hover:bg-red-500"></span>
                            }
                            {tos("detailModal.optSet.budgetSensitivityGraph.startSimulation")}
                          </span>
                        </button>
                      </div>
                    </div>
                  </div>
                )}
              </div>
              )}
            </DialogPanel>
          </TransitionChild>
        </div>
      </div>
    </Dialog>
  )
}