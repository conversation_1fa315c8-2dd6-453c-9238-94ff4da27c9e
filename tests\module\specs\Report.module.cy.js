import ReportMock from "../mock/ReportMock";
import ReportPage from "../../pages/ReportPage";

const service = new ReportMock();
const page = new ReportPage();

describe('Dashboard', () => {
    it.guide('dashboard 화면에 처음 접속하면, vendor를 설정할 수 있는 dropdown, 조회 조건을 설정할 수 있는 필터, 레포트 내용을 확인할 수 있다.', {
        mockFunc: () => {
            cy.login();
            cy.intercept('GET', '/api/member', {
                body: {
                    "id": "mem_1",
                    "email": "<EMAIL>",
                    "lwa_accounts": [
                        {
                            "account_id": "acc_vendor_1",
                            "account_type": "vendor",
                            "vendor_code": "VENDOR1",
                            "marketplaces": [
                                {
                                    "id": "mk_vendor_us",
                                    "marketplace_id": "ATVPDKIKX0DER",
                                    "marketplace_name": "United States",
                                    "country_code": "US",
                                    "default_currency_code": "USD",
                                    "subscription_yn": "Y",
                                    "ad_lwa_validation_yn": "Y",
                                    "sp_lwa_validation_yn": "Y",
                                    "subscription_features": { "number_of_optimization_sets": 5 }
                                }
                            ],
                        }
                    ]
                }
            }).as('getMember');
            service.successWhenGetJoinedVendorData();
            service.successWhenGetJoinedVendorDailyPerformance();
            service.successWhenGetReportListOptimizations();
        },
        actionFunc: () => {
            cy.visit('/dashboard?tab=reports');
        },
        waitFunc: () => {
            cy.wait([
                '@getMember',
                '@successWhenGetJoinedVendorData',
                '@successWhenGetJoinedVendorDailyPerformance',
                '@successWhenGetReportListOptimizations',
            ]);
        },
        assertFunc: () => {
            //dropdown
            page.assertDropDown();
            //filter
            page.assertFilter();
            //dashboard
            page.assertReport();            
        }
    });

    it.guide('excel로 다운로드 할 수 있는 버튼을 확인할 수 있다.',{
        mockFunc: () => {
            cy.login();
            cy.intercept('GET', '/api/member', {
                body: {
                    "id": "mem_1",
                    "email": "<EMAIL>",
                    "lwa_accounts": [
                        {
                            "account_id": "acc_vendor_1",
                            "account_type": "vendor",
                            "vendor_code": "VENDOR1",
                            "marketplaces": [
                                {
                                    "id": "mk_vendor_us",
                                    "marketplace_id": "ATVPDKIKX0DER",
                                    "marketplace_name": "United States",
                                    "country_code": "US",
                                    "default_currency_code": "USD",
                                    "subscription_yn": "Y",
                                    "ad_lwa_validation_yn": "Y",
                                    "sp_lwa_validation_yn": "Y",
                                    "subscription_features": { "number_of_optimization_sets": 5 }
                                }
                            ],
                        }
                    ]
                }
            }).as('getMember');
            service.successWhenGetJoinedVendorData();
            service.successWhenGetJoinedVendorDailyPerformance();
            service.successWhenGetReportListOptimizations();
        },
        actionFunc: () => {
            cy.visit('/dashboard?tab=reports');
        },
        waitFunc: () => {
            cy.wait([
                '@getMember',
                '@successWhenGetJoinedVendorData',
                '@successWhenGetJoinedVendorDailyPerformance',
                '@successWhenGetReportListOptimizations',
            ]);
        },
        assertFunc:()=>{
            page.assertExcelButton();
        },
        afterFunc: () => {
            cy.clearCookies();
        }
    })
});
  