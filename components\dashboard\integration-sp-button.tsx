"use client";

import actions from "@/actions";
import { cn } from "@/utils/msc";
import { PlusIcon } from "@heroicons/react/20/solid";
import { useState } from "react";

interface IntegrateButtonProps extends React.HTMLAttributes<HTMLDivElement> {
  isLNBExpanded?: boolean;
  buttonClassName?: string;
}

export default function IntegrateSpButton({
  isLNBExpanded,
  buttonClassName,
  ...props
}: IntegrateButtonProps) {
  const [countryCode, setCountryCode] = useState("US"); // Add state for country code

  const handleClick = async () => {
    const oauthLink = await actions.oauth.getSpLoginLink(countryCode);
    window.location.href = oauthLink.url;
  };

  return (
    <div className="flex items-center gap-2">
      <select
        value={countryCode}
        onChange={(e) => setCountryCode(e.target.value)}
      >
        <option value="US">🇺🇸 US</option>
        <option value="CA">🇨🇦 CA</option>
        <option value="MX">🇲🇽 MX</option>
        <option value="BR">🇧🇷 BR</option>
        <option value="ES">🇪🇸 ES</option>
        <option value="UK">🇬🇧 UK</option>
        <option value="FR">🇫🇷 FR</option>
        <option value="BE">🇧🇪 BE</option>
        <option value="NL">🇳🇱 NL</option>
        <option value="DE">🇩🇪 DE</option>
        <option value="IT">🇮🇹 IT</option>
        <option value="SE">🇸🇪 SE</option>
        <option value="ZA">🇿🇦 ZA</option>
        <option value="PL">🇵🇱 PL</option>
        <option value="EG">🇪🇬 EG</option>
        <option value="SA">🇸🇦 SA</option>
        <option value="TR">🇹🇷 TR</option>
        <option value="AE">🇦🇪 AE</option>
        <option value="IN">🇮🇳 IN</option>
        <option value="SG">🇸🇬 SG</option>
        <option value="AU">🇦🇺 AU</option>
        <option value="JP">🇯🇵 JP</option>
      </select>
      <button className={cn(buttonClassName)} onClick={handleClick}>
        <PlusIcon
          className="flex-shrink-0 h-5 w-5 text-blue-400 group-hover:text-blue-600"
          aria-hidden="true"
        />
        {isLNBExpanded ? (
          <span className="flex-shrink-0 block text-blue-400 group-hover:text-blue-600 font-semibold">
            Integrate SP
          </span>
        ) : (
          ""
        )}
      </button>
    </div>
  );
}
