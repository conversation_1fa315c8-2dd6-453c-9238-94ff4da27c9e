import { Avatar, AvatarFallback, AvatarImage, AvatarSmileAnimation } from "./ui/avatar"
import { Button } from "./ui/button"
import { auth } from "auth"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuTrigger,
} from "./ui/dropdown-menu"
import { SignInButton } from "./ui/sign-in-button"
import { SignOutButton } from "./ui/sign-out-button"
import { UserPlusIcon } from "@heroicons/react/20/solid"
import CustomLink from "./custom-link"

export default async function UserButton() {
  const session = await auth()
  if (!session?.user) return <SignInButton provider="cognito"/>
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" className="relative w-8 h-8 rounded-full">
          {/* <Avatar className="w-8 h-8">
            {session.user.image && (
              <AvatarImage
                src={session.user.image}
                alt={session.user.name ?? ""}
              />
            )}
            <AvatarFallback>{session.user.email}</AvatarFallback>
          </Avatar> */}
          <div className="relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full">
            <AvatarSmileAnimation/>
          </div>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent className="min-w-[160px] p-0 shadow-md" align="end" forceMount>
        <div className="flex items-center justify-center bg-gray-50 py-3 px-4 text-xs text-gray-600 font-semibold border-b border-gray-100">
          {session.user.email}
        </div>
        <div className="p-1 space-y-1">
          <CustomLink
            href="/dashboard?tab=manage-profile"
            className="flex items-center justify-start w-full h-6 py-4 px-4 text-gray-600 text-xs hover:bg-accent hover:text-accent-foreground rounded-md font-medium"
          >
            <UserPlusIcon className="w-4 h-4 mr-2"/>
            Account Settings
          </CustomLink>
          <SignOutButton />
        </div>
      </DropdownMenuContent>
    </DropdownMenu>
  )
}
