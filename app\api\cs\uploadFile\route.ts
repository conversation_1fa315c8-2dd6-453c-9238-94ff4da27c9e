import { ErrorResponse } from "@/app/api/_models";
import { getServerApiHostUrl } from "@/utils/host";
import { NextRequest, NextResponse } from "next/server";

export type UploadFileResponse = any;

export async function POST(
  request: NextRequest
): Promise<NextResponse<UploadFileResponse | ErrorResponse>> {
  const accessToken = request.headers.get("Authorization");
  if (!accessToken) {
    return NextResponse.json(
      { message: "Authorization header is missing" },
      { status: 400 }
    );
  }
  try {
    const formData = await request.formData(); // Extract the FormData directly from the request body
    const uploadFileResponse = await fetch(
      `${await getServerApiHostUrl()}/api/cs/upload`,
      {
        method: "POST",
        headers: {
          Authorization: `Bearer ${accessToken}`,
        },
        body: formData, // Use the extracted FormData directly
      }
    ).then((res) => res.json());
    return NextResponse.json(uploadFileResponse, { status: 200 });
  } catch (err) {
    return NextResponse.json(
      { message: "Error processing file upload" },
      { status: 500 }
    );
  }
}