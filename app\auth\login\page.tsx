"use client";

import { useEffect } from "react"
import { signIn } from "next-auth/react"
import { useLocale } from "next-intl"
import { locales } from "../../../config"

export default function LogInWithRedirectionPage() {
  const locale = useLocale();

  useEffect(() => {
    const signInUser = async () => {
      const authorizationParams: { lang?: string } = {};
      // The user wants to pass 'ko', 'en', 'cn'
      if (locale && locales.includes(locale as any)) {
        if (locale === 'cn') {
          authorizationParams.lang = 'zh-CN';
        } else {
          authorizationParams.lang = locale;
        }
      }
      await signIn("cognito", { callbackUrl: "/" }, authorizationParams);
    };
    signInUser();
  }, [locale]);

  return (
    <div className="flex items-center justify-center overflow-hidden relative w-full h-full">
			<svg className="animate-spin h-10 w-10 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
				<circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
				<path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
			</svg>
    </div>
  );
}