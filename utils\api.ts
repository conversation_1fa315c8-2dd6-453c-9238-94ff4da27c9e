"use client";

import { OAuthCodeToTokenDataResponse } from "@/app/api/oauth/token/route";
import { MemberResponse } from "@/app/api/member/route";
import { ExMemberResponse } from "@/app/api/exMember/route";
import { ProductAdsResponse } from "@/app/api/ads/sp/productAds/list/route";
import { ProductMetadataResponse } from "@/app/api/ads/product/metadata/route";
import { SPAdvertisedProductsResponse } from "@/app/api/ads/reporting/ads/sp_advertised_products/route";
import { SPReportByDateResponse } from "@/app/api/sp/reporting/sales_and_traffic_by_date/route";
import { SPReportByAsinResponse } from "@/app/api/sp/reporting/sales_and_traffic_by_asin/route";
import { JoinedReportByAsinAndDateResponse } from "@/app/api/report/new_sales_and_traffic_per_asin_by_date_range/route";
import { ListOptimizationsResponse } from "@/app/api/optimization/list_optimizations/route";
import { ListTargetCandidateAsinsResponse } from "@/app/api/optimization/list_target_candidate_asins/route";
import { CreateOptimizationResponse } from "@/app/api/optimization/create_optimization/route";
import { EditOptimizationResponse } from "@/app/api/optimization/edit_optimization/route";
import { PauseOptimizationResponse } from "@/app/api/optimization/pause_optimization/route";
import { AddOptimizationTargetsResponse } from "@/app/api/optimization/add_targets/route";
import { ListNewCampaignsAndPausedAdsResponse } from "@/app/api/optimization/list_new_campaigns_and_paused_ads/route";
import { JoinedSellerDataResponse } from "@/app/api/report/seller_data/route";
import { JoinedVendorDataResponse } from "@/app/api/report/vendor_data/route";
import { JoinedSellerDailyPerformanceResponse } from "@/app/api/report/seller_daily_performance/route";
import { JoinedVendorDailyPerformanceResponse } from "@/app/api/report/vendor_daily_performance/route";
import { HourlyReportResponse } from "@/app/api/hourlyReport/hourly/route";
import { HourlyReportByDateResponse } from "@/app/api/hourlyReport/hourly/by-date/route";
import { AvailableProfilesResponse } from "@/app/api/oauth/availableProfiles/route";
import { AvailableConnectionByProfileIdResponse } from "@/app/api/oauth/availableConnectionByProfileId/route";
import { ConnectProfileResponse } from "@/app/api/oauth/connectProfile/route";
import { ChangePasswordResponse } from "@/app/api/cognito/change-password/route";
import { WithDrawAccountResponse } from "@/app/api/cognito/withdraw/route";
import { CreateTicketResponse } from "@/app/api/cs/createTicket/route";
import { ListTicketsResponse } from "@/app/api/cs/getTicketList/route";
import { ListFaqsResponse } from "@/app/api/cs/getFaqList/route";
import { CollectionStatusResponse } from "@/app/api/collectionStatus/route";
import { TargetsResponse } from "@/app/api/optimization/list_targets/route";
import { ListCampaignHistoryResponse } from "@/app/api/optimization/list_campaign_history/route";
import { ListTargetHistoryResponse } from "@/app/api/optimization/list_target_history/route";
import { OptimizationBudgetSensitivitySummaryResponse } from "@/app/api/optimization/budget_sensitivity_summary/route";
import { OptimizationBudgetSensitivityDetailResponse } from "@/app/api/optimization/budget_sensitivity_detail/route";
import { CampaignBudgetUsageResponse } from "@/app/api/hourlyReport/campaign-budget-usage/route";
import { BudgetPacingResponse } from "@/app/api/hourlyReport/new_budget_pacing/route";

export const api = {
  getTokenData: async (
    oauthCode: string
  ): Promise<OAuthCodeToTokenDataResponse> =>
    fetch(`/api/oauth/token`, {
      method: "GET",
      headers: {
        "X-Lwa-OAuth-Code": oauthCode,
      },
    })
      .then(async (res) => {
        if (res.status !== 200 || (await res.clone().json()).detail === "Token has expired") {
          window.location.href = "/auth/logout";
          return;
        }
        return res.json();
      })
      .catch(console.error),
  getProfileData: async (
    accessToken: string
  ): Promise<OAuthCodeToTokenDataResponse> =>
    fetch(`/api/oauth/profile`, {
      method: "GET",
      headers: {
        "X-LwA-Access-Token": accessToken,
      },
    })
      .then(async (res) => {
        if (res.status !== 200 || (await res.clone().json()).detail === "Token has expired") {
          window.location.href = "/auth/logout";
          return;
        }
        return res.json();
      })
      .catch(console.error),
  getMember: async (accessToken: string): Promise<MemberResponse> =>
    fetch(`/api/member`, {
      method: "GET",
      headers: {
        "Authorization": accessToken,
      },
    })
      .then(async (res) => {
        if (res.status !== 200 || (await res.clone().json()).detail === "Token has expired") {
          window.location.href = "/auth/logout";
          return;
        }
        return res.json();
      })
      .catch(console.error),
  getExMember: async (accessToken: string): Promise<ExMemberResponse> =>
    fetch(`/api/exMember`, {
      method: "GET",
      headers: {
        "Authorization": accessToken,
      },
    })
      .then(async (res) => {
        if (res.status !== 200 || (await res.clone().json()).detail === "Token has expired") {
          window.location.href = "/auth/logout";
          return;
        }
        return res.json();
      })
      .catch(console.error),
  getAvailableProfiles: async (
    accessToken: string,
  ): Promise<AvailableProfilesResponse> =>
    fetch(`/api/oauth/availableProfiles`, {
      method: "GET",
      headers: {
        "Authorization": accessToken,
      },
    })
      .then(async (res) => {
        if (res.status !== 200 || (await res.clone().json()).detail === "Token has expired") {
          window.location.href = "/auth/logout";
          return;
        }
        return res.json();
      })
      .catch(console.error),
  getAvailableConnectionByProfileId: async (
    profileId: string,
    accessToken: string
  ): Promise<AvailableConnectionByProfileIdResponse> =>
    fetch(`/api/oauth/availableConnectionByProfileId?profile_id=${profileId}`, {
      method: "GET",
      headers: {
        "Authorization": accessToken,
      },
    })
      .then(async (res) => {
        if (res.status !== 200 || (await res.clone().json()).detail === "Token has expired") {
          window.location.href = "/auth/logout";
          return;
        }
        return res.json();
      })
      .catch(console.error),
  connectProfile: async (
    profileId: string,
    spLwaAccountId: string,
    accessToken: string
  ): Promise<ConnectProfileResponse> =>
    fetch(`/api/oauth/connectProfile?profile_id=${profileId}&sp_lwa_account_id=${spLwaAccountId}`, {
      method: "POST",
      headers: {
        "Authorization": accessToken,
        "Content-Type": "application/json",
      },
    })
      .then(async (res) => {
        if (res.status !== 200 || (await res.clone().json()).detail === "Token has expired") {
          window.location.href = "/auth/logout";
          return;
        }
        return res.json();
      })
      .catch(console.error),
  createStripeUserSession: async (
    userInfo: any,
    accessToken: string
  ): Promise<CreateOptimizationResponse> =>
    fetch(`/api/stripe/create_customer_session`, {
      method: "POST",
      headers: {
        "Authorization": accessToken,
        "Content-Type": "application/json",
      },
      body: JSON.stringify(userInfo),
    })
      .then(async (res) => {
        if (res.status !== 200 || (await res.clone().json()).detail === "Token has expired") {
          window.location.href = "/auth/logout";
          return;
        }
        return res.json();
      })
      .catch(console.error),
  createStripeCustomerPortalSession: async (
    userInfo: any,
    accessToken: string
  ): Promise<CreateOptimizationResponse> =>
    fetch(`/api/stripe/create_customer_portal_session`, {
      method: "POST",
      headers: {
        "Authorization": accessToken,
        "Content-Type": "application/json",
      },
      body: JSON.stringify(userInfo),
    })
      .then(async (res) => {
        if (res.status !== 200 || (await res.clone().json()).detail === "Token has expired") {
          window.location.href = "/auth/logout";
          return;
        }
        return res.json();
      })
      .catch(console.error),
  postProductAdsList: async (
    profileId: string,
    accessToken: string
  ): Promise<ProductAdsResponse> =>
    fetch(`/api/ads/sp/productAds/list?profile_id=${profileId}`, {
      method: "POST",
      headers: {
        "Authorization": accessToken,
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        maxResults: 10,
        nextToken: null,
        includeExtendedDataFields: true,
      }),
    })
      .then(async (res) => {
        if (res.status !== 200 || (await res.clone().json()).detail === "Token has expired") {
          window.location.href = "/auth/logout";
          return;
        }
        return res.json();
      })
      .catch(console.error),
  postProductMetadata: async (
    profileId: string,
    asins: string[],
    accessToken: string
  ): Promise<ProductMetadataResponse> =>
    fetch(`/api/ads/product/metadata?profile_id=${profileId}`, {
      method: "POST",
      headers: {
        "Authorization": accessToken,
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        asins: asins,
        pageIndex: 0,
        pageSize: 100,
        checkItemDetails: true,
      }),
    })
      .then(async (res) => {
        if (res.status !== 200 || (await res.clone().json()).detail === "Token has expired") {
          window.location.href = "/auth/logout";
          return;
        }
        return res.json();
      })
      .catch(console.error),
  getSPAdvertisedProducts: async (
    profileId: string,
    startDate: string,
    endDate: string,
    accessToken: string
  ): Promise<SPAdvertisedProductsResponse> =>
    fetch(
      `/api/ads/reporting/ads/sp_advertised_products?profile_id=${profileId}&start_date=${startDate}&end_date=${endDate}`,
      {
        method: "GET",
        headers: {
          "Authorization": accessToken,
          "Content-Type": "application/json",
        },
      }
    )
      .then(async (res) => {
        if (res.status !== 200 || (await res.clone().json()).detail === "Token has expired") {
          window.location.href = "/auth/logout";
          return;
        }
        return res.json();
      })
      .catch(console.error),
  getSPReportByDate: async (
    lwaAccountId: string,
    marketplaceId: string,
    startDate: string,
    endDate: string,
    accessToken: string
  ): Promise<SPReportByDateResponse> =>
    fetch(
      `/api/sp/reporting/sales_and_traffic_by_date?lwa_account_id=${lwaAccountId}&marketplace_id=${marketplaceId}&start_date=${startDate}&end_date=${endDate}`,
      {
        method: "GET",
        headers: {
          "Authorization": accessToken,
          "Content-Type": "application/json",
        },
      }
    )
      .then(async (res) => {
        if (res.status !== 200 || (await res.clone().json()).detail === "Token has expired") {
          window.location.href = "/auth/logout";
          return;
        }
        return res.json();
      })
      .catch(console.error),
  getSPReportByAsin: async (
    lwaAccountId: string,
    marketplaceId: string,
    startDate: string,
    endDate: string,
    accessToken: string
  ): Promise<SPReportByAsinResponse> =>
    fetch(
      `/api/sp/reporting/sales_and_traffic_by_asin?lwa_account_id=${lwaAccountId}&marketplace_id=${marketplaceId}&start_date=${startDate}&end_date=${endDate}`,
      {
        method: "GET",
        headers: {
          "Authorization": accessToken,
          "Content-Type": "application/json",
        },
      }
    )
      .then(async (res) => {
        if (res.status !== 200 || (await res.clone().json()).detail === "Token has expired") {
          window.location.href = "/auth/logout";
          return;
        }
        return res.json();
      })
      .catch(console.error),
  getJoinedReportByAsinAndDate: async (
    sellerPartnerId: string,
    marketplaceId: string,
    startDate: string,
    endDate: string,
    accessToken: string,
    signal: AbortSignal
  ): Promise<JoinedReportByAsinAndDateResponse> =>
    fetch(`/api/report/new_sales_and_traffic_per_asin_by_date_range?account_id=${sellerPartnerId}&marketplace_id=${marketplaceId}&start_date=${startDate}&end_date=${endDate}`, {
      method: "GET",
      headers: {
        "Authorization": accessToken,
        "Content-Type": "application/json",
      },
      signal: signal
    })
      .then(async (res) => {
        if (res.status !== 200 || (await res.clone().json()).detail === "Token has expired") {
          window.location.href = "/auth/logout";
          return;
        }
        return res.json();
      })
      .catch(console.error),
  getJoinedVendorReportByAsinAndDate: async (
    accountId: string,
    marketplaceId: string,
    startDate: string,
    endDate: string,
    accessToken: string,
    signal: AbortSignal
  ): Promise<JoinedReportByAsinAndDateResponse> =>
    fetch(`/api/report/vendor_sales_and_traffic_per_asin_by_date_range?account_id=${accountId}&marketplace_id=${marketplaceId}&start_date=${startDate}&end_date=${endDate}`, {
      method: "GET",
      headers: {
        "Authorization": accessToken,
        "Content-Type": "application/json",
      },
      signal: signal
    })
      .then(async (res) => {
        if (res.status !== 200 || (await res.clone().json()).detail === "Token has expired") {
          window.location.href = "/auth/logout";
          return;
        }
        return res.json();
      })
      .catch(console.error),
  getJoinedSellerData: async (
    accountId: string,
    marketplaceId: string,
    startDate: string,
    endDate: string,
    accessToken: string,
    signal: AbortSignal
  ): Promise<JoinedSellerDataResponse> =>
    fetch(`/api/report/seller_data?account_id=${accountId}&marketplace_id=${marketplaceId}&start_date=${startDate}&end_date=${endDate}`, {
      method: "GET",
      headers: {
        "Authorization": accessToken,
        "Content-Type": "application/json",
      },
      signal: signal
    })
      .then(async (res) => {
        if (res.status !== 200 || (await res.clone().json()).detail === "Token has expired") {
          window.location.href = "/auth/logout";
          return;
        }
        return res.json();
      })
      .catch(console.error),
  getJoinedVendorData: async (
    accountId: string,
    marketplaceId: string,
    startDate: string,
    endDate: string,
    accessToken: string,
    signal: AbortSignal
  ): Promise<JoinedVendorDataResponse> =>
    fetch(`/api/report/vendor_data?account_id=${accountId}&marketplace_id=${marketplaceId}&start_date=${startDate}&end_date=${endDate}`, {
      method: "GET",
      headers: {
        "Authorization": accessToken,
        "Content-Type": "application/json",
      },
      signal: signal
    })
      .then(async (res) => {
        if (res.status !== 200 || (await res.clone().json()).detail === "Token has expired") {
          window.location.href = "/auth/logout";
          return;
        }
        return res.json();
      })
      .catch(console.error),
  getJoinedSellerDailyPerformance: async (
    accountId: string,
    marketplaceId: string,
    startDate: string,
    endDate: string,
    targetAsin: string,
    optimizationSets: string,
    accessToken: string,
    signal: AbortSignal
  ): Promise<JoinedSellerDailyPerformanceResponse> =>
    fetch(`/api/report/seller_daily_performance?account_id=${accountId}&marketplace_id=${marketplaceId}&start_date=${startDate}&end_date=${endDate}&target_asin=${targetAsin}&optimization_sets=${optimizationSets}`, {
      method: "GET",
      headers: {
        "Authorization": accessToken,
        "Content-Type": "application/json",
      },
      signal: signal
    })
      .then(async (res) => {
        if (res.status !== 200 || (await res.clone().json()).detail === "Token has expired") {
          window.location.href = "/auth/logout";
          return;
        }
        return res.json();
      })
      .catch(console.error),
  getJoinedVendorDailyPerformance: async (
    accountId: string,
    marketplaceId: string,
    startDate: string,
    endDate: string,
    targetAsin: string,
    optimizationSets: string,
    accessToken: string,
    signal: AbortSignal
  ): Promise<JoinedVendorDailyPerformanceResponse> =>
    fetch(`/api/report/vendor_daily_performance?account_id=${accountId}&marketplace_id=${marketplaceId}&start_date=${startDate}&end_date=${endDate}&target_asin=${targetAsin}&optimization_sets=${optimizationSets}`, {
      method: "GET",
      headers: {
        "Authorization": accessToken,
        "Content-Type": "application/json",
      },
      signal: signal
    })
      .then(async (res) => {
        if (res.status !== 200 || (await res.clone().json()).detail === "Token has expired") {
          window.location.href = "/auth/logout";
          return;
        }
        return res.json();
      })
      .catch(console.error),
  getHourlyReport: async (
    accountId: string,
    marketplaceId: string,
    metrics: string,
    startDate: string,
    endDate: string,
    asin: string,
    campaignId: string,
    accessToken: string,
    signal: AbortSignal
  ): Promise<HourlyReportResponse> =>
    fetch(`/api/hourlyReport/hourly?account_id=${accountId}&marketplace_id=${marketplaceId}&metrics=${metrics}&start_date=${startDate}&end_date=${endDate}&asin=${asin}&campaign_id=${campaignId}`, {
      method: "GET",
      headers: {
        "Authorization": accessToken,
        "Content-Type": "application/json",
      },
      signal: signal
    })
      .then(async (res) => {
        if (res.status !== 200 || (await res.clone().json()).detail === "Token has expired") {
          window.location.href = "/auth/logout";
          return;
        }
        return res.json();
      })
      .catch(console.error),
  getHourlyReportByDate: async (
    accountId: string,
    marketplaceId: string,
    optimizationId: string | null,
    attributionWindow: number,
    accessToken: string,
    signal: AbortSignal
  ): Promise<HourlyReportByDateResponse> =>
    fetch(`/api/hourlyReport/hourly/by-date?account_id=${accountId}&marketplace_id=${marketplaceId}${optimizationId ? `&optimization_id=${optimizationId}` : ''}&attribution_window=${attributionWindow}`, {
      method: "GET",
      headers: {
        "Authorization": accessToken,
        "Content-Type": "application/json",
      },
      signal: signal
    })
      .then(async (res) => {
        if (res.status !== 200 || (await res.clone().json()).detail === "Token has expired") {
          window.location.href = "/auth/logout";
          return;
        }
        return res.json();
      })
      .catch(console.error),
  getHourlyReportToday: async (
    accountId: string,
    marketplaceId: string,
    optimizationId: string | null,
    attributionWindow: number,
    accessToken: string,
    signal: AbortSignal
  ): Promise<any> =>
    fetch(`/api/hourlyReport/hourly/today?account_id=${accountId}&marketplace_id=${marketplaceId}${optimizationId ? `&optimization_id=${optimizationId}` : ''}&attribution_window=${attributionWindow}`, {
      method: "GET",
      headers: {
        "Authorization": accessToken,
        "Content-Type": "application/json",
      },
      signal: signal,
    })
      .then(async (res) => {
        if (res.status !== 200 || (await res.clone().json()).detail === "Token has expired") {
          window.location.href = "/auth/logout";
          return;
        }
        return res.json();
      })
      .catch(console.error),
  getHourlyInventory: async (
    accountId: string,
    marketplaceId: string,
    optimizationId: string,
    accessToken: string,
    signal: AbortSignal
  ): Promise<any> =>
    fetch(`/api/hourlyReport/hourly/inventory?account_id=${accountId}&marketplace_id=${marketplaceId}&optimization_id=${optimizationId}`, {
      method: "GET",
      headers: {
        "Authorization": accessToken,
        "Content-Type": "application/json",
      },
      signal: signal,
    })
      .then(async (res) => {
        if (res.status !== 200 || (await res.clone().json()).detail === "Token has expired") {
          window.location.href = "/auth/logout";
          return;
        }
        return res.json();
      })
      .catch(console.error),
  getNewBudgetPacing: async (
    params: { optimization_ids: number[] } | { account_id: string; marketplace_id: string },
    accessToken: string,
    signal: AbortSignal
  ): Promise<BudgetPacingResponse[]> =>
    fetch(`/api/hourlyReport/new_budget_pacing`, {
      method: "POST",
      headers: {
        "Authorization": accessToken,
        "Content-Type": "application/json",
      },
      body: JSON.stringify(params),
      signal: signal,
    })
      .then(async (res) => {
        if (res.status !== 200 || (await res.clone().json()).detail === "Token has expired") {
          window.location.href = "/auth/logout";
          return;
        }
        return res.json();
      })
      .catch(console.error),
  getBudgetPacing: async (
    optimizationIds: number[],
    accessToken: string,
    signal: AbortSignal
  ): Promise<any> =>
    fetch(`/api/hourlyReport/budget_pacing`, {
      method: "POST",
      headers: {
        "Authorization": accessToken,
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ optimization_ids: optimizationIds }),
      signal: signal,
    })
      .then(async (res) => {
        if (res.status !== 200 || (await res.clone().json()).detail === "Token has expired") {
          window.location.href = "/auth/logout";
          return;
        }
        return res.json();
      })
      .catch(console.error),
  getBrandCompetitionOverview: async (
    accountId: string,
    marketplaceId: string,
    endDate: string,
    accountType: string,
    accessToken: string,
    signal: AbortSignal
  ): Promise<any[]> =>
    fetch(`/api/brandCompetition/overview?account_id=${accountId}&marketplace_id=${marketplaceId}&end_date=${endDate}&account_type=${accountType}`, {
      method: "GET",
      headers: {
        "Authorization": accessToken,
        "Content-Type": "application/json",
      },
      signal: signal
    })
      .then(async (res) => {
        if (res.status !== 200 || (await res.clone().json()).detail === "Token has expired") {
          window.location.href = "/auth/logout";
          return;
        }
        return res.json();
      })
      .catch(console.error),
  getBrandCompetitionWeeklyOverview: async (
    accountId: string,
    marketplaceId: string,
    accessToken: string,
    signal: AbortSignal
  ): Promise<any[]> =>
    fetch(`/api/brandCompetition/weekly/overview?account_id=${accountId}&marketplace_id=${marketplaceId}`, {
      method: "GET",
      headers: {
        "Authorization": accessToken,
        "Content-Type": "application/json",
      },
      signal: signal
    })
      .then(async (res) => {
        if (res.status !== 200 || (await res.clone().json()).detail === "Token has expired") {
          window.location.href = "/auth/logout";
          return;
        }
        return res.json();
      })
      .catch(console.error),
  getBrandCompetitionClickShareChart: async (
    accountId: string,
    marketplaceId: string,
    endDate: string,
    accountType: string,
    accessToken: string,
    signal: AbortSignal
  ): Promise<any[]> =>
    fetch(`/api/brandCompetition/clickShareChart?account_id=${accountId}&marketplace_id=${marketplaceId}&end_date=${endDate}&account_type=${accountType}`, {
      method: "GET",
      headers: {
        "Authorization": accessToken,
        "Content-Type": "application/json",
      },
      signal: signal
    })
      .then(async (res) => {
        if (res.status !== 200 || (await res.clone().json()).detail === "Token has expired") {
          window.location.href = "/auth/logout";
          return;
        }
        return res.json();
      })
      .catch(console.error),
  getBrandCompetitionClickShareTable: async (
    accountId: string,
    marketplaceId: string,
    date: string,
    searchTerm: string,
    page: number,
    pageSize: number,
    accountType: string,
    accessToken: string,
    signal: AbortSignal
  ): Promise<any[]> =>
    fetch(`/api/brandCompetition/clickShareTable?account_id=${accountId}&marketplace_id=${marketplaceId}&date=${date}&search_term=${searchTerm}&page=${page}&page_size=${pageSize}&account_type=${accountType}`, {
      method: "GET",
      headers: {
        "Authorization": accessToken,
        "Content-Type": "application/json",
      },
      signal: signal
    })
      .then(async (res) => {
        if (res.status !== 200 || (await res.clone().json()).detail === "Token has expired") {
          window.location.href = "/auth/logout";
          return;
        }
        return res.json();
      })
      .catch(console.error),
  getBrandCompetitionWeeklyClickShareTable: async (
    accountId: string,
    marketplaceId: string,
    date: string,
    searchTerm: string,
    page: number,
    pageSize: number,
    accountType: string,
    accessToken: string,
    signal: AbortSignal
  ): Promise<any[]> =>
    fetch(`/api/brandCompetition/weekly/clickShareTable?account_id=${accountId}&marketplace_id=${marketplaceId}&date=${date}&search_term=${searchTerm}&page=${page}&page_size=${pageSize}&account_type=${accountType}`, {
      method: "GET",
      headers: {
        "Authorization": accessToken,
        "Content-Type": "application/json",
      },
      signal: signal
    })
      .then(async (res) => {
        if (res.status !== 200 || (await res.clone().json()).detail === "Token has expired") {
          window.location.href = "/auth/logout";
          return;
        }
        return res.json();
      })
      .catch(console.error),
  getBrandCompetitionKeywordDetail: async (
    accountId: string,
    marketplaceId: string,
    date: string,
    searchTerm: string,
    accountType: string,
    accessToken: string,
    signal: AbortSignal
  ): Promise<any> =>
    fetch(`/api/brandCompetition/keywordDetail?account_id=${accountId}&marketplace_id=${marketplaceId}&date=${date}&search_term=${searchTerm}&account_type=${accountType}`, {
      method: "GET",
      headers: {
        "Authorization": accessToken,
        "Content-Type": "application/json",
      },
      signal: signal
    })
      .then(async (res) => {
        if (res.status !== 200 || (await res.clone().json()).detail === "Token has expired") {
          window.location.href = "/auth/logout";
          return;
        }
        return res.json();
      })
      .catch(console.error),
  getBrandCompetitionKeywordDetailProducts: async (
    accountId: string,
    marketplaceId: string,
    date: string,
    searchTerm: string,
    accountType: string,
    accessToken: string,
    signal: AbortSignal
  ): Promise<any> =>
    fetch(`/api/brandCompetition/keywordDetailProducts?account_id=${accountId}&marketplace_id=${marketplaceId}&date=${date}&search_term=${searchTerm}&account_type=${accountType}`, {
      method: "GET",
      headers: {
        "Authorization": accessToken,
        "Content-Type": "application/json",
      },
      signal: signal
    })
      .then(async (res) => {
        if (res.status !== 200 || (await res.clone().json()).detail === "Token has expired") {
          window.location.href = "/auth/logout";
          return;
        }
        return res.json();
      })
      .catch(console.error),
  getBrandCompetitionWeeklyKeywordDetail: async (
    accountId: string,
    marketplaceId: string,
    date: string,
    searchTerm: string,
    accessToken: string,
    signal: AbortSignal
  ): Promise<any> =>
    fetch(`/api/brandCompetition/weekly/keywordDetail?account_id=${accountId}&marketplace_id=${marketplaceId}&date=${date}&search_term=${searchTerm}`, {
      method: "GET",
      headers: {
        "Authorization": accessToken,
        "Content-Type": "application/json",
      },
      signal: signal
    })
      .then(async (res) => {
        if (res.status !== 200 || (await res.clone().json()).detail === "Token has expired") {
          window.location.href = "/auth/logout";
          return;
        }
        return res.json();
      })
      .catch(console.error),
  getBrandCompetitionWeeklyKeywordDetailProducts: async (
    accountId: string,
    marketplaceId: string,
    date: string,
    searchTerm: string,
    accessToken: string,
    signal: AbortSignal
  ): Promise<any> =>
    fetch(`/api/brandCompetition/weekly/keywordDetailProducts?account_id=${accountId}&marketplace_id=${marketplaceId}&date=${date}&search_term=${searchTerm}`, {
      method: "GET",
      headers: {
        "Authorization": accessToken,
        "Content-Type": "application/json",
      },
      signal: signal
    })
      .then(async (res) => {
        if (res.status !== 200 || (await res.clone().json()).detail === "Token has expired") {
          window.location.href = "/auth/logout";
          return;
        }
        return res.json();
      })
      .catch(console.error),
  getBrandCompetitionFunnelDataDetail: async (
    accountId: string,
    marketplaceId: string,
    date: string,
    accountType: string,
    accessToken: string,
    signal: AbortSignal
  ): Promise<any[]> =>
    fetch(`/api/brandCompetition/funnelDataDetail?account_id=${accountId}&marketplace_id=${marketplaceId}&date=${date}&account_type=${accountType}`, {
      method: "GET",
      headers: {
        "Authorization": accessToken,
        "Content-Type": "application/json",
      },
      signal: signal
    })
      .then(async (res) => {
        if (res.status !== 200 || (await res.clone().json()).detail === "Token has expired") {
          window.location.href = "/auth/logout";
          return;
        }
        return res.json();
      })
      .catch(console.error),
  getListOptimizations: async (
    sellerPartnerId: string,
    marketplaceId: string,
    accessToken: string,
    signal: AbortSignal
  ): Promise<ListOptimizationsResponse> =>
    fetch(`/api/optimization/list_optimizations?account_id=${sellerPartnerId}&marketplace_id=${marketplaceId}`, {
      method: "GET",
      headers: {
        "Authorization": accessToken,
        "Content-Type": "application/json",
      },
      signal: signal
    })
      .then(async (res) => {
        if (res.status !== 200 || (await res.clone().json()).detail === "Token has expired") {
          window.location.href = "/auth/logout";
          return;
        }
        return res.json();
      })
      .catch(console.error),
  getListOptimizationsForReport: async (
    sellerPartnerId: string,
    marketplaceId: string,
    accessToken: string,
    signal: AbortSignal
  ): Promise<ListOptimizationsResponse> =>
    fetch(`/api/report/list_optimizations?account_id=${sellerPartnerId}&marketplace_id=${marketplaceId}`, {
      method: "GET",
      headers: {
        "Authorization": accessToken,
        "Content-Type": "application/json",
      },
      signal: signal
    })
      .then(async (res) => {
        if (res.status !== 200 || (await res.clone().json()).detail === "Token has expired") {
          window.location.href = "/auth/logout";
          return;
        }
        return res.json();
      })
      .catch(console.error),
  getListOptimizationHistory: async (
    optimizationId: string,
    startDate: string,
    endDate: string,
    type: string,
    accessToken: string
  ): Promise<ListOptimizationsResponse> =>
    fetch(`/api/optimization/list_optimization_history?optimization_id=${optimizationId}&start_date=${startDate}&end_date=${endDate}&type=${type}`, {
      method: "GET",
      headers: {
        "Authorization": accessToken,
        "Content-Type": "application/json",
      }
    })
      .then(async (res) => {
        if (res.status !== 200 || (await res.clone().json()).detail === "Token has expired") {
          window.location.href = "/auth/logout";
          return;
        }
        return res.json();
      })
      .catch(console.error),
  getListAsinHistory: async (
    optimizationId: string,
    asin: string,
    startDate: string,
    endDate: string,
    type: string,
    accessToken: string
  ): Promise<ListOptimizationsResponse> =>
    fetch(`/api/optimization/list_asin_history?optimization_id=${optimizationId}&asin=${asin}&start_date=${startDate}&end_date=${endDate}&type=${type}`, {
      method: "GET",
      headers: {
        "Authorization": accessToken,
        "Content-Type": "application/json",
      }
    })
      .then(async (res) => {
        if (res.status !== 200 || (await res.clone().json()).detail === "Token has expired") {
          window.location.href = "/auth/logout";
          return;
        }
        return res.json();
      })
      .catch(console.error),
  getOptimizationBudgetSensitivitySummary: async (
    optimizationId: string,
    accessToken: string
  ): Promise<OptimizationBudgetSensitivitySummaryResponse> =>
    fetch(`/api/optimization/budget_sensitivity_summary?optimization_id=${optimizationId}`, {
      method: "GET",
      headers: {
        "Authorization": accessToken,
        "Content-Type": "application/json",
      }
    })
      .then(async (res) => {
        if (res.status !== 200 || (await res.clone().json()).detail === "Token has expired") {
          window.location.href = "/auth/logout";
          return;
        }
        return res.json();
      })
      .catch(console.error),
  getOptimizationBudgetSensitivityDetail: async (
    optimizationId: string,
    accessToken: string
  ): Promise<OptimizationBudgetSensitivityDetailResponse> =>
    fetch(`/api/optimization/budget_sensitivity_detail?optimization_id=${optimizationId}`, {
      method: "GET",
      headers: {
        "Authorization": accessToken,
        "Content-Type": "application/json",
      }
    })
      .then(async (res) => {
        if (res.status !== 200 || (await res.clone().json()).detail === "Token has expired") {
          window.location.href = "/auth/logout";
          return;
        }
        return res.json();
      })
      .catch(console.error),
  getListTargetCandidateAsins: async (
    sellerPartnerId: string,
    marketplaceId: string,
    accessToken: string,
    signal: AbortSignal
  ): Promise<ListTargetCandidateAsinsResponse> =>
    fetch(`/api/optimization/list_target_candidate_asins?account_id=${sellerPartnerId}&marketplace_id=${marketplaceId}`, {
      method: "GET",
      headers: {
        "Authorization": accessToken,
        "Content-Type": "application/json",
      },
      signal: signal
    })
      .then(async (res) => {
        if (res.status !== 200 || (await res.clone().json()).detail === "Token has expired") {
          window.location.href = "/auth/logout";
          return;
        }
        return res.json();
      })
      .catch(console.error),
  createOptimizationSet: async (
    optimizationSetInfo: any,
    sellerPartnerId: string,
    marketplaceId: string,
    accessToken: string
  ): Promise<CreateOptimizationResponse> =>
    fetch(`/api/optimization/create_optimization?account_id=${sellerPartnerId}&marketplace_id=${marketplaceId}`, {
      method: "POST",
      headers: {
        "Authorization": accessToken,
        "Content-Type": "application/json",
      },
      body: JSON.stringify(optimizationSetInfo),
    })
      .then(async (res) => {
        if (res.status !== 200 || (await res.clone().json()).detail === "Token has expired") {
          window.location.href = "/auth/logout";
          return;
        }
        return res.json();
      })
      .catch(console.error),
  editOptimizationSet: async (
    optimizationSetInfo: any,
    sellerPartnerId: string,
    marketplaceId: string,
    accessToken: string
  ): Promise<EditOptimizationResponse> =>
    fetch(`/api/optimization/edit_optimization?account_id=${sellerPartnerId}&marketplace_id=${marketplaceId}`, {
      method: "PUT",
      headers: {
        "Authorization": accessToken,
        "Content-Type": "application/json",
      },
      body: JSON.stringify(optimizationSetInfo),
    })
      .then(async (res) => {
        if (res.status !== 200 || (await res.clone().json()).detail === "Token has expired") {
          window.location.href = "/auth/logout";
          return;
        }
        return res.json();
      })
      .catch(console.error),
  pauseOptimizationSet: async (
    optimizationSetInfo: any,
    sellerPartnerId: string,
    marketplaceId: string,
    accessToken: string
  ): Promise<PauseOptimizationResponse> =>
    fetch(`/api/optimization/pause_optimization?account_id=${sellerPartnerId}&marketplace_id=${marketplaceId}`, {
      method: "PUT",
      headers: {
        "Authorization": accessToken,
        "Content-Type": "application/json",
      },
      body: JSON.stringify(optimizationSetInfo),
    })
      .then(async (res) => {
        if (res.status !== 200 || (await res.clone().json()).detail === "Token has expired") {
          window.location.href = "/auth/logout";
          return;
        }
        return res.json();
      })
      .catch(console.error),
  archiveOptimizationSet: async (
    optimizationSetInfo: any,
    sellerPartnerId: string,
    marketplaceId: string,
    accessToken: string
  ): Promise<PauseOptimizationResponse> =>
    fetch(`/api/optimization/archive_optimization?account_id=${sellerPartnerId}&marketplace_id=${marketplaceId}`, {
      method: "DELETE",
      headers: {
        "Authorization": accessToken,
        "Content-Type": "application/json",
      },
      body: JSON.stringify(optimizationSetInfo),
    })
      .then(async (res) => {
        if (res.status !== 200 || (await res.clone().json()).detail === "Token has expired") {
          window.location.href = "/auth/logout";
          return;
        }
        return res.json();
      })
      .catch(console.error),
  listNewCampaignsAndPausedAds: async (
    targetInfo: any,
    sellerPartnerId: string,
    marketplaceId: string,
    accessToken: string
  ): Promise<ListNewCampaignsAndPausedAdsResponse> =>
    fetch(`/api/optimization/list_new_campaigns_and_paused_ads?account_id=${sellerPartnerId}&marketplace_id=${marketplaceId}`, {
      method: "POST",
      headers: {
        "Authorization": accessToken,
        "Content-Type": "application/json",
      },
      body: JSON.stringify(targetInfo),
    })
      .then(async (res) => {
        if (res.status !== 200 || (await res.clone().json()).detail === "Token has expired") {
          window.location.href = "/auth/logout";
          return;
        }
        return res.json();
      })
      .catch(console.error),
  addOptimizationTargets: async (
    targetInfo: any,
    sellerPartnerId: string,
    marketplaceId: string,
    accessToken: string
  ): Promise<AddOptimizationTargetsResponse> =>
    fetch(`/api/optimization/add_targets?account_id=${sellerPartnerId}&marketplace_id=${marketplaceId}`, {
      method: "PUT",
      headers: {
        "Authorization": accessToken,
        "Content-Type": "application/json",
      },
      body: JSON.stringify(targetInfo),
    })
      .then(async (res) => {
        if (res.status !== 200 || (await res.clone().json()).detail === "Token has expired") {
          window.location.href = "/auth/logout";
          return;
        }
        return res.json();
      })
      .catch(console.error),
  changePassword: async (
    newPassword: string,
    accessToken: string
  ): Promise<ChangePasswordResponse> =>
    fetch(`/api/cognito/change-password`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "Authorization": accessToken,
      },
      body: JSON.stringify({ password: newPassword }),
    })
      .then(async (res) => {
        if (res.status !== 200 || (await res.clone().json()).detail === "Token has expired") {
          window.location.href = "/auth/logout";
          return;
        }
        return res.json();
      })
      .catch(console.error),
  skipPasswordChange: async (
    accessToken: string
  ): Promise<ChangePasswordResponse> =>
    fetch(`/api/cognito/skip-password-change`, {
      method: "GET",
      headers: {
        "Authorization": accessToken,
      },
    })
      .then(async (res) => {
        if (res.status !== 200 || (await res.clone().json()).detail === "Token has expired") {
          window.location.href = "/auth/logout";
          return;
        }
        return res.json();
      })
      .catch(console.error),
  reactivateAccount: async (
    accessToken: string,
    successUrl: string,
    failUrl: string
  ): Promise<ChangePasswordResponse> =>
    fetch(`/api/cognito/reactivate-account`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "Authorization": accessToken,
      },
      body: JSON.stringify({ success_url: successUrl, fail_url: failUrl }),
    })
      .then(async (res) => {
        if (res.status !== 200 || (await res.clone().json()).detail === "Token has expired") {
          window.location.href = "/auth/logout";
          return;
        }
        return res.json();
      })
      .catch(console.error),
  withdrawAccount: async (
    accessToken: string,
  ): Promise<WithDrawAccountResponse> =>
    fetch(`/api/cognito/withdraw`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "Authorization": accessToken,
      },
    })
      .then(async (res) => {
        if (res.status !== 200 || (await res.clone().json()).detail === "Token has expired") {
          window.location.href = "/auth/logout";
          return;
        }
        return res.json();
      })
      .catch(console.error),
  createTicket: async (
    ticketInfo: any,
    accessToken: string
  ): Promise<CreateTicketResponse> =>
    fetch(`/api/cs/createTicket`, {
      method: "POST",
      headers: {
        "Authorization": accessToken,
        "Content-Type": "application/json",
      },
      body: JSON.stringify(ticketInfo),
    })
      .then(async (res) => {
        if (res.status !== 200 || (await res.clone().json()).detail === "Token has expired") {
          window.location.href = "/auth/logout";
          return;
        }
        return res.json();
      })
      .catch(console.error),
  getTicketList: async (
    accessToken: string,
    signal: AbortSignal
  ): Promise<ListTicketsResponse> =>
    fetch(`/api/cs/getTicketList`, {
      method: "GET",
      headers: {
        "Authorization": accessToken,
        "Content-Type": "application/json",
      },
      signal: signal
    })
      .then(async (res) => {
        if (res.status !== 200 || (await res.clone().json()).detail === "Token has expired") {
          window.location.href = "/auth/logout";
          return;
        }
        return res.json();
      })
      .catch(console.error),
  getTicketDetail: async (
    ticketId: string,
    accessToken: string,
    signal: AbortSignal
  ): Promise<any> =>
    fetch(`/api/cs/getTicketDetail?ticket_id=${ticketId}`, {
      method: "GET",
      headers: {
        "Authorization": accessToken,
        "Content-Type": "application/json",
      },
      signal: signal,
    })
      .then(async (res) => {
        if (res.status !== 200 || (await res.clone().json()).detail === "Token has expired") {
          window.location.href = "/auth/logout";
          return;
        }
        return res.json();
      })
      .catch(console.error),
  sendMessage: async (
    messageInfo: any,
    accessToken: string
  ): Promise<any> =>
    fetch(`/api/cs/sendMessage`, {
      method: "POST",
      headers: {
        "Authorization": accessToken,
        "Content-Type": "application/json",
      },
      body: JSON.stringify(messageInfo),
    })
      .then(async (res) => {
        if (res.status !== 200 || (await res.clone().json()).detail === "Token has expired") {
          window.location.href = "/auth/logout";
          return;
        }
        return res.json();
      })
      .catch(console.error),
  uploadFile: async (
    fileInfo: any,
    accessToken: string
  ): Promise<any> =>
    fetch(`/api/cs/uploadFile`, {
      method: "POST",
      headers: {
        "Authorization": accessToken,
      },
      body: fileInfo,
    })
      .then(async (res) => {
        if (res.status !== 200 || (await res.clone().json()).detail === "Token has expired") {
          window.location.href = "/auth/logout";
          return;
        }
        return res.json();
      })
      .catch(console.error),
  getFaqList: async (
    accessToken: string,
    signal: AbortSignal,
    locale: string
  ): Promise<ListFaqsResponse> =>
    fetch(`/api/cs/getFaqList?locale=${locale}`, {
      method: "GET",
      headers: {
        "Authorization": accessToken,
        "Content-Type": "application/json",
      },
      signal: signal
    })
      .then(async (res) => {
        if (res.status !== 200 || (await res.clone().json()).detail === "Token has expired") {
          window.location.href = "/auth/logout";
          return;
        }
        return res.json();
      })
      .catch(console.error),
  getCollectionStatus: async (
    accountId: string,
    marketplaceId: string,
    accessToken: string
  ): Promise<CollectionStatusResponse> =>
    fetch(`/api/collectionStatus?account_id=${accountId}&marketplace_id=${marketplaceId}`, {
      method: "GET",
      headers: {
        "Authorization": accessToken,
        "Content-Type": "application/json",
      },
    })
      .then(async (res) => {
        if (res.status !== 200 || (await res.clone().json()).detail === "Token has expired") {
          window.location.href = "/auth/logout";
          return;
        }
        return res.json();
      })
      .catch(console.error),
   requestReportDownload: async (
      reportInfo: any,
      accessToken: string
      ): Promise<any> =>
     fetch(`/api/custom_report/create`, {
          method: "POST",
          headers: {
            "Authorization": accessToken,
          },
          body: JSON.stringify(reportInfo),
      })
        .then(async (res) => {
            if (res.status !== 200 && (await res.clone().json()).detail === "Token has expired") {
              window.location.href = "/auth/logout";
              return;
            }
            return res.json();
        })
        .catch(console.error),      
   getReportList: async (
       accountId: string,
       marketplaceId: string,
       accessToken: string,
       ): Promise<any> =>
            fetch(`/api/custom_report/get_report_list?account_id=${accountId}&marketplace_id=${marketplaceId}`, {
              method: "GET",
              headers: {
                "Authorization": accessToken,
                "Content-Type": "application/json",
              },
             })
            .then(async (res) => {
              // Handle non-200 responses better
              if (!res.ok) {
                const errorData = await res.text().catch(() => null);
                console.error(`Server error ${res.status}: ${errorData || res.statusText}`);
                throw new Error(`Server returned ${res.status}: ${errorData || res.statusText}`);
              }
              
              const data = await res.json();
              if (data.detail === "Token has expired") {
                window.location.href = "/auth/logout";
                return null;
              }
              return data;
            })
            .catch(error => {
              console.error("Error in getReportList:", error);
              // Return an empty reports array instead of null to avoid UI issues
              return { reports: [] };
            }),
    getReportPresignedUrl: async (        
        reportId: string,
        key : string,       
        accessToken: string,        
    ): Promise<any> =>
        fetch(`/api/custom_report/get_report_presigned_url?report_id=${reportId}&key=${key}`, {
          method: "GET",
          headers: {
            "Authorization": accessToken,
            "Content-Type": "application/json",
          },
        })
        .then(async (res) => {
          // Handle non-200 responses better
          if (!res.ok) {
            const errorData = await res.text().catch(() => null);
            console.error(`Server error ${res.status}: ${errorData || res.statusText}`);
            throw new Error(`Server returned ${res.status}: ${errorData || res.statusText}`);
          }
          
          const data = await res.json();
          if (data.detail === "Token has expired") {
            window.location.href = "/auth/logout";
            return null;
          }
          return data;
        })
        .catch(error => {
          console.error("Error in getReportPresignedUrl:", error);
          throw error; // Re-throw to allow caller to handle it
        }),          
  getListTargets: async (
    accountId: string,
    marketplaceId: string,
    campaignId: string,
    adGroupId: string,
    accessToken: string
  ): Promise<TargetsResponse[]> =>
    fetch(`/api/optimization/list_targets?account_id=${accountId}&marketplace_id=${marketplaceId}&campaign_id=${campaignId}&ad_group_id=${adGroupId}`, {
      method: "GET",
      headers: {
        "Authorization": accessToken,
        "Content-Type": "application/json",
      }
    })
      .then(async (res) => {
        if (res.status !== 200 || (await res.clone().json()).detail === "Token has expired") {
          window.location.href = "/auth/logout";
          return;
        }
        return res.json();
      })
      .catch(console.error),
  getListCampaignHistory: async (
    accountId: string,
    marketplaceId: string,
    campaignId: string,
    startDate: string,
    endDate: string,
    accessToken: string
  ): Promise<ListCampaignHistoryResponse> =>
    fetch(`/api/optimization/list_campaign_history?account_id=${accountId}&marketplace_id=${marketplaceId}&campaign_id=${campaignId}&start_date=${startDate}&end_date=${endDate}`, {
      method: "GET",
      headers: {
        "Authorization": accessToken,
        "Content-Type": "application/json",
      }
    })
      .then(async (res) => {
        if (res.status !== 200 || (await res.clone().json()).detail === "Token has expired") {
          window.location.href = "/auth/logout";
          return;
        }
        return res.json();
      })
      .catch(console.error),
  getListTargetHistory: async (
    accountId: string,
    marketplaceId: string,
    targetId: string,
    startDate: string,
    endDate: string,
    accessToken: string
  ): Promise<ListTargetHistoryResponse> =>
    fetch(`/api/optimization/list_target_history?account_id=${accountId}&marketplace_id=${marketplaceId}&target_id=${targetId}&start_date=${startDate}&end_date=${endDate}`, {
      method: "GET",
      headers: {
        "Authorization": accessToken,
        "Content-Type": "application/json",
      }
    })
      .then(async (res) => {
        if (res.status !== 200 || (await res.clone().json()).detail === "Token has expired") {
          window.location.href = "/auth/logout";
          return;
        }
        return res.json();
      })
      .catch(console.error),
  changeStatusOfTarget: async (
    accountId: string,
    marketplaceId: string,
    targetId: string,
    accessToken: string
  ): Promise<ListTargetHistoryResponse> =>
    fetch(`/api/optimization/pause_target?account_id=${accountId}&marketplace_id=${marketplaceId}&target_id=${targetId}`, {
      method: "GET",
      headers: {
        "Authorization": accessToken,
        "Content-Type": "application/json",
      }
    })
      .then(async (res) => {
        if (res.status !== 200 || (await res.clone().json()).detail === "Token has expired") {
          window.location.href = "/auth/logout";
          return;
        }
        return res.json();
      })
      .catch(console.error),
  getHourlyAdShare: async (
    targetMetric: string,
    accountId: string,
    marketplaceId: string,
    optimizationId: number | null,
    attributionWindow: number,
    accessToken: string,
    signal: AbortSignal
  ): Promise<any> => {
    let url = `/api/hourlyReport/hourly/ad-share?target_metric=${targetMetric}&account_id=${accountId}&marketplace_id=${marketplaceId}&attribution_window=${attributionWindow}`;
    if (optimizationId) {
      url += `&optimization_id=${optimizationId}`;
    }
    
    return fetch(url, {
      method: "GET",
      headers: {
        "Authorization": accessToken,
        "Content-Type": "application/json",
      },
      signal: signal
    })
      .then(async (res) => {
        if (res.status !== 200 || (await res.clone().json()).detail === "Token has expired") {
          window.location.href = "/auth/logout";
          return;
        }
        return res.json();
      })
      .catch(console.error);
  },
  getCampaignBudgetUsage: async (
    optimizationId: number,
    accessToken: string
  ): Promise<CampaignBudgetUsageResponse> =>
    fetch(`/api/hourlyReport/campaign-budget-usage?optimization_id=${optimizationId}`, {
      method: "GET",
      headers: {
        "Authorization": accessToken,
        "Content-Type": "application/json",
      },
    })
      .then(async (res) => {
        if (res.status !== 200 || (await res.clone().json()).detail === "Token has expired") {
          window.location.href = "/auth/logout";
          return;
        }
        return res.json();
      })
      .catch(console.error),
};
