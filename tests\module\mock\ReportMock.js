/// <reference types='cypress' />

class ReportMock {
    successWhenGetJoinedVendorData() {
        cy.intercept('GET', '/api/report/vendor_data?account_id=*&marketplace_id=*', {
          body: [
            {
                "asin":"B0032AMC6K",
                "status":"Active",
                "fulfillment_channel":null,
                "parent_asin":null,
                "item_name":"LG LT600P Genuine Replacement Refrigerator Water Filter, 1-Pack (LT600P/PC/PCS) by LG Canada, LT600P",
                "image":"https://m.media-amazon.com/images/I/2169l9kseDL._SS60_.jpg",
                "classification_rank":null,
                "classification_rank_title":null,
                "condition":null,
                "available_quantity":57,
                "unfulfillable_quantity":3,
                "reserved_quantity":null,
                "inbound_quantity":null,
                "currency":"CAD",
                "listing_price":43.99,
                "shipping_price":null,
                "fba_fee":null,
                "referral_fee":null,
                "return_fee":null,
                "refund_fee":null,
                "eligibility_status":"ELIGIBLE",
                "eligibility_status_list":null,
                "product_group":null,
                "product_size_tier":null,
                "shipping_weight":null,
                "item_volume":null,
                "merchant_inventory_quantity":null,
                "mfn_inventory_quantity":null,
                "afn_inventory_quantity":null,
                "account_type":"vendor",
                "selling_partner_id":"amzn1.vg.2048300",
                "open_purchase_order_units":18,
                "state":"ACTIVE",
                "estimated_fee":5.********,
                "vp_performance":{"manufacturing":{"ordered_revenue_amount":0,
                "ordered_units":0,
                "shipped_cogs_amount":0,
                "shipped_revenue_amount":0,
                "shipped_units":0,
                "customer_returns":0},
                "sourcing":{"shipped_cogs_amount":672.66,
                "shipped_revenue_amount":791.82,
                "shipped_units":18,
                "customer_returns":0},
                "glanceViews":0,
                "estimated_margin":573.662001,
                "estimated_fee":98.997999,
                "return_amount":0},
                "ad_performance":[]
            }
        ],
        }).as('successWhenGetJoinedVendorData');
      }

      successWhenGetJoinedVendorDailyPerformance(){
        cy.intercept('GET', '/api/report/vendor_daily_performance?account_id=*&marketplace_id=*&start_date=*&end_date=*&target_asin=*&optimization_sets=*', {
            body: {
                "2025-02-01T00:00:00-08:00": {
                    "sourcing": {
                        "shipped_cogs_amount": 95718.***********,
                        "shipped_revenue_amount": 92890.***********,
                        "shipped_units": 585,
                        "customer_returns": 44
                    },
                    "manufacturing": {
                        "ordered_revenue_amount": 93832.***********,
                        "ordered_units": 198,
                        "shipped_cogs_amount": 84837.***********,
                        "shipped_revenue_amount": 80734.58,
                        "shipped_units": 281,
                        "customer_returns": 42
                    },
                    "ad": {
                        "sd_impressions": 0,
                        "sd_clicks": 0,
                        "sd_cost": 0,
                        "sd_sales": 0,
                        "sd_sales_promoted_clicks": 0,
                        "sd_units_sold": 0,
                        "sd_impressions_views": 0,
                        "sd_cumulative_reach": 34602,
                        "sd_detail_page_views": 0,
                        "sd_new_to_brand_detail_page_views": 0,
                        "sd_new_to_brand_sales": 0,
                        "impressions": 0,
                        "clicks": 0,
                        "cost": 0,
                        "sales1d": 0,
                        "sales7d": 0,
                        "sales14d": 0,
                        "sales30d": 0,
                        "units_sold_same_sku1d": 0,
                        "units_sold_same_sku7d": 0,
                        "units_sold_same_sku14d": 0,
                        "units_sold_same_sku30d": 0,
                        "attributed_sales_same_sku1d": 0,
                        "attributed_sales_same_sku7d": 0,
                        "attributed_sales_same_sku14d": 0,
                        "attributed_sales_same_sku30d": 0
                    },
                    "estimated_margin": 69203.35836999997,
                    "estimated_fee": 19605.181630000006,
                    "return_amount": 6909.679999999999,
                    "glanceViews": 27773,
                    "ccogs_fee_rate": 6.228207954439998
                },
            },
        }).as('successWhenGetJoinedVendorDailyPerformance');
      }

      successWhenGetExMember(){
        cy.intercept('GET', '/api/exMember', {
            body: {
                "id":114,
                "company_id":null,
                "member_role":"CNS_Optapex",
                "advertiser_id":null,
                "use_yn":null,
                "created_datetime":"2025-02-05T02:14:43",
                "updated_datetime":"2025-02-05T02:14:43",
                "member_email":"<EMAIL>",
                "country_code":null,
                "company_role":null,
                "admin_member_id":null,
                "cognito_sub":"e4b874e8-40a1-70db-4e4f-451bc299d22e",
                "created_by":null,
                "updated_by":null,
                "amazon_accounts":[
                    {
                        "ad_account_id":"A1TCZ5EZWQ7Q0U",
                        "ad_account_name":"MEDIPICKME",
                        "sp_default_currency_code":"CAD",
                        "updated_by":16,
                        "ad_marketplace_id":"A2EUQ1WTGCTBG2",
                        "ad_account_sub_type":null,
                        "sp_default_language_code":"en_CA",
                        "updated_datetime":"2025-02-02T05:44:07",
                        "id":357,
                        "ad_profile_id":"****************",
                        "ad_account_valid_payment_method":true,
                        "sp_domain_name":"www.amazon.ca",
                        "ad_country_code":"CA",
                        "selling_partner_id":"A1TCZ5EZWQ7Q0U",
                        "status":"DONE",
                        "ad_currency_code":"CAD",
                        "sp_account_type":"seller",
                        "use_yn":"Y",
                        "ad_lwa_account_id":2,
                        "ad_daily_budget":**********,
                        "sp_marketplace_id":"A2EUQ1WTGCTBG2",
                        "sp_lwa_account_id":2,
                        "ad_timezone":"America/Los_Angeles",
                        "sp_country_code":"CA",
                        "created_by":16,
                        "advertiser_id":null,
                        "ad_account_type":"seller",
                        "sp_marketplace_name":"Amazon.ca",
                        "created_datetime":"2024-10-11T06:09:23"
                    },
                    {
                        "ad_account_id":"A1TCZ5EZWQ7Q0U",
                        "ad_account_name":"MEDIPICKME",
                        "sp_default_currency_code":"MXN",
                        "updated_by":16,
                        "ad_marketplace_id":"A1AM78C64UM0Y8",
                        "ad_account_sub_type":null,
                        "sp_default_language_code":"es_MX",
                        "updated_datetime":"2025-02-02T05:44:07",
                        "id":358,
                        "ad_profile_id":"****************",
                        "ad_account_valid_payment_method":true,
                        "sp_domain_name":"www.amazon.com.mx",
                        "ad_country_code":"MX",
                        "selling_partner_id":"A1TCZ5EZWQ7Q0U",
                        "status":"DONE",
                        "ad_currency_code":"MXN",
                        "sp_account_type":"seller",
                        "use_yn":"Y",
                        "ad_lwa_account_id":2,
                        "ad_daily_budget":**********,
                        "sp_marketplace_id":"A1AM78C64UM0Y8",
                        "sp_lwa_account_id":2,
                        "ad_timezone":"America/Los_Angeles",
                        "sp_country_code":"MX",
                        "created_by":16,
                        "advertiser_id":null,
                        "ad_account_type":"seller",
                        "sp_marketplace_name":"Amazon.com.mx",
                        "created_datetime":"2024-10-11T06:09:23"
                    },
                    {   
                        "ad_account_id":"A340LPUQ7DJ1RE",
                        "ad_account_name":"Salted.,LTD",
                        "sp_default_currency_code":"GBP",
                        "updated_by":15,
                        "ad_marketplace_id":"A1F83G8C2ARO7P",
                        "ad_account_sub_type":null,
                        "sp_default_language_code":"en_GB",
                        "updated_datetime":"2025-02-03T02:35:38",
                        "id":597,
                        "ad_profile_id":"***************",
                        "ad_account_valid_payment_method":true,
                        "sp_domain_name":"www.amazon.co.uk",
                        "ad_country_code":"UK",
                        "selling_partner_id":"A340LPUQ7DJ1RE",
                        "status":"IN_COLLECT",
                        "ad_currency_code":"GBP",
                        "sp_account_type":"seller",
                        "use_yn":"Y",
                        "ad_lwa_account_id":17,
                        "ad_daily_budget":**********,
                        "sp_marketplace_id":"A1F83G8C2ARO7P",
                        "sp_lwa_account_id":86,
                        "ad_timezone":"Europe/London",
                        "sp_country_code":"GB",
                        "created_by":15,
                        "advertiser_id":null,
                        "ad_account_type":"seller",
                        "sp_marketplace_name":"Amazon.co.uk",
                        "created_datetime":"2025-02-03T02:35:38"
                    }
                ]
            }
        }).as('successWhenGetExMember');
      }

    successwhenGetListOptimizationsForReport(){
        cy.intercept('GET', '/api/report/optimization?account_id=*', {
            body:[
                {
                    "id": 98,
                    "account_id": "ENTITYPHJJN8ZVVUKS",
                    "marketplace_id": "A2EUQ1WTGCTBG2",
                    "optimization_name": "LGE Canada Opt Set 1",
                    "ad_budget_type": "DATERANGE",
                    "ad_budget_amount": 14112,
                    "ad_budget_start_date": "2024-10-04",
                    "ad_budget_end_date": "2024-12-31",
                    "optimization_range": "SPPLUS",
                    "optimization_goal": "SALES",
                    "optimization_option": "NONE",
                    "optimization_target_type": "NONE",
                    "optimization_target_value": 0,
                    "bid_yn": "Y",
                    "use_yn": "Y",
                    "created_by": 4,
                    "creation_datetime": "2024-10-15T16:43:06",
                    "updated_by": 4,
                    "last_update_datetime": "2024-12-10T05:21:03",
                    "request_status": "DONE",
                    "portfolio_id": "**************",
                    "account_type": "vendor",
                    "selling_partner_id": "amzn1.vg.2048300",
                    "display_yn": "N",
                    "target_products": [
                        {
                            "asin": "B0B44DQ8NF"
                        },
                        {
                            "asin": "B0D3WJM5GM"
                        },
                        {
                            "asin": "B0D3WMJ1B2"
                        },
                        {
                            "asin": "B0D3WMK82B"
                        },
                        {
                            "asin": "B0D3WPVFBK"
                        }
                    ]
                },
                {
                    "id": 119,
                    "account_id": "ENTITYPHJJN8ZVVUKS",
                    "marketplace_id": "A2EUQ1WTGCTBG2",
                    "optimization_name": "LGE Optimization Set 2 ",
                    "ad_budget_type": "DATERANGE",
                    "ad_budget_amount": 32928,
                    "ad_budget_start_date": "2024-10-04",
                    "ad_budget_end_date": "2024-12-31",
                    "optimization_range": "ADONLY",
                    "optimization_goal": "REVENUE",
                    "optimization_option": "NONE",
                    "optimization_target_type": "NONE",
                    "optimization_target_value": 0,
                    "bid_yn": "Y",
                    "use_yn": "Y",
                    "created_by": 4,
                    "creation_datetime": "2024-10-30T05:52:56",
                    "updated_by": 4,
                    "last_update_datetime": "2024-12-10T05:21:04",
                    "request_status": "DONE",
                    "portfolio_id": "**************",
                    "account_type": "vendor",
                    "selling_partner_id": "amzn1.vg.2048300",
                    "display_yn": "N",
                    "target_products": [
                        {
                            "asin": "B0BRBPTCJW"
                        },
                        {
                            "asin": "B0D3WKV3X7"
                        },
                        {
                            "asin": "B0D3WL9QS3"
                        },
                        {
                            "asin": "B0D3WLBBQC"
                        },
                        {
                            "asin": "B0D3WVN6LH"
                        },
                        {
                            "asin": "B0D3WWGP6T"
                        }
                    ]
                }
            ]
        }).as('successwhenGetListOptimizationsForReport');
    }

    // New: align with frontend endpoint used by utils/api.getListOptimizationsForReport
    successWhenGetReportListOptimizations(){
        cy.intercept('GET', '/api/report/list_optimizations?account_id=*&marketplace_id=*', {
            body:[
                {
                    "id": 1,
                    "account_id": "acc_vendor_1",
                    "marketplace_id": "ATVPDKIKX0DER",
                    "optimization_name": "Report OptSet 1",
                    "ad_budget_type": "DATERANGE",
                    "ad_budget_amount": 10000,
                    "ad_budget_start_date": "2025-01-01",
                    "ad_budget_end_date": "2025-12-31",
                    "optimization_range": "DEFAULT",
                    "optimization_goal": "SALES",
                    "optimization_option": "NONE",
                    "optimization_target_type": "NONE",
                    "optimization_target_value": 0,
                    "bid_yn": "Y",
                    "use_yn": "Y",
                    "created_by": 1,
                    "creation_datetime": "2025-01-01T00:00:00",
                    "updated_by": 1,
                    "last_update_datetime": "2025-01-02T00:00:00",
                    "request_status": "DONE",
                    "portfolio_id": "pf_1",
                    "account_type": "vendor",
                    "display_yn": "Y",
                    "target_products": [ { "asin": "B0032AMC6K" } ]
                }
            ]
        }).as('successWhenGetReportListOptimizations');
    }
}
export default ReportMock;