import { ErrorResponse } from "@/app/api/_models";
import { getServerApiHostUrl } from "@/utils/host";
import { NextRequest, NextResponse } from "next/server";

export type BrandCompetitionWeeklyClickShareTableResponse = any;

export async function GET(
  request: NextRequest
): Promise<NextResponse<BrandCompetitionWeeklyClickShareTableResponse | ErrorResponse>> {
  const accessToken = request.headers.get("Authorization");
  if (!accessToken) {
    return NextResponse.json(
      { message: "Authorization header is missing" },
      { status: 400 }
    );
  }
  const accountId = request.nextUrl.searchParams.get("account_id")
  if (!accountId) {
    return NextResponse.json(
      { message: "account_id query is missing" },
      { status: 400 }
    );
  }
  const marketplaceId = request.nextUrl.searchParams.get("marketplace_id")
  if (!marketplaceId) {
    return NextResponse.json(
      { message: "marketplace_id query is missing" },
      { status: 400 }
    );
  }
  const date = request.nextUrl.searchParams.get("date")
  const searchTerm = request.nextUrl.searchParams.get("search_term")
  const page = request.nextUrl.searchParams.get("page")
  const pageSize = request.nextUrl.searchParams.get("page_size")
  const accountType = request.nextUrl.searchParams.get("account_type")
  const brandCompetitionWeeklyClickShareTableResponse = await fetch(
    `${await getServerApiHostUrl()}/api/brand-competition/weekly/click-share-table?account_id=${accountId}&marketplace_id=${marketplaceId}&date=${date}&search_term=${searchTerm}&page=${page}&page_size=${pageSize}&account_type=${accountType}`,
    {
      method: "GET",
      headers: {
        "Authorization": `Bearer ${accessToken}`,
        "Content-Type": "application/json",
      },
      cache: "force-cache",
      next: { revalidate: 600 }, // 10 minutes
    }
  ).then((res) => res.json());

  return NextResponse.json(brandCompetitionWeeklyClickShareTableResponse, { status: 200 });
}
