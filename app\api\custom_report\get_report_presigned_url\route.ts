import { ErrorResponse } from "@/app/api/_models";
import { getServerApiHostUrl } from "@/utils/host";
import { NextRequest, NextResponse } from "next/server";

export interface PresignedUrlResponse {
  url: string;
  expires_in?: number;
  success: boolean;
}

export type CustomErrorResponse = ErrorResponse & {
  detail?: string;
};

export async function GET(
  request: NextRequest
): Promise<NextResponse<PresignedUrlResponse | CustomErrorResponse>> {
  const accessToken = request.headers.get("Authorization");
  if (!accessToken) {
    return NextResponse.json(
      { message: "Authorization header is missing" },
      { status: 400 }
    );
  }
  
  const reportId = request.nextUrl.searchParams.get("report_id");
  if (!reportId) {
    return NextResponse.json(
      { message: "report_id parameter is missing" },
      { status: 400 }
    );
  }

  const key = request.nextUrl.searchParams.get("key") || "raw";

  try {    
    const presignedUrlResponse = await fetch(
      `${await getServerApiHostUrl()}/api/custom_report/get_report_presigned_url?report_id=${reportId}&key=${key}`,
      {
        method: "GET",
        headers: {
          "Authorization": `Bearer ${accessToken}`,
          "Content-Type": "application/json",
        },
        cache: "no-store",
      }
    );

    if (!presignedUrlResponse.ok) {
      const errorData = await presignedUrlResponse.json();
      if (errorData.detail === "Token has expired") {
        return NextResponse.json(
          { detail: "Token has expired" } as CustomErrorResponse,
          { status: 401 }
        );
      }
      return NextResponse.json(
        { message: "Failed to get presigned URL", error: errorData } as CustomErrorResponse,
        { status: presignedUrlResponse.status }
      );
    }

    const result = await presignedUrlResponse.json();
    return NextResponse.json(result, { status: 200 });
  } catch (error) {
    console.error("Error getting presigned URL:", error);
    return NextResponse.json(
      { message: "Internal server error", error: String(error) } as CustomErrorResponse,
      { status: 500 }
    );
  }
}