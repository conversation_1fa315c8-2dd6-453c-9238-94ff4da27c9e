import React from 'react'
import { render, screen } from '@testing-library/react'
import '@testing-library/jest-dom'
import userEvent from '@testing-library/user-event'
import { describe, test, expect } from '@jest/globals'
import { Input } from '@/components/ui/input'

describe('components/ui/Input', () => {
  test('renders with placeholder and accepts typing', async () => {
    const user = userEvent.setup()
    render(<Input placeholder="Your name" />)
    const el = screen.getByPlaceholderText('Your name') as HTMLInputElement
    expect(el).toBeInTheDocument()
    await user.type(el, 'MOP')
    expect(el.value).toBe('MOP')
  })
}) 