@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --foreground-rgb: 0, 0, 0;
    --background-start-rgb: 214, 219, 220;
    --background-end-rgb: 255, 255, 255;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;

    --radius: 0.5rem;
  }
}

/* body {
  color: rgb(var(--foreground-rgb));
  background: linear-gradient(
      to bottom,
      transparent,
      rgb(var(--background-end-rgb))
    )
    rgb(var(--background-start-rgb));
} */

@layer base {
  * {
    @apply border-border;
  }
  html, body {
    @apply size-full;
  }
  body {
    @apply bg-background text-foreground;
  }
}

.dashboard-date-range.react-datepicker {
  border: none;
  margin-top: -6px;
  margin-left: 40px;
  @apply shadow-lg ring-1 ring-black/5 rounded-lg py-1 px-0;
}
.dashboard-date-range .react-datepicker__header {
  background-color: transparent;
  border-bottom: none;
  padding-top: 8px;
  padding-bottom: 0; 
}
.react-datepicker-popper {
  z-index: 2;
}
.react-datepicker__day--in-selecting-range:not(.react-datepicker__day--in-range, .react-datepicker__month-text--in-range, .react-datepicker__quarter-text--in-range, .react-datepicker__year-text--in-range), .react-datepicker__month-text--in-selecting-range:not(.react-datepicker__day--in-range, .react-datepicker__month-text--in-range, .react-datepicker__quarter-text--in-range, .react-datepicker__year-text--in-range), .react-datepicker__quarter-text--in-selecting-range:not(.react-datepicker__day--in-range, .react-datepicker__month-text--in-range, .react-datepicker__quarter-text--in-range, .react-datepicker__year-text--in-range), .react-datepicker__year-text--in-selecting-range:not(.react-datepicker__day--in-range, .react-datepicker__month-text--in-range, .react-datepicker__quarter-text--in-range, .react-datepicker__year-text--in-range) {
  background-color: rgba(191, 219, 254, 0.3) !important;
}
.react-datepicker__day--keyboard-selected, .react-datepicker__month-text--keyboard-selected, .react-datepicker__quarter-text--keyboard-selected, .react-datepicker__year-text--keyboard-selected {
  background-color: white !important;
  border-radius: 100% !important;
  /* border: 2px solid #60a5fa !important; */
  @apply ring-2 ring-blue-400;
}
.react-datepicker__day--selected, .react-datepicker__day--in-selecting-range, .react-datepicker__day--in-range, .react-datepicker__month-text--selected, .react-datepicker__month-text--in-selecting-range, .react-datepicker__month-text--in-range, .react-datepicker__quarter-text--selected, .react-datepicker__quarter-text--in-selecting-range, .react-datepicker__quarter-text--in-range, .react-datepicker__year-text--selected, .react-datepicker__year-text--in-selecting-range, .react-datepicker__year-text--in-range {
  background-color: rgba(191, 219, 254, 0.5) !important;
  border-radius: 100% !important;
  @apply !text-blue-900;
}
.react-datepicker__day:hover, .react-datepicker__month-text:hover, .react-datepicker__quarter-text:hover, .react-datepicker__year-text:hover {
  border-radius: 100% !important;
}
.dashboard-date-range .react-datepicker__day, .dashboard-date-range .react-datepicker__time-name {
  @apply text-gray-500 text-xs;
  line-height: 1.7rem;
}
.react-datepicker__day--disabled, .react-datepicker__month-text--disabled, .react-datepicker__quarter-text--disabled, .react-datepicker__year-text--disabled {
  @apply !text-gray-300 !cursor-not-allowed;
}
.react-datepicker__day-name {
  @apply !text-gray-300 text-xs font-semibold;
}
.react-datepicker__current-month, .react-datepicker-time__header, .react-datepicker-year-header {
  margin-bottom: 4px;
  @apply !text-gray-500 !text-sm;
}
.react-datepicker__navigation-icon::before {
  border-width: 2px 2px 0 0 !important;
  @apply border-gray-400;
}
.react-datepicker__navigation:hover *::before {
  @apply border-gray-500;
}
.react-datepicker__navigation {
  top: 8px !important;
}
.react-datepicker__triangle {
  display: none;
}