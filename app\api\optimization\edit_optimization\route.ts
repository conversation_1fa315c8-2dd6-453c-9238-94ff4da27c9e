import { ErrorResponse } from "@/app/api/_models";
import { getServerApiHostUrl } from "@/utils/host";
import { NextRequest, NextResponse } from "next/server";

export type EditOptimizationResponse = any;

export async function PUT(
  request: NextRequest
): Promise<NextResponse<EditOptimizationResponse | ErrorResponse>> {
  const accessToken = request.headers.get("Authorization");
  const body = await request.json();

  if (!accessToken) {
    return NextResponse.json(
      { message: "Authorization header is missing" },
      { status: 400 }
    );
  }
  const accountId = request.nextUrl.searchParams.get("account_id")
  if (!accountId) {
    return NextResponse.json(
      { message: "account_id query is missing" },
      { status: 400 }
    );
  }
  const marketplaceId = request.nextUrl.searchParams.get("marketplace_id")
  if (!marketplaceId) {
    return NextResponse.json(
      { message: "marketplace_id query is missing" },
      { status: 400 }
    );
  }
  const editOptimizationsResponse = await fetch(
    `${await getServerApiHostUrl()}/api/optimization/edit_optimization?account_id=${accountId}&marketplace_id=${marketplaceId}`,
    {
      method: "PUT",
      headers: {
        "Authorization": `Bearer ${accessToken}`,
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        "optimization_id": body?.optimization_id,
        "optimization_name": body?.optimization_name,
        // "target_products": body?.target_products,
        "ad_budget_type": body?.ad_budget_type,
        "ad_budget_amount": body?.ad_budget_amount,
        "ad_budget_start_date": body?.ad_budget_start_date,
        "ad_budget_end_date": body?.ad_budget_end_date,
        "optimization_range": body?.optimization_range,
        "optimization_goal": body?.optimization_goal,
        "optimization_option": body?.optimization_option,
        "optimization_target_type": body?.optimization_target_type,
        "optimization_target_value": body?.optimization_target_value,
        "display_yn": body?.display_yn,
        "competition_option" : body?.competition_option,
        "target_same_sku_only_yn": body?.target_same_sku_only_yn,
        "limit_cpc" : body?.limit_cpc,       
      }),
      cache: "no-cache",
    }
  ).then((res) => res.json());

  return NextResponse.json(editOptimizationsResponse, { status: 200 });
}
