import { ErrorResponse } from "@/app/api/_models";
import { getServerApiHostUrl } from "@/utils/host";
import { NextRequest, NextResponse } from "next/server";

export type OAuthCodeToTokenDataResponse = {
  access_token: string;
  refresh_token: string;
};

export async function GET(
  request: NextRequest
): Promise<NextResponse<OAuthCodeToTokenDataResponse | ErrorResponse>> {
  const oauthCode = request.headers.get("X-Lwa-OAuth-Code");
  if (!oauthCode) {
    return NextResponse.json(
      { message: "X-Lwa-OAuth-Code header is missing" },
      { status: 400 }
    );
  }

  const oauthLoginResponse = await fetch(
    `${await getServerApiHostUrl()}/api/oauth/token`,
    {
      method: "GET",
      headers: {
        "X-Lwa-OAuth-Code": oauthCode,
      },
      cache: "no-cache",
    }
  ).then((res) => res.json());
  // TODO: redirectUrl을 클라에서 주입하도록 수정
  
  return NextResponse.json(oauthLoginResponse, { status: 200 });
}
