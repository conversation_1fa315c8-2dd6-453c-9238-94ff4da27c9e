"use client"

import Plot from "react-plotly.js"
import { formatDate } from "@/utils/msc"

type GraphData = {
  date: string;
  value: number;
}

export default function PaceGraph({ data }: { data: GraphData[] }) {
  const dataSet = []
  dataSet.push({
    x: data.map((d) => d.date),
    y: data.map((d) => data.slice(0, data.indexOf(d) + 1).reduce((acc, cur) => acc + cur.value, 0)),
    customdata: data.map((d) => formatDate(d.date, '.')),
    type: 'scatter',
    mode: 'lines',
		line: {shape: 'spline', smoothing: 1.3},
    xperiodalignment: 'start',
    fill: 'tozeroy',
    fillgradient: {
      colorscale: [
        [0, 'rgba(59, 130, 246, 0.1)'],
        [1, 'rgba(59, 130, 246, 0.6)'],
      ],
      type: 'vertical',
    },
    marker: {
			color: '#3B82F6',
		},
    name: 'Total Sales',
  })
  return (
    <Plot
      data={dataSet as any}
      layout={{
        margin: {
          l: 0,
          r: 0,
          b: 0,
          t: 0,
          pad: 0
        },
				paper_bgcolor: 'rgba(0,0,0,0)',
				plot_bgcolor: 'rgba(0,0,0,0)',
        autosize: true,
        showlegend: false,
        xaxis: {
					visible: false,
          tickfont: {
            size: 10,
            color: '#9CA3AF'
          },
          tickformat: '%y.%m.%d',
          zerolinecolor: '#e5e7eb',
          tickmode: 'auto',
          nticks: data.length > 10 ? 10 : data.length,
        },
        yaxis: {
					visible: false,
          tickformat: '$,',
          tickfont: {
            size: 10,
            color: '#9CA3AF'
          },
          gridcolor: '#f3f4f6',
          zerolinecolor: '#e5e7eb',
          automargin: true,
        },
        hovermode: 'x unified',
        hoverlabel: {
          bgcolor: 'rgba(17, 24, 39, 0.9)',
          font: {
            size: 10,
            color: '#e5e7eb'
          },
        },
        dragmode: false,
      }}
      config={{
        displayModeBar: false,
      }}
      useResizeHandler={true}
      className="w-full h-full"
    />
  )
}
