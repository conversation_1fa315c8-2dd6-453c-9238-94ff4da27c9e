describe('Login', () => {
  it.guide('처음 접속하면, EULA 내용과 "Sign in" 버튼이 있다.', {
    actionFunc: () => {
      cy.visit('/eula');
    },
    assertFunc: () => {
      cy.get('h1.font-bold').should('be.exist');
      cy.get('h1.font-bold').contains('END USER LISCENSE AGREEMENT');
      cy.get('header form[method="POST"] > button').should('be.exist');
      
    },
  });

  it.guide('"Sign In" 버튼을 클릭하면, email/password 입력창이 있다.', {
    actionFunc: () => {
      cy.visit('/eula');
      cy.get('header form[method="POST"] > button').click({ force: true });
    },
    assertFunc: () => {
      cy.get('input[name="username"]').should('be.exist');
      cy.get('input[name="password"]').should('be.exist');
    },
  });
});
