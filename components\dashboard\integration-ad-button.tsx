"use client";

import actions from "@/actions";
import { cn } from "@/utils/msc";
import { PlusIcon } from "@heroicons/react/20/solid";

interface IntegrateButtonProps extends React.HTMLAttributes<HTMLDivElement> {
  isLNBExpanded?: boolean;
  buttonClassName?: string;
}

export default function IntegrateAdButton({
  isLNBExpanded,
  buttonClassName,
  ...props
}: IntegrateButtonProps) {
  const handleClick = async () => {
    const oauthLink = await actions.oauth.getAdLoginLink();
    window.location.href = oauthLink.url;
  };

  return (
    <button className={cn(buttonClassName)} onClick={handleClick}>
      <PlusIcon
        className="flex-shrink-0 h-5 w-5 text-blue-400 group-hover:text-blue-600"
        aria-hidden="true"
      />
      {isLNBExpanded ? (
        <span className="flex-shrink-0 block text-blue-400 group-hover:text-blue-600 font-semibold">
          Integrate AD
        </span>
      ) : (
        ""
      )}
    </button>
  );
}
