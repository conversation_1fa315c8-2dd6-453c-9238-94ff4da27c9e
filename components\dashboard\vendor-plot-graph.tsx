"use client"

import Plot from "react-plotly.js"
import { formatUTCDate } from "@/utils/msc"
import { getCurrencySymbol, getCurrencySymbolUnicode } from "@/utils/currency"

type GraphData = {
  date: Date;
	ad_sales: number,
  ad_sales_same_sku: number,
	ad_cost: number,
	clicks: number,
  impressions: number,
	glance_views: number,
	sourcing_shipped_cogs_amount: number,
	sourcing_shipped_revenue_amount: number,
  roas: number,
  sd_cost: number;
  sd_sales: number;
  sd_sales_promoted_clicks: number;
  sd_clicks: number;
  sd_impressions: number;
  sd_roas: number;
}

export default function VendorPlotGraph({ selectedLegend, data, comparedData, currencyCode = 'USD' }: { selectedLegend: any[], data: GraphData[], comparedData?: GraphData[], currencyCode?: string }) {
  const dataSet = []
  const currencySymbol = getCurrencySymbol(currencyCode)
  const currencySymbolUnicode = getCurrencySymbolUnicode(currencyCode)
  const currencyPrefix = currencySymbolUnicode === currencySymbol ? `${currencySymbol} ` : `${currencySymbolUnicode} `
  
  const revenueSelected = selectedLegend.find((legend) => legend.type === 'revenue')
  revenueSelected && dataSet.push({
    x: data.map((d) => formatUTCDate(d.date)),
    y: data.map((d) => d.sourcing_shipped_revenue_amount),
    customdata: data.map((d) => formatUTCDate(d.date, '.')),
    type: 'scatter',
    mode: 'lines+markers',
    xperiodalignment: 'start',
    hovertemplate: `%{fullData.name}: ${currencyPrefix}%{y:,.0f} (%{customdata})<extra></extra>`,
    marker: {color: '#a855f7'},
    name: revenueSelected.name,
  })
  const comparedRevenueSelected = selectedLegend.find((legend) => legend.type === 'compared-revenue')
  comparedData && comparedRevenueSelected && dataSet.push({
    x: data.map((d) => formatUTCDate(d.date)),
    y: comparedData.map((d) => d.sourcing_shipped_revenue_amount),
    customdata: comparedData.map((d) => formatUTCDate(d.date, '.')),
    type: 'scatter',
    mode: 'lines+markers',
    line: {
      dash: 'dash',
    },
    xperiodalignment: 'start',
    hovertemplate: `%{fullData.name}: ${currencyPrefix}%{y:,.0f} (%{customdata})<extra></extra>`,
    marker: {
      color: 'rgba(168, 85, 247, 0.8)',
    },
    name: comparedRevenueSelected.name,
  })
  const cogsRevenueSelected = selectedLegend.find((legend) => legend.type === 'cogs-revenue')
  cogsRevenueSelected && dataSet.push({
    x: data.map((d) => formatUTCDate(d.date)),
    y: data.map((d) => d.sourcing_shipped_cogs_amount),
    customdata: data.map((d) => formatUTCDate(d.date, '.')),
    type: 'scatter',
    mode: 'lines+markers',
    xperiodalignment: 'start',
    hovertemplate: `%{fullData.name}: ${currencyPrefix}%{y:,.0f} (%{customdata})<extra></extra>`,
    marker: {color: '#6366f1'},
    name: cogsRevenueSelected.name,
  })
  const comparedCogsRevenueSelected = selectedLegend.find((legend) => legend.type === 'compared-cogs-revenue')
  comparedData && comparedCogsRevenueSelected && dataSet.push({
    x: data.map((d) => formatUTCDate(d.date)),
    y: comparedData.map((d) => d.sourcing_shipped_cogs_amount),
    customdata: comparedData.map((d) => formatUTCDate(d.date, '.')),
    type: 'scatter',
    mode: 'lines+markers',
    line: {
      dash: 'dash',
    },
    xperiodalignment: 'start',
    hovertemplate: `%{fullData.name}: ${currencyPrefix}%{y:,.0f} (%{customdata})<extra></extra>`,
    marker: {
      color: 'rgba(99, 102, 241, 0.8)',
    },
    name: comparedCogsRevenueSelected.name,
  })
  const glanceViewsSelected = selectedLegend.find((legend) => legend.type === 'glance-views')
  glanceViewsSelected && dataSet.push({
    x: data.map((d) => formatUTCDate(d.date)),
    y: data.map((d) => d.glance_views),
    customdata: data.map((d) => formatUTCDate(d.date, '.')),
    yaxis: 'y2',
    type: 'scatter',
    mode: 'lines+markers',
    xperiodalignment: 'start',
    hovertemplate: '%{fullData.name}: %{y} (%{customdata})<extra></extra>',
    marker: {color: '#f59e0b'},
    name: glanceViewsSelected.name,
  })
  const comparedGlanceViewsSelected = selectedLegend.find((legend) => legend.type === 'compared-glance-views')
  comparedData && comparedGlanceViewsSelected && dataSet.push({
    x: data.map((d) => formatUTCDate(d.date)),
    y: comparedData.map((d) => d.glance_views),
    customdata: comparedData.map((d) => formatUTCDate(d.date, '.')),
    yaxis: 'y2',
    type: 'scatter',
    mode: 'lines+markers',
    line: {
      dash: 'dash',
    },
    xperiodalignment: 'start',
    hovertemplate: '%{fullData.name}: %{y} (%{customdata})<extra></extra>',
    marker: {
      color: 'rgba(245, 158, 11, 0.8)',
    },
    name: comparedGlanceViewsSelected.name,
  })
  const adCostSelected = selectedLegend.find((legend) => legend.type === 'ad-cost')
  adCostSelected && dataSet.push({
    x: data.map((d) => formatUTCDate(d.date)),
    y: data.map((d) => d.ad_cost),
    customdata: data.map((d) => formatUTCDate(d.date, '.')),
    type: 'scatter',
    mode: 'lines+markers',
    xperiodalignment: 'start',
    hovertemplate: `%{fullData.name}: ${currencyPrefix}%{y:,.0f} (%{customdata})<extra></extra>`,
    marker: {color: '#1d4ed8'},
    name: adCostSelected.name,
  })
  const comparedAdCostSelected = selectedLegend.find((legend) => legend.type === 'compared-ad-cost')
  comparedData && comparedAdCostSelected && dataSet.push({
    x: data.map((d) => formatUTCDate(d.date)),
    y: comparedData.map((d) => d.ad_cost),
    customdata: comparedData.map((d) => formatUTCDate(d.date, '.')),
    type: 'scatter',
    mode: 'lines+markers',
    line: {
      dash: 'dash',
    },
    xperiodalignment: 'start',
    hovertemplate: `%{fullData.name}: ${currencyPrefix}%{y:,.0f} (%{customdata})<extra></extra>`,
    marker: {
      color: 'rgba(29, 78, 216, 0.8)',
    },
    name: comparedAdCostSelected.name,
  })
  const adSalesSelected = selectedLegend.find((legend) => legend.type === 'ad-sales')
  adSalesSelected && dataSet.push({
    x: data.map((d) => formatUTCDate(d.date)),
    y: data.map((d) => d.ad_sales),
    customdata: data.map((d) => formatUTCDate(d.date, '.')),
    type: 'scatter',
    mode: 'lines+markers',
    xperiodalignment: 'start',
    hovertemplate: `%{fullData.name}: ${currencyPrefix}%{y:,.0f} (%{customdata})<extra></extra>`,
    marker: {color: '#6366f1'},
    name: adSalesSelected.name,
  })
  const comparedAdSalesSelected = selectedLegend.find((legend) => legend.type === 'compared-ad-sales')
  comparedData && comparedAdSalesSelected && dataSet.push({
    x: data.map((d) => formatUTCDate(d.date)),
    y: comparedData.map((d) => d.ad_sales),
    customdata: comparedData.map((d) => formatUTCDate(d.date, '.')),
    type: 'scatter',
    mode: 'lines+markers',
    line: {
      dash: 'dash',
    },
    xperiodalignment: 'start',
    hovertemplate: `%{fullData.name}: ${currencyPrefix}%{y:,.0f} (%{customdata})<extra></extra>`,
    marker: {
      color: 'rgba(99, 102, 241, 0.8)',
    },
    name: comparedAdSalesSelected.name,
  })
  const adSalesSameSkuSelected = selectedLegend.find((legend) => legend.type === 'ad-sales-same-sku')
  adSalesSameSkuSelected && dataSet.push({
    x: data.map((d) => formatUTCDate(d.date)),
    y: data.map((d) => d.ad_sales_same_sku),
    customdata: data.map((d) => formatUTCDate(d.date, '.')),
    type: 'scatter',
    mode: 'lines+markers',
    xperiodalignment: 'start',
    hovertemplate: `%{fullData.name}: ${currencyPrefix}%{y:,.0f} (%{customdata})<extra></extra>`,
    marker: {color: '#66d9e8'},
    name: adSalesSameSkuSelected.name,
  })
  const comparedAdSalesSameSkuSelected = selectedLegend.find((legend) => legend.type === 'compared-ad-sales-same-sku')
  comparedData && comparedAdSalesSameSkuSelected && dataSet.push({
    x: data.map((d) => formatUTCDate(d.date)),
    y: comparedData.map((d) => d.ad_sales_same_sku),
    customdata: comparedData.map((d) => formatUTCDate(d.date, '.')),
    type: 'scatter',
    mode: 'lines+markers',
    line: {
      dash: 'dash',
    },
    xperiodalignment: 'start',
    hovertemplate: `%{fullData.name}: ${currencyPrefix}%{y:,.0f} (%{customdata})<extra></extra>`,
    marker: {
      color: 'rgba(102, 217, 232, 0.8)',
    },
    name: comparedAdSalesSameSkuSelected.name,
  })
  const clicksSelected = selectedLegend.find((legend) => legend.type === 'clicks')
  clicksSelected && dataSet.push({
    x: data.map((d) => formatUTCDate(d.date)),
    y: data.map((d) => d.clicks),
    customdata: data.map((d) => formatUTCDate(d.date, '.')),
    yaxis: 'y2',
    type: 'scatter',
    mode: 'lines+markers',
    xperiodalignment: 'start',
    hovertemplate: '%{fullData.name}: %{y} (%{customdata})<extra></extra>',
    marker: {color: '#f97316'},
    name: clicksSelected.name,
  })
  const comparedClicksSelected = selectedLegend.find((legend) => legend.type === 'compared-clicks')
  comparedData && comparedClicksSelected && dataSet.push({
    x: data.map((d) => formatUTCDate(d.date)),
    y: comparedData.map((d) => d.clicks),
    customdata: comparedData.map((d) => formatUTCDate(d.date, '.')),
    yaxis: 'y2',
    type: 'scatter',
    mode: 'lines+markers',
    line: {
      dash: 'dash',
    },
    xperiodalignment: 'start',
    hovertemplate: '%{fullData.name}: %{y} (%{customdata})<extra></extra>',
    marker: {
      color: 'rgba(249, 115, 22, 0.8)',
    },
    name: comparedClicksSelected.name,
  })
  const impressionsSelected = selectedLegend.find((legend) => legend.type === 'impressions')
  impressionsSelected && dataSet.push({
    x: data.map((d) => formatUTCDate(d.date)),
    y: data.map((d) => d.impressions),
    customdata: data.map((d) => formatUTCDate(d.date, '.')),
    yaxis: 'y2',
    type: 'scatter',
    mode: 'lines+markers',
    xperiodalignment: 'start',
    hovertemplate: '%{fullData.name}: %{y} (%{customdata})<extra></extra>',
    marker: {color: '#c53030'},
    name: impressionsSelected.name,
  })
  const comparedImpressionsSelected = selectedLegend.find((legend) => legend.type === 'compared-impressions')
  comparedData && comparedImpressionsSelected && dataSet.push({
    x: data.map((d) => formatUTCDate(d.date)),
    y: comparedData.map((d) => d.impressions),
    customdata: comparedData.map((d) => formatUTCDate(d.date, '.')),
    yaxis: 'y2',
    type: 'scatter',
    mode: 'lines+markers',
    line: {
      dash: 'dash',
    },
    xperiodalignment: 'start',
    hovertemplate: '%{fullData.name}: %{y} (%{customdata})<extra></extra>',
    marker: {
      color: 'rgba(197, 48, 48, 0.8)',
    },
    name: comparedImpressionsSelected.name,
  })
  const roasSelected = selectedLegend.find((legend) => legend.type === 'roas')
  roasSelected && dataSet.push({
    x: data.map((d) => formatUTCDate(d.date)),
    y: data.map((d) => d.roas),
    customdata: data.map((d) => formatUTCDate(d.date, '.')),
    yaxis: 'y2',
    type: 'scatter',
    mode: 'lines+markers',
    xperiodalignment: 'start',
    hovertemplate: '%{fullData.name}: %{y}% (%{customdata})<extra></extra>',
    marker: {color: '#22c55e'},
    name: roasSelected.name,
  })
  const comparedRoasSelected = selectedLegend.find((legend) => legend.type === 'compared-roas')
  comparedData && comparedRoasSelected && dataSet.push({
    x: data.map((d) => formatUTCDate(d.date)),
    y: comparedData.map((d) => d.roas),
    customdata: comparedData.map((d) => formatUTCDate(d.date, '.')),
    yaxis: 'y2',
    type: 'scatter',
    mode: 'lines+markers',
    line: {
      dash: 'dash',
    },
    xperiodalignment: 'start',
    hovertemplate: '%{fullData.name}: %{y}% (%{customdata})<extra></extra>',
    marker: {
      color: 'rgba(34, 197, 94, 0.8)',
    },
    name: comparedRoasSelected.name,
  })
  const sdCostSelected = selectedLegend.find((legend) => legend.type === 'sd-cost')
  sdCostSelected && dataSet.push({
    x: data.map((d) => formatUTCDate(d.date)),
    y: data.map((d) => d.sd_cost),
    customdata: data.map((d) => formatUTCDate(d.date, '.')),
    type: 'scatter',
    mode: 'lines+markers',
    xperiodalignment: 'start',
    hovertemplate: `%{fullData.name}: ${currencyPrefix}%{y:,.0f} (%{customdata})<extra></extra>`,
    marker: {color: '#d8a71d'},
    name: sdCostSelected.name,
  })
  const comparedSdCostSelected = selectedLegend.find((legend) => legend.type === 'compared-sd-cost')
  comparedData && comparedSdCostSelected && dataSet.push({
    x: data.map((d) => formatUTCDate(d.date)),
    y: comparedData.map((d) => d.sd_cost),
    customdata: comparedData.map((d) => formatUTCDate(d.date, '.')),
    type: 'scatter',
    mode: 'lines+markers',
    line: {
      dash: 'dash',
    },
    xperiodalignment: 'start',
    hovertemplate: `%{fullData.name}: ${currencyPrefix}%{y:,.0f} (%{customdata})<extra></extra>`,
    marker: {
      color: 'rgba(216, 167, 29, 0.8)',
    },
    name: comparedSdCostSelected.name,
  })
  const sdSalesSelected = selectedLegend.find((legend) => legend.type === 'sd-sales')
  sdSalesSelected && dataSet.push({
    x: data.map((d) => formatUTCDate(d.date)),
    y: data.map((d) => d.sd_sales),
    customdata: data.map((d) => formatUTCDate(d.date, '.')),
    type: 'scatter',
    mode: 'lines+markers',
    xperiodalignment: 'start',
    hovertemplate: `%{fullData.name}: ${currencyPrefix}%{y:,.0f} (%{customdata})<extra></extra>`,
    marker: {color: '#f1ee63'},
    name: sdSalesSelected.name,
  })
  const comparedSdSalesSelected = selectedLegend.find((legend) => legend.type === 'compared-sd-sales')
  comparedData && comparedSdSalesSelected && dataSet.push({
    x: data.map((d) => formatUTCDate(d.date)),
    y: comparedData.map((d) => d.sd_sales),
    customdata: comparedData.map((d) => formatUTCDate(d.date, '.')),
    type: 'scatter',
    mode: 'lines+markers',
    line: {
      dash: 'dash',
    },
    xperiodalignment: 'start',
    hovertemplate: `%{fullData.name}: ${currencyPrefix}%{y:,.0f} (%{customdata})<extra></extra>`,
    marker: {
      color: 'rgba(241, 238, 99, 0.8)',
    },
    name: comparedSdSalesSelected.name,
  })
  const sdSalesPromotedClicksSelected = selectedLegend.find((legend) => legend.type === 'sd-sales-promoted-clicks')
  sdSalesPromotedClicksSelected && dataSet.push({
    x: data.map((d) => formatUTCDate(d.date)),
    y: data.map((d) => d.sd_sales_promoted_clicks),
    customdata: data.map((d) => formatUTCDate(d.date, '.')),
    type: 'scatter',
    mode: 'lines+markers',
    xperiodalignment: 'start',
    hovertemplate: `%{fullData.name}: ${currencyPrefix}%{y:,.0f} (%{customdata})<extra></extra>`,
    marker: {color: '#e87566'},
    name: sdSalesPromotedClicksSelected.name,
  })
  const comparedSdSalesPromotedClicksSelected = selectedLegend.find((legend) => legend.type === 'compared-sd-sales-promoted-clicks')
  comparedData && comparedSdSalesPromotedClicksSelected && dataSet.push({
    x: data.map((d) => formatUTCDate(d.date)),
    y: comparedData.map((d) => d.sd_sales_promoted_clicks),
    customdata: comparedData.map((d) => formatUTCDate(d.date, '.')),
    type: 'scatter',
    mode: 'lines+markers',
    line: {
      dash: 'dash',
    },
    xperiodalignment: 'start',
    hovertemplate: `%{fullData.name}: ${currencyPrefix}%{y:,.0f} (%{customdata})<extra></extra>`,
    marker: {
      color: 'rgba(232, 117, 102, 0.8)',
    },
    name: comparedSdSalesPromotedClicksSelected.name,
  })
  const sdClicksSelected = selectedLegend.find((legend) => legend.type === 'sd-clicks')
  sdClicksSelected && dataSet.push({
    x: data.map((d) => formatUTCDate(d.date)),
    y: data.map((d) => d.sd_clicks),
    customdata: data.map((d) => formatUTCDate(d.date, '.')),
    yaxis: 'y2',
    type: 'scatter',
    mode: 'lines+markers',
    xperiodalignment: 'start',
    hovertemplate: '%{fullData.name}: %{y} (%{customdata})<extra></extra>',
    marker: {color: '#169cf9'},
    name: sdClicksSelected.name,
  })
  const comparedSdClicksSelected = selectedLegend.find((legend) => legend.type === 'compared-sd-clicks')
  comparedData && comparedSdClicksSelected && dataSet.push({
    x: data.map((d) => formatUTCDate(d.date)),
    y: comparedData.map((d) => d.sd_clicks),
    customdata: comparedData.map((d) => formatUTCDate(d.date, '.')),
    yaxis: 'y2',
    type: 'scatter',
    mode: 'lines+markers',
    line: {
      dash: 'dash',
    },
    xperiodalignment: 'start',
    hovertemplate: '%{fullData.name}: %{y} (%{customdata})<extra></extra>',
    marker: {
      color: 'rgba(22, 156, 249, 0.8)',
    },
    name: comparedSdClicksSelected.name,
  })
  const sdImpressionsSelected = selectedLegend.find((legend) => legend.type === 'sd-impressions')
  sdImpressionsSelected && dataSet.push({
    x: data.map((d) => formatUTCDate(d.date)),
    y: data.map((d) => d.sd_impressions),
    customdata: data.map((d) => formatUTCDate(d.date, '.')),
    yaxis: 'y2',
    type: 'scatter',
    mode: 'lines+markers',
    xperiodalignment: 'start',
    hovertemplate: '%{fullData.name}: %{y} (%{customdata})<extra></extra>',
    marker: {color: '#30c5c5'},
    name: sdImpressionsSelected.name,
  })
  const comparedSdImpressionsSelected = selectedLegend.find((legend) => legend.type === 'compared-sd-impressions')
  comparedData && comparedSdImpressionsSelected && dataSet.push({
    x: data.map((d) => formatUTCDate(d.date)),
    y: comparedData.map((d) => d.sd_impressions),
    customdata: comparedData.map((d) => formatUTCDate(d.date, '.')),
    yaxis: 'y2',
    type: 'scatter',
    mode: 'lines+markers',
    line: {
      dash: 'dash',
    },
    xperiodalignment: 'start',
    hovertemplate: '%{fullData.name}: %{y} (%{customdata})<extra></extra>',
    marker: {
      color: 'rgba(48, 197, 197, 0.8)',
    },
    name: comparedSdImpressionsSelected.name
  })
  const sdRoasSelected = selectedLegend.find((legend) => legend.type === 'sd-roas')
  sdRoasSelected && dataSet.push({
    x: data.map((d) => formatUTCDate(d.date)),
    y: data.map((d) => d.sd_roas),
    customdata: data.map((d) => formatUTCDate(d.date, '.')),
    yaxis: 'y2',
    type: 'scatter',
    mode: 'lines+markers',
    xperiodalignment: 'start',
    hovertemplate: '%{fullData.name}: %{y}% (%{customdata})<extra></extra>',
    marker: {color: '#c52289'},
    name: sdRoasSelected.name,
  })
  const comparedSdRoasSelected = selectedLegend.find((legend) => legend.type === 'compared-sd-roas')
  comparedData && comparedSdRoasSelected && dataSet.push({
    x: data.map((d) => formatUTCDate(d.date)),
    y: comparedData.map((d) => d.sd_roas),
    customdata: comparedData.map((d) => formatUTCDate(d.date, '.')),
    yaxis: 'y2',
    type: 'scatter',
    mode: 'lines+markers',
    line: {
      dash: 'dash',
    },
    xperiodalignment: 'start',
    hovertemplate: '%{fullData.name}: %{y}% (%{customdata})<extra></extra>',
    marker: {
      color: 'rgba(197, 34, 137, 0.8)',
    },
    name: comparedSdRoasSelected.name,
  })
  const PlotComponent = Plot as any
  return (
    <PlotComponent
      data={dataSet as any}
      layout={{
        margin: {
          l: 20,
          r: 20,
          b: 20,
          t: 20,
          pad: 0
        },
        autosize: true,
        showlegend: false,
        // legend: {
        //   font: {
        //     size: 10,
        //     color: '#6B7280'
        //   }
        // },
        xaxis: {
          tickfont: {
            size: 10,
            color: '#9CA3AF'
          },
          tickformat: '%y.%m.%d',
          zerolinecolor: '#e5e7eb',
          tickmode: 'auto',
          nticks: data.length > 10 ? 10 : data.length,
        },
        yaxis: {
          tickformat: ',.0f',
          tickprefix: currencyPrefix,
          tickfont: {
            size: 10,
            color: '#9CA3AF'
          },
          gridcolor: '#f3f4f6',
          zerolinecolor: '#e5e7eb',
          automargin: true,
        },
        yaxis2: {
          tickformat: ',',
          tickfont: {
            size: 10,
            color: '#9CA3AF'
          },
          gridcolor: '#f3f4f6',
          zerolinecolor: '#e5e7eb',
          overlaying: 'y',
          side: 'right',
          automargin: true,
        },
        hovermode: 'x unified',
        hoverlabel: {
          bgcolor: 'rgba(17, 24, 39, 0.9)',
          font: {
            size: 10,
            color: '#e5e7eb'
          },
        },
        dragmode: false,
      }}
      config={{
        displayModeBar: false,
      }}
      useResizeHandler={true}
      className="w-full grow"
    />
  )
}
