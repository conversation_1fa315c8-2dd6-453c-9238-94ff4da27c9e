"use client";

import actions from "@/actions";
import { cn } from "@/utils/msc";
import { Dialog, DialogPanel, DialogTitle, Transition, TransitionChild } from "@headlessui/react";
import { PlusIcon } from "@heroicons/react/20/solid";
import { useState } from "react";
import { useTranslations } from "next-intl";

interface ConnectButtonProps extends React.HTMLAttributes<HTMLDivElement> {
  isLNBExpanded?: boolean;
  buttonClassName?: string;
}

export default function ConnectAdButton({
  isLNBExpanded,
  buttonClassName,
  ...props
}: ConnectButtonProps) {
  const t = useTranslations('component.accounts');
  const [regionCode, setRegionCode] = useState("NA"); // Add state for region code
  const [isModalOpen, setIsModalOpen] = useState(false);

  const handleClick = async () => {
    const oauthLink = await actions.oauth.getExAdLoginLink(regionCode);
    window.location.href = oauthLink.url;
  };

  return (
    <div className="flex-shrink-0 flex items-center gap-x-2">
      {/* <div className="flex items-center border border-gray-100 rounded-lg h-11 px-2">
        <select
          value={regionCode}
          onChange={(e) => setRegionCode(e.target.value)}
          className="text-sm text-gray-600 outline-none cursor-pointer"
        >
          <option value="NA">North America</option>
          <option value="EU">Europe</option>
        </select>
      </div> */}
      <button className={cn(buttonClassName)} onClick={() => setIsModalOpen(true)}>
        <PlusIcon
          className="flex-shrink-0 h-5 w-5"
          aria-hidden="true"
        />
        {isLNBExpanded ? (
          <span className="flex-shrink-0 block font-semibold">
            {t('authorizeAds')}
          </span>
        ) : (
          ""
        )}
      </button>
      <Transition appear show={isModalOpen}>
        <Dialog as="div" className="relative z-10 focus:outline-none" onClose={() => setIsModalOpen(false)}>
          <div className="fixed inset-0 bg-black/30 backdrop-blur-sm" aria-hidden="true" />
          <div className="fixed inset-0 z-10 w-screen overflow-y-auto">
            <div className="flex min-h-full items-center justify-center p-4">
              <TransitionChild
                enter="ease-out duration-300"
                enterFrom="opacity-0 transform-[scale(95%)]"
                enterTo="opacity-100 transform-[scale(100%)]"
                leave="ease-in duration-200"
                leaveFrom="opacity-100 transform-[scale(100%)]"
                leaveTo="opacity-0 transform-[scale(95%)]"
              >
                <DialogPanel className="w-full max-w-md rounded-xl bg-white p-6">
                  <DialogTitle as="h3" className="text-base/7 font-medium text-gray-600">
                    {t('selectRegion')}
                  </DialogTitle>
                  <p className="mt-2 text-sm/6 text-gray-500">
                    {t('selectRegionDescription')}
                  </p>
                  <div className="mt-4">
                    <div className="p-2 border border-gray-300 rounded-lg overflow-hidden">
                      <select
                        value={regionCode}
                        onChange={(e) => setRegionCode(e.target.value)}
                        className="w-full text-sm text-gray-600 focus:outline-none"
                      >
                        <option value="NA">North America</option>
                        <option value="EU">Europe</option>
                        <option value="FE">Far East</option>
                      </select>
                    </div>
                  </div>
                  <div className="mt-4 flex items-center justify-end gap-x-4">
                    <button
                      className="inline-flex items-center gap-2 rounded-md bg-white py-1.5 px-3 text-sm/6 font-semibold text-gray-500 shadow-inner shadow-white/10 focus:outline-none data-[hover]:bg-gray-100 data-[open]:bg-gray-700 data-[focus]:outline-1 data-[focus]:outline-white"
                      onClick={() => setIsModalOpen(false)}
                    >
                      {t('cancel')}
                    </button>
                    <button
                      className="inline-flex items-center justify-center gap-x-2 rounded-md border border-transparent bg-blue-600 px-4 py-2 text-sm font-medium text-white hover:bg-blue-700 focus:outline-none focus-visible:ring-2 focus-visible:ring-offset-2"
                      onClick={() => {
                        handleClick();
                        setIsModalOpen(false);
                      }}
                    >
                      {t('confirm')}
                    </button>
                  </div>
                </DialogPanel>
              </TransitionChild>
            </div>
          </div>
        </Dialog>
      </Transition>
    </div>
  );
}
