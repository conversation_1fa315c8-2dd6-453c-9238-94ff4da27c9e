import { ErrorResponse } from "@/app/api/_models";
import { getServerApiHostUrl } from "@/utils/host";
import { NextRequest, NextResponse } from "next/server";

export type SPReportByAsinResponse = any[];

export async function GET(
  request: NextRequest
): Promise<NextResponse<SPReportByAsinResponse | ErrorResponse>> {
  const accessToken = request.headers.get("Authorization");
  if (!accessToken) {
    return NextResponse.json(
      { message: "Authorization header is missing" },
      { status: 400 }
    );
  }
  const lwaAccountId = request.nextUrl.searchParams.get("lwa_account_id")
  if (!lwaAccountId) {
    return NextResponse.json(
      { message: "lwa_account_id query is missing" },
      { status: 400 }
    );
  }
  const marketplaceId = request.nextUrl.searchParams.get("marketplace_id")
  if (!marketplaceId) {
    return NextResponse.json(
      { message: "marketplace_id query is missing" },
      { status: 400 }
    );
  }
  const startDate = request.nextUrl.searchParams.get("start_date")
  if (!startDate) {
    return NextResponse.json(
      { message: "start_date query is missing" },
      { status: 400 }
    );
  }
  const endDate = request.nextUrl.searchParams.get("end_date")
  if (!endDate) {
    return NextResponse.json(
      { message: "end_date query is missing" },
      { status: 400 }
    );
  }
  const spReportByAsinResponse = await fetch(
    `${await getServerApiHostUrl()}/api/sp/reporting/sales_and_traffic_by_asin?lwa_account_id=${lwaAccountId}&marketplace_id=${marketplaceId}&start_date=${startDate}&end_date=${endDate}`,
    {
      method: "GET",
      headers: {
        "Authorization": `Bearer ${accessToken}`,
        "Content-Type": "application/json",
      },
      cache: "no-cache",
    }
  ).then((res) => res.json());

  return NextResponse.json(spReportByAsinResponse, { status: 200 });
}
