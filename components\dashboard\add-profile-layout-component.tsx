"use client"

import { useSession } from "next-auth/react"
import IntegrateAdButton from "@/components/dashboard/integration-ad-button"
import IntegrateSpButton from "@/components/dashboard/integration-sp-button"
import "react-datepicker/dist/react-datepicker.css"
import { cn } from "@/utils/msc"
import { ProfileOption } from "@/components/dashboard/profile-select"
import { MarketplaceOption } from "@/components/dashboard/marketplace-select"
import IntegrateSpVendorButton from "./integration-sp-vendor-button"

interface AddProfileLayoutProps {
  mopUserData: any;
}

export default function AddProfileLayoutComponent({
  mopUserData,
}: AddProfileLayoutProps) {
  const { data: session, status } = useSession()

  return (
    <div className="relative flex items-center justify-center w-full h-full divide-x divide-gray-200">
      <div className="flex-shrink-0 flex flex-col w-full h-full bg-white py-4 sm:py-6 px-8 sm:px-12">
        {/* Add Amazon Account */}
        <div className="mt-4">
          <IntegrateSpButton
            buttonClassName={cn(
                "group relative flex items-center gap-x-2 px-2 cursor-pointer rounded-lg h-9 py-2 focus:outline-none text-sm overflow-hidden",
                "transition-width ease-in-out delay-150",
                "w-[180px] border-2 border-blue-300 hover:border-blue-500"
            )}
            isLNBExpanded={true}
          />
        </div>
        <div className="mt-4">
          <IntegrateSpVendorButton
            buttonClassName={cn(
                "group relative flex items-center gap-x-2 px-2 cursor-pointer rounded-lg h-9 py-2 focus:outline-none text-sm overflow-hidden",
                "transition-width ease-in-out delay-150",
                "w-[180px] border-2 border-blue-300 hover:border-blue-500"
            )}
            isLNBExpanded={true}
          />
        </div>
        <div className="mt-4">
          <IntegrateAdButton
            buttonClassName={cn(
                "group relative flex items-center gap-x-2 px-2 cursor-pointer rounded-lg h-9 py-2 focus:outline-none text-sm overflow-hidden",
                "transition-width ease-in-out delay-150",
                "w-[180px] border-2 border-blue-300 hover:border-blue-500"
            )}
            isLNBExpanded={true}
          />
        </div>
      </div>
    </div>
  )
}
