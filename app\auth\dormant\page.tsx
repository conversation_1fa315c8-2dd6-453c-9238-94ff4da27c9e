import { auth } from "auth"
import DormantPageLayout from "@/components/auth/dormant-layout"
import { SessionProvider } from "next-auth/react"

export default async function DormantPage() {
  const session = await auth()
  if (session?.user) {
    session.user = {
      ...session.user,
    }
  }

  return (
    <SessionProvider session={session}>
      <DormantPageLayout />
    </SessionProvider>
  );
}