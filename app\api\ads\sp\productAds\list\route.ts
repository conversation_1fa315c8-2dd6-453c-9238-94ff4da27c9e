import { ErrorResponse } from "@/app/api/_models";
import { getServerApiHostUrl } from "@/utils/host";
import { NextRequest, NextResponse } from "next/server";

export type ProductAdsResponse = {
  nextToken: string,
  productAds: any[],
  totalResults: number
};

export async function POST(
  request: NextRequest
): Promise<NextResponse<ProductAdsResponse | ErrorResponse>> {
  const accessToken = request.headers.get("Authorization");
  if (!accessToken) {
    return NextResponse.json(
      { message: "Authorization header is missing" },
      { status: 400 }
    );
  }
  const profileId = request.nextUrl.searchParams.get("profile_id")
  if (!profileId) {
    return NextResponse.json(
      { message: "profile_id query is missing" },
      { status: 400 }
    );
  }
  const productAdsResponse = await fetch(
    `${await getServerApiHostUrl()}/api/ads/sp/productAds/list?profile_id=${profileId}`,
    {
      method: "POST",
      headers: {
        "Authorization": `Bearer ${accessToken}`,
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        // [TODO] api.ts로부터 받아온 request body값을 그대로 토스해주도록 수정해야 함.
        "maxResults": 10,
        "nextToken": null,
        "includeExtendedDataFields": true
      }),
      cache: "no-cache",
    }
  ).then((res) => res.json());

  return NextResponse.json(productAdsResponse, { status: 200 });
}
