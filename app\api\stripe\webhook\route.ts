import { ErrorResponse } from "@/app/api/_models";
import { getServerApiHostUrl } from "@/utils/host";
import { NextRequest, NextResponse } from "next/server";

export type StripeWebhookResponse = any;

export async function POST(
  request: NextRequest
): Promise<NextResponse<StripeWebhookResponse | ErrorResponse>> {
  const rawBody = await request.text();

  const stripeSignature = request.headers.get("Stripe-Signature");

  const stripeWebhookResponse = await fetch(
    `${await getServerApiHostUrl()}/api/stripe/webhook`,
    {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "Stripe-Signature": stripeSignature || "",
      },
      body: rawBody,
      cache: "no-cache",
    }
  ).then((res) => res.json());

  return NextResponse.json(stripeWebhookResponse, { status: 200 });
}
