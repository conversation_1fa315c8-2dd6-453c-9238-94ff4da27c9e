"use client"

import Plot from "react-plotly.js"
import { formatDate } from "@/utils/msc"
import { useEffect, useMemo, useState } from "react"

type GraphData = {
  date: string;
  value: number;
}

export default function KeywordBrandShareGraph({ myData, otherData, targetDate, setTargetDate, isWeekly }: { myData: GraphData[], otherData: GraphData[], targetDate: Date, setTargetDate: (date: Date) => void, isWeekly: boolean }) {
  const initialDataset: any[] = useMemo(() => {
    const dataset = []
    dataset.push({
      x: myData.map((d) => new Date(d.date)),
      y: myData.map((d) => d.value),
      customdata: isWeekly
        ? myData.map((d) => {
          const startDate = new Date(d.date);
          const endDate = new Date(startDate);
          endDate.setDate(startDate.getDate() + 6); // 7 days period (including start date)
          return `${formatDate(startDate, '.')} - ${formatDate(endDate, '.')}`;
        })
        : myData.map((d) => formatDate(d.date, '.')),
      type: 'scatter',
      mode: 'lines+markers',
      line: { shape: 'spline', smoothing: 1.3 },
      xperiodalignment: 'start',
      hovertemplate: '%{y} (%{customdata})',
      marker: {
        color: 'rgba(59, 130, 246, 1)',
        symbol: myData.map(() => 'circle-open'),
        size: myData.map(() => 0),
        line: {
          width: myData.map(() => 0),
          color: 'rgba(59, 130, 246, 1)'
        }
      },
      name: 'My Brand Click Share',
    })
    dataset.push({
      x: otherData.map((d) => new Date(d.date)),
      y: otherData.map((d) => d.value),
      customdata: isWeekly
        ? otherData.map((d) => {
          const startDate = new Date(d.date);
          const endDate = new Date(startDate);
          endDate.setDate(startDate.getDate() + 6); // 7 days period (including start date)
          return `${formatDate(startDate, '.')} - ${formatDate(endDate, '.')}`;
        })
        : otherData.map((d) => formatDate(d.date, '.')),
      type: 'scatter',
      mode: 'lines+markers',
      line: { shape: 'spline', smoothing: 1.3 },
      xperiodalignment: 'start',
      hovertemplate: '%{y} (%{customdata})',
      marker: {
        color: 'rgba(234, 179, 8, 1)',
        symbol: otherData.map(() => 'circle-open'),
        size: otherData.map(() => 0),
        line: {
          width: otherData.map(() => 0),
          color: 'rgba(234, 179, 8, 1)'
        }
      },
      name: 'Other Brand Click Share',
    })

    return dataset
  }, [myData, otherData])

  const changedDataset = useMemo(() => {
    const dataset = []
    dataset.push({
      x: myData.map((d) => new Date(d.date)),
      y: myData.map((d) => d.value),
      customdata: isWeekly
        ? myData.map((d) => {
          const startDate = new Date(d.date);
          const endDate = new Date(startDate);
          endDate.setDate(startDate.getDate() + 6); // 7 days period (including start date)
          return `${formatDate(startDate, '.')} - ${formatDate(endDate, '.')}`;
        })
        : myData.map((d) => formatDate(d.date, '.')),
      type: 'scatter',
      mode: 'lines+markers',
      line: { shape: 'spline', smoothing: 1.3 },
      xperiodalignment: 'start',
      hovertemplate: '%{y} (%{customdata})',
      marker: {
        color: 'rgba(59, 130, 246, 1)',
        symbol: myData.map((d) => 'circle-open'),
        size: myData.map((d) => 0),
        line: {
          width: myData.map((d) => 0),
          color: 'rgba(59, 130, 246, 1)'
        }
      },
      name: 'My Brand Click Share',
    })
    dataset.push({
      x: otherData.map((d) => new Date(d.date)),
      y: otherData.map((d) => d.value),
      customdata: isWeekly
        ? otherData.map((d) => {
          const startDate = new Date(d.date);
          const endDate = new Date(startDate);
          endDate.setDate(startDate.getDate() + 6); // 7 days period (including start date)
          return `${formatDate(startDate, '.')} - ${formatDate(endDate, '.')}`;
        })
        : otherData.map((d) => formatDate(d.date, '.')),
      type: 'scatter',
      mode: 'lines+markers',
      line: { shape: 'spline', smoothing: 1.3 },
      xperiodalignment: 'start',
      hovertemplate: '%{y} (%{customdata})',
      marker: {
        color: 'rgba(234, 179, 8, 1)',
        symbol: otherData.map((d) => 'circle-open'),
        size: otherData.map((d) => 0),
        line: {
          width: otherData.map((d) => 0),
          color: 'rgba(234, 179, 8, 1)'
        }
      },
      name: 'Other Brand Click Share',
    })

    return dataset
  }, [myData, otherData])

  const layout = {
    transition: {
      duration: 2000,
      easing: 'cubic-in-out',
    },
    margin: {
      l: 0,
      r: 0,
      b: 20,
      t: 60,
      pad: 0
    },
    paper_bgcolor: 'rgba(0,0,0,0)',
    plot_bgcolor: 'rgba(0,0,0,0)',
    autosize: true,
    showlegend: false,
    xaxis: {
      tickfont: {
        size: 10,
        color: '#4b5563'
      },
      tickformat: '%y.%m.%d',
      ticks: 'outside',
      tickcolor: 'rgba(0,0,0,0)',
      gridcolor: '#374151',
      zerolinecolor: '#374151',
      tickmode: 'auto',
      nticks: myData.length > 10 ? 10 : myData.length,
      range: [
        myData.length > 0 ? myData[0].date : new Date().toISOString(),
        myData.length > 0
          ? new Date(new Date(myData[myData.length - 1].date).setDate(new Date(myData[myData.length - 1].date).getDate() + 1))
          : new Date(new Date().setDate(new Date().getDate() + 1)).toISOString(),
      ],
    },
    yaxis: {
      tickformat: '.2f',
      tickfont: {
        size: 10,
        color: '#4b5563'
      },
      ticks: 'outside',
      tickcolor: 'rgba(0,0,0,0)',
      gridcolor: '#374151',
      zerolinecolor: '#374151',
      automargin: true,
      range: [0, 1],
    },
    hovermode: 'x unified',
    hoverlabel: {
      bgcolor: 'rgba(17, 24, 39, 0.9)',
      font: {
        size: 12,
        color: '#ffffff'
      }
    }
  };
  const [graphInfo, setGraphInfo] = useState<{ dataset: any[]; layout: any }>({
    dataset: initialDataset,
    layout: layout
  });
  const handleClick = (event: Readonly<Plotly.PlotMouseEvent>) => {
    const clickedIndex = event.points[0].pointIndex;
    const clickedTraceIndex = event.points[0].curveNumber;
    let clickedDate: string | undefined;
    if (clickedTraceIndex === 0) {
      clickedDate = myData[clickedIndex]?.date;
    } else if (clickedTraceIndex === 1) {
      clickedDate = otherData[clickedIndex]?.date;
    }
    if (clickedDate) {
      setTargetDate(new Date(clickedDate));
    }
    setGraphInfo((prev: { dataset: any[]; layout: any }) => {
      const updatedDataset = prev.dataset.map((trace: any, traceIdx: number) => {
        const highlightIndex = traceIdx === clickedTraceIndex ? clickedIndex : -1;
        return {
          ...trace,
          marker: {
            ...trace.marker,
            symbol: (trace.marker.symbol ?? Array(trace.x.length).fill('circle-open')).map((_: string, idx: number) =>
              idx === highlightIndex ? 'circle' : 'circle-open'
            ),
            size: (trace.marker.size ?? Array(trace.x.length).fill(0)).map((_: number, idx: number) =>
              idx === highlightIndex ? 8 : 0
            ),
            line: {
              width: (trace.marker.line?.width ?? Array(trace.x.length).fill(0)).map((_: number, idx: number) =>
                idx === highlightIndex ? 2 : 0
              ),
              color: trace.marker.line?.color
            }
          }
        };
      });
      return { ...prev, dataset: updatedDataset };
    });
  };
      
  useEffect(() => {
    setGraphInfo({
      dataset: initialDataset,
      layout: {
        ...layout,
      }
    })
  // Only run when data changes
  }, [initialDataset])

  // Update marker to show circle only at the selected targetDate
  useEffect(() => {
    if (!targetDate) return;
    const myIndex = myData.findIndex(d => {
      const dDate = new Date(d.date);
      return dDate.getFullYear() === targetDate.getFullYear() &&
        dDate.getMonth() === targetDate.getMonth() &&
        dDate.getDate() === targetDate.getDate();
    });
    const otherIndex = otherData.findIndex(d => {
      const dDate = new Date(d.date);
      return dDate.getFullYear() === targetDate.getFullYear() &&
        dDate.getMonth() === targetDate.getMonth() &&
        dDate.getDate() === targetDate.getDate();
    });
    setGraphInfo((prev: { dataset: any[]; layout: any }) => {
      const updatedDataset = prev.dataset.map((trace: any, traceIdx: number) => {
        // 0: myData, 1: otherData
        const highlightIndex = traceIdx === 0 ? myIndex : otherIndex;
        // Add a vertical line (shape) at the highlighted index if it exists
        if (highlightIndex !== -1 && traceIdx === 0) {
          // Only add the shape once for the first trace (myData)
          const xValue = trace.x[highlightIndex];
          // Add a vertical line to the layout
          setGraphInfo(prev => ({
            ...prev,
            layout: {
              ...prev.layout,
              shapes: [
                {
                  type: 'line',
                  xref: 'x',
                  yref: 'paper',
                  x0: xValue,
                  x1: xValue,
                  y0: 0,
                  y1: 1,
                  line: {
                    color: 'rgba(255,255,255,0.7)',
                    width: 2,
                    dash: 'dot'
                  }
                }
              ]
            }
          }));
        }
        return {
          ...trace,
          marker: {
            ...trace.marker,
            symbol: (trace.marker.symbol ?? Array(trace.x.length).fill('circle-open')).map((_: string, idx: number) =>
              idx === highlightIndex ? 'circle' : 'circle-open'
            ),
            size: (trace.marker.size ?? Array(trace.x.length).fill(0)).map((_: number, idx: number) =>
              idx === highlightIndex ? 8 : 0
            ),
            line: {
              width: (trace.marker.line?.width ?? Array(trace.x.length).fill(0)).map((_: number, idx: number) =>
                idx === highlightIndex ? 2 : 0
              ),
              color: trace.marker.line?.color
            }
          }
        };
      });
      return { ...prev, dataset: updatedDataset };
    });
  }, [targetDate, myData, otherData]);
  return (
    <Plot
      data={graphInfo.dataset}
      layout={graphInfo.layout}
      config={{
        displayModeBar: false,
      }}
      useResizeHandler={true}
      onClick={handleClick}
      className="w-full h-full"
    />
  )
}
