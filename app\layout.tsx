import "./globals.css"
import type { Metada<PERSON> } from "next"
import { Inter } from "next/font/google"
import { NextIntlClientProvider } from "next-intl"
import { getLocale, getMessages } from "next-intl/server"

const inter = Inter({ subsets: ["latin"] })

export const metadata: Metadata = {
  title: 'Optapex',
  description: 'Optapex now serves Amazon sellers and vendors to optimize their budget choices for advertisements and improve ROI of their organizations.',
}

export default async function RootLayout({ children }: React.PropsWithChildren) {
  const locale = await getLocale()
  const messages = await getMessages()

  return (
    <html lang={locale}>
      <body className={inter.className}>
        <NextIntlClientProvider messages={messages}>
          {children}
        </NextIntlClientProvider>
      </body>
    </html>
  )
}
