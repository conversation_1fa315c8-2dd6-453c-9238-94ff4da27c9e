import { ErrorResponse } from "@/app/api/_models";
import { getServerApiHostUrl } from "@/utils/host";
import { NextRequest, NextResponse } from "next/server";

export type OAuthProfileResponse = {
  access_token: string;
  refresh_token: string;
};

export async function GET(
  request: NextRequest
): Promise<NextResponse<OAuthProfileResponse | ErrorResponse>> {
  const accessToken = request.headers.get("X-LwA-Access-Token");
  if (!accessToken) {
    return NextResponse.json(
      { message: "X-LwA-Access-Token header is missing" },
      { status: 400 }
    );
  }

  const profile = await fetch(`${await getServerApiHostUrl()}/api/oauth/profile`, {
    method: "GET",
    headers: {
      "X-LwA-Access-Token": accessToken,
    },
    cache: "no-cache",
  }).then((res) => res.json());
  // TODO: Error 처리

  return NextResponse.json(profile, { status: 200 });
}
