import { ErrorResponse } from "@/app/api/_models";
import { getServerApiHostUrl } from "@/utils/host";
import { NextRequest, NextResponse } from "next/server";

export type ProductMetadataResponse = {
  cursorToken: string;
  ProductMetadataList: any[];
};

export async function POST(
  request: NextRequest
): Promise<NextResponse<ProductMetadataResponse | ErrorResponse>> {
  const accessToken = request.headers.get("Authorization");
  const body = await request.json();

  if (!accessToken) {
    return NextResponse.json(
      { message: "Authorization header is missing" },
      { status: 400 }
    );
  }

  const profileId = request.nextUrl.searchParams.get("profile_id");
  if (!profileId) {
    return NextResponse.json(
      { message: "profile_id query is missing" },
      { status: 400 }
    );
  }

  const productMetadataResponse = await fetch(
    `${await getServerApiHostUrl()}/api/ads/product/metadata?profile_id=${profileId}`,
    {
      method: "POST",
      headers: {
        "Authorization": `Bearer ${accessToken}`,
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        "asins": body?.asins,
        "pageIndex": 0,
        "pageSize": 100,
        "checkItemDetails": true,
      }),
      cache: "no-cache",
    }
  ).then((res) => {
    if (res.status === 200) {
      return res.json();
    } else {
      return {
        cursorToken: null,
        ProductMetadataList: [],
      };
    }
  });

  return NextResponse.json(productMetadataResponse, { status: 200 });
}