"use client"

import { api } from "@/utils/api"
import { BudgetPacingResponse } from "@/app/api/hourlyReport/new_budget_pacing/route"
import { forwardRef, Fragment, useEffect, useState, useMemo } from 'react'
import { useSession } from "next-auth/react"
import { useLocale, useTranslations } from 'next-intl'
import { Button, Dialog, DialogPanel, DialogTitle, Listbox, Menu, MenuButton, MenuItem, MenuItems, Transition, TransitionChild } from '@headlessui/react'
import { PortfolioListItem, ProductListItem } from './ad-portfolio-layout-component'
import { CampaignBudgetUsageResponse } from "@/app/api/hourlyReport/campaign-budget-usage/route"
import { ProfileOption } from "@/components/dashboard/profile-select"
import PortfolioStatus from './portfolio-status'
import ProductStatus from './product-status'
import DatePicker, { registerLocale } from "react-datepicker"
import { ko } from "date-fns/locale"
import ProductHistoryTypeSelect, { productHistoryTypeOptions } from "@/components/dashboard/product-history-type-select"
import { colorSetList, cn, formatDate, formatDateTime, getDateDifference } from "@/utils/msc"
import { useCurrencyFormatter, getCurrencyCodeFromMarketplace } from "@/utils/currency"
import { ArrowDownCircleIcon, ArrowsPointingOutIcon, CalendarDaysIcon, CalendarIcon, CheckBadgeIcon, ChevronUpDownIcon, CursorArrowRaysIcon, EllipsisHorizontalIcon, ExclamationTriangleIcon, FireIcon, MagnifyingGlassIcon, PauseIcon, PencilSquareIcon, PlayIcon, QuestionMarkCircleIcon, Square2StackIcon, TrashIcon, TrophyIcon, ViewfinderCircleIcon, XMarkIcon } from "@heroicons/react/20/solid"
import dynamic from "next/dynamic"
import { ListOptimizationHistoryResponse } from "@/app/api/optimization/list_optimization_history/route"
import { ArrowRight } from "lucide-react"
import BudgetSensitivityOptimizeSlider from "./budget-sensitivity-optimize-slider"
registerLocale('ko', ko)
const PaceGraph = dynamic(() => import('@/components/dashboard/pace-graph'), { ssr: false })
const DetailPaceGraph = dynamic(() => import('@/components/dashboard/detail-pace-graph'), { ssr: false })
const BudgetSensitivityGraph = dynamic(() => import('./budget-sensitivity-graph'), { ssr: false })

interface AdPortfolioViewSliderProps {
  selectedProfile: ProfileOption;
  handlePortfolioViewCloseClick: () => void;
  handleEditPortfolioClick: (portfolio: PortfolioListItem) => void;
  handlePausePortfolioClick: (portfolio: PortfolioListItem) => void;
  handleResumePortfolioClick: (portfolio: PortfolioListItem) => void;
  handleDeletePortfolioClick: (portfolio: PortfolioListItem) => void;
  handleProductItemClick: (portfolio: PortfolioListItem, product: ProductListItem) => void;
  selectedPortfolioItem: PortfolioListItem;
  isOptSetsLoading: boolean;
  budgetSensitivityStatus: string | undefined;
  budgetPacingData: BudgetPacingResponse | null;
  campaignBudgetUsage: CampaignBudgetUsageResponse | null;
  fetchCampaignBudgetUsage: (optimizationId: number) => Promise<void>;
}

export default function AdPortfolioViewSlider({
  selectedProfile,
  handlePortfolioViewCloseClick,
  handleEditPortfolioClick,
  handlePausePortfolioClick,
  handleResumePortfolioClick,
  handleDeletePortfolioClick,
  handleProductItemClick,
  selectedPortfolioItem,
  isOptSetsLoading,
  budgetSensitivityStatus,
  budgetPacingData,
  campaignBudgetUsage,
  fetchCampaignBudgetUsage,
}: AdPortfolioViewSliderProps) {
  const t = useTranslations('component')
  const tos = useTranslations('optimizationSets')
  const locale = useLocale()
  const { data: session, status } = useSession()
  
  // Currency formatting - marketplace_id를 기반으로 통화 코드 결정
  const { formatCurrency } = useCurrencyFormatter()
  const currencyCode = useMemo(() => {
    // selectedPortfolioItem의 marketplace_id를 사용하여 통화 코드 결정
    const mockMarketplace = { marketplace_id: selectedPortfolioItem?.marketplace_id }
    return getCurrencyCodeFromMarketplace(mockMarketplace)
  }, [selectedPortfolioItem?.marketplace_id])
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false)
  const [isPauseLoading, setIsPauseLoading] = useState(false)
  const [isResumeLoading, setIsResumeLoading] = useState(false)
  const [isDeleteLoading, setIsDeleteLoading] = useState(false)
  const [isBudgetSensitivityLoading, setIsBudgetSensitivityLoading] = useState(false)
  const [isPaceGraphModalOpen, setIsPaceGraphModalOpen] = useState(false)
  const [isSensitivityGraphModalOpen, setIsSensitivityGraphModalOpen] = useState(false)
  const [productSearchText, setProductSearchText] = useState('')
  const [optimizationHistory, setOptimizationHistory] = useState<ListOptimizationHistoryResponse>([])
  const [optimizationBudgetSensitivitySummary, setOptimizationBudgetSensitivitySummary] = useState<any>([])
  const [optimizationBudgetSensitivityDetail, setOptimizationBudgetSensitivityDetail] = useState<any>([])
  const [isHistoryLoading, setIsHistoryLoading] = useState(false)
  // const [selectedProductHistoryType, setSelectedProductHistoryType] = useState(productHistoryTypeOptions[1])
  const [dateRange, setDateRange] = useState<Date[]>([new Date(new Date().getFullYear(), new Date().getMonth(), 1), new Date()])
  const [startDate, endDate] = dateRange
  const DateRangeInput = forwardRef(({ value, onClick }: { value: string, onClick: () => void }, ref) => (
    // @ts-ignore
    <button className="w-auto mt-1 rounded-lg bg-white border border-gray-100 overflow-hidden cursor-pointer text-sm text-gray-600 focus:outline-none" onClick={onClick} ref={ref}>
      <div className="min-w-[200px] inline-flex py-2 px-4 bg-white hover:bg-gray-100/20">
        {value}
      </div>
    </button>
  ));
  DateRangeInput.displayName = 'DateRangeInput'
  // budget pacing related
  const ineligibleProducts = selectedPortfolioItem.target_products.filter((productItem) => productItem.eligibility_status === "INELIGIBLE")
  const currentDate = new Date()
  const endOfMonth = new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, 0).getDate()
  const budgetEndDate = selectedPortfolioItem.ad_budget_type === "DATERANGE" && selectedPortfolioItem.ad_budget_end_date
    ? new Date(selectedPortfolioItem.ad_budget_end_date)
    : new Date(currentDate.getFullYear(), currentDate.getMonth(), endOfMonth)
  
  // Budget Pacing API의 spent_budget 사용
  const spentBudget = budgetPacingData?.spent_budget || 0
  const lastDate = new Date() // 현재 날짜 사용
  const expectedUsage: number = spentBudget + (selectedPortfolioItem.prediction?.budget_usage?.TARGET || 0) * getDateDifference(lastDate, budgetEndDate)
  const minFeasibleBudget: number = spentBudget + (selectedPortfolioItem.prediction?.budget_usage?.MIN || 0) * getDateDifference(lastDate, budgetEndDate)
  const maxFeasibleBudget: number = spentBudget + (selectedPortfolioItem.prediction?.budget_usage?.MAX || 0) * getDateDifference(lastDate, budgetEndDate)

  // const budgetAbnormality = selectedPortfolioItem.ad_budget_amount && (expectedUsage * 0.9 > selectedPortfolioItem.ad_budget_amount || expectedUsage * 1.1 < selectedPortfolioItem.ad_budget_amount)
  const budgetAbnormality = selectedPortfolioItem.prediction?.budget_usage?.estimated_budget_state === "BUDGET_OVER" || selectedPortfolioItem.prediction?.budget_usage?.estimated_budget_state === "BUDGET_LACK"


  const fetchOptimizationHistory = async (optimizationId: string, targetStartDate: Date, targetEndDate: Date, type: string) => {
    if (!(session?.user as any).access_token) {
      console.log('access token is missing in the session.')
    }
    if (targetStartDate === null || targetEndDate === null) {
      return
    }
    setIsHistoryLoading(true)
    let optimizationHistoryResponse = await api.getListOptimizationHistory(optimizationId, formatDate(targetStartDate), formatDate(targetEndDate), type, (session?.user as any).access_token)
    setIsHistoryLoading(false)
    optimizationHistoryResponse?.length >= 0 && setOptimizationHistory(optimizationHistoryResponse || [])
  }
  const fetchBudgetSensitivitySummary = async (optimizationId: string) => {
    if (!(session?.user as any).access_token) {
      console.log('access token is missing in the session.')
    }
    setIsBudgetSensitivityLoading(true)
    let optimizationBudgetSensitivitySummaryResponse = await api.getOptimizationBudgetSensitivitySummary(optimizationId, (session?.user as any).access_token)
    setIsBudgetSensitivityLoading(false)
    // optimizationBudgetSensitivitySummaryResponse = {
    //   "optimization_id": 104,
    //   "summary": {
    //     "total_profit": {
    //       "max_value": 7158.17,
    //       "budget_ratio_at_max": 1.82275,
    //       "budget_ratio_percentage": "182.3%"
    //     },
    //     "total_sales": {
    //       "max_value": 8286.9,
    //       "budget_ratio_at_max": 1.82275,
    //       "budget_ratio_percentage": "182.3%"
    //     },
    //     "total_cost": {
    //       "min_value": 435.833,
    //       "budget_ratio_at_min": 0.263203,
    //       "budget_ratio_percentage": "26.3%"
    //     },
    //     "ad_spending": {
    //       "min_value": 118.025,
    //       "budget_ratio_at_min": 0.263203,
    //       "budget_ratio_percentage": "26.3%"
    //     },
    //     "ad_sales": {
    //       "max_value": 1171.95,
    //       "budget_ratio_at_max": 1.82275,
    //       "budget_ratio_percentage": "182.3%"
    //     },
    //     "ad_sales_same_sku": {
    //       "max_value": 914.311,
    //       "budget_ratio_at_max": 1.82275,
    //       "budget_ratio_percentage": "182.3%"
    //     }
    //   }
    // }
    optimizationBudgetSensitivitySummaryResponse?.optimization_id && setOptimizationBudgetSensitivitySummary(optimizationBudgetSensitivitySummaryResponse)
  }
  const fetchBudgetSensitivityDetail = async (optimizationId: string) => {
    if (!(session?.user as any).access_token) {
      console.log('access token is missing in the session.')
    }
    setIsBudgetSensitivityLoading(true)
    let optimizationBudgetSensitivityDetailResponse = await api.getOptimizationBudgetSensitivityDetail(optimizationId, (session?.user as any).access_token)
    setIsBudgetSensitivityLoading(false)
    optimizationBudgetSensitivityDetailResponse?.optimization_id && setOptimizationBudgetSensitivityDetail(optimizationBudgetSensitivityDetailResponse)
  }



  // ASIN별 총 예산 사용량 계산 함수 (메모이제이션 적용)
  const calculateTotalProductUsage = useMemo(() => {
    return (asin: string): number => {
      // Campaign Budget Usage API 데이터가 없는 경우 0 반환
      if (!campaignBudgetUsage || !campaignBudgetUsage.asins) {
        return 0
      }
      
      const asinData = campaignBudgetUsage.asins.find((asinItem) => asinItem.asin === asin)
      if (!asinData || !asinData.campaigns) {
        return 0
      }
      
      return asinData.campaigns.reduce((acc, campaign) => {
        return acc + (campaign.total_budget_usage || 0)
      }, 0)
    }
  }, [campaignBudgetUsage])

  useEffect(() => {
    if (!selectedPortfolioItem) {
      return
    }
    fetchOptimizationHistory(selectedPortfolioItem.id.toString(), startDate, endDate, "")
  }, [])
  useEffect(() => {
    if (!selectedPortfolioItem) {
      return
    }
    if (startDate && endDate) {
      fetchOptimizationHistory(selectedPortfolioItem.id.toString(), startDate, endDate, "")
    }
  }, [dateRange])
  useEffect(() => {
    if (selectedPortfolioItem) {
      fetchBudgetSensitivitySummary(selectedPortfolioItem.id.toString())
      fetchBudgetSensitivityDetail(selectedPortfolioItem.id.toString())
      fetchCampaignBudgetUsage(selectedPortfolioItem.id)
    }
  }, [selectedPortfolioItem])
  
  const productListItemCard = (item: ProductListItem, mode: string) => {
    return (
      <div className="grow relative flex items-center gap-x-4 px-4 overflow-hidden">
        { item.image
        ? (<img src={item.image} alt="Item Image" className="flex-shrink-0 w-10 h-10 rounded" />)
        : (<div className="flex-shrink-0 flex items-center justify-center w-10 h-10 bg-gray-100 rounded">
            <ExclamationTriangleIcon className="h-5 w-5 text-gray-300" />
          </div>)
        }
        <div className="flex-1 flex flex-col gap-y-0.5 overflow-hidden">
          <div className={cn(
            "text-xs text-left font-semibold truncate",
            mode === "dark" ? "text-gray-200" : "text-gray-500"
          )}>
            {item.item_name
              ? item.item_name
              : "No Title"
            }
          </div>
          <div className={cn(
            "flex items-center gap-x-2 divide-x divide-gray-100",
            mode === "dark" ? "divide-gray-600" : "divide-gray-100"
          )}>
            <div className="text-xs text-red-400 font-semibold">{formatCurrency(item.listing_price || 0, currencyCode)}</div>
            {item.eligibility_status &&
              item.eligibility_status === "ELIGIBLE"
                ? <div className="pl-2 text-xs text-blue-400 font-semibold">{t("eligibility.eligible")}</div>
                : item.eligibility_status === "INELIGIBLE"
                  ? <div className="pl-2 text-xs text-gray-400 font-semibold">{t("eligibility.ineligible")}</div>
                  : <div className="pl-2 text-xs text-gray-400 font-semibold">{t("eligibility.unknown")}</div>
            }
          </div>
          <div className={cn(
            "flex items-center gap-x-2 divide-x",
            mode === "dark" ? "divide-gray-600" : "divide-gray-100"
          )}>
            <div className="text-[10px] text-gray-400 truncate">{tos("detailModal.optSet.budgetTab.table.content.productInfo.asin")}: {item.asin}</div>
            <div className="pl-2 text-[10px] text-gray-400 truncate">{tos("detailModal.optSet.budgetTab.table.content.productInfo.sku")}: {item.sku}</div>
          </div>
        </div>
      </div>
    )
  }
  
  const pacingContent = (selectedPortfolioItem: PortfolioListItem) => {
    return (
      <div className="flex flex-col h-full gap-y-4 items-start justify-start">
        <div className="flex-shrink-0 w-full pl-5">
          <div className="flex items-start w-full gap-x-3">
            <div className="grow">
              {/* Campaign Budget Usage 로딩 중일 때 스켈레톤 표시 */}
              {!campaignBudgetUsage ? (
                <div className="animate-pulse">
                  <div className="w-full h-8 bg-gray-200 rounded-sm"></div>
                  <div className="mt-1 flex justify-between">
                    <div className="w-4 h-3 bg-gray-200 rounded"></div>
                    <div className="w-16 h-3 bg-gray-200 rounded"></div>
                  </div>
                </div>
              ) : (
                <>
                  <div className="relative flex items-center justify-start w-full h-8 bg-gray-100 rounded-sm gap-x-0.5">
                     {selectedPortfolioItem.target_products.map((item, index) => {
                       const total_product_usage = calculateTotalProductUsage(item.asin)
                       const total_product_percent = selectedPortfolioItem.ad_budget_amount
                         ? total_product_usage / selectedPortfolioItem.ad_budget_amount * 100
                         : 0
                       return item ? (
                         <div
                           className={cn(
                             "group relative flex items-center justify-end min-w-[48px] h-full text-xs border font-semibold rounded-sm cursor-pointer",
                             `${colorSetList[index % colorSetList.length].border} ${colorSetList[index % colorSetList.length].bg} ${colorSetList[index % colorSetList.length].hoverbg} ${colorSetList[index % colorSetList.length].text} ${colorSetList[index % colorSetList.length].hovertext}`,
                             total_product_percent > 0 ? "" : "hidden"
                           )}
                           style={{ width: `${total_product_percent}%` }}
                           key={index}
                           onClick={() => handleProductItemClick(selectedPortfolioItem, item)}
                         >
                           <div className="pl-1">{total_product_percent.toFixed(2)}%</div>
                           <div className="shrink w-2 h-full"></div>
                           <div
                             className={cn(
                               "hidden group-hover:block z-[2] absolute -bottom-[82px] min-w-[400px] max-w-[440px] py-3 bg-gray-900/90 shadow-lg rounded-lg",
                               selectedPortfolioItem.target_products.length / 2 > index
                                 ? "left-0"
                                 : "right-0"
 
                             )}>
                             {productListItemCard(item, "dark")}
                           </div>
                         </div>
                       ) : null
                     })}
                   </div>
                  <div className="mt-1 w-full flex items-center justify-between text-xs">
                    <div className="text-gray-400">0</div>
                    <div className="text-gray-400">{formatCurrency(selectedPortfolioItem.ad_budget_amount || 0, currencyCode, { maximumFractionDigits: 0, minimumFractionDigits: 0 })}</div>
                  </div>
                </>
              )}
            </div>
            <div className="mt-2 flex-shrink-0 text-xs text-gray-500 font-semibold">
              {/* Budget Pacing API의 spent_budget 사용 */}
              {formatCurrency(budgetPacingData?.spent_budget || 0, currencyCode, { maximumFractionDigits: 0, minimumFractionDigits: 0 })}
              <span className="pl-1 text-gray-400 font-normal">
                ({selectedPortfolioItem.ad_budget_amount
                  ? ((budgetPacingData?.spent_budget || 0) * 100 / selectedPortfolioItem.ad_budget_amount).toFixed(2)
                  : 0
                }%)
              </span>
            </div>
          </div>
        </div>
        {/* search box */}
        <div className="relative flex-shrink-0 w-full rounded-lg overflow-hidden cursor-pointer">
          <MagnifyingGlassIcon
            className={cn(
              "h-5 w-5 absolute top-1/2 left-3 transform -translate-y-1/2",
              productSearchText ? "text-gray-500" : "text-gray-300"
            )}
          />
          <input
            type="text"
            className="w-full pl-10 pr-4 py-2 bg-white text-sm text-gray-500 placeholder:text-gray-300 border border-gray-100 focus:border-transparent focus:outline-blue-200 rounded-lg"
            value={productSearchText}
            onChange={(e) => setProductSearchText(e.target.value)}
            placeholder= {tos("detailModal.optSet.budgetTab.searchBar.placeholder")} 
          />
        </div>
        {/* Product List in a Portfolio */}
        <div className="grow relative w-full rounded-lg bg-white border border-gray-100 overflow-y-scroll">
          <div className="sticky inset-x-0 top-0 z-[1] flex items-center gap-x-3 py-3 text-center text-sm text-gray-400 font-bold bg-gray-100/40 backdrop-blur-md">
            <div className="flex-shrink-0 w-[140px] pl-8">
              {tos("detailModal.optSet.budgetTab.table.header.status")}
            </div>
            <div className="grow px-8">
              {tos("detailModal.optSet.budgetTab.table.header.productInfo")}
            </div>
            <div className="flex-shrink-0 w-[180px] pr-6">
              {tos("detailModal.optSet.budgetTab.table.header.totalBudgetUsage")}
            </div>
          </div>
          <ul className="divide-y divide-gray-100">
            {(() => {
              const searchResult = selectedPortfolioItem?.target_products?.filter((item) => {
                return productSearchText
                  ? item?.item_name?.toLowerCase().includes(productSearchText.toLowerCase()) || item?.asin?.toLowerCase().includes(productSearchText.toLowerCase())
                  : true
              }) || []
              return searchResult.length === 0
                ? <div className="absolute inset-0 flex items-center justify-center w-full text-gray-300 text-sm font-normal">
                    { productSearchText
                    // ? "No search result found"
                    ? tos("detailModal.optSet.budgetTab.table.content.searchResultNull")
                    // : "No optimization sets"
                    : tos("detailModal.optSet.budgetTab.table.content.optSetNull")
                    }
                  </div>
                : searchResult.map((item, index) => {
                  const total_product_usage = calculateTotalProductUsage(item.asin)
                  const total_product_percent = selectedPortfolioItem.ad_budget_amount
                    ? total_product_usage / selectedPortfolioItem.ad_budget_amount * 100
                    : 0
                  return item && (
                    <li
                      className="relative flex items-center gap-x-3 py-6 cursor-pointer hover:bg-gray-100/50 text-center text-gray-500 text-sm"
                      key={index}
                      onClick={() => handleProductItemClick(selectedPortfolioItem, item)}
                    >
                      <div className="relative flex-shrink-0 flex flex-col items-start justify-center gap-x-2 gap-y-2 w-[140px] pl-8">
                        {(() => {
                          const targetProductInventory = selectedPortfolioItem.prediction?.inventory?.find((inventory: any) => inventory.asin === item.asin)
                          const eligibilityAbnormality = targetProductInventory
                            ? targetProductInventory.estimated_inventory_state === "DANGER"
                            : false
                          return (
                            item.eligibility_status === "INELIGIBLE"
                              ? <div className={cn(
                                  "absolute pl-4 left-3.5 -top-4 flex items-center gap-x-1 text-[10px] font-semibold text-red-400 rounded-md",
                                )}>
                                  <ExclamationTriangleIcon className="h-3 w-3" />
                                  {tos("detailModal.optSet.budgetTab.table.content.statusAlert.abnormal")}
                                </div>
                              : eligibilityAbnormality
                                ? <div className={cn(
                                    "absolute pl-4 left-3.5 -top-4 flex items-center gap-x-1 text-[10px] font-semibold text-orange-400 rounded-md",
                                  )}>
                                  <ExclamationTriangleIcon className="h-3 w-3" />
                                  {tos("detailModal.optSet.budgetTab.table.content.statusAlert.warning")}
                                </div>
                                : ""
                          )
                        })()}
                        <ProductStatus productItem={item} />
                      </div>
                      {productListItemCard(item, "light")}
                      <div className="flex-shrink-0 w-[180px] pr-6">
                        {/* Campaign Budget Usage 로딩 중일 때 스켈레톤 표시 */}
                        {!campaignBudgetUsage ? (
                          <div className="animate-pulse flex items-start justify-center w-full gap-x-3">
                            <div className="w-[70px] h-8 bg-gray-200 rounded-sm"></div>
                            <div className="mt-2 w-16 h-3 bg-gray-200 rounded"></div>
                          </div>
                        ) : (
                          <div className="flex items-start justify-center w-full gap-x-3">
                            <div
                              className={cn(
                                "inline-flex items-center justify-end w-[70px] h-8 px-3 text-xs border font-semibold rounded-sm",
                                `${colorSetList[index % colorSetList.length].border} ${colorSetList[index % colorSetList.length].bg} ${colorSetList[index % colorSetList.length].text}`
                              )}
                            >
                              {total_product_percent.toFixed(2)}%
                            </div>
                            <div className="mt-2 flex-shrink-0 text-xs text-gray-500 font-semibold">
                              {formatCurrency(total_product_usage || 0, currencyCode)}
                            </div>
                          </div>
                        )}
                      </div>
                    </li>
                  )
                })
            })()}
          </ul>
        </div>
      </div>
    )
  }
  const historyContent = (selectedPortfolioItem: PortfolioListItem) => {
    const historyTypeMapper = {
      "CREATE_OPTIMIZATIONS": "Optimization set created",
      "EDIT_OPTIMIZATIONS": "Optimization set edited",
      "PAUSE_OPTIMIZATIONS": "Optimization set paused/resumed",
    }
    const historyKeyMapper = {
      "ad_budget_type": "budget policy",
      "ad_budget_amount": "budget amount",
      "ad_budget_start_date": "budget start date",
      "ad_budget_end_date": "budget end date",
      "bid_yn": "bid status",
    }
    return (
      <div className="flex flex-col h-full gap-y-4 items-start justify-start">
        <div className="flex-shrink-0 flex items-center gap-x-3">
          {/* <div>
            <div className="text-xs text-gray-400 font-semibold">History Type</div>
            <ProductHistoryTypeSelect
              className="mt-1 z-[2]"
              listboxClassName="absolute mt-1 max-h-60 w-full overflow-auto rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black/5 focus:outline-none sm:text-sm"
              selected={selectedProductHistoryType}
              setSelected={setSelectedProductHistoryType}
            />
          </div> */}
          <div className="history-date-range">
            <div className="text-xs text-gray-400 font-semibold">{tos("detailModal.optSet.historyTab.filter.dateRange.label")} *</div>
            {isHistoryLoading
              ? <div className="animate-pulse pt-1">
                  <div className="w-[200px] h-9 rounded-md bg-gray-100"/>
                </div>
              : <DatePicker
                  selectsRange={true}
                  minDate={new Date('2023-01-01')}
                  maxDate={new Date()}
                  startDate={startDate}
                  endDate={endDate}
                  onChange={(update) => {
                    setDateRange(update as Date[])
                  }}
                  dateFormat="yyyy.MM.dd"
                  calendarClassName="dashboard-date-range"
                  // @ts-ignore
                  customInput={<DateRangeInput />}
                  locale={locale}
                />
            }
          </div>
        </div>
        <div className="grow relative w-full rounded-lg bg-white border border-gray-100 overflow-y-scroll">
          {/* List */}
          <div className="sticky inset-x-0 top-0 z-[1] flex items-center gap-x-3 py-3 text-center text-sm text-gray-400 font-bold bg-gray-100/40 backdrop-blur-md">
            <div className="flex-shrink-0 w-[160px] px-4">
              {tos("detailModal.optSet.historyTab.table.header.actionType")}
            </div>
            <div className="grow px-4">
              {tos("detailModal.optSet.historyTab.table.header.actionDetails")}
            </div>
            <div className="flex-shrink-0 w-[200px] px-4">
              {tos("detailModal.optSet.historyTab.table.header.dateTime")}
            </div>
          </div>
          { isHistoryLoading
            ? <ul className="animate-pulse p-6 space-y-3">
                {Array.from({ length: 8 }, (_, index) => (
                  <li key={index} className="w-full flex items-center gap-x-3">
                    <div className="flex-shrink-0 w-[160px] h-16 rounded-md bg-gray-100"></div>
                    <div className="flex-1 h-16 rounded-md bg-gray-100"></div>
                    <div className="flex-shrink-0 w-[200px] h-16 rounded-md bg-gray-100"></div>
                  </li>
                ))}
              </ul>
            : <ul className="divide-y divide-gray-100">
              {
                optimizationHistory.length === 0
                  ? <div className="absolute inset-0 flex items-center justify-center w-full text-gray-300 text-sm font-normal">
                      {tos("detailModal.optSet.historyTab.table.content.null")}
                    </div>
                  : optimizationHistory.map((historyItem, index) => {
                      return (
                        <li
                          className="relative flex items-center gap-x-3 py-6 cursor-pointer hover:bg-gray-100/50 text-center text-gray-500 text-sm"
                          key={index}
                        >
                          <div className="flex-shrink-0 w-[160px] px-4 text-xs font-semibold">
                            {historyTypeMapper[historyItem.type as keyof typeof historyTypeMapper] || historyItem.type}
                          </div>
                          <div className="grow relative flex flex-col px-4 overflow-hidden text-left">
                            <div className="space-y-2 text-xs text-left break-words">
                              {Object.entries(historyItem.values).map(([key, value], index) => (
                                <div key={key + "-" + index}>
                                  <div className="text-gray-400 text-[10px]">
                                    {historyKeyMapper[key as keyof typeof historyKeyMapper] || key}
                                  </div>
                                  <div className="text-xs text-gray-500">
                                    {key === "bid_yn"
                                      ? value === "Y"
                                        ? "Resumed"
                                        : "Paused"
                                      : key === "ad_budget_start_date" || key === "ad_budget_end_date"
                                        ? value
                                          ? (value as string).slice(0, 4) + "." + (value as string).slice(4, 6) + "." + (value as string).slice(6)
                                          : "Not set"
                                        : value as React.ReactNode
                                    }
                                  </div>
                                </div>
                              ))}
                            </div>
                          </div>
                          <div className="flex-shrink-0 w-[200px] px-4">
                            {formatDateTime(historyItem.datetime, ".")}
                          </div>
                        </li>
                      )
                    })
              }
              </ul>
          }
        </div>
      </div>
    )
  }
  return (
    <>
      <TransitionChild
        as={Fragment}
        enter="ease-out duration-300"
        enterFrom="opacity-0"
        enterTo="opacity-100"
        leave="ease-in duration-200"
        leaveFrom="opacity-100"
        leaveTo="opacity-0"
      >
        <div className="w-full h-full bg-black/30" onClick={handlePortfolioViewCloseClick} />
      </TransitionChild>
      <TransitionChild
        as={Fragment}
        enter="ease-out duration-300"
        enterFrom="opacity-0 w-[0vw]"
        enterTo="opacity-100 w-[80vw]"
        leave="ease-in duration-200"
        leaveFrom="opacity-100 w-[80vw]"
        leaveTo="opacity-0 w-[0vw]"
      >
        <div className="absolute inset-y-0 right-0 w-[80vw] h-full overflow-hidden shadow-md">
        {/* item content for portfolio item */}
        { selectedPortfolioItem &&
          <div className="flex-shrink-0 flex flex-col w-[80vw] h-full px-6 pt-6 pb-10 bg-white">
            {/* right side slider header */}
            <div className="flex-shrink-0 w-full flex flex-col gap-y-3">
              {(budgetAbnormality || ineligibleProducts.length > 0) &&
                <div className="relative flex-shrink-0 w-full bg-red-100 rounded-md border border-red-400 overflow-hidden">
                  <div className="w-full flex items-center justify-between py-2 px-4 bg-red-400 text-xs font-semibold text-white">
                    <div className="flex items-center gap-x-1">
                      <ExclamationTriangleIcon className="h-4 w-4" />
                      <span>
                        {budgetAbnormality
                          ? selectedPortfolioItem.ad_budget_amount && selectedPortfolioItem.prediction?.budget_usage?.estimated_budget_state === "BUDGET_LACK"
                            ? tos("detailModal.optSet.message.budgetIncrease")
                            : tos("detailModal.optSet.message.budgetDecrease")
                          : ""
                        }
                      </span>
                      <span>
                        {budgetAbnormality && ineligibleProducts.length > 0
                          ? tos("detailModal.optSet.message.and")
                          : ""
                        }
                      </span>
                      <span>
                        {ineligibleProducts.length > 0
                          ? tos("detailModal.optSet.message.ineligible")
                          : ""
                        }
                      </span>
                    </div>
                  </div>
                  <div className="grow relative flex items-center w-full py-4 gap-x-4 overflow-y-scroll text-xs text-gray-500 divide-x divide-red-400">
                    {budgetAbnormality && selectedPortfolioItem.ad_budget_amount &&
                    <>
                      <div className="pl-4 flex items-center gap-x-1">
                        {/* 
                        <div className="font-semibold text-lg text-red-500">${selectedPortfolioItem.ad_budget_amount.toFixed(2)}</div>
                        <div className="pt-1"> {tos("detailModal.optSet.message.totalBudget")}</div> */}
                        {
                          tos.rich("detailModal.optSet.message.totalBudget",{
                            value: formatCurrency(selectedPortfolioItem.ad_budget_amount || 0, currencyCode, { maximumFractionDigits: 0, minimumFractionDigits: 0 }),
                            first: (chunks) => <div className="font-semibold text-lg text-red-500">{chunks}</div>,
                            second: (chunks) => <div className="pt-1">{chunks}</div>
                          })
                        }
                      </div>
                      <div className="pl-4 flex items-center gap-x-1">
                      {/* <div className="font-semibold text-lg text-red-500">${expectedUsage.toFixed(2)}</div>
                        <div className="pt-1"> {tos("detailModal.optSet.message.expectedUsage")}</div> */}
                      {
                        tos.rich("detailModal.optSet.message.expectedUsage",{
                          value: formatCurrency(expectedUsage || 0, currencyCode, { maximumFractionDigits: 0, minimumFractionDigits: 0 }),
                          first: (chunks) => <div className="font-semibold text-lg text-red-500">{chunks}</div>,
                          second: (chunks) => <div className="pt-1">{chunks}</div>
                        })
                      }
                      </div>
                      <div className="pl-4 flex items-center gap-x-1">
                      {
                        tos.rich("detailModal.optSet.message.feasibleBudgetRange",{
                          value: formatCurrency(minFeasibleBudget || 0, currencyCode, { maximumFractionDigits: 0, minimumFractionDigits: 0 }) + " ~ " + formatCurrency(maxFeasibleBudget || 0, currencyCode, { maximumFractionDigits: 0, minimumFractionDigits: 0 }),
                          first: (chunks) => <div className="font-semibold text-lg text-red-500">{chunks}</div>,
                          second: (chunks) => <div className="pt-1">{chunks}</div>
                        })
                      }
                      </div>
                    </>
                    }
                    {ineligibleProducts.length > 0 &&
                    <div className="pl-4 flex items-center gap-x-1">
                      {/*
                        <div className="font-semibold text-lg text-red-500">{ineligibleProducts.length}</div>
                        <div className="pt-1">
                          <span className="font-semibold text-red-500"> {tos("detailModal.optSet.message.ineligibleDetails")}</span> {tos("detailModal.optSet.message.exist")}
                        </div>
                      */}
                      {
                        tos.rich("detailModal.optSet.message.ineligibleDetails",{
                          value: ineligibleProducts.length,
                          red: (chunks) => <span className="font-semibold text-red-500">{chunks}</span>,
                          first: (chunks) => <div className="font-semibold text-lg text-red-500">{chunks}</div>,
                          second: (chunks) => <div className="pt-1">{chunks}</div>
                        })
                      }
                    </div>
                    }
                  </div>
                </div>
              }
              <div className="flex-shrink-0 w-full flex items-center justify-between">
                <button onClick={handlePortfolioViewCloseClick}>
                  <XMarkIcon className="h-5 w-5 text-gray-500 hover:text-gray-800" />
                </button>
                <div className="flex items-center gap-x-4">
                  <PortfolioStatus portfolioItem={selectedPortfolioItem} />
                  <button
                    data-testid="edit-button"
                    className={cn(
                      "flex items-center gap-x-2 p-3 cursor-pointer rounded-md focus:outline-none text-sm overflow-hidden font-semibold",
                      isOptSetsLoading 
                        ? "bg-gray-200 text-gray-500 cursor-not-allowed" 
                        : "bg-blue-100 hover:bg-blue-200 text-blue-500"
                    )}
                    onClick={() => handleEditPortfolioClick(selectedPortfolioItem)}
                    disabled={isOptSetsLoading}
                  >
                    <PencilSquareIcon
                      className="flex-shrink-0 h-4 w-4"
                      aria-hidden="true"
                    />
                    <div>{tos("detailModal.optSet.topButton.edit")}</div>
                  </button>
                  { selectedPortfolioItem && selectedPortfolioItem.use_yn === "Y" && selectedPortfolioItem.bid_yn === "N"
                    ? (<button
                        data-testid="resume-button"
                        className={cn(
                          "flex items-center gap-x-2 p-3 cursor-pointer rounded-md focus:outline-none text-sm overflow-hidden bg-purple-100 hover:bg-purple-200 text-purple-500 font-semibold",
                          isResumeLoading && "cursor-not-allowed"
                          )}
                        onClick={async () => {
                          setIsResumeLoading(true)
                          await handleResumePortfolioClick(selectedPortfolioItem)
                          setIsResumeLoading(false)
                        }}
                        disabled={isResumeLoading}
                      >
                        {isResumeLoading
                          ? <svg className="animate-spin h-4 w-4 text-purple-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                            </svg>
                          : <PlayIcon
                              className="flex-shrink-0 h-3 w-3"
                              aria-hidden="true"
                            />
                        }
                        <div>{tos("detailModal.optSet.topButton.resume")}</div>
                      </button>)
                    : selectedPortfolioItem && selectedPortfolioItem.use_yn === "Y" && selectedPortfolioItem.bid_yn === "Y"
                      ? (<button
                          data-testid="pause-button"
                          className={cn(
                            "flex items-center gap-x-2 p-3 cursor-pointer rounded-md focus:outline-none text-sm overflow-hidden bg-purple-100 hover:bg-purple-200 text-purple-500 font-semibold",
                            isPauseLoading && "cursor-not-allowed"
                            )}
                          onClick={async () => {
                            setIsPauseLoading(true)
                            await handlePausePortfolioClick(selectedPortfolioItem)
                            setIsPauseLoading(false)
                          }}
                          disabled={isPauseLoading}
                        >
                          {isPauseLoading
                            ? <svg className="animate-spin h-4 w-4 text-purple-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                              </svg>
                            : <PauseIcon
                                className="flex-shrink-0 h-3 w-3"
                                aria-hidden="true"
                              />
                          }
                          <div>{tos("detailModal.optSet.topButton.pause")}</div>
                        </button>)
                      : ""
                  }
                  <Menu>
                    <MenuButton className="w-[44px] h-[44px] flex items-center justify-center cursor-pointer rounded-md focus:outline-none text-sm overflow-hidden border border-gray-200 hover:bg-gray-100 text-gray-500 font-semibold">
                      <EllipsisHorizontalIcon
                        className="flex-shrink-0 h-6 w-6"
                        aria-hidden="true"
                      />
                    </MenuButton>
                    <Transition
                      as={Fragment}
                      leave="transition ease-in duration-100"
                      leaveFrom="opacity-100"
                      leaveTo="opacity-0"
                    >
                      <MenuItems
                        anchor="bottom end"
                        className="z-[1] mt-1 overflow-auto rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black/5 focus:outline-none sm:text-sm divide-y divide-gray-100">
                        <MenuItem>
                          <button
                            onClick={() => setIsDeleteModalOpen(true)}
                            className="cursor-pointer flex items-center gap-x-2 py-2 px-4 text-red-500 text-sm font-semibold hover:bg-gray-100"
                          >
                            <TrashIcon
                              className="flex-shrink-0 h-4 w-4"
                              aria-hidden="true"
                            />
                            {tos("detailModal.optSet.topButton.delete")}
                          </button>
                        </MenuItem>
                      </MenuItems>
                    </Transition>
                  </Menu>
                  <Transition appear show={isDeleteModalOpen}>
                    <Dialog as="div" className="relative z-10 focus:outline-none" onClose={() => setIsDeleteModalOpen(false)}>
                      <div className="fixed inset-0 bg-black/30 backdrop-blur-sm" aria-hidden="true" />
                      <div className="fixed inset-0 z-10 w-screen overflow-y-auto">
                        <div className="flex min-h-full items-center justify-center p-4">
                          <TransitionChild
                            enter="ease-out duration-300"
                            enterFrom="opacity-0 transform-[scale(95%)]"
                            enterTo="opacity-100 transform-[scale(100%)]"
                            leave="ease-in duration-200"
                            leaveFrom="opacity-100 transform-[scale(100%)]"
                            leaveTo="opacity-0 transform-[scale(95%)]"
                          >
                            <DialogPanel className="w-full max-w-md rounded-xl bg-white p-6">
                              <DialogTitle as="h3" className="text-base/7 font-medium text-gray-600">
                                Delete Optimization Set
                              </DialogTitle>
                              <p className="mt-2 text-sm/6 text-gray-500">
                                Are you sure you want to delete this optimization set?
                                <br/>
                                All related campaigns created and manged by Optapex will be paused immediately.
                              </p>
                              <div className="mt-4 flex items-center justify-end gap-x-4">
                                <Button
                                  className="inline-flex items-center gap-2 rounded-md bg-white py-1.5 px-3 text-sm/6 font-semibold text-gray-500 shadow-inner shadow-white/10 focus:outline-none data-[hover]:bg-gray-100 data-[open]:bg-gray-700 data-[focus]:outline-1 data-[focus]:outline-white"
                                  onClick={() => setIsDeleteModalOpen(false)}
                                >
                                  Cancel
                                </Button>
                                <Button
                                  className={cn(
                                    "inline-flex items-center gap-2 rounded-md bg-red-600 py-1.5 px-3 text-sm/6 font-semibold text-white shadow-inner shadow-white/10 focus:outline-none data-[hover]:bg-red-700 data-[open]:bg-gray-700 data-[focus]:outline-1 data-[focus]:outline-white cursor-pointer",
                                    isDeleteLoading && "cursor-not-allowed"
                                    )}
                                  onClick={async () => {
                                    setIsDeleteLoading(true)
                                    await handleDeletePortfolioClick(selectedPortfolioItem)
                                    setIsDeleteLoading(false)
                                    setIsDeleteModalOpen(false)
                                  }}
                                  disabled={isDeleteLoading}
                                >
                                  {isDeleteLoading
                                    ? <svg className="animate-spin h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                      </svg>
                                    : ""
                                  }
                                  <div>Delete</div>
                                </Button>
                              </div>
                            </DialogPanel>
                          </TransitionChild>
                        </div>
                      </div>
                    </Dialog>
                  </Transition>
                </div>
              </div>
            </div>
            <div className="grow relative flex flex-col w-full min-h-0">
              <div className="flex-shrink-0">
                <div className="flex items-center pt-3 pb-6 gap-x-6 border-b border-gray-100">
                  {/* optimization set info */}
                  <div className="flex-shrink-0 px-6">
                    <div className="text-gray-400 text-xs font-semibold">
                      {tos("detailModal.optSet.optProfile.label")}
                    </div>
                    <div className="mt-1 text-gray-500 text-lg font-semibold">
                      {selectedPortfolioItem.optimization_name}
                    </div>
                    <div className="mt-6 grid grid-cols-2 divide-x divide-gray-100">
                      <div className="pr-6">
                        <div className="flex items-center">
                          <div className="mr-2 py-0.5 px-1.5 bg-gray-200 text-xs text-gray-500 font-normal rounded-full break-normal hover:no-underline">
                            {tos("detailModal.optSet.optProfile.optSetObjective.label")}
                          </div>
                          <div className="text-gray-500 text-sm">
                            {selectedPortfolioItem.optimization_goal === "SALES"
                              ? selectedProfile.account_type === "vendor" ? tos("detailModal.optSet.optProfile.optSetObjective.objectiveType.vendorMaxSales") : tos("detailModal.optSet.optProfile.optSetObjective.objectiveType.maxSales")
                              : selectedPortfolioItem.optimization_goal === "ROI"
                                ? selectedProfile.account_type === "vendor" ? tos("detailModal.optSet.optProfile.optSetObjective.objectiveType.vendorMaxProfit") : tos("detailModal.optSet.optProfile.optSetObjective.objectiveType.maxProfit")
                                : selectedPortfolioItem.optimization_goal === "REVENUE"
                                  ? tos("detailModal.optSet.optProfile.optSetObjective.objectiveType.maxAdSales")
                                  : tos("detailModal.optSet.optProfile.optSetObjective.objectiveType.maxRoas")
                            }
                          </div>
                        </div>
                        <div className="mt-2 flex items-center gap-x-4 divide-x divide-gray-100">
                          <div className="flex items-center gap-x-1 text-gray-400 text-xs">
                            <FireIcon className="h-4 w-4" />
                            <div className="">
                              {selectedPortfolioItem.optimization_option === "INVENTORY"
                                ? tos("detailModal.optSet.optProfile.optSetObjective.option.minInventory")
                                : selectedPortfolioItem.optimization_option === "IMPRESSIONS"
                                  ? tos("detailModal.optSet.optProfile.optSetObjective.option.boostImpression")
                                  : tos("detailModal.optSet.optProfile.optSetObjective.option.default")
                              }
                            </div>
                          </div>
                          {selectedPortfolioItem.competition_option !== "NONE" &&
                          <div className="pl-4 flex items-center gap-x-1 text-gray-400 text-xs">
                            <TrophyIcon className="h-4 w-4" />
                            <div className="">
                              {selectedPortfolioItem.competition_option === "COMPETITOR_CONQUESTING"
                                ? "Competitor Conquesting"
                                : selectedPortfolioItem.competition_option === "BRAND_DEFENSE"
                                  ? "Brand Defense"
                                  : ""
                              }
                            </div>
                          </div>
                          }
                          {selectedPortfolioItem.target_same_sku_only_yn === "Y" &&
                          <div className="pl-4 flex items-center gap-x-1 text-gray-400 text-xs">
                            <ViewfinderCircleIcon className="h-4 w-4" />
                            <div className="">
                              {tos("addEditModal.objective.advancedOptions.salesTargeting.option")}
                            </div>
                          </div>
                          }
                        </div>
                        <div className="mt-4 flex items-center">
                          <div className="mr-2 py-0.5 px-1.5 bg-gray-200 text-xs text-gray-500 font-normal rounded-full break-normal hover:no-underline">
                            {tos("detailModal.optSet.optProfile.optSetPeriod.label")}
                          </div>
                          {selectedPortfolioItem.ad_budget_type === "DATERANGE"
                            ? (<div className="text-gray-500 text-sm">
                                <span>
                                  {formatDate(selectedPortfolioItem.ad_budget_start_date!, ".")}
                                </span>
                                <span className="px-1">-</span>
                                <span>
                                  {selectedPortfolioItem.ad_budget_end_date
                                    ? formatDate(selectedPortfolioItem.ad_budget_end_date, ".")
                                    : "unlimited"
                                  }
                                </span>
                              </div>)
                            : (<div className="text-gray-500 text-sm">
                              {selectedPortfolioItem.ad_budget_end_date && (new Date(selectedPortfolioItem.ad_budget_end_date)).getFullYear() >= 9000
                                ? (<>
                                  <span>
                                    Starting from {formatDate(selectedPortfolioItem.creation_datetime, ".")}
                                  </span>
                                  </>)
                                : (<>
                                  <span>
                                    {formatDate(selectedPortfolioItem.creation_datetime, ".")}
                                  </span>
                                  <span className="px-1">-</span>
                                  <span>
                                    {selectedPortfolioItem.ad_budget_end_date
                                      ? formatDate(selectedPortfolioItem.ad_budget_end_date, ".")
                                      : "not set"
                                    }
                                  </span>
                                  </>)
                              }
                            </div>)
                          }
                        </div>
                        <div className="mt-2 flex items-center gap-x-4 divide-x divide-gray-100">
                          <div className="flex items-center gap-x-1 text-gray-400 text-xs">
                            <CalendarDaysIcon className="h-4 w-4" />
                            <div className="">
                              {selectedPortfolioItem.ad_budget_type === "DATERANGE"
                                ? tos("detailModal.optSet.optProfile.optSetPeriod.policy.daterange")
                                : tos("detailModal.optSet.optProfile.optSetPeriod.policy.monthly")
                              }
                            </div>
                          </div>
                          <div className="pl-4 flex items-center gap-x-1 text-gray-400 text-xs">
                            <CalendarIcon className="h-4 w-4" />
                            <div className="">
                              {tos("detailModal.optSet.optProfile.optSetPeriod.createdDate")} {formatDate(selectedPortfolioItem.creation_datetime, ".")}
                            </div>
                          </div>
                          {selectedPortfolioItem.limit_cpc !== null && selectedPortfolioItem.limit_cpc !== -1 &&
                          <div className="pl-4 flex items-center gap-x-1 text-gray-400 text-xs">
                            <CursorArrowRaysIcon className="h-4 w-4" />
                            <div className="">
                              {tos("addEditModal.scheduleBudget.limitCpc.label")}
                            </div>
                            <div className="">
                              {formatCurrency(selectedPortfolioItem.limit_cpc, currencyCode)}
                            </div>
                          </div>
                          }
                        </div>
                      </div>
                      <div className="pl-6 flex flex-col items-start justify-between">
                        <div className="">
                          <div className="flex items-center">
                            <div className="mr-2 py-0.5 px-1.5 bg-gray-200 text-xs text-gray-500 font-normal rounded-full break-normal hover:no-underline">
                              {selectedPortfolioItem.ad_budget_type === "DATERANGE" ? tos("detailModal.optSet.budgetPaceGraph.metricType.usage.daterange") : tos("detailModal.optSet.budgetPaceGraph.metricType.usage.monthly")} 
                            </div>
                            <div className="text-gray-500 text-sm" data-testid="usage">
                              {formatCurrency(budgetPacingData?.spent_budget || 0, currencyCode, { maximumFractionDigits: 0, minimumFractionDigits: 0 })}
                            </div>
                          </div>
                          <div className="mt-4 flex items-center">
                            <div className="mr-2 py-0.5 px-1.5 bg-gray-200 text-xs text-gray-500 font-normal rounded-full break-normal hover:no-underline">
                              {selectedPortfolioItem.ad_budget_type === "DATERANGE" ? tos("detailModal.optSet.budgetPaceGraph.metricType.budget.daterange") : tos("detailModal.optSet.budgetPaceGraph.metricType.budget.monthly")} 
                            </div>
                            <div className="text-gray-500 text-sm" data-testid="period">
                              {formatCurrency(selectedPortfolioItem.ad_budget_amount || 0, currencyCode, { maximumFractionDigits: 0, minimumFractionDigits: 0 })}
                            </div>
                          </div>
                        </div>
                        <div
                          className={cn(
                            "mt-4 py-2 px-3 group flex-shrink-0 rounded-md border border-gray-200",
                            budgetSensitivityStatus === "disabled"
                              ? "bg-gray-100 cursor-not-allowed"
                              : "hover:bg-gray-100 cursor-pointer"
                          )}
                          onClick={() => {
                            if (budgetSensitivityStatus !== "disabled") {
                              setIsSensitivityGraphModalOpen(true)
                            }
                          }}
                        >
                          <div className="flex item-center gap-x-4">
                            <div className={cn(
                              "text-gray-500 text-xs font-semibold",
                              budgetSensitivityStatus === "disabled" && "text-gray-400"
                            )}>
                              <span className="relative flex-shrink-0 block">
                                <span className="block absolute -right-1.5 -top-0.5 w-1.5 h-1.5 rounded-full bg-red-400 group-hover:bg-red-500"></span>
                                {tos("detailModal.optSet.budgetSensitivityGraph.cta")}
                              </span>
                            </div>
                            <button
                              className={cn(
                                "flex items-center justify-center text-gray-400",
                                budgetSensitivityStatus === "disabled"
                                  ? "cursor-not-allowed"
                                  : "group-hover:text-gray-500"
                              )}
                              disabled={budgetSensitivityStatus === "disabled"}
                            >
                              <ArrowRight	className="h-4 w-4" />
                            </button>
                          </div>
                          <Transition appear show={isSensitivityGraphModalOpen}>
                            <div>
                              <BudgetSensitivityOptimizeSlider
                                handleCloseClick={() => setIsSensitivityGraphModalOpen(false)}
                                selectedPortfolioItem={selectedPortfolioItem}
                                optimizationBudgetSensitivityDetail={optimizationBudgetSensitivityDetail}
                              />
                            </div>
                          </Transition>
                        </div>
                      </div>
                    </div>
                  </div>
                  {/* budget sensitivity graph */}
                  <div className="hidden relative flex-1 h-[206px]">
                    <div
                      className="group relative size-full bg-gray-900/90 hover:bg-gray-900/85 rounded-md cursor-pointer overflow-hidden"
                      onClick={() => setIsSensitivityGraphModalOpen(true)}
                    >
                      <div className="absolute w-full h-full inset-0 flex flex-col justify-between p-4">
                        <div className="rounded-md">
                          <div className="text-gray-400 text-xs font-semibold">
                            {tos("detailModal.optSet.budgetSensitivityGraph.title")}
                          </div>
                        </div>
                        { optimizationBudgetSensitivitySummary?.summary && (
                          <div className="grid grid-cols-3 grid-rows-2 gap-2">
                            {/* total profit */}
                            <div className="p-2 rounded-md bg-gray-100/10 shadow-sm backdrop-blur-sm">
                              <div className="text-gray-400 text-xs font-semibold">
                                {tos("detailModal.optSet.budgetSensitivityGraph.totalProfit.label")} 
                              </div>
                              <div className="mt-1 flex items-center justify-between text-xs text-gray-100 font-semibold">
                                <span className="text-[10px] text-gray-300">{tos("detailModal.optSet.budgetSensitivityGraph.maximum")}</span>
                                {formatCurrency(optimizationBudgetSensitivitySummary?.summary?.total_profit?.max_value || 0, currencyCode, { maximumFractionDigits: 0, minimumFractionDigits: 0 })}
                              </div>
                              <div className="mt-0.5 flex items-center justify-between text-xs text-gray-100 font-semibold">
                                <span className="text-[10px] text-gray-300">{tos("detailModal.optSet.budgetSensitivityGraph.optimalBudget")}</span>
                                <div
                                  className={cn(
                                    "flex items-center justify-center gap-x-1",
                                    optimizationBudgetSensitivitySummary?.summary?.total_profit?.budget_ratio_at_max - 1 === 0
                                      ? "text-gray-400"
                                      : optimizationBudgetSensitivitySummary?.summary?.total_profit?.budget_ratio_at_max - 1 > 0
                                        ? "text-red-400"
                                        : "text-blue-400"
                                  )}
                                >
                                  <ArrowDownCircleIcon
                                    className={cn(
                                      "h-4 w-4 inline-block",
                                      optimizationBudgetSensitivitySummary?.summary?.total_profit?.budget_ratio_at_max - 1 > 0
                                        ? "transform rotate-180"
                                        : ""
                                    )}
                                  />
                                  <span className="text-xs">
                                    {optimizationBudgetSensitivitySummary?.summary?.total_profit?.budget_ratio_at_max - 1 === 0
                                      ? '0%'
                                      : `${Math.abs((optimizationBudgetSensitivitySummary?.summary?.total_profit?.budget_ratio_at_max - 1) * 100).toFixed(0)}%`
                                    }
                                  </span>
                                </div>
                              </div>
                            </div>
                            {/* total sales */}
                            <div className="p-2 rounded-md bg-gray-100/10 shadow-sm backdrop-blur-sm">
                              <div className="text-gray-400 text-xs font-semibold">
                                {tos("detailModal.optSet.budgetSensitivityGraph.totalSales.label")} 
                              </div>
                              <div className="mt-1 flex items-center justify-between text-xs text-gray-100 font-semibold">
                                <span className="text-[10px] text-gray-300">{tos("detailModal.optSet.budgetSensitivityGraph.maximum")}</span>
                                {formatCurrency(optimizationBudgetSensitivitySummary?.summary?.total_sales?.max_value || 0, currencyCode, { maximumFractionDigits: 0, minimumFractionDigits: 0 })}
                              </div>
                              <div className="mt-0.5 flex items-center justify-between text-xs text-gray-100 font-semibold">
                                <span className="text-[10px] text-gray-300">{tos("detailModal.optSet.budgetSensitivityGraph.optimalBudget")}</span>
                                <div
                                  className={cn(
                                    "flex items-center justify-center gap-x-1",
                                    optimizationBudgetSensitivitySummary?.summary?.total_sales?.budget_ratio_at_max - 1 === 0
                                      ? "text-gray-400"
                                      : optimizationBudgetSensitivitySummary?.summary?.total_sales?.budget_ratio_at_max - 1 > 0
                                        ? "text-red-400"
                                        : "text-blue-400"
                                  )}
                                >
                                  <ArrowDownCircleIcon
                                    className={cn(
                                      "h-4 w-4 inline-block",
                                      optimizationBudgetSensitivitySummary?.summary?.total_sales?.budget_ratio_at_max - 1 > 0
                                        ? "transform rotate-180"
                                        : ""
                                    )}
                                  />
                                  <span className="text-xs">
                                    {optimizationBudgetSensitivitySummary?.summary?.total_sales?.budget_ratio_at_max - 1 === 0
                                      ? '0%'
                                      : `${Math.abs((optimizationBudgetSensitivitySummary?.summary?.total_sales?.budget_ratio_at_max - 1) * 100).toFixed(0)}%`
                                    }
                                  </span>
                                </div>
                              </div>
                            </div>
                            {/* total cost */}
                            <div className="p-2 rounded-md bg-gray-100/10 shadow-sm backdrop-blur-sm">
                              <div className="text-gray-400 text-xs font-semibold">
                                {tos("detailModal.optSet.budgetSensitivityGraph.totalCost.label")} 
                              </div>
                              <div className="mt-1 flex items-center justify-between text-xs text-gray-100 font-semibold">
                                <span className="text-[10px] text-gray-300">{tos("detailModal.optSet.budgetSensitivityGraph.minimum")}</span>
                                {formatCurrency(optimizationBudgetSensitivitySummary?.summary?.total_cost?.min_value || 0, currencyCode, { maximumFractionDigits: 0, minimumFractionDigits: 0 })}
                              </div>
                              <div className="mt-0.5 flex items-center justify-between text-xs text-gray-100 font-semibold">
                                <span className="text-[10px] text-gray-300">{tos("detailModal.optSet.budgetSensitivityGraph.optimalBudget")}</span>
                                <div
                                  className={cn(
                                    "flex items-center justify-center gap-x-1",
                                    optimizationBudgetSensitivitySummary?.summary?.total_cost?.budget_ratio_at_min - 1 === 0
                                      ? "text-gray-400"
                                      : optimizationBudgetSensitivitySummary?.summary?.total_cost?.budget_ratio_at_min - 1 > 0
                                        ? "text-red-400"
                                        : "text-blue-400"
                                  )}
                                >
                                  <ArrowDownCircleIcon
                                    className={cn(
                                      "h-4 w-4 inline-block",
                                      optimizationBudgetSensitivitySummary?.summary?.total_cost?.budget_ratio_at_min - 1 > 0
                                        ? "transform rotate-180"
                                        : ""
                                    )}
                                  />
                                  <span className="text-xs">
                                    {optimizationBudgetSensitivitySummary?.summary?.total_cost?.budget_ratio_at_min - 1 === 0
                                      ? '0%'
                                      : `${Math.abs((optimizationBudgetSensitivitySummary?.summary?.total_cost?.budget_ratio_at_min - 1) * 100).toFixed(0)}%`
                                    }
                                  </span>
                                </div>
                              </div>
                            </div>
                            {/* ad spend */}
                            <div className="p-2 rounded-md bg-gray-100/10 shadow-sm backdrop-blur-sm">
                              <div className="text-gray-400 text-xs font-semibold">
                                {tos("detailModal.optSet.budgetSensitivityGraph.adSpend.label")} 
                              </div>
                              <div className="mt-1 flex items-center justify-between text-xs text-gray-100 font-semibold">
                                <span className="text-[10px] text-gray-300">{tos("detailModal.optSet.budgetSensitivityGraph.minimum")}</span>
                                {formatCurrency(optimizationBudgetSensitivitySummary?.summary?.ad_spending?.min_value || 0, currencyCode, { maximumFractionDigits: 0, minimumFractionDigits: 0 })}
                              </div>
                              <div className="mt-0.5 flex items-center justify-between text-xs text-gray-100 font-semibold">
                                <span className="text-[10px] text-gray-300">{tos("detailModal.optSet.budgetSensitivityGraph.optimalBudget")}</span>
                                <div
                                  className={cn(
                                    "flex items-center justify-center gap-x-1",
                                    optimizationBudgetSensitivitySummary?.summary?.ad_spending?.budget_ratio_at_min - 1 === 0
                                      ? "text-gray-400"
                                      : optimizationBudgetSensitivitySummary?.summary?.ad_spending?.budget_ratio_at_min - 1 > 0
                                        ? "text-red-400"
                                        : "text-blue-400"
                                  )}
                                >
                                  <ArrowDownCircleIcon
                                    className={cn(
                                      "h-4 w-4 inline-block",
                                      optimizationBudgetSensitivitySummary?.summary?.ad_spending?.budget_ratio_at_min - 1 > 0
                                        ? "transform rotate-180"
                                        : ""
                                    )}
                                  />
                                  <span className="text-xs">
                                    {optimizationBudgetSensitivitySummary?.summary?.ad_spending?.budget_ratio_at_min - 1 === 0
                                      ? '0%'
                                      : `${Math.abs((optimizationBudgetSensitivitySummary?.summary?.ad_spending?.budget_ratio_at_min - 1) * 100).toFixed(0)}%`
                                    }
                                  </span>
                                </div>
                              </div>
                            </div>
                            {/* ad sales */}
                            <div className="p-2 rounded-md bg-gray-100/10 shadow-sm backdrop-blur-sm">
                              <div className="text-gray-400 text-xs font-semibold">
                                {tos("detailModal.optSet.budgetSensitivityGraph.adSales.label")} 
                              </div>
                              <div className="mt-1 flex items-center justify-between text-xs text-gray-100 font-semibold">
                                <span className="text-[10px] text-gray-300">{tos("detailModal.optSet.budgetSensitivityGraph.maximum")}</span>
                                {formatCurrency(optimizationBudgetSensitivitySummary?.summary?.ad_sales?.max_value || 0, currencyCode, { maximumFractionDigits: 0, minimumFractionDigits: 0 })}
                              </div>
                              <div className="mt-0.5 flex items-center justify-between text-xs text-gray-100 font-semibold">
                                <span className="text-[10px] text-gray-300">{tos("detailModal.optSet.budgetSensitivityGraph.optimalBudget")}</span>
                                <div
                                  className={cn(
                                    "flex items-center justify-center gap-x-1",
                                    optimizationBudgetSensitivitySummary?.summary?.ad_sales?.budget_ratio_at_max - 1 === 0
                                      ? "text-gray-400"
                                      : optimizationBudgetSensitivitySummary?.summary?.ad_sales?.budget_ratio_at_max - 1 > 0
                                        ? "text-red-400"
                                        : "text-blue-400"
                                  )}
                                >
                                  <ArrowDownCircleIcon
                                    className={cn(
                                      "h-4 w-4 inline-block",
                                      optimizationBudgetSensitivitySummary?.summary?.ad_sales?.budget_ratio_at_max - 1 > 0
                                        ? "transform rotate-180"
                                        : ""
                                    )}
                                  />
                                  <span className="text-xs">
                                    {optimizationBudgetSensitivitySummary?.summary?.ad_sales?.budget_ratio_at_max - 1 === 0
                                      ? '0%'
                                      : `${Math.abs((optimizationBudgetSensitivitySummary?.summary?.ad_sales?.budget_ratio_at_max - 1) * 100).toFixed(0)}%`
                                    }
                                  </span>
                                </div>
                              </div>
                            </div>
                            {/* ad sales same SKU */}
                            <div className="p-2 rounded-md bg-gray-100/10 shadow-sm backdrop-blur-sm">
                              <div className="text-gray-400 text-xs font-semibold">
                                {tos("detailModal.optSet.budgetSensitivityGraph.adSalesSameSku.label")} 
                              </div>
                              <div className="mt-1 flex items-center justify-between text-xs text-gray-100 font-semibold">
                                <span className="text-[10px] text-gray-300">{tos("detailModal.optSet.budgetSensitivityGraph.maximum")}</span>
                                {formatCurrency(optimizationBudgetSensitivitySummary?.summary?.ad_sales_same_sku?.max_value || 0, currencyCode, { maximumFractionDigits: 0, minimumFractionDigits: 0 })}
                              </div>
                              <div className="mt-0.5 flex items-center justify-between text-xs text-gray-100 font-semibold">
                                <span className="text-[10px] text-gray-300">{tos("detailModal.optSet.budgetSensitivityGraph.optimalBudget")}</span>
                                <div
                                  className={cn(
                                    "flex items-center justify-center gap-x-1",
                                    optimizationBudgetSensitivitySummary?.summary?.ad_sales_same_sku?.budget_ratio_at_max - 1 === 0
                                      ? "text-gray-400"
                                      : optimizationBudgetSensitivitySummary?.summary?.ad_sales_same_sku?.budget_ratio_at_max - 1 > 0
                                        ? "text-red-400"
                                        : "text-blue-400"
                                  )}
                                >
                                  <ArrowDownCircleIcon
                                    className={cn(
                                      "h-4 w-4 inline-block",
                                      optimizationBudgetSensitivitySummary?.summary?.ad_sales_same_sku?.budget_ratio_at_max - 1 > 0
                                        ? "transform rotate-180"
                                        : ""
                                    )}
                                  />
                                  <span className="text-xs">
                                    {optimizationBudgetSensitivitySummary?.summary?.ad_sales_same_sku?.budget_ratio_at_max - 1 === 0
                                      ? '0%'
                                      : `${Math.abs((optimizationBudgetSensitivitySummary?.summary?.ad_sales_same_sku?.budget_ratio_at_max - 1) * 100).toFixed(0)}%`
                                    }
                                  </span>
                                </div>
                              </div>
                            </div>
                          </div>
                        )}
                      </div>
                      <ArrowsPointingOutIcon className="absolute top-3 right-3 h-4 w-4 text-gray-200 group-hover:text-gray-100 group-hover:scale-110 transition duration-150 ease-in-out" />
                    </div>
                  </div>
                </div>
              </div>
              <div className="grow relative flex flex-col min-h-0 mt-6">
                <div className="relative grow w-full min-h-0">
                  {pacingContent(selectedPortfolioItem)}
                </div>
                {/* <TabGroup className="flex flex-col h-full">
                  <div className="flex-shrink-0">
                    <TabList className="inline-flex gap-x-1 bg-gray-100 p-1 rounded-lg">
                      <Tab
                        className="flex items-center gap-x-1 py-2 px-4 text-sm font-semibold text-gray-400 focus:outline-none data-[selected]:text-gray-500 data-[selected]:bg-white data-[selected]:shadow-md data-[selected]:data-[hover]:bg-white data-[hover]:bg-white/40 data-[focus]:outline-1 data-[focus]:outline-white rounded-lg"
                      >
                        {tos("detailModal.optSet.budgetTab.tabLabel")}
                      </Tab>
                      <Tab
                        className="flex items-center gap-x-1 py-2 px-4 text-sm font-semibold text-gray-400 focus:outline-none data-[selected]:text-gray-500 data-[selected]:bg-white data-[selected]:shadow-md data-[selected]:data-[hover]:bg-white data-[hover]:bg-white/40 data-[focus]:outline-1 data-[focus]:outline-white rounded-lg"
                      >
                        {tos("detailModal.optSet.historyTab.tabLabel")}
                      </Tab>
                    </TabList>
                  </div>
                  <TabPanels className="mt-6 grow relative flex flex-col min-h-0">
                    <TabPanel className="flex flex-col w-full h-full">
                      <div className="relative grow w-full min-h-0">
                        {pacingContent(selectedPortfolioItem)}
                      </div>
                    </TabPanel>
                    <TabPanel className="flex flex-col w-full h-full">
                      <div className="relative grow w-full min-h-0">
                        {historyContent(selectedPortfolioItem)}
                      </div>
                    </TabPanel>
                  </TabPanels>
                </TabGroup> */}
              </div>
            </div>
          </div>
        }
        </div>
      </TransitionChild>
    </>
  )
}
