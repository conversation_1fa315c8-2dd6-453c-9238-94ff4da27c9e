import { ErrorResponse } from "@/app/api/_models";
import { getServerApiHostUrl } from "@/utils/host";
import { NextRequest, NextResponse } from "next/server";

export type HourlyReportResponse = any[];

export async function GET(
  request: NextRequest
): Promise<NextResponse<HourlyReportResponse | ErrorResponse>> {
  const accessToken = request.headers.get("Authorization");
  if (!accessToken) {
    return NextResponse.json(
      { message: "Authorization header is missing" },
      { status: 400 }
    );
  }
  const accountId = request.nextUrl.searchParams.get("account_id")
  if (!accountId) {
    return NextResponse.json(
      { message: "account_id query is missing" },
      { status: 400 }
    );
  }
  const marketplaceId = request.nextUrl.searchParams.get("marketplace_id")
  if (!marketplaceId) {
    return NextResponse.json(
      { message: "marketplace_id query is missing" },
      { status: 400 }
    );
  }
  const startDate = request.nextUrl.searchParams.get("start_date")
  if (!startDate) {
    return NextResponse.json(
      { message: "start_date query is missing" },
      { status: 400 }
    );
  }
  const endDate = request.nextUrl.searchParams.get("end_date")
  if (!endDate) {
    return NextResponse.json(
      { message: "end_date query is missing" },
      { status: 400 }
    );
  }
  const metrics = request.nextUrl.searchParams.get("metrics")
  const asin = request.nextUrl.searchParams.get("asin")
  const campaignId = request.nextUrl.searchParams.get("campaign_id")
  const hourlyReportResponse = await fetch(
    `${await getServerApiHostUrl()}/api/hourly_report/hourly?account_id=${accountId}&marketplace_id=${marketplaceId}&metrics=${metrics}&start_date=${startDate}&end_date=${endDate}&asin=${asin}&campaign_id=${campaignId}`,
    {
      method: "GET",
      headers: {
        "Authorization": `Bearer ${accessToken}`,
        "Content-Type": "application/json",
      },
      cache: "force-cache",
      next: { revalidate: 600 }, // 10 minutes
    }
  ).then((res) => res.json());

  return NextResponse.json(hourlyReportResponse, { status: 200 });
}
