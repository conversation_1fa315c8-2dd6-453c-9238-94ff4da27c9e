import ReportMock from "../mock/ReportMock";
import ReportPage from "../../pages/ReportPage";

const service = new ReportMock();
const page = new ReportPage();

describe('Dashboard', () => {
    it.guide('dashboard 화면에 처음 접속하면, vendor를 설정할 수 있는 dropdown이 있다.', {
        mockFunc: () => {
            cy.login();
        },
        actionFunc: () => {
            cy.visit('/dashboard?tab=manage-profile');
        },
        waitFunc: () => {
        },
        assertFunc: () => {

        }
    });
    it.guide('authorized ads 버튼을 누르면 관리할 아마존 계정을 로그인할 수 있는 버튼이 있다..',{
        mockFunc: () => {
            cy.login();
        },
        actionFunc: () => {
            cy.visit('/dashboard?tab=manage-profile');
        },
        waitFunc: () => {
        },
        assertFunc: () => {
            cy.get('.gap-x-2 > .group').should('be.visible');        
        }
    })
});
  