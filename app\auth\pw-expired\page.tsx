import { auth } from "auth"
import PasswordExpiredPageLayout from "@/components/auth/pw-expired-layout"
import { SessionProvider } from "next-auth/react"

export default async function PWExpiredPage() {
  const session = await auth()
  if (session?.user) {
    session.user = {
      ...session.user,
    }
  }

  return (
    <SessionProvider session={session}>
      <PasswordExpiredPageLayout />
    </SessionProvider>
  )
}
