import { ErrorResponse } from "@/app/api/_models";
import { getServerApiHostUrl } from "@/utils/host";
import { NextRequest, NextResponse } from "next/server";

export type CampaignBudgetUsageResponse = {
  optimization_id: number;
  portfolio_id: string;
  asins: Array<{
    asin: string;
    campaigns: Array<{
      campaign_id: string;
      total_budget_usage: number;
      today_budget_usage: number;
    }>;
  }>;
  period: {
    start_date: string;
    end_date: string;
    is_monthly_recurring: boolean;
  };
};

export async function GET(
  request: NextRequest
): Promise<NextResponse<CampaignBudgetUsageResponse | ErrorResponse>> {
  const accessToken = request.headers.get("Authorization");
  if (!accessToken) {
    return NextResponse.json(
      { message: "Authorization header is missing" },
      { status: 400 }
    );
  }

  const optimizationId = request.nextUrl.searchParams.get("optimization_id");
  if (!optimizationId) {
    return NextResponse.json(
      { message: "optimization_id query is missing" },
      { status: 400 }
    );
  }

  try {
    const campaignBudgetUsageResponse = await fetch(
      `${await getServerApiHostUrl()}/api/hourly_report/campaign-budget-usage?optimization_id=${optimizationId}`,
      {
        method: "GET",
        headers: {
          "Authorization": `Bearer ${accessToken}`,
          "Content-Type": "application/json",
        },
        cache: "no-cache",
      }
    );

    if (!campaignBudgetUsageResponse.ok) {
      const errorData = await campaignBudgetUsageResponse.json();
      return NextResponse.json(
        { message: errorData.message || "Failed to fetch campaign budget usage" },
        { status: campaignBudgetUsageResponse.status }
      );
    }

    const data = await campaignBudgetUsageResponse.json();
    return NextResponse.json(data, { status: 200 });
  } catch (error) {
    console.error("Error fetching campaign budget usage:", error);
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    );
  }
} 