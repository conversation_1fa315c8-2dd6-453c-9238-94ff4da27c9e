'use client';

import { useTranslations } from "next-intl"
import { But<PERSON> } from "./button"
import { ArrowRightStartOnRectangleIcon } from "@heroicons/react/20/solid"
import { signOut } from "next-auth/react"
import { getEnvValue } from "@/utils/host"

export function SignOutButton(props: React.ComponentPropsWithRef<typeof Button>) {
  const t = useTranslations('component')
  return (
    <form
      action={async () => {
        const authDomain = await getEnvValue('AUTH_DOMAIN')
        const cognitoClientId = await getEnvValue('COGNITO_CLIENT_ID')
        const nextAuthUrl = await getEnvValue('NEXTAUTH_URL')
        await signOut({
          redirect: false
        }).then(() => {
          window.location.href = `https://${authDomain}/logout?client_id=${cognitoClientId}&logout_uri=${nextAuthUrl}/auth/login&redirect_uri=${nextAuthUrl}/auth/login&response_type=code`
        });
      }}
      className="w-full"
    >
      <Button variant="ghost" className="w-full h-6 py-4 justify-start text-gray-600 text-xs" {...props}>
        <ArrowRightStartOnRectangleIcon className="w-4 h-4 mr-2"/>
        {t("gnb.account.logout")}
      </Button>
    </form>
  )
}