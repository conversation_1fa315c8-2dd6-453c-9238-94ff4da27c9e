import { ErrorResponse } from "@/app/api/_models";
import { getServerApiHostUrl } from "@/utils/host";
import { NextRequest, NextResponse } from "next/server";

export type ArchiveOptimizationResponse = any[];

export async function DELETE(
  request: NextRequest
): Promise<NextResponse<ArchiveOptimizationResponse | ErrorResponse>> {
  const accessToken = request.headers.get("Authorization");
  const body = await request.json();

  if (!accessToken) {
    return NextResponse.json(
      { message: "Authorization header is missing" },
      { status: 400 }
    );
  }
  const accountId = request.nextUrl.searchParams.get("account_id")
  if (!accountId) {
    return NextResponse.json(
      { message: "account_id query is missing" },
      { status: 400 }
    );
  }
  const marketplaceId = request.nextUrl.searchParams.get("marketplace_id")
  if (!marketplaceId) {
    return NextResponse.json(
      { message: "marketplace_id query is missing" },
      { status: 400 }
    );
  }
  const archiveOptimizationsResponse = await fetch(
    `${await getServerApiHostUrl()}/api/optimization/archive_optimization?account_id=${accountId}&marketplace_id=${marketplaceId}`,
    {
      method: "DELETE",
      headers: {
        "Authorization": `Bearer ${accessToken}`,
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        "optimization_id": body?.optimization_id,
      }),
      cache: "no-cache",
    }
  ).then((res) => res.json());

  return NextResponse.json(archiveOptimizationsResponse, { status: 200 });
}
