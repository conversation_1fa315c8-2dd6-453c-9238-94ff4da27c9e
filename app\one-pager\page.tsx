"use client"

import Footer from "@/components/footer"
import Image from "next/image";
import dynamic from "next/dynamic"
const Lottie = dynamic(() => import("lottie-react"), { ssr: false })
import MarketingOnlineAnimationData from "@/public/lottie/marketing-online.json"
import BusinessChartAnimationData from "@/public/lottie/business-chart.json"
import BusinessGoalAnimationData from "@/public/lottie/business-goal.json"
import WorkingChartAnimationData from "@/public/lottie/working-chart.json"
import SeekingDevelopmentAnimationData from "@/public/lottie/seeking-development.json"
import RocketAnimationData from "@/public/lottie/rocket.json"
import { ArrowRightIcon } from "@heroicons/react/20/solid"

export default function OnePager() {
  return (
    <>
      <div className="w-full">
        <div className="w-full max-w-4xl px-4 sm:px-6 mx-auto">
          <div className="flex flex-col sm:flex-row sm:items-end gap-x-10 pt-8 sm:pt-0">
            <div className="w-1/2 flex-shrink-0">
              <h1 className="sm:pb-10 text-4xl sm:text-6xl font-bold !leading-tight">
                Let&apos;s Optimize
                <br/>
                Your Ads
                <br/>
                Here!
              </h1>
            </div>
            <div className="hidden sm:flex flex-col w-1/2 max-w-sm justify-center items-center">
              <Lottie
                animationData={MarketingOnlineAnimationData}
                className="flex justify-center items-center"
                loop={true}
              />
            </div>
          </div>
          <p className="mt-4 md:-mt-6 text-xl sm:text-2xl font-light">
            Achieve the best marketing performance <br className="hidden sm:block"/>with an intelligent advertising optimization algorithm!
          </p>
          <div className="flex items-center justify-center">
            <a className="mt-12 mx-auto" href="mailto:<EMAIL>">
              <div className="inline-flex items-center justify-center h-10 px-4 rounded-md bg-blue-700 hover:bg-blue-800 text-sm outline-2 text-gray-50">
                Apply For Waitlist{" "}
                <ArrowRightIcon className="ml-1 h-5 w-5 text-gray-50" />
              </div>
            </a>
          </div>
          <div className="mt-24 px-10">
            <div className="flex flex-wrap items-center justify-center gap-x-20 gap-y-16">
              <Image
                src="/logo/trip.com-logo.svg"
                alt="Trip.com Logo"
                className=""
                width={120}
                height={24}
              />
              <Image
                src="/logo/dyson_logo.svg"
                alt="Dyson Logo"
                className=""
                width={100}
                height={24}
              />
              <Image
                src="/logo/lg-electronics-logo.svg"
                alt="LG Electronics Logo"
                className=""
                width={180}
                height={24}
              />
              <Image
                src="/logo/hansung-mall-logo.png"
                alt="Hansung mall Logo"
                className=""
                width={200}
                height={24}
              />
              <Image
                src="/logo/looxloo-logo.svg"
                alt="LooxlooLogo"
                className=""
                width={160}
                height={24}
              />
            </div>
          </div>
          <div className="-mt-16 sm:mt-20 flex items-center justify-between flex-col sm:flex-row gap-x-20">
            <div className="flex-shrink-0 w-full sm:w-1/2">
              <Lottie
                animationData={BusinessChartAnimationData}
                className="flex justify-center items-center"
                loop={true}
              />
            </div>
            <div className="sm:pt-10">
              <h2 className="text-2xl font-semibold">
                Why do your competitors
                <br/>
                choose Optapex?
              </h2>
              <p className="mt-4 text-base">
                Based on advanced AI and mathematical optimization models, it dynamically determines the appropriate budget and bidding prices for time and materials (keywords) that are difficult for humans to intuitively judge. By managing bids and goals in real-time, taking into account the changing market conditions, it maximizes advertising performance.
              </p>
            </div>
          </div>
          <div className="sm:mt-20 flex items-center justify-between flex-col-reverse sm:flex-row gap-x-20">
            <div className="sm:pt-10">
              <h2 className="text-2xl font-semibold">
                Precision in Predicting
                <br/>
                Advertising Performance
              </h2>
              <p className="mt-4 text-base">
                Based on a minimum of 14 to 60 days of advertising performance data, we create an ML performance prediction model. Additionally, we apply ensemble techniques to enhance the accuracy of advertising performance predictions.
              </p>
            </div>
            <div className="flex-shrink-0 w-full sm:w-1/2">
              <Lottie
                animationData={BusinessGoalAnimationData}
                className="flex justify-center items-center"
                loop={true}
              />
            </div>
          </div>
          <div className="sm:mt-20 flex items-center justify-between flex-col sm:flex-row gap-x-20">
            <div className="flex-shrink-0 w-full sm:w-1/2">
              <Lottie
                animationData={WorkingChartAnimationData}
                className="flex justify-center items-center"
                loop={true}
              />
            </div>
            <div className="sm:pt-10">
              <h2 className="text-2xl font-semibold">
                Automated
                <br/>
                Bid Planning
              </h2>
              <p className="mt-4 text-base">
                Utilizing advanced mathematical optimization techniques, we automatically generate bid plans on an hourly basis for advertising materials (keywords) based on advertising goals (click optimization, conversion optimization, multi-goal optimization).
              </p>
            </div>
          </div>
          <div className="sm:mt-20 flex items-center justify-between flex-col-reverse sm:flex-row gap-x-20">
            <div className="sm:pt-10">
              <h2 className="text-2xl font-semibold">
                Real-time
                <br/>
                Anomaly Detection
                <br/>
                in Performance.
              </h2>
              <p className="mt-4 text-base">
                We conduct automated bidding and monitoring on an hourly and minute basis according to the plan. Our system automatically detects anomalies in real-time, allowing for prompt adjustments to market changes.
              </p>
            </div>
            <div className="flex-shrink-0 w-full sm:w-1/2">
              <Lottie
                animationData={SeekingDevelopmentAnimationData}
                className="flex justify-center items-center"
                loop={true}
              />
            </div>
          </div>
        </div>

        <div className="w-full mt-20 bg-blue-200">
          <div className="w-full max-w-4xl py-12 sm:pt-4 sm:pb-20 px-4 sm:px-6 mx-auto text-xl sm:text-3xl text-gray-800 font-semibold text-left">
            <div className="flex items-center justify-center">
              <div className="leading-normal">
                Boost your ads with Optapex
                <br/>
                to reach your ultimate profit goals!
              </div>
              <div className="hidden sm:block sm:max-w-[250px] flex items-center justify-center">
                <Lottie
                  animationData={RocketAnimationData}
                  className="flex justify-center items-center"
                  loop={true}
                />
              </div>
            </div>
            <div className="mt-6 sm:-mt-12 flex items-center justify-center">
              <a className="mx-auto" href="mailto:<EMAIL>">
                <div className="inline-flex items-center justify-center h-10 px-4 rounded-md bg-blue-700 hover:bg-blue-800 text-sm outline-2 text-gray-50">
                  Apply For Waitlist{" "}
                  <ArrowRightIcon className="ml-1 h-5 w-5 text-gray-50" />
                </div>
              </a>
            </div>
          </div>
        </div>
      </div>
      <Footer />
    </>
  )
}