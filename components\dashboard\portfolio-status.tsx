"use client"

import { cn } from "@/utils/msc"
import { PortfolioListItem } from './ad-portfolio-layout-component'
import { useTranslations } from "next-intl"


interface PortfolioStatusProps extends React.HTMLAttributes<HTMLDivElement> {
    portfolioItem: PortfolioListItem;
}
export default function PortfolioStatus({
  portfolioItem
}: PortfolioStatusProps) {
  const t = useTranslations('component')
  return (
    <div className="flex items-center justify-center gap-x-2 text-gray-500 text-sm">
      <span className="relative flex h-2 w-2">
        <span className={cn(
          "absolute inline-flex h-full w-full rounded-full opacity-75",
          portfolioItem.use_yn === "Y" && portfolioItem.bid_yn === "N"
            ? "bg-red-500"
            : portfolioItem.request_status === "REQUESTED" || portfolioItem.request_status === "PROCESSING"
              ? "bg-blue-500"
              : portfolioItem.use_yn === "Y" && portfolioItem.bid_yn === "Y" && portfolioItem.ad_budget_end_date && new Date(portfolioItem.ad_budget_end_date) < new Date()
                ? "bg-gray-300"
                : portfolioItem.use_yn === "Y" && portfolioItem.bid_yn === "Y"
                  ? "animate-ping bg-green-500"
                  : "bg-gray-300"
        )}></span>
        <span className={cn(
          "relative inline-flex rounded-full h-2 w-2",
          portfolioItem.use_yn === "Y" && portfolioItem.bid_yn === "N"
            ? "bg-red-500"
            : portfolioItem.request_status === "REQUESTED" || portfolioItem.request_status === "PROCESSING"
              ? "bg-blue-500"
              : portfolioItem.use_yn === "Y" && portfolioItem.bid_yn === "Y" && portfolioItem.ad_budget_end_date && new Date(portfolioItem.ad_budget_end_date) < new Date()
                ? "bg-gray-300"
                : portfolioItem.use_yn === "Y" && portfolioItem.bid_yn === "Y"
                  ? "bg-green-500"
                  : "bg-gray-300"
        )}></span>
      </span>
      {portfolioItem.request_status === "REQUESTED"
        ? t("portfolioStatus.initializing")
        : portfolioItem.request_status === "PROCESSING"
          ? t("portfolioStatus.processing")
          : portfolioItem.use_yn === "Y" && portfolioItem.bid_yn === "N"
            ? t("portfolioStatus.paused")
            : portfolioItem.use_yn === "Y" && portfolioItem.bid_yn === "Y" && portfolioItem.ad_budget_end_date && new Date(portfolioItem.ad_budget_end_date) < new Date()
              ? t("portfolioStatus.expired")
              : portfolioItem.use_yn === "Y" && portfolioItem.bid_yn === "Y"
                ? t("portfolioStatus.active")
                : portfolioItem.request_status
      }
    </div>
  )
}
