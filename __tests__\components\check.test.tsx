import React from 'react'
import { render } from '@testing-library/react'
import '@testing-library/jest-dom'
import { describe, test, expect } from '@jest/globals'
import { Check } from '@/components/ui/check'

describe('components/ui/Check', () => {
  test('renders svg with given className', () => {
    const { container } = render(<Check className="h-4 w-4 text-red-500" />)
    const svg = container.querySelector('svg')
    expect(svg).not.toBeNull()
  })
}) 