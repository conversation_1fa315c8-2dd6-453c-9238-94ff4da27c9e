"use client"

import { Fragment } from "react"
import { cn } from "@/utils/msc"
import { Listbox, ListboxButton, ListboxOptions, ListboxOption, Transition } from "@headlessui/react"
import { ChevronUpDownIcon } from "@heroicons/react/20/solid"
import { Check } from "@/components/ui/check"
import { PortfolioListItem } from "./ad-portfolio-layout-component"

interface OptimizationSetSelectProps extends React.HTMLAttributes<HTMLDivElement> {
  listboxClassName?: string;
  selected: PortfolioListItem[];
  setSelected: (options: PortfolioListItem[]) => void;
	options: PortfolioListItem[];
  multiple?: boolean;
}

export default function OptimizationSetSelect({
  className,
  listboxClassName,
  selected,
  setSelected,
  options,
  multiple = false,
  ...props
}: OptimizationSetSelectProps) {
  return (
    <div
      className={cn(
        "relative w-[200px]",
        className
      )}
      {...props}
    >
      <Listbox
        value={multiple ? selected : (selected.length > 0 ? selected[0] : null)}
        onChange={(option: PortfolioListItem | PortfolioListItem[] | null) => {
          if (multiple) {
            setSelected(Array.isArray(option) ? option : []);
          } else {
            setSelected(option ? [option as PortfolioListItem] : []);
          }
        }}
        multiple={multiple}
      >
        <div className="relative">
          <ListboxButton className="relative w-full cursor-pointer rounded-lg bg-white text-gray-600 py-2 pl-3 pr-10 text-left focus:outline-none focus-visible:border-indigo-500 focus-visible:ring-2 focus-visible:ring-white/75 focus-visible:ring-offset-2 focus-visible:ring-offset-orange-300 sm:text-sm">
            <span className="block truncate">
              {multiple
                ? (selected.length > 0
                    ? selected.map((s) => s.optimization_name).join(", ")
                    : 'None')
                : (selected.length > 0
                    ? selected[0].optimization_name
                    : 'None')
              }
            </span>
            <span className="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-2">
              <ChevronUpDownIcon
                className="h-5 w-5 text-gray-400"
                aria-hidden="true"
              />
            </span>
          </ListboxButton>
          <Transition
            as={Fragment}
            leave="transition ease-in duration-100"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
            <ListboxOptions
              className={cn(
                "absolute z-[1] mt-1 max-h-60 w-full overflow-auto rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black/5 focus:outline-none sm:text-sm",
                listboxClassName
              )}
            >
              {/* None option */}
              {!multiple && (
                <ListboxOption
                  className={({ active }) =>
                    `relative cursor-pointer select-none py-2 pl-4 pr-10 ${
                      active ? 'bg-blue-100/40 text-blue-900' : 'text-gray-600'
                    }`
                  }
                  value={null}
                >
                  {({ selected }) => (
                    <>
                      <span
                        className={`block truncate ${
                          selected ? 'font-medium' : 'font-normal'
                        }`}
                      >
                        None
                      </span>
                      {selected ? (
                        <div className="absolute inset-y-0 right-0 flex items-center pr-3">
                          <div className="shrink-0 flex items-center justify-center w-5 h-5 rounded-full">
                            <Check className="w-4 h-4 stroke-blue-400" aria-hidden="true" />
                          </div>
                        </div>
                      ) : null}
                    </>
                  )}
                </ListboxOption>
              )}
              {/* Optimization set options */}
              {options.map((option, optionIdx) => (
                <ListboxOption
                  key={optionIdx}
                  className={({ active }) =>
                    `relative cursor-pointer select-none py-2 pl-4 pr-10 ${
                      active ? 'bg-blue-100/40 text-blue-900' : 'text-gray-600'
                    }`
                  }
                  value={option}
                >
                  {({ selected }) => (
                    <>
                      <span
                        className={`block truncate ${
                          selected ? 'font-medium' : 'font-normal'
                        }`}
                      >
                        {option.optimization_name}
                      </span>
                      {selected ? (
                        <div className="absolute inset-y-0 right-0 flex items-center pr-3">
                          <div className="shrink-0 flex items-center justify-center w-5 h-5 rounded-full">
                            <Check className="w-4 h-4 stroke-blue-400" aria-hidden="true" />
                          </div>
                        </div>
                      ) : null}
                    </>
                  )}
                </ListboxOption>
              ))}
            </ListboxOptions>
          </Transition>
        </div>
      </Listbox>
    </div>
  )
}
