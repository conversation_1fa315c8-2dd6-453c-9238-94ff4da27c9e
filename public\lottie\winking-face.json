{"v": "4.8.0", "meta": {"g": "LottieFiles AE 3.1.1", "a": "", "k": "", "d": "", "tc": ""}, "fr": 29.9700012207031, "ip": 0, "op": 60.0000024438501, "w": 1920, "h": 1920, "nm": "wink", "ddd": 0, "assets": [], "layers": [{"ddd": 0, "ind": 3, "ty": 3, "nm": "Null 2", "sr": 1, "ks": {"o": {"a": 0, "k": 0, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [1016, 992, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [500, 500, 100], "ix": 6}}, "ao": 0, "ip": 0, "op": 60.0000024438501, "st": 0, "bm": 0}, {"ddd": 0, "ind": 4, "ty": 3, "nm": "Smile to <PERSON><PERSON>!", "sr": 1, "ks": {"o": {"a": 0, "k": 0, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [960, 960, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "ef": [{"ty": 5, "nm": "Slider Control", "np": 3, "mn": "ADBE Slider Control", "ix": 1, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 1, "k": [{"i": {"x": [0.353], "y": [0.998]}, "o": {"x": [1], "y": [0.007]}, "t": 5, "s": [0]}, {"i": {"x": [0.031], "y": [0.996]}, "o": {"x": [0.478], "y": [-0.009]}, "t": 20, "s": [100]}, {"t": 35.0000014255792, "s": [0]}], "ix": 1}}]}], "ip": 0, "op": 60.0000024438501, "st": 0, "bm": 0}, {"ddd": 0, "ind": 7, "ty": 4, "nm": "brows 3", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [1049.8, 658.813, 0], "ix": 2}, "a": {"a": 0, "k": [-86.2, -299.7, 0], "ix": 1}, "s": {"a": 0, "k": [-100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[107, -22], [-3, 5], [-105, 3], [0, 0]], "o": [[-107, 22], [3, -5], [105, -3], [0, 0]], "v": [[-193, -304], [-305, -234], [-191, -275], [-87, -301]], "c": true}, "ix": 2, "x": "var $bm_rt;\nif (numKeys > 0 || typeof createPath === 'undefined') {\n    $bm_rt = value;\n} else {\n    var contrl = thisComp.layer('Smile to Eh!');\n    var contrlCurVal1 = contrl('ADBE Effect Parade')(1)('ADBE Slider Control-0001').value;\n    var k1 = {\n            closed: true,\n            vertices: [\n                [\n                    -193,\n                    -304\n                ],\n                [\n                    -305,\n                    -234\n                ],\n                [\n                    -191,\n                    -275\n                ],\n                [\n                    -87,\n                    -301\n                ]\n            ],\n            inTangents: [\n                [\n                    107,\n                    -22\n                ],\n                [\n                    -3,\n                    5\n                ],\n                [\n                    -105,\n                    3\n                ],\n                [\n                    0,\n                    0\n                ]\n            ],\n            outTangents: [\n                [\n                    -107,\n                    22\n                ],\n                [\n                    3,\n                    -5\n                ],\n                [\n                    105,\n                    -3\n                ],\n                [\n                    0,\n                    0\n                ]\n            ],\n            featherSegLocs: [],\n            featherRelSegLocs: [],\n            featherRadii: [],\n            featherInterps: [],\n            featherTensions: [],\n            featherTypes: [],\n            featherRelCornerAngles: []\n        };\n    var k2 = {\n            closed: true,\n            vertices: [\n                [\n                    -222.4,\n                    -265.5\n                ],\n                [\n                    -337.3,\n                    -208.5\n                ],\n                [\n                    -216,\n                    -237.1\n                ],\n                [\n                    -124,\n                    -244.7\n                ]\n            ],\n            inTangents: [\n                [\n                    98.3,\n                    -25.1\n                ],\n                [\n                    -4.8,\n                    3.3\n                ],\n                [\n                    -105,\n                    2.9\n                ],\n                [null]\n            ],\n            outTangents: [\n                [\n                    -105.8,\n                    27\n                ],\n                [\n                    4.8,\n                    -3.3\n                ],\n                [\n                    97.5,\n                    -2.7\n                ],\n                [null]\n            ],\n            featherSegLocs: [],\n            featherRelSegLocs: [],\n            featherRadii: [],\n            featherInterps: [],\n            featherTensions: [],\n            featherTypes: [],\n            featherRelCornerAngles: []\n        };\n    var rotoBezier = false;\n    function slider(k1, k2) {\n        k1 = unArray(k1);\n        k2 = unArray(k2);\n        if (k2 === null) {\n            var resultAdd1 = k1;\n        } else {\n            if (contrlCurVal1 > 0) {\n                var resultAdd1 = linear(contrlCurVal1, 0, 100, k1, k2);\n            } else if (contrlCurVal1 < 0) {\n                var resultAdd1 = $bm_sum(k1, $bm_sum($bm_neg(linear($bm_sub(0, contrlCurVal1), 0, 100, k1, k2)), k1));\n            } else {\n                var resultAdd1 = k1;\n            }\n        }\n        var myResult = resultAdd1;\n        return myResult;\n    }\n    function unArray(inKey) {\n        if (inKey instanceof Array && inKey.length === 1) {\n            return inKey[0];\n        } else {\n            return inKey;\n        }\n    }\n    var myPoints = [];\n    var myInTangents = [];\n    var myOutTangents = [];\n    var k1vl = k1.vertices.length;\n    var i;\n    for (i = 0; i < k1vl; i++) {\n        myPoints.push(slider(k1.vertices[i], k2.vertices[i]));\n        myInTangents.push(slider(k1.inTangents[i], k2.inTangents[i]));\n        myOutTangents.push(slider(k1.outTangents[i], k2.outTangents[i]));\n    }\n    $bm_rt = createPath(myPoints, myInTangents, myOutTangents, k1.closed);\n}"}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.388235324037, 0.207843152214, 0.054901964524, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Shape 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 60.0000024438501, "st": 0, "bm": 0}, {"ddd": 0, "ind": 8, "ty": 4, "nm": "brows 2", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [873.8, 660.3, 0], "ix": 2}, "a": {"a": 0, "k": [-86.2, -299.7, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[107, -22], [-3, 5], [-105, 3], [0, 0]], "o": [[-107, 22], [3, -5], [105, -3], [0, 0]], "v": [[-193, -304], [-305, -234], [-191, -275], [-87, -301]], "c": true}, "ix": 2, "x": "var $bm_rt;\nif (numKeys > 0 || typeof createPath === 'undefined') {\n    $bm_rt = value;\n} else {\n    var contrl = thisComp.layer('Smile to Eh!');\n    var contrlCurVal1 = contrl('ADBE Effect Parade')(1)('ADBE Slider Control-0001').value;\n    var k1 = {\n            closed: true,\n            vertices: [\n                [\n                    -193,\n                    -304\n                ],\n                [\n                    -305,\n                    -234\n                ],\n                [\n                    -191,\n                    -275\n                ],\n                [\n                    -87,\n                    -301\n                ]\n            ],\n            inTangents: [\n                [\n                    107,\n                    -22\n                ],\n                [\n                    -3,\n                    5\n                ],\n                [\n                    -105,\n                    3\n                ],\n                [\n                    0,\n                    0\n                ]\n            ],\n            outTangents: [\n                [\n                    -107,\n                    22\n                ],\n                [\n                    3,\n                    -5\n                ],\n                [\n                    105,\n                    -3\n                ],\n                [\n                    0,\n                    0\n                ]\n            ],\n            featherSegLocs: [],\n            featherRelSegLocs: [],\n            featherRadii: [],\n            featherInterps: [],\n            featherTensions: [],\n            featherTypes: [],\n            featherRelCornerAngles: []\n        };\n    var k2 = {\n            closed: true,\n            vertices: [\n                [\n                    -241.6,\n                    -148.7\n                ],\n                [\n                    -346.8,\n                    -87.8\n                ],\n                [\n                    -235.4,\n                    -120.4\n                ],\n                [\n                    -146.8,\n                    -136.8\n                ]\n            ],\n            inTangents: [\n                [\n                    102.6,\n                    -37.5\n                ],\n                [\n                    -4.6,\n                    3.5\n                ],\n                [\n                    -103.4,\n                    18.4\n                ],\n                [null]\n            ],\n            outTangents: [\n                [\n                    -102.6,\n                    37.5\n                ],\n                [\n                    4.6,\n                    -3.5\n                ],\n                [\n                    103.4,\n                    -18.4\n                ],\n                [null]\n            ],\n            featherSegLocs: [],\n            featherRelSegLocs: [],\n            featherRadii: [],\n            featherInterps: [],\n            featherTensions: [],\n            featherTypes: [],\n            featherRelCornerAngles: []\n        };\n    var rotoBezier = false;\n    function slider(k1, k2) {\n        k1 = unArray(k1);\n        k2 = unArray(k2);\n        if (k2 === null) {\n            var resultAdd1 = k1;\n        } else {\n            if (contrlCurVal1 > 0) {\n                var resultAdd1 = linear(contrlCurVal1, 0, 100, k1, k2);\n            } else if (contrlCurVal1 < 0) {\n                var resultAdd1 = $bm_sum(k1, $bm_sum($bm_neg(linear($bm_sub(0, contrlCurVal1), 0, 100, k1, k2)), k1));\n            } else {\n                var resultAdd1 = k1;\n            }\n        }\n        var myResult = resultAdd1;\n        return myResult;\n    }\n    function unArray(inKey) {\n        if (inKey instanceof Array && inKey.length === 1) {\n            return inKey[0];\n        } else {\n            return inKey;\n        }\n    }\n    var myPoints = [];\n    var myInTangents = [];\n    var myOutTangents = [];\n    var k1vl = k1.vertices.length;\n    var i;\n    for (i = 0; i < k1vl; i++) {\n        myPoints.push(slider(k1.vertices[i], k2.vertices[i]));\n        myInTangents.push(slider(k1.inTangents[i], k2.inTangents[i]));\n        myOutTangents.push(slider(k1.outTangents[i], k2.outTangents[i]));\n    }\n    $bm_rt = createPath(myPoints, myInTangents, myOutTangents, k1.closed);\n}"}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.388235324037, 0.207843152214, 0.054901964524, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Shape 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 60.0000024438501, "st": 0, "bm": 0}, {"ddd": 0, "ind": 10, "ty": 4, "nm": "Mouth 2", "parent": 14, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [-22.952, -14.857, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [95.238, 95.238, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [-34.6, -0.4], [0, 0], [26.6, -0.8]], "o": [[0, 0], [30.8, -0.6], [0, 0], [-24.8, 1]], "v": [[-522, -274.9], [-480.2, -256.7], [-438.8, -274.7], [-480.2, -262.5]], "c": true}, "ix": 2, "x": "var $bm_rt;\nif (numKeys > 0 || typeof createPath === 'undefined') {\n    $bm_rt = value;\n} else {\n    var contrl = thisComp.layer('Smile to Eh!');\n    var contrlCurVal1 = contrl('ADBE Effect Parade')(1)('ADBE Slider Control-0001').value;\n    var k1 = {\n            closed: true,\n            vertices: [\n                [\n                    -522,\n                    -274.9\n                ],\n                [\n                    -480.2,\n                    -256.7\n                ],\n                [\n                    -438.8,\n                    -274.7\n                ],\n                [\n                    -480.2,\n                    -262.5\n                ]\n            ],\n            inTangents: [\n                [\n                    0,\n                    0\n                ],\n                [\n                    -34.6,\n                    -0.4\n                ],\n                [\n                    0,\n                    0\n                ],\n                [\n                    26.6,\n                    -0.8\n                ]\n            ],\n            outTangents: [\n                [\n                    0,\n                    0\n                ],\n                [\n                    30.8,\n                    -0.6\n                ],\n                [\n                    0,\n                    0\n                ],\n                [\n                    -24.8,\n                    1\n                ]\n            ],\n            featherSegLocs: [],\n            featherRelSegLocs: [],\n            featherRadii: [],\n            featherInterps: [],\n            featherTensions: [],\n            featherTypes: [],\n            featherRelCornerAngles: []\n        };\n    var k2 = {\n            closed: true,\n            vertices: [\n                [\n                    -522,\n                    -281.3\n                ],\n                [\n                    -480.2,\n                    -263.1\n                ],\n                [\n                    -438.8,\n                    -281.1\n                ],\n                [\n                    -480.2,\n                    -268.9\n                ]\n            ],\n            inTangents: [\n                [null],\n                [null],\n                [null],\n                [null]\n            ],\n            outTangents: [\n                [null],\n                [null],\n                [null],\n                [null]\n            ],\n            featherSegLocs: [],\n            featherRelSegLocs: [],\n            featherRadii: [],\n            featherInterps: [],\n            featherTensions: [],\n            featherTypes: [],\n            featherRelCornerAngles: []\n        };\n    var rotoBezier = false;\n    function slider(k1, k2) {\n        k1 = unArray(k1);\n        k2 = unArray(k2);\n        if (k2 === null) {\n            var resultAdd1 = k1;\n        } else {\n            if (contrlCurVal1 > 0) {\n                var resultAdd1 = linear(contrlCurVal1, 0, 100, k1, k2);\n            } else if (contrlCurVal1 < 0) {\n                var resultAdd1 = $bm_sum(k1, $bm_sum($bm_neg(linear($bm_sub(0, contrlCurVal1), 0, 100, k1, k2)), k1));\n            } else {\n                var resultAdd1 = k1;\n            }\n        }\n        var myResult = resultAdd1;\n        return myResult;\n    }\n    function unArray(inKey) {\n        if (inKey instanceof Array && inKey.length === 1) {\n            return inKey[0];\n        } else {\n            return inKey;\n        }\n    }\n    var myPoints = [];\n    var myInTangents = [];\n    var myOutTangents = [];\n    var k1vl = k1.vertices.length;\n    var i;\n    for (i = 0; i < k1vl; i++) {\n        myPoints.push(slider(k1.vertices[i], k2.vertices[i]));\n        myInTangents.push(slider(k1.inTangents[i], k2.inTangents[i]));\n        myOutTangents.push(slider(k1.outTangents[i], k2.outTangents[i]));\n    }\n    $bm_rt = createPath(myPoints, myInTangents, myOutTangents, k1.closed);\n}"}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.388235324037, 0.207843152214, 0.054901964524, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Shape 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 60.0000024438501, "st": 0, "bm": 0}, {"ddd": 0, "ind": 12, "ty": 4, "nm": "eyes", "parent": 14, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [-22.952, -14.857, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [95.238, 91.088, 100], "ix": 6}}, "ao": 0, "ef": [{"ty": 21, "nm": "Fill", "np": 9, "mn": "ADBE Fill", "ix": 1, "en": 1, "ef": [{"ty": 10, "nm": "Fill Mask", "mn": "ADBE Fill-0001", "ix": 1, "v": {"a": 0, "k": 0, "ix": 1}}, {"ty": 7, "nm": "All Masks", "mn": "ADBE Fill-0007", "ix": 2, "v": {"a": 0, "k": 0, "ix": 2}}, {"ty": 2, "nm": "Color", "mn": "ADBE Fill-0002", "ix": 3, "v": {"a": 0, "k": [0.352941185236, 0.1882353127, 0.031372550875, 1], "ix": 3}}, {"ty": 7, "nm": "Invert", "mn": "ADBE Fill-0006", "ix": 4, "v": {"a": 0, "k": 0, "ix": 4}}, {"ty": 0, "nm": "<PERSON>tal Feather", "mn": "ADBE Fill-0003", "ix": 5, "v": {"a": 0, "k": 0, "ix": 5}}, {"ty": 0, "nm": "Vertical Feather", "mn": "ADBE Fill-0004", "ix": 6, "v": {"a": 0, "k": 0, "ix": 6}}, {"ty": 0, "nm": "Opacity", "mn": "ADBE Fill-0005", "ix": 7, "v": {"a": 0, "k": 1, "ix": 7}}]}], "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-5.2, 0], [0, -9.3], [5.2, 0], [0, 9.3]], "o": [[5.2, 0], [0, 9.3], [-5.2, 0], [0, -9.3]], "v": [[0, -16.7], [9.5, 0], [0, 16.8], [-9.5, 0]], "c": true}, "ix": 2, "x": "var $bm_rt;\nif (numKeys > 0 || typeof createPath === 'undefined') {\n    $bm_rt = value;\n} else {\n    var contrl = thisComp.layer('Smile to Eh!');\n    var contrlCurVal1 = contrl('ADBE Effect Parade')(1)('ADBE Slider Control-0001').value;\n    var k1 = {\n            closed: true,\n            vertices: [\n                [\n                    0,\n                    -16.7\n                ],\n                [\n                    9.5,\n                    0\n                ],\n                [\n                    0,\n                    16.8\n                ],\n                [\n                    -9.5,\n                    0\n                ]\n            ],\n            inTangents: [\n                [\n                    -5.2,\n                    0\n                ],\n                [\n                    0,\n                    -9.3\n                ],\n                [\n                    5.2,\n                    0\n                ],\n                [\n                    0,\n                    9.3\n                ]\n            ],\n            outTangents: [\n                [\n                    5.2,\n                    0\n                ],\n                [\n                    0,\n                    9.3\n                ],\n                [\n                    -5.2,\n                    0\n                ],\n                [\n                    0,\n                    -9.3\n                ]\n            ],\n            featherSegLocs: [],\n            featherRelSegLocs: [],\n            featherRadii: [],\n            featherInterps: [],\n            featherTensions: [],\n            featherTypes: [],\n            featherRelCornerAngles: []\n        };\n    var k2 = {\n            closed: true,\n            vertices: [\n                [\n                    -6,\n                    -7.7\n                ],\n                [\n                    10.9,\n                    0.8\n                ],\n                [\n                    -6.4,\n                    -1.6\n                ],\n                [\n                    -23.3,\n                    0\n                ]\n            ],\n            inTangents: [\n                [null],\n                [\n                    -3.2,\n                    -5.8\n                ],\n                [\n                    4.3,\n                    0.2\n                ],\n                [\n                    6.6,\n                    -1.4\n                ]\n            ],\n            outTangents: [\n                [null],\n                [\n                    -9.8,\n                    -2.7\n                ],\n                [\n                    -5.2,\n                    -0.2\n                ],\n                [\n                    3.6,\n                    -4.4\n                ]\n            ],\n            featherSegLocs: [],\n            featherRelSegLocs: [],\n            featherRadii: [],\n            featherInterps: [],\n            featherTensions: [],\n            featherTypes: [],\n            featherRelCornerAngles: []\n        };\n    var rotoBezier = false;\n    function slider(k1, k2) {\n        k1 = unArray(k1);\n        k2 = unArray(k2);\n        if (k2 === null) {\n            var resultAdd1 = k1;\n        } else {\n            if (contrlCurVal1 > 0) {\n                var resultAdd1 = linear(contrlCurVal1, 0, 100, k1, k2);\n            } else if (contrlCurVal1 < 0) {\n                var resultAdd1 = $bm_sum(k1, $bm_sum($bm_neg(linear($bm_sub(0, contrlCurVal1), 0, 100, k1, k2)), k1));\n            } else {\n                var resultAdd1 = k1;\n            }\n        }\n        var myResult = resultAdd1;\n        return myResult;\n    }\n    function unArray(inKey) {\n        if (inKey instanceof Array && inKey.length === 1) {\n            return inKey[0];\n        } else {\n            return inKey;\n        }\n    }\n    var myPoints = [];\n    var myInTangents = [];\n    var myOutTangents = [];\n    var k1vl = k1.vertices.length;\n    var i;\n    for (i = 0; i < k1vl; i++) {\n        myPoints.push(slider(k1.vertices[i], k2.vertices[i]));\n        myInTangents.push(slider(k1.inTangents[i], k2.inTangents[i]));\n        myOutTangents.push(slider(k1.outTangents[i], k2.outTangents[i]));\n    }\n    $bm_rt = createPath(myPoints, myInTangents, myOutTangents, k1.closed);\n}"}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.352941176471, 0.188235309077, 0.03137254902, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [-514.75, -331.75], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "left eye", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-5.2, 0], [0, -9.3], [5.2, 0], [0, 9.3]], "o": [[5.2, 0], [0, 9.3], [-5.2, 0], [0, -9.3]], "v": [[0, -16.7], [9.5, 0], [0, 16.8], [-9.5, 0]], "c": true}, "ix": 2, "x": "var $bm_rt;\nif (numKeys > 0 || typeof createPath === 'undefined') {\n    $bm_rt = value;\n} else {\n    var contrl = thisComp.layer('Smile to Eh!');\n    var contrlCurVal1 = contrl('ADBE Effect Parade')(1)('ADBE Slider Control-0001').value;\n    var k1 = {\n            closed: true,\n            vertices: [\n                [\n                    0,\n                    -16.7\n                ],\n                [\n                    9.5,\n                    0\n                ],\n                [\n                    0,\n                    16.8\n                ],\n                [\n                    -9.5,\n                    0\n                ]\n            ],\n            inTangents: [\n                [\n                    -5.2,\n                    0\n                ],\n                [\n                    0,\n                    -9.3\n                ],\n                [\n                    5.2,\n                    0\n                ],\n                [\n                    0,\n                    9.3\n                ]\n            ],\n            outTangents: [\n                [\n                    5.2,\n                    0\n                ],\n                [\n                    0,\n                    9.3\n                ],\n                [\n                    -5.2,\n                    0\n                ],\n                [\n                    0,\n                    -9.3\n                ]\n            ],\n            featherSegLocs: [],\n            featherRelSegLocs: [],\n            featherRadii: [],\n            featherInterps: [],\n            featherTensions: [],\n            featherTypes: [],\n            featherRelCornerAngles: []\n        };\n    var k2 = {\n            closed: true,\n            vertices: [\n                [\n                    3.2,\n                    -16.7\n                ],\n                [\n                    15.8,\n                    -3.7\n                ],\n                [\n                    3.2,\n                    9.4\n                ],\n                [\n                    -9.5,\n                    -3.7\n                ]\n            ],\n            inTangents: [\n                [\n                    -6.9,\n                    0\n                ],\n                [\n                    0,\n                    -7.2\n                ],\n                [\n                    6.9,\n                    0\n                ],\n                [\n                    0,\n                    7.2\n                ]\n            ],\n            outTangents: [\n                [\n                    6.9,\n                    0\n                ],\n                [\n                    0,\n                    7.2\n                ],\n                [\n                    -6.9,\n                    0\n                ],\n                [\n                    0,\n                    -7.2\n                ]\n            ],\n            featherSegLocs: [],\n            featherRelSegLocs: [],\n            featherRadii: [],\n            featherInterps: [],\n            featherTensions: [],\n            featherTypes: [],\n            featherRelCornerAngles: []\n        };\n    var rotoBezier = false;\n    function slider(k1, k2) {\n        k1 = unArray(k1);\n        k2 = unArray(k2);\n        if (k2 === null) {\n            var resultAdd1 = k1;\n        } else {\n            if (contrlCurVal1 > 0) {\n                var resultAdd1 = linear(contrlCurVal1, 0, 100, k1, k2);\n            } else if (contrlCurVal1 < 0) {\n                var resultAdd1 = $bm_sum(k1, $bm_sum($bm_neg(linear($bm_sub(0, contrlCurVal1), 0, 100, k1, k2)), k1));\n            } else {\n                var resultAdd1 = k1;\n            }\n        }\n        var myResult = resultAdd1;\n        return myResult;\n    }\n    function unArray(inKey) {\n        if (inKey instanceof Array && inKey.length === 1) {\n            return inKey[0];\n        } else {\n            return inKey;\n        }\n    }\n    var myPoints = [];\n    var myInTangents = [];\n    var myOutTangents = [];\n    var k1vl = k1.vertices.length;\n    var i;\n    for (i = 0; i < k1vl; i++) {\n        myPoints.push(slider(k1.vertices[i], k2.vertices[i]));\n        myInTangents.push(slider(k1.inTangents[i], k2.inTangents[i]));\n        myOutTangents.push(slider(k1.outTangents[i], k2.outTangents[i]));\n    }\n    $bm_rt = createPath(myPoints, myInTangents, myOutTangents, k1.closed);\n}"}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.352941176471, 0.188235309077, 0.03137254902, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [-449.25, -331.25], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "right eye", "np": 3, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 60.0000024438501, "st": 0, "bm": 0}, {"ddd": 0, "ind": 14, "ty": 4, "nm": "head", "parent": 3, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [-11.352, -5.335, 0], "ix": 2}, "a": {"a": 0, "k": [-482, -312, 0], "ix": 1}, "s": {"a": 0, "k": [105, 105, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[110.032, 11.619], [0, -57.438], [56.333, 0]], "o": [[56.333, 0], [0, 57.438], [141.47, -7.81]], "v": [[0, -104], [102, 0], [0, 104]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.98431378603, 1, 0.803921639919, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [-482, -312], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [-103, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "bright side ", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[158.66, 15.429], [0, -57.438], [56.333, 0]], "o": [[56.333, 0], [0, 57.438], [92.628, 2.667]], "v": [[0, -104], [102, 0], [0, 104]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.960784375668, 0.756862819195, 0.305882364511, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [-482, -312], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [103.846, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "dark side", "np": 3, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"d": 1, "ty": "el", "s": {"a": 0, "k": [204, 208], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "nm": "Ellipse Path 1", "mn": "ADBE Vector Shape - Ellipse", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.980392216701, 0.917647118662, 0.376470618154, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [-482, -312], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [103.846, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "<PERSON><PERSON><PERSON>", "np": 3, "cix": 2, "bm": 0, "ix": 3, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 60.0000024438501, "st": 0, "bm": 0}], "markers": []}