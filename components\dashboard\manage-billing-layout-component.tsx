"use client"

import { Fragment, useEffect, useState } from "react"
import { useSession } from "next-auth/react"
import { <PERSON><PERSON>, Dialog, DialogPanel, DialogBackdrop, DialogTitle, Disclosure, DisclosureButton, DisclosurePanel, Popover, Transition, TransitionChild } from '@headlessui/react'
import { useTranslations } from "next-intl"
import ConnectAdButton from "@/components/dashboard/connect-ad-button"
import ConnectSpButton from "@/components/dashboard/connect-sp-button"
import ConnectVendorButton from "@/components/dashboard/connect-vendor-button"
import "react-datepicker/dist/react-datepicker.css"
import { cn, formatDate, getRegionName } from "@/utils/msc"
import { api } from "@/utils/api"
import AccountStatus from "./account-status"
import AccountPairViewSlider from "./account-pair-view-slider"
import { ArrowsRightLeftIcon, ChevronDownIcon, PlusIcon, PowerIcon, QuestionMarkCircleIcon } from "@heroicons/react/20/solid"
import { Check } from "@/components/ui/check"
import { CircleFlag } from "react-circle-flags"
import StripePricingTable from "./stripe-pricing-table"
import { Power } from "lucide-react"


interface ManageBillingLayoutProps {
  mopUserData: any;
}

export default function ManageBillingLayoutComponent({
  mopUserData,
}: ManageBillingLayoutProps) {
  const { data: session, status } = useSession()
  const t = useTranslations('component')
	const [availableProfiles, setAvailableProfiles] = useState<any>(null)
	const fetchProfiles = async () => {
		const availableProfilesResponse = await api.getAvailableProfiles((session?.user as any).access_token)
		setAvailableProfiles(availableProfilesResponse)
	}
	useEffect(() => {
		if (session?.user) {
			fetchProfiles()
		}
	}, [session])
	const [availableConnectionByProfileId, setAvailableConnectionByProfileId] = useState<any>(null)
	const fetchConnectionByProfileId = async (profileId: string) => {
		setIsConnectProductDataLoading(true)
		setSelectedProfileId(profileId)
		const availableProfilesResponse = await api.getAvailableConnectionByProfileId(profileId, (session?.user as any).access_token)
		setAvailableConnectionByProfileId(availableProfilesResponse)
		setIsConnectProductDataLoading(false)
	}
	const [isConnectProductDataModalOpen, setIsConnectProductDataModalOpen] = useState(false)
	const [isConnectProductDataLoading, setIsConnectProductDataLoading] = useState(false)
	const [selectedProfileId, setSelectedProfileId] = useState<string | null>(null)
	const [isConnectProfileLoading, setIsConnectProfileLoading] = useState(false)
	const [selectedSpLwaAccountId, setSelectedSpLwaAccountId] = useState<string | null>(null)
	const handleConnectProductDataModalCloseClick = () => {
    setAvailableConnectionByProfileId(null)
		setSelectedProfileId(null)
    setIsConnectProductDataModalOpen(false)
  }

	const [selectedAccountPairItem, setSelectedAccountPairItem] = useState<any | null>(null)
	const [isAccountPairViewSliderOpen, setIsAccountPairViewSliderOpen] = useState(false)
	const handleAccountItemClick = (accountItem: any) => {
    setSelectedAccountPairItem(accountItem)
    setIsAccountPairViewSliderOpen(true)
  };
	const handleAccountPairViewCloseClick = () => {
    setSelectedAccountPairItem(null)
    setIsAccountPairViewSliderOpen(false)
  }
	const [isAddProductDataModalOpen, setIsAddProductDataModalOpen] = useState(false)
	

  return (
		<div className="relative flex items-center justify-center w-full h-full divide-x divide-gray-200">
      <div className="flex-shrink-0 flex flex-col w-full h-full bg-white py-4 sm:py-6 px-8 sm:px-12">
        <div className="flex items-center justify-between">
					<div className="flex-shrink-0 flex items-center gap-x-1">
						<h1 className="text-2xl text-gray-800 font-medium">{t('billing.title')}</h1>
						<Popover className="relative flex items-center justify-center">
							{({ open }) => (
								<>
									<Popover.Button
										className={cn(
											"inline-flex items-center justify-center rounded-full text-gray-300 hover:text-gray-400 focus:outline-none",
										)}
									>
										<QuestionMarkCircleIcon className="w-5 h-5"/>
									</Popover.Button>
									<Transition
										as={Fragment}
										enter="transition ease-out duration-200"
										enterFrom="opacity-0 translate-y-1"
										enterTo="opacity-100 translate-y-0"
										leave="transition ease-in duration-150"
										leaveFrom="opacity-100 translate-y-0"
										leaveTo="opacity-0 translate-y-1"
									>
										<Popover.Panel className="absolute top-full left-1/2 z-10 mt-2 -translate-x-1/2 transform">
											<div className="w-[400px] p-2 rounded-lg shadow-lg bg-gray-900/90 text-xs text-white font-normal">
												<span>If you click a pair, then you will be redirected to the billing dashboard.</span>
											</div>
										</Popover.Panel>
									</Transition>
								</>
							)}
						</Popover>
					</div>
          <div className="h-[44px]" />
        </div>
				{/* accounts pair list */}
        {
					availableProfiles && (
					<div
						className={cn(
							'mt-4 grow relative w-full min-h-0 rounded-lg bg-white border border-gray-100 overflow-y-scroll',
						)}
					>
						<div className="sticky inset-x-0 top-0 z-[2] flex items-center gap-x-3 py-3 text-center text-sm text-gray-400 font-bold bg-gray-100/40 backdrop-blur-md">
							<div className="flex-shrink-0 w-[176px] pl-6 text-center">
								{t('billing.table.header.status')}
							</div>
							<div className="grow w-[200px] px-4">
								{t('billing.table.header.accountsPair')}
							</div>
							<div className="flex-shrink-0 w-[130px] px-4">
								{t('billing.table.header.updatedDate')}
							</div>
							<div className="flex-shrink-0 w-[180px] pr-4">
								{t('billing.table.header.pairActions')}
							</div>
						</div>
						<ul className="divide-y divide-gray-100">
							{(() => {
                const subscribedAccountPairs = availableProfiles?.filter((adAccount: any) => adAccount?.profiles?.filter((profile: any) => profile.is_subscription_active).length > 0)
								return (subscribedAccountPairs?.length > 0
									? subscribedAccountPairs.map((adAccount: any, index: number) => {
										return (
											<li className="" key={index}>
												<Disclosure defaultOpen={true}>
													{({ open }) => (
														<>
															<div className="relative flex items-center cursor-pointer hover:bg-gray-100/40 text-center text-gray-500 text-sm">
																<DisclosureButton className="flex-shrink-0 w-6 h-6 ml-6 mr-2 flex items-center justify-center rounded-full hover:bg-gray-200">
																	<ChevronDownIcon
																		className={cn(
																			"w-5 h-5 text-gray-400 transition duration-200 ease-in-out",
																			open ? "rotate-180" : ""
																		)}
																		aria-hidden="true"
																	/>
																</DisclosureButton>
																<div className="flex-1 flex items-center py-8 gap-x-3 overflow-hidden" onClick={() => {/* handleAccountItemClick(adAccount) */}}>
																	{/* <div
																		className={cn(
																			'absolute inset-y-0 left-0 w-1 h-full',
																			selectedAccountPairItem && adAccount.id === selectedAccountPairItem.id ? 'bg-blue-400' : 'bg-transparent'
																		)}
																	></div> */}
																	<div className="flex-shrink-0 flex items-center jutify-start w-[120px] pl-6">
																		{/* <AccountStatus accountItem={adAccount}/> */}
																	</div>
																	<div className="grow relative px-4 overflow-hidden text-xs text-gray-500 font-semibold space-y-2">
																		<div className="max-w-[540px] mx-auto">
																			<div className="flex items-center gap-x-2">
																				<div className="border border-purple-600 bg-purple-100/40 text-purple-800 text-xs font-semibold px-2 py-1 rounded-md">
																					Ads
																				</div>
																				<div className="truncate divide-x divide-gray-200 space-x-2">
																					<span>{adAccount?.ad_lwa_account?.profile_email}</span>
																					<span className="pl-2">
																						{getRegionName(adAccount?.ad_lwa_account?.region)}
																					</span>
																				</div>
																			</div>
																		</div>
																	</div>
																	<div className="flex-shrink-0 w-[130px] px-4">
																		<span className="text-xs text-gray-500 font-semibold">
																			{formatDate(adAccount?.ad_lwa_account?.updated_datetime, ".")}
																		</span>
																	</div>
																	<div className="flex-shrink-0 w-[180px] pr-6">
																		<span className="text-xs text-gray-500 font-semibold">
																			<span className="text-blue-400">{adAccount?.profiles?.filter((profile: any) => profile.is_connected).length}</span>/{adAccount?.profiles?.length} {t('status.connected')}
																		</span>
																	</div>
																</div>
															</div>
															<Transition
																show={open}
																enter="transition-all duration-200"
																enterFrom="max-h-0 opacity-0"
																enterTo="max-h-fit opacity-100"
																leave="transition-all duration-50"
																leaveFrom="max-h-fit opacity-100"
																leaveTo="max-h-0 opacity-0"
															>
																<DisclosurePanel static>
																	<div className="grow relative w-full">
																		{/* List */}
																		<ul className="divide-y divide-gray-200">
																			{adAccount?.profiles.filter((profile: any) => profile.is_subscription_active).map((profile: any, index: number) => 
																				<li
																					className="relative flex items-center gap-x-3 py-6 cursor-pointer bg-gray-100/80 hover:bg-gray-200/60 text-center text-gray-500 text-sm"
																					key={index}
																					onClick={() => {}}
																				>
																					<div
																						className={cn(
																							'absolute inset-y-0 left-0 w-1 h-full',
																							// selectedAccountPairItem && accountItem.id === selectedAccountPairItem.id ? 'bg-blue-400' : 'bg-transparent'
																						)}
																					></div>
																					<div className="flex-shrink-0 flex items-center jutify-start w-[176px] pl-14">
																						<AccountStatus accountItem={profile.amazon_account ? profile.amazon_account : { request_status: t("accounts.notConnected")}}/>
																					</div>
																					<div className="grow relative px-4 overflow-hidden text-xs text-gray-500 font-semibold text-left">
																						<div className="flex items-center max-w-[540px] mx-auto">
																							<div className="relative w-1/2 h-full flex flex-col items-start justify-between rounded-lg bg-purple-100/40 border border-purple-600 overflow-hidden">
																								<div className="flex-shrink-0 w-full h-9 flex items-center justify-between gap-x-2 py-2 pl-3 pr-4 bg-purple-600 text-white text-xs font-semibold">
																									<div className="flex-shrink-0 flex items-center gap-x-1.5">
																										<div className="text-[10px] py-0.5 px-1.5 bg-white/20 text-white rounded-sm">
																											{profile.account_type === "vendor" ? "Vendor" : "Seller"}
																										</div>
																										Ads
																									</div>
																									<div className="flex items-center gap-x-1">
																										<div className="w-4 h-4 rounded-full border-2 border-white/20">
																											<CircleFlag countryCode={profile.country_code.toLowerCase()} />
																										</div>
																										<div className="text-xs text-gray-100 font-normal">{profile.country_code}</div>
																									</div>
																								</div>
																								<div className="grow w-full min-h-0 flex flex-col justify-center px-4 py-3 space-y-1">
																									<div>
																										<div className="text-gray-400 text-[10px]">
																											account ID
																										</div>
																										<div className="text-xs text-gray-500 truncate">
																											{profile.account_id}
																										</div>
																									</div>
																									<div>
																										<div className="text-gray-400 text-[10px]">
																											account name
																										</div>
																										<div className="text-xs text-gray-500 truncate">
																											{profile.account_name}
																										</div>
																									</div>
																								</div>
																							</div>
																							<div className="flex-shrink-0 relative w-[60px] h-full flex items-start z-[1]">
																								<div className="relative w-1/2 h-1/2 border-b-2 border-purple-400 border-dashed">
																									<div className="absolute bottom-0 left-0 -translate-x-1/2 translate-y-1/2 inline-flex items-center justify-center border border-purple-600 p-1 rounded-full bg-white">
																										<span className="relative flex h-2 w-2">
																											<span className={cn(
																												"absolute inline-flex h-full w-full rounded-full opacity-75 bg-purple-600",
																												profile.is_connected ? "animate-ping" : "",
																											)}></span>
																											<span className={cn(
																												"relative inline-flex rounded-full h-2 w-2",
																												profile.is_connected ? "bg-purple-600" : "bg-purple-200",
																											)}></span>
																										</span>
																									</div>
																								</div>
																								<div className="relative w-1/2 h-1/2 border-b-2 border-blue-400 border-dashed">
																									<div className="absolute bottom-0 right-0 translate-x-1/2 translate-y-1/2 inline-flex items-center justify-center border border-blue-600 p-1 rounded-full bg-white">
																										<span className="relative flex h-2 w-2">
																											<span className={cn(
																												"absolute inline-flex h-full w-full rounded-full opacity-75 bg-blue-600",
																												profile.is_connected ? "animate-ping" : "",
																											)}></span>
																											<span className={cn(
																												"relative inline-flex rounded-full h-2 w-2",
																												profile.is_connected ? "bg-blue-600" : "bg-blue-200",
																											)}></span>
																										</span>
																									</div>
																								</div>
																							</div>
																							{ profile.is_connected
																								? (<div className="relative w-1/2 h-full flex flex-col items-start justify-between rounded-lg bg-blue-100/40 border border-blue-600 overflow-hidden">
																										<div className="flex-shrink-0 w-full h-9 flex items-center justify-between gap-x-2 py-2 px-4 bg-blue-600 text-white text-xs font-semibold">
																											<span className="flex-shrink-0">
																												{profile.amazon_account.sp_account_type === "vendor"
																													? "Vendor"
																													: "Seller"
																												}
																											</span>
																											<div className="flex items-center gap-x-1">
																												<div className="w-4 h-4 rounded-full border-2 border-white/20">
																													<CircleFlag countryCode={profile.amazon_account.sp_country_code.toLowerCase()} />
																												</div>
																												<div className="text-xs text-gray-100 font-normal">{profile.amazon_account.sp_country_code}</div>
																											</div>
																										</div>
																										<div className="grow w-full min-h-0 flex flex-col justify-center px-4 py-3 space-y-1">
																											<div>
																												<div className="text-gray-400 text-[10px]">
																													marketplace ID
																												</div>
																												<div className="text-xs text-gray-500 truncate">
																													{profile.amazon_account.sp_marketplace_id}
																												</div>
																											</div>
																											<div>
																												<div className="text-gray-400 text-[10px]">
																													selling partner ID
																												</div>
																												<div className="text-xs text-gray-500 truncate">
																													{profile.amazon_account.selling_partner_id}
																												</div>
																											</div>
																										</div>
																									</div>)
																								: (<div className="relative w-1/2 h-[126px] flex items-center justify-center rounded-lg bg-blue-100/40 border-2 border-blue-400 border-dashed overflow-hidden">
																										<div className="px-4 text-gray-400 text-xs font-semibold text-center">
																											No product data connected
																										</div>
																									</div>)
																							}
																						</div>
																					</div>
																					<div className="flex-shrink-0 w-[130px] px-4">
																						<span className="text-xs text-gray-500 font-semibold">
																							{formatDate(profile.amazon_account?.updated_datetime ?? adAccount?.ad_lwa_account?.updated_datetime, ".")}
																						</span>
																					</div>
																					<div className="flex-shrink-0 w-[180px] pr-8">
																						<button 
																							onClick={async (e) => {
																								e.stopPropagation()
																								const response = await api.createStripeCustomerPortalSession(
																									{
																										customer_id: profile.customer_id,
																									},
																									(session?.user as any).access_token
																								)
                                                if (response.url) {
                                                  window.location.href = response.url
                                                }
																							}}
																							className={cn(
																								"group relative flex-shrink-0 inline-flflex items-center gap-x-1 py-1.5 px-3 ml-auto text-sm/6 cursor-pointer rounded-md focus:outline-none overflow-hidden bg-blue-600 hover:bg-blue-700 text-white font-semibold",
																								"transition-width ease-in-out delay-150",
																							)}
																						>
																							<div className="flex-shrink-0 font-semibold">
																								{t('billing.table.header.billing')}
																							</div>
																						</button>
																					</div>
																				</li>
																			)}
																		</ul>
																	</div>
																</DisclosurePanel>
															</Transition>
														</>
													)}
												</Disclosure>
											</li>
										)})
									: (
										<li className="absolute inset-0 size-full flex items-center justify-center text-gray-400 text-sm">
											No Amazon account connected
										</li>
									)
								)
							})()}
						</ul>
					</div>
					)
				}
			</div>
			{/* right side slider for portfolio view content */}
			<Transition show={isAccountPairViewSliderOpen} as={Fragment}>
				<div className="absolute inset-0 z-[1]">
					{isAccountPairViewSliderOpen && selectedAccountPairItem &&
						<AccountPairViewSlider
							handleAccountPairViewCloseClick={handleAccountPairViewCloseClick}	
							selectedAccountPairItem={selectedAccountPairItem}
						/>
					}
				</div>
			</Transition>
			<Transition appear show={isConnectProductDataModalOpen}>
				<Dialog as="div" className="relative z-10 focus:outline-none" onClose={() => handleConnectProductDataModalCloseClick()}>
					<DialogBackdrop className="fixed inset-0 bg-black/30 backdrop-blur-sm" aria-hidden="true" />
					<div className="fixed inset-0 z-10 w-screen overflow-y-auto">
						<div className="flex min-h-full items-center justify-center p-4">
							<TransitionChild
								enter="ease-out duration-300"
								enterFrom="opacity-0 transform-[scale(95%)]"
								enterTo="opacity-100 transform-[scale(100%)]"
								leave="ease-in duration-200"
								leaveFrom="opacity-100 transform-[scale(100%)]"
								leaveTo="opacity-0 transform-[scale(95%)]"
							>
								<DialogPanel className="w-full max-w-3xl rounded-xl bg-white p-6">
									<div className="flex items-center justify-between">
										<div className="">
											<DialogTitle as="h3" className="text-base/7 font-medium text-gray-600">
												{t('accounts.chooseProductData')}
											</DialogTitle>
											<div className="flex-shrink-0 text-gray-500 text-sm space-x-1">
												<span className="text-blue-400 text-sm font-semibold">
													{(() => {
														return (availableConnectionByProfileId?.length > 0
															? availableConnectionByProfileId.length
															: "0"
														)
													})()}
												</span>
												<span>{t('accounts.productDataAvailable')}</span>
											</div>
										</div>
										<button
											className={cn(
												"inline-flex items-center gap-x-1 rounded-md bg-blue-600 py-1.5 px-3 text-sm/6 font-semibold text-white shadow-inner shadow-white/10 focus:outline-none hover:bg-blue-700 open:bg-gray-700 focus:outline-1 focus:outline-white cursor-pointer",
												"transition-width ease-in-out delay-150",
											)}
											onClick={(e) => {
												e.stopPropagation()
												setIsAddProductDataModalOpen(true)
											}}
										>
											<PlusIcon
												className="flex-shrink-0 h-5 w-5"
												aria-hidden="true"
											/>
												<span className="flex-shrink-0 block font-semibold">
													Add New
												</span>
										</button>
									</div>
									<div className="mt-4 relative grow w-full min-h-[200px] max-h-[400px] rounded-lg bg-white border border-gray-100 overflow-y-scroll">
										{/* List */}
										<div className="sticky inset-x-0 top-0 z-[1] flex items-center gap-x-3 py-3 text-center text-sm text-gray-400 font-bold bg-gray-100/40 backdrop-blur-md">
											<div className="flex-shrink-0 w-[100px] pl-6">
												{t('accounts.type')}
											</div>
											<div className="grow px-4">
												{t('accounts.productData')}
											</div>
											<div className="flex-shrink-0 w-[160px] pr-6">
												{t('accounts.actions')}
											</div>
										</div>
										{isConnectProductDataLoading
											? <ul className="animate-pulse p-6 space-y-3">
												<li className="w-full flex items-center gap-x-3">
													<div className="flex-shrink-0 w-24 h-16 rounded-md bg-gray-100"></div>
													<div className="flex-1 h-16 rounded-md bg-gray-100"></div>
													<div className="flex-shrink-0 w-32 h-16 rounded-md bg-gray-100"></div>
												</li>
												<li className="w-full flex items-center gap-x-3">
													<div className="flex-shrink-0 w-24 h-16 rounded-md bg-gray-100"></div>
													<div className="flex-1 h-16 rounded-md bg-gray-100"></div>
													<div className="flex-shrink-0 w-32 h-16 rounded-md bg-gray-100"></div>
												</li>
												<li className="w-full flex items-center gap-x-3">
													<div className="flex-shrink-0 w-24 h-16 rounded-md bg-gray-100"></div>
													<div className="flex-1 h-16 rounded-md bg-gray-100"></div>
													<div className="flex-shrink-0 w-32 h-16 rounded-md bg-gray-100"></div>
												</li>
												<li className="w-full flex items-center gap-x-3">
													<div className="flex-shrink-0 w-24 h-16 rounded-md bg-gray-100"></div>
													<div className="flex-1 h-16 rounded-md bg-gray-100"></div>
													<div className="flex-shrink-0 w-32 h-16 rounded-md bg-gray-100"></div>
												</li>
											</ul>
											: <ul className="divide-y divide-gray-100">
												{(() => {
													return (availableConnectionByProfileId?.length > 0
														? availableConnectionByProfileId.map((spAccount: any, index: number) => {
															return (
																<li
																	className="relative flex items-center gap-x-3 py-6 cursor-pointer hover:bg-gray-100/50 text-center text-gray-500 text-sm"
																	key={index}
																>
																	<div className="flex-shrink-0 w-[100px] pl-6">
																		<span className="text-xs text-gray-500 font-semibold">
																			{spAccount.account_type === "vendor"
																				? "Vendor"
																				: "Seller"
																			}
																		</span>
																	</div>
																	<div className="grow relative flex items-center justify-center px-4 gap-x-4 divide-x divide-gray-200 overflow-hidden text-xs text-gray-500 font-semibold text-left">
																		{/* <div>
																			<div className="text-gray-400 text-[10px]">
																				country code
																			</div>
																			<div className="flex items-center gap-x-1">
																				<div className="w-4 h-4 rounded-full border-2 border-white/20">
																					<CircleFlag countryCode={account.amazon_account?.sp_country_code?.toLowerCase()} />
																				</div>
																				<div className="text-xs text-gray-500">{account.amazon_account?.sp_country_code}</div>
																			</div>
																		</div> */}
																		{/* <div className="">
																			<div className="text-gray-400 text-[10px]">
																				marketplace ID
																			</div>
																			<div className="text-xs text-gray-500">
																				{spAccount?.sp_marketplace_id}
																			</div>
																		</div> */}
																		<div className="pl-4">
																			<div className="text-gray-400 text-[10px]">
																				selling partner ID
																			</div>
																			<div className="text-xs text-gray-500">
																				{spAccount?.selling_partner_id}
																			</div>
																		</div>
																		<div className="pl-4">
																			<div className="text-gray-400 text-[10px]">
																				created date
																			</div>
																			<div className="text-xs text-gray-500">
																				{formatDate(spAccount?.created_datetime, ".")}
																			</div>
																		</div>
																	</div>
																	<div className="flex-shrink-0 w-[160px] pr-6">
																		<button
																			className={cn(
																				"group relative flex-shrink-0 flex items-center ml-auto gap-x-1 py-1.5 px-3 text-sm/6 cursor-pointer rounded-md focus:outline-none overflow-hidden bg-blue-100 hover:bg-blue-200 text-blue-500 font-semibold",
																				"transition-width ease-in-out delay-150",
																			)}
																			onClick={async (e) => {
																				e.stopPropagation()
																				if (selectedProfileId && spAccount.id) {
																					setIsConnectProfileLoading(true)
																					setSelectedSpLwaAccountId(spAccount.id)
																					const connectProfileResponse = await api.connectProfile(selectedProfileId, spAccount.id, (session?.user as any).access_token)
																					fetchProfiles()
																					setSelectedSpLwaAccountId(null)
																					setIsConnectProfileLoading(false)
																					setIsAddProductDataModalOpen(false)
																				}
																			}}
																		>
																			{(() => {
																				const spLwaAccountId = spAccount?.id || ""
																				return (
																					isConnectProfileLoading && selectedSpLwaAccountId && selectedSpLwaAccountId === spLwaAccountId
																						? <svg className="animate-spin h-4 w-4 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
																								<circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
																								<path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
																							</svg>
																						: <ArrowsRightLeftIcon
																								className="flex-shrink-0 h-4 w-4"
																								aria-hidden="true"
																							/>
																				)
																				})()
																			}
																			<div className="flex-shrink-0 font-semibold">
																				Connect
																			</div>
																		</button>
																	</div>
																</li>
															)})
														: (
															<li className="absolute inset-0 size-full flex items-center justify-center text-gray-400 text-sm">
																No product data available
															</li>
														)
													)
												})()}
											</ul>
										}
									</div>
									<div className="mt-4 flex items-center justify-end gap-x-4">
										<Button
											className="inline-flex items-center gap-2 rounded-md bg-white py-1.5 px-3 text-sm/6 font-semibold text-gray-500 shadow-inner shadow-white/10 focus:outline-none data-[hover]:bg-gray-100 data-[open]:bg-gray-700 data-[focus]:outline-1 data-[focus]:outline-white"
											onClick={() => handleConnectProductDataModalCloseClick()}
										>
											Cancel
										</Button>
									</div>
								</DialogPanel>
							</TransitionChild>
						</div>
					</div>
				</Dialog>
			</Transition>
			<Transition appear show={isAddProductDataModalOpen}>
				<Dialog as="div" className="relative z-10 focus:outline-none" onClose={() => setIsAddProductDataModalOpen(false)}>
					<DialogBackdrop className="fixed inset-0 bg-black/30 backdrop-blur-sm" aria-hidden="true" />
					<div className="fixed inset-0 z-10 w-screen overflow-y-auto">
						<div className="flex min-h-full items-center justify-center p-4">
							<TransitionChild
								enter="ease-out duration-300"
								enterFrom="opacity-0 transform-[scale(95%)]"
								enterTo="opacity-100 transform-[scale(100%)]"
								leave="ease-in duration-200"
								leaveFrom="opacity-100 transform-[scale(100%)]"
								leaveTo="opacity-0 transform-[scale(95%)]"
							>
								<DialogPanel className="w-full max-w-fit rounded-xl bg-white p-6">
									<DialogTitle as="h3" className="text-base/7 font-medium text-gray-600">
										{t('accounts.chooseProductType')}
									</DialogTitle>
									<div className="relative size-full flex flex-col">
										<div className="flex-shrink-0 max-w-lg text-gray-500 text-sm space-x-1">
											<span>{t('accounts.authorizationDescription')}</span>
										</div>
										<div className="mt-6 relative grow grid grid-cols-2 gap-x-6 w-full">
											<div className="p-4 rounded-lg border border-gray-100">
												<div className="mb-4 text-gray-700 text-sm font-semibold">
													Are you a seller?
												</div>
												<ConnectSpButton
													buttonClassName={cn(
															"group relative flex-shrink-0 flex-1 flex items-center justify-center gap-x-1 p-3 cursor-pointer rounded-md focus:outline-none text-sm overflow-hidden bg-blue-100 hover:bg-blue-200 text-blue-500 font-semibold",
															"transition-width ease-in-out delay-150"
													)}
													isLNBExpanded={true}
												/>
											</div>
											<div className="p-4 rounded-lg border border-gray-100">
												<div className="mb-4 text-gray-700 text-sm font-semibold">
													Are you a vendor?
												</div>
												<ConnectVendorButton
													buttonClassName={cn(
															"group relative flex-shrink-0 flex-1 flex items-center justify-center gap-x-1 p-3 cursor-pointer rounded-md focus:outline-none text-sm overflow-hidden bg-blue-100 hover:bg-blue-200 text-blue-500 font-semibold",
															"transition-width ease-in-out delay-150"
													)}
													isLNBExpanded={true}
												/>
											</div>
										</div>
									</div>
									<div className="mt-4 flex items-center justify-end gap-x-4">
										<Button
											className="inline-flex items-center gap-2 rounded-md bg-white py-1.5 px-3 text-sm/6 font-semibold text-gray-500 shadow-inner shadow-white/10 focus:outline-none data-[hover]:bg-gray-100 data-[open]:bg-gray-700 data-[focus]:outline-1 data-[focus]:outline-white"
											onClick={() => setIsAddProductDataModalOpen(false)}
										>
											Cancel
										</Button>
									</div>
								</DialogPanel>
							</TransitionChild>
						</div>
					</div>
				</Dialog>
			</Transition>
		</div>
  )
}