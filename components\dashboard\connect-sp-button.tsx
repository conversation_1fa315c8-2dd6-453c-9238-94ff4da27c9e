"use client";

import actions from "@/actions";
import { cn } from "@/utils/msc";
import { PlusIcon } from "@heroicons/react/20/solid";
import { useState } from "react";
import { useTranslations } from "next-intl";

interface ConnectButtonProps extends React.HTMLAttributes<HTMLDivElement> {
  isLNBExpanded?: boolean;
  buttonClassName?: string;
  selectedCountryCode?: string;
}

export default function ConnectSpButton({
  isLNBExpanded,
  buttonClassName,
  selectedCountryCode,
  ...props
}: ConnectButtonProps) {
  const t = useTranslations('component');
  const [countryCode, setCountryCode] = useState(selectedCountryCode || "US");

  const handleClick = async () => {
    const oauthLink = await actions.oauth.getExSpLoginLink(countryCode);
    window.location.href = oauthLink.url;
  };

  return (
    <div className="flex-shrink-0 flex items-center gap-x-2">
      <div className={cn("flex items-center border border-gray-100 rounded-lg h-11 px-2", selectedCountryCode && "hidden")}>
        <select
          value={countryCode}
          onChange={(e) => setCountryCode(e.target.value)}
          disabled={!!selectedCountryCode}
          className={cn("text-sm text-gray-600 outline-none", selectedCountryCode && "cursor-not-allowed")}
        >
          <option value="US">🇺🇸 US</option>
          <option value="CA">🇨🇦 CA</option>
          <option value="MX">🇲🇽 MX</option>
          <option value="DE">🇩🇪 DE</option>
          <option value="JP">🇯🇵 JP</option>
          <option value="BR">🇧🇷 BR</option>
          <option value="ES">🇪🇸 ES</option>
          <option value="UK">🇬🇧 UK</option>
          <option value="FR">🇫🇷 FR</option>
          <option value="BE">🇧🇪 BE</option>
          <option value="NL">🇳🇱 NL</option>
          <option value="IT">🇮🇹 IT</option>
          <option value="SE">🇸🇪 SE</option>
          <option value="ZA">🇿🇦 ZA</option>
          <option value="PL">🇵🇱 PL</option>
          <option value="EG">🇪🇬 EG</option>
          <option value="SA">🇸🇦 SA</option>
          <option value="TR">🇹🇷 TR</option>
          <option value="AE">🇦🇪 AE</option>
          <option value="IN">🇮🇳 IN</option>
          <option value="SG">🇸🇬 SG</option>
          <option value="AU">🇦🇺 AU</option>
        </select>
      </div>
      <button className={cn(buttonClassName)} onClick={handleClick}>
        <PlusIcon
          className="flex-shrink-0 h-5 w-5"
          aria-hidden="true"
        />
        {isLNBExpanded ? (
          <span className="flex-shrink-0 block font-semibold">
            {t('accounts.authorizeSP')}
          </span>
        ) : (
          ""
        )}
      </button>
    </div>
  );
}
