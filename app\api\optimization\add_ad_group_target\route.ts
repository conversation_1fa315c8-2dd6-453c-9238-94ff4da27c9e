import { ErrorResponse } from "@/app/api/_models";
import { getServerApiHostUrl } from "@/utils/host";
import { NextRequest, NextResponse } from "next/server";

export type AddAdGroupTargetResponse = {
  success: boolean;
  target_id?: string;
  message?: string;
};

export async function POST(
  request: NextRequest
): Promise<NextResponse<AddAdGroupTargetResponse | ErrorResponse>> {
  const accessToken = request.headers.get("Authorization");
  const body = await request.json();

  if (!accessToken) {
    return NextResponse.json(
      { message: "Authorization header is missing" },
      { status: 400 }
    );
  }

  const accountId = request.nextUrl.searchParams.get("account_id")
  if (!accountId) {
    return NextResponse.json(
      { message: "account_id query is missing" },
      { status: 400 }
    );
  }

  const marketplaceId = request.nextUrl.searchParams.get("marketplace_id")
  if (!marketplaceId) {
    return NextResponse.json(
      { message: "marketplace_id query is missing" },
      { status: 400 }
    );
  }

  const campaignId = request.nextUrl.searchParams.get("campaign_id")
  if (!campaignId) {
    return NextResponse.json(
      { message: "campaign_id query is missing" },
      { status: 400 }
    );
  }

  const adGroupId = request.nextUrl.searchParams.get("ad_group_id")
  if (!adGroupId) {
    return NextResponse.json(
      { message: "ad_group_id query is missing" },
      { status: 400 }
    );
  }

  try {
    const addAdGroupTargetResponse = await fetch(
      `${await getServerApiHostUrl()}/api/optimization/add_ad_group_target?account_id=${accountId}&marketplace_id=${marketplaceId}&campaign_id=${campaignId}&ad_group_id=${adGroupId}`,
      {
        method: "POST",
        headers: {
          "Authorization": `Bearer ${accessToken}`,
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          "target_type": body?.target_type,
          "keyword": body?.keyword,
          "match_type": body?.match_type,
          "asin": body?.asin,
          "bid": body?.bid,
          "negative": body?.target_type?.includes("NEGATIVE") || false
        }),
        cache: "no-cache",
      }
    ).then((res) => res.json());

    return NextResponse.json(addAdGroupTargetResponse, { status: 200 });
  } catch (error) {
    console.error('Error adding ad group target:', error);
    return NextResponse.json(
      { message: "Failed to add target" },
      { status: 500 }
    );
  }
}
