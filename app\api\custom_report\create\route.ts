import { ErrorResponse } from "@/app/api/_models";
import { getServerApiHostUrl } from "@/utils/host";
import { NextRequest, NextResponse } from "next/server";

export async function POST(
  request: NextRequest
): Promise<NextResponse<any | ErrorResponse>> {
  const accessToken = request.headers.get("Authorization");
  if (!accessToken) {
    return NextResponse.json(
      { message: "Authorization header is missing" },
      { status: 400 }
    );
  }

  try {
    const reportInfo = await request.json();
    
    const createReportResponse = await fetch(
      `${await getServerApiHostUrl()}/api/custom_report/create`,
      {
        method: "POST",
        headers: {
          "Authorization": `Bearer ${accessToken}`,
          "Content-Type": "application/json",
        },
        body: JSON.stringify(reportInfo)
      }
    );

    if (!createReportResponse.ok) {
      const errorData = await createReportResponse.json();
      if (errorData.detail === "Token has expired") {
        return NextResponse.json(
          { detail: "Token has expired" },
          { status: 401 }
        );
      }
      return NextResponse.json(
        { message: "Failed to create report", error: errorData },
        { status: createReportResponse.status }
      );
    }

    const result = await createReportResponse.json();
    return NextResponse.json(result, { status: 200 });
  } catch (error) {
    console.error("Error creating report:", error);
    return NextResponse.json(
      { message: "Internal server error", error: String(error) },
      { status: 500 }
    );
  }
} 