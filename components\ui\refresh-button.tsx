import { ArrowPathIcon } from '@heroicons/react/24/outline';
import { cn } from '@/utils/msc';
import { ButtonHTMLAttributes } from 'react';

interface RefreshButtonProps extends ButtonHTMLAttributes<HTMLButtonElement> {
  isLoading?: boolean;
  label?: string;
  size?: 'sm' | 'md' | 'lg';
  variant?: 'primary' | 'secondary' | 'ghost';
}

export default function RefreshButton({
  isLoading = false,
  label,
  size = 'md',
  variant = 'primary',
  className,
  ...props
}: RefreshButtonProps) {
  const sizeClasses = {
    sm: 'h-8 w-8',
    md: 'h-9 w-9',
    lg: 'h-10 w-10',
  };

  const iconSizeClasses = {
    sm: 'h-4 w-4',
    md: 'h-5 w-5',
    lg: 'h-6 w-6',
  };

  const variantClasses = {
    primary: 'bg-blue-100 hover:bg-blue-200 text-blue-500',
    secondary: 'bg-gray-100 hover:bg-gray-200 text-gray-500',
    ghost: 'hover:bg-gray-100 text-gray-500',
  };

  return (
    <button
      className={cn(
        'flex items-center justify-center rounded-full transition-all focus:outline-none',
        sizeClasses[size],
        variantClasses[variant],
        isLoading && 'cursor-not-allowed opacity-70',
        className
      )}
      disabled={isLoading}
      title={label || 'Refresh'}
      {...props}
    >
      <ArrowPathIcon 
        className={cn(
          iconSizeClasses[size],
          isLoading && 'animate-spin'
        )} 
      />
    </button>
  );
} 