import { ErrorResponse } from "@/app/api/_models";
import { getServerApiHostUrl } from "@/utils/host";
import { NextRequest, NextResponse } from "next/server";

export type ConnectProfileResponse = any;

export async function POST(
  request: NextRequest
): Promise<NextResponse<ConnectProfileResponse | ErrorResponse>> {
  const accessToken = request.headers.get("Authorization");
  if (!accessToken) {
    return NextResponse.json(
      { message: "Authorization header is missing" },
      { status: 400 }
    );
  }
  const profileId = request.nextUrl.searchParams.get("profile_id")
  if (!profileId) {
    return NextResponse.json(
      { message: "profile_id query is missing" },
      { status: 400 }
    );
  }
  const spLwaAccountId = request.nextUrl.searchParams.get("sp_lwa_account_id")
  if (!spLwaAccountId) {
    return NextResponse.json(
      { message: "sp_lwa_account_id query is missing" },
      { status: 400 }
    );
  }
  const connectProfileResponse = await fetch(
    `${await getServerApiHostUrl()}/api/ex/oauth/connect_profile?profile_id=${profileId}&sp_lwa_account_id=${spLwaAccountId}`,
    {
      method: "POST",
      headers: {
        "Authorization": `Bearer ${accessToken}`,
        "Content-Type": "application/json",
      },
      cache: "no-cache",
    }
  ).then((res) => res.json());

  return NextResponse.json(connectProfileResponse, { status: 200 });
}
