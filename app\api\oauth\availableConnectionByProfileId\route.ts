import { ErrorResponse } from "@/app/api/_models";
import { getServerApiHostUrl } from "@/utils/host";
import { NextRequest, NextResponse } from "next/server";

export type AvailableConnectionByProfileIdResponse = any;

export async function GET(
  request: NextRequest
): Promise<NextResponse<AvailableConnectionByProfileIdResponse | ErrorResponse>> {
  const accessToken = request.headers.get("Authorization");
  if (!accessToken) {
    return NextResponse.json(
      { message: "Authorization header is missing" },
      { status: 400 }
    );
  }

	const profileId = request.nextUrl.searchParams.get("profile_id")
  if (!profileId) {
    return NextResponse.json(
      { message: "profile_id query is missing" },
      { status: 400 }
    );
  }

  const availableConnectionByProfileIdResponse = await fetch(
    `${await getServerApiHostUrl()}/api/ex/oauth/available_connection_by_profile_id?profile_id=${profileId}`,
    {
      method: "GET",
      headers: {
        "Authorization": `Bearer ${accessToken}`,
      },
      cache: "no-cache",
    }
  ).then((res) => res.json());

  return NextResponse.json(availableConnectionByProfileIdResponse, { status: 200 });
}
