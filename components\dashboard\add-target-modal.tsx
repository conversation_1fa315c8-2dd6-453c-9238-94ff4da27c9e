'use client';

import { useState } from 'react';
import {
  Dialog,
  DialogPanel,
  DialogTitle,
  Transition,
  TransitionChild,
  Listbox,
} from '@headlessui/react';
import { XMarkIcon, ChevronUpDownIcon } from '@heroicons/react/20/solid';
import { Check } from '@/components/ui/check';
import { useTranslations } from 'next-intl';
import { cn } from '@/utils/msc';

interface AddTargetModalProps {
  isOpen: boolean;
  onClose: () => void;
  selectedTargetType: string;
  onAdd: (targetData: any) => void;
}

// Match types for keyword targeting
const matchTypeOptions = [
  { type: 'EXACT', name: 'Exact' },
  { type: 'PHRASE', name: 'Phrase' },
  { type: 'BROAD', name: 'Broad' },
];

export default function AddTargetModal({
  isOpen,
  onClose,
  selectedTargetType,
  onAdd,
}: AddTargetModalProps) {
  const t = useTranslations('component');

  // Form states
  const [keyword, setKeyword] = useState('');
  const [asin, setAsin] = useState('');
  const [bid, setBid] = useState('');
  const [selectedMatchType, setSelectedMatchType] = useState(
    matchTypeOptions[0]
  );
  const [errors, setErrors] = useState<{ [key: string]: string }>({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  const resetForm = () => {
    setKeyword('');
    setAsin('');
    setBid('');
    setSelectedMatchType(matchTypeOptions[0]);
    setErrors({});
    setIsSubmitting(false);
  };

  const handleClose = () => {
    resetForm();
    onClose();
  };

  const validateForm = () => {
    const newErrors: { [key: string]: string } = {};

    if (
      selectedTargetType === 'KEYWORD' ||
      selectedTargetType === 'NEGATIVE_KEYWORD'
    ) {
      if (!keyword.trim()) {
        newErrors.keyword = 'Keyword is required';
      } else if (keyword.trim().length < 2) {
        newErrors.keyword = 'Keyword must be at least 2 characters long';
      }
    }

    if (
      selectedTargetType === 'PRODUCT' ||
      selectedTargetType === 'NEGATIVE_PRODUCT'
    ) {
      if (!asin.trim()) {
        newErrors.asin = 'ASIN is required';
      } else if (!/^[A-Z0-9]{10}$/.test(asin.trim().toUpperCase())) {
        newErrors.asin =
          'ASIN must be 10 characters long and contain only letters and numbers';
      }
    }

    if (
      (selectedTargetType === 'KEYWORD' || selectedTargetType === 'PRODUCT') &&
      !bid.trim()
    ) {
      newErrors.bid = 'Bid is required';
    }

    if (bid && (isNaN(parseFloat(bid)) || parseFloat(bid) <= 0)) {
      newErrors.bid = 'Bid must be a valid positive number';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async () => {
    if (!validateForm()) return;

    setIsSubmitting(true);

    try {
      const targetData: any = {
        target_type: selectedTargetType,
      };

      if (
        selectedTargetType === 'KEYWORD' ||
        selectedTargetType === 'NEGATIVE_KEYWORD'
      ) {
        targetData.keyword = keyword.trim();
        if (selectedTargetType === 'KEYWORD') {
          targetData.match_type = selectedMatchType.type;
          targetData.bid = parseFloat(bid);
        }
      }

      if (
        selectedTargetType === 'PRODUCT' ||
        selectedTargetType === 'NEGATIVE_PRODUCT'
      ) {
        targetData.asin = asin.trim().toUpperCase();
        if (selectedTargetType === 'PRODUCT') {
          targetData.bid = parseFloat(bid);
        }
      }

      await onAdd(targetData);
      handleClose();
    } catch (error) {
      console.error('Error adding target:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const getModalTitle = () => {
    switch (selectedTargetType) {
      case 'KEYWORD':
        return 'Add Keyword Target';
      case 'NEGATIVE_KEYWORD':
        return 'Add Negative Keyword';
      case 'PRODUCT':
        return 'Add Product Target';
      case 'NEGATIVE_PRODUCT':
        return 'Add Negative Product';
      default:
        return 'Add Target';
    }
  };

  return (
    <Transition appear show={isOpen}>
      <Dialog
        as='div'
        className='relative z-50 focus:outline-none'
        onClose={handleClose}
      >
        <div
          className='fixed inset-0 bg-black/30 backdrop-blur-sm'
          aria-hidden='true'
        />
        <div className='fixed inset-0 z-10 w-screen overflow-y-auto'>
          <div className='flex min-h-full items-center justify-center p-4'>
            <TransitionChild
              enter='ease-out duration-300'
              enterFrom='opacity-0 transform-[scale(95%)]'
              enterTo='opacity-100 transform-[scale(100%)]'
              leave='ease-in duration-200'
              leaveFrom='opacity-100 transform-[scale(100%)]'
              leaveTo='opacity-0 transform-[scale(95%)]'
            >
              <DialogPanel className='w-full max-w-md rounded-xl bg-white p-6'>
                <div className='flex items-center justify-between mb-4'>
                  <DialogTitle
                    as='h3'
                    className='text-lg font-semibold text-gray-900'
                  >
                    {getModalTitle()}
                  </DialogTitle>
                  <button
                    onClick={handleClose}
                    className='rounded-md p-1 hover:bg-gray-100 focus:outline-none'
                  >
                    <XMarkIcon className='h-5 w-5 text-gray-400' />
                  </button>
                </div>

                <div className='space-y-4'>
                  {/* Keyword Input */}
                  {(selectedTargetType === 'KEYWORD' ||
                    selectedTargetType === 'NEGATIVE_KEYWORD') && (
                    <>
                      <div>
                        <label className='block text-sm font-medium text-gray-700 mb-1'>
                          Keyword <span className='text-red-500'>*</span>
                        </label>
                        <input
                          type='text'
                          value={keyword}
                          onChange={(e) => setKeyword(e.target.value)}
                          className={cn(
                            'w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:outline-blue-200',
                            errors.keyword
                              ? 'border-red-300'
                              : 'border-gray-300'
                          )}
                          placeholder='Enter keyword'
                        />
                        {errors.keyword && (
                          <p className='mt-1 text-sm text-red-600'>
                            {errors.keyword}
                          </p>
                        )}
                      </div>

                      <div>
                        <label className='block text-sm font-medium text-gray-700 mb-1'>
                          Match Type <span className='text-red-500'>*</span>
                        </label>
                        <Listbox
                          value={selectedMatchType}
                          onChange={setSelectedMatchType}
                        >
                          <div className='relative'>
                            <Listbox.Button className='relative w-full cursor-pointer rounded-md bg-white py-2 pl-3 pr-10 text-left border border-gray-300 focus:outline-none focus:ring-2 focus:outline-blue-200'>
                              <span className='block truncate'>
                                {selectedMatchType.name}
                              </span>
                              <span className='pointer-events-none absolute inset-y-0 right-0 flex items-center pr-2'>
                                <ChevronUpDownIcon
                                  className='h-5 w-5 text-gray-400'
                                  aria-hidden='true'
                                />
                              </span>
                            </Listbox.Button>
                            <Transition
                              leave='transition ease-in duration-100'
                              leaveFrom='opacity-100'
                              leaveTo='opacity-0'
                            >
                              <Listbox.Options className='absolute z-10 mt-1 max-h-60 w-full overflow-auto rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none'>
                                {matchTypeOptions.map((option) => (
                                  <Listbox.Option
                                    key={option.type}
                                    className={({ active }) =>
                                      cn(
                                        'relative cursor-pointer select-none py-2 pl-10 pr-4',
                                        active
                                          ? 'bg-blue-100 text-blue-900'
                                          : 'text-gray-900'
                                      )
                                    }
                                    value={option}
                                  >
                                    {({ selected }) => (
                                      <>
                                        <span
                                          className={cn(
                                            'block truncate',
                                            selected
                                              ? 'font-medium'
                                              : 'font-normal'
                                          )}
                                        >
                                          {option.name}
                                        </span>
                                        {selected && (
                                          <span className='absolute inset-y-0 left-0 flex items-center pl-3 text-blue-600'>
                                            <Check
                                              className='h-5 w-5'
                                              aria-hidden='true'
                                            />
                                          </span>
                                        )}
                                      </>
                                    )}
                                  </Listbox.Option>
                                ))}
                              </Listbox.Options>
                            </Transition>
                          </div>
                        </Listbox>
                      </div>
                    </>
                  )}

                  {(selectedTargetType === 'PRODUCT' ||
                    selectedTargetType === 'NEGATIVE_PRODUCT') && (
                    <div>
                      <label className='block text-sm font-medium text-gray-700 mb-1'>
                        ASIN <span className='text-red-500'>*</span>
                      </label>
                      <input
                        type='text'
                        value={asin}
                        onChange={(e) => setAsin(e.target.value.toUpperCase())}
                        maxLength={10}
                        className={cn(
                          'w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:outline-blue-200',
                          errors.asin ? 'border-red-300' : 'border-gray-300'
                        )}
                        placeholder='Enter ASIN (e.g., B08N5WRWNW)'
                      />
                      {errors.asin && (
                        <p className='mt-1 text-sm text-red-600'>
                          {errors.asin}
                        </p>
                      )}
                    </div>
                  )}

                  {(selectedTargetType === 'KEYWORD' ||
                    selectedTargetType === 'PRODUCT') && (
                    <div>
                      <label className='block text-sm font-medium text-gray-700 mb-1'>
                        Bid <span className='text-red-500'>*</span>
                      </label>
                      <input
                        type='number'
                        step='0.01'
                        min='0'
                        value={bid}
                        onChange={(e) => setBid(e.target.value)}
                        className={cn(
                          'w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:outline-blue-200',
                          errors.bid ? 'border-red-300' : 'border-gray-300'
                        )}
                        placeholder='Enter bid amount'
                      />
                      {errors.bid && (
                        <p className='mt-1 text-sm text-red-600'>
                          {errors.bid}
                        </p>
                      )}
                    </div>
                  )}
                </div>

                <div className='flex justify-end gap-3 mt-6'>
                  <button
                    onClick={handleClose}
                    className='px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:outline-blue-200'
                  >
                    Cancel
                  </button>
                  <button
                    onClick={handleSubmit}
                    disabled={isSubmitting}
                    className='px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:outline-blue-200 disabled:opacity-50 disabled:cursor-not-allowed'
                  >
                    {isSubmitting ? 'Adding...' : 'Add'}
                  </button>
                </div>
              </DialogPanel>
            </TransitionChild>
          </div>
        </div>
      </Dialog>
    </Transition>
  );
}
