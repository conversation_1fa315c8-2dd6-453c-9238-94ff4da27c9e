"use client"

import Plot from "react-plotly.js"
import { formatDate } from "@/utils/msc"

type PieChartData = {
  id: string;
  budget_usage_percentage: number;
	border: string;
	bg: string;
	text: string;
}

export default function DailyBudgetPieChart({ data }: { data: PieChartData[] }) {
  const dataSet = []
  dataSet.push({
		values: data.filter(d => d.budget_usage_percentage > 0).map((d) => d.budget_usage_percentage),
		hole: 0.7,
		pull: 0.015,
    type: 'pie',
    marker: {
			colors: data.filter(d => d.budget_usage_percentage > 0).map((d) => d.bg),
			line: {
				color: data.filter(d => d.budget_usage_percentage > 0).map((d) => d.border),
				width: 1,
			},
		},
		textfont: {
			color: data.filter(d => d.budget_usage_percentage > 0).map((d) => d.text),
			size: 10,
			weight: 'bold',
		},
    name: 'Daily Budget Usage',
  })
  return (
    <Plot
      data={dataSet as any}
      layout={{
        margin: {
          l: 1,
          r: 1,
          b: 1,
          t: 1,
          pad: 0
        },
				paper_bgcolor: 'rgba(0,0,0,0)',
				plot_bgcolor: 'rgba(0,0,0,0)',
        autosize: true,
        showlegend: false,
        dragmode: false,
      }}
      config={{
        displayModeBar: false,
      }}
      useResizeHandler={true}
      className="w-full h-full"
    />
  )
}
