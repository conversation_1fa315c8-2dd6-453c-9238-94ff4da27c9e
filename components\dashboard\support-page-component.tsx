"use client"

import { api } from "@/utils/api"
import { cn, formatDateTime, hexToRgbA, stringToColor, useScrollToBottom } from "@/utils/msc"
import { useSession } from "next-auth/react"
import { Dialog, DialogPanel, DialogTitle, Disclosure, DisclosureButton, DisclosurePanel, Tab, TabGroup, TabList, TabPanel, TabPanels, Transition, TransitionChild } from '@headlessui/react'
import { Fragment, Key, useEffect, useRef, useState } from "react"
import { ChevronDownIcon, ChatBubbleOvalLeftEllipsisIcon, ChevronLeftIcon, PlusIcon, PaperAirplaneIcon, PaperClipIcon, PlusCircleIcon, XMarkIcon } from "@heroicons/react/20/solid"
import Image from "next/image"
import ProfileSelect, { ProfileOption } from "@/components/dashboard/profile-select"
import MarketplaceSelect, { MarketplaceOption } from "@/components/dashboard/marketplace-select"
import { useLocale, useTranslations } from "next-intl"
import { StaticImport } from "next/dist/shared/lib/get-img-props"
import { ArrowRight } from "lucide-react"

interface SupportPageComponentProps {
  locale: string;
  setMaximizeWindow: (value: boolean) => void;
}

export default function SupportPageComponent({
  locale,
  setMaximizeWindow
}: SupportPageComponentProps) {
  const menuOptions = {
    menu1: {
      category: "Getting Started",
      title: "Get Started With LG Optapex",
      url: "https://agricultural-hoverfly.super.site/get-started-with-lg-optapex",
    },
    menu2: {
      category: "Getting Started",
      title: "Understanding Optimization",
      url: "https://agricultural-hoverfly.super.site/learn-how-optimization-works",
    },
    menu3: {
      category: "Getting Started",
      title: "Manage Optimization",
      url: "https://agricultural-hoverfly.super.site/management-optimization/monitoring-and-controlling-budget",
    },
    menu4: {
      category: "Find Out More",
      title: "Reports",
      url: "https://agricultural-hoverfly.super.site/reports-overview",
    },
    menu5: {
      category: "Find Out More",
      title: "Brand Protection Dashboard",
      url: "https://agricultural-hoverfly.super.site/brand-protection-dashboard-overview",
    },
    menu6: {
      category: "Find Out More",
      title: "Realtime Dashboard",
      url: "https://agricultural-hoverfly.super.site/real-time-dashboard-overview",
    },
    menu7: {
      category: "Find Out More",
      title: "Downloads",
      url: "https://agricultural-hoverfly.super.site/downloads-overview",
    },
    menu8: {
      category: "Find Out More",
      title: "Account & Subscription",
      url: "https://agricultural-hoverfly.super.site/account-subscription",
    },
  }
  const [selectedMenu, setSelectedMenu] = useState<{ title: string; url: string } | null>(null);
  
  return (
    <div className="absolute inset-0 rounded-md overflow-hidden">
      {selectedMenu
        ? (
          <div className="size-full flex flex-col">
            <div className="flex items-center p-2 border-b bg-white">
              <button
                onClick={() => {
                  setSelectedMenu(null);
                  setMaximizeWindow(false);
                }}
                className="flex-shrink-0 flex items-center gap-x-1 px-3 py-2 text-sm font-medium text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-md transition-colors"
              >
                <ChevronLeftIcon className="h-4 w-4" />
                Back
              </button>
            </div>
            
            <div className="relative flex-1 w-full min-h-0">
              <div className="relative size-full flex items-center justify-center bg-white/70">
                <svg className="animate-spin h-6 w-6 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
              </div>
              <iframe
                src={
                  // locale === "en"
                  //   ? "https://agricultural-hoverfly.super.site/"
                  //   : "https://help-kr.optapex.com/"
                  selectedMenu.url
                }
                title="Documentation"
                className="block absolute inset-0 size-full border-none"
              />
            </div>
          </div>
        )
        : (
          <div className="space-y-6 h-full overflow-y-auto">
            <div className="pt-10 px-4">
              <div className="text-3xl font-bold">
                <div className="text-gray-400">Hi there 👋</div>
                <div className="text-gray-800">Do you need any help?</div>
              </div>
              <p className="mt-4 text-gray-600">
                Welcome to the LG Optapex support and documentation center. Here you can find resources to help you get started, troubleshoot issues, and learn more about our platform.
              </p>
            </div>
            <div className="px-4 pb-4">
              <div className="p-4 space-y-6 border border-white/20 rounded-md shadow-lg bg-white/40 backdrop-blur-md">
                {Object.entries(
                  Object.values(menuOptions).reduce((acc, option) => {
                    if (!acc[option.category]) {
                      acc[option.category] = [];
                    }
                    acc[option.category].push(option);
                    return acc;
                  }, {} as Record<string, typeof menuOptions[keyof typeof menuOptions][]>)
                ).map(([category, options]) => (
                  <div key={category} className="space-y-2">
                    <h3 className="text-xs font-bold text-blue-500">{category}</h3>
                    <div className="space-y-2">
                      {options.map((option, index) => (
                        <button
                          key={`${category}-${index}`}
                          onClick={() => {
                            setSelectedMenu(option);
                            setMaximizeWindow(true);
                          }}
                          className={cn(
                            "group flex items-center justify-between w-full py-4 px-3 flex-shrink-0 rounded-md border border-gray-200 hover:border-purple-400 bg-white/60 hover:bg-white/80 transition duration-200 ease-in-out cursor-pointer"
                          )}
                        >
                          <div className={cn(
                            "text-gray-500 group-hover:text-gray-700 text-sm font-semibold",
                          )}>
                            <span className="relative flex-shrink-0 block">
                              {option.title}
                            </span>
                          </div>
                          <ArrowRight className="h-4 w-4 text-gray-400 group-hover:text-gray-500 group-hover:text-purple-500" />
                        </button>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )
      }
      
    </div>
    
  )
}
