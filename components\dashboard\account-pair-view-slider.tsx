"use client"

import { useTranslations } from 'next-intl'
import { Fragment, Key } from 'react'
import { Tab, TabGroup, TabList, TabPanel, TabPanels, TransitionChild } from '@headlessui/react'
import { formatDateTime, getDateDifference, getRegionName } from "@/utils/msc"
import { ExclamationTriangleIcon, XMarkIcon } from "@heroicons/react/20/solid"
import AccountStatus from "./account-status"


interface AccountPairViewSliderProps {
  handleAccountPairViewCloseClick: () => void;
  selectedAccountPairItem: any;
}

export default function AccountPairViewSlider({
  handleAccountPairViewCloseClick,
  selectedAccountPairItem,
}: AccountPairViewSliderProps) {
  const t = useTranslations('component')
  const tos = useTranslations('optimizationSets')

  const spContent = (selectedAccountPairItem: any) => {
    return (
      <div className="flex flex-col h-full gap-y-4 items-start justify-start">
        <div className="grow relative w-full rounded-lg bg-white border border-gray-100 overflow-y-scroll">
          {/* List */}
          <div className="sticky inset-x-0 top-0 z-[1] flex items-center gap-x-3 py-3 text-center text-sm text-gray-400 font-bold bg-gray-100/40 backdrop-blur-md">
            <div className="flex-shrink-0 w-[140px] px-4">
              Country Code
            </div>
            <div className="grow px-4">
              Marketplace
            </div>
            <div className="flex-shrink-0 w-[180px] px-4">
              Updated Date
            </div>
          </div>
          <ul className="divide-y divide-gray-100">
            <li
              className="relative flex items-center gap-x-3 py-6 cursor-pointer hover:bg-gray-100/50 text-center text-gray-500 text-sm"
              key={1}
            >
              <div className="flex-shrink-0 w-[140px] px-4 text-xs font-semibold">
                {selectedAccountPairItem.sp_country_code + " (" + selectedAccountPairItem.sp_default_currency_code + ")"}
              </div>
              <div className="grow relative flex flex-col px-4 overflow-hidden text-left">
                <div className="space-y-2 text-xs text-left break-words">
                  <div>
                    <div className="text-gray-400 text-[10px]">
                      marketplace name
                    </div>
                    <div className="text-xs text-gray-500">
                      {selectedAccountPairItem.sp_marketplace_name}
                    </div>
                  </div>
                  <div>
                    <div className="text-gray-400 text-[10px]">
                      marketplace ID
                    </div>
                    <div className="text-xs text-gray-500">
                      {selectedAccountPairItem.sp_marketplace_id}
                    </div>
                  </div>
                </div>
              </div>
              <div className="flex-shrink-0 w-[180px] px-4">
                {formatDateTime(selectedAccountPairItem.updated_datetime, ".")}
              </div>
            </li>
          </ul>
        </div>
      </div>
    )
  }

  const adsContent = (selectedAccountPairItem: any) => {
    return (
      <div className="flex flex-col h-full gap-y-4 items-start justify-start">
        <div className="grow relative w-full rounded-lg bg-white border border-gray-100 overflow-y-scroll">
          {/* List */}
          <div className="sticky inset-x-0 top-0 z-[1] flex items-center gap-x-3 py-3 text-center text-sm text-gray-400 font-bold bg-gray-100/40 backdrop-blur-md">
            <div className="flex-shrink-0 w-[140px] px-4">
              Country Code
            </div>
            <div className="grow px-4">
              Ad Profile
            </div>
            <div className="flex-shrink-0 w-[180px] px-4">
              Updated Date
            </div>
          </div>
          <ul className="divide-y divide-gray-100">
            <li
              className="relative flex items-center gap-x-3 py-6 cursor-pointer hover:bg-gray-100/50 text-center text-gray-500 text-sm"
              key={1}
            >
              <div className="flex-shrink-0 w-[140px] px-4 text-xs font-semibold">
                {selectedAccountPairItem.ad_country_code + " (" + selectedAccountPairItem.ad_currency_code + ")"}
              </div>
              <div className="grow relative flex flex-col px-4 overflow-hidden text-left">
                <div className="space-y-2 text-xs text-left break-words">
                  <div>
                    <div className="text-gray-400 text-[10px]">
                      account ID
                    </div>
                    <div className="text-xs text-gray-500">
                      {selectedAccountPairItem.ad_account_id}
                    </div>
                  </div>
                  <div>
                    <div className="text-gray-400 text-[10px]">
                      account name
                    </div>
                    <div className="text-xs text-gray-500">
                      {selectedAccountPairItem.ad_account_name}
                    </div>
                  </div>
                  <div>
                    <div className="text-gray-400 text-[10px]">
                      marketplace ID
                    </div>
                    <div className="text-xs text-gray-500">
                      {selectedAccountPairItem.ad_marketplace_id}
                    </div>
                  </div>
                </div>
              </div>
              <div className="flex-shrink-0 w-[180px] px-4">
                {formatDateTime(selectedAccountPairItem.updated_datetime, ".")}
              </div>
            </li>
          </ul>
        </div>
      </div>
    )
  }
  
  return (
    <>
      <TransitionChild
        as={Fragment}
        enter="ease-out duration-300"
        enterFrom="opacity-0"
        enterTo="opacity-100"
        leave="ease-in duration-200"
        leaveFrom="opacity-100"
        leaveTo="opacity-0"
      >
        <div className="w-full h-full bg-black/30" onClick={handleAccountPairViewCloseClick} />
      </TransitionChild>
      <TransitionChild
        as={Fragment}
        enter="ease-out duration-300"
        enterFrom="opacity-0 w-[0vw]"
        enterTo="opacity-100 w-[50vw]"
        leave="ease-in duration-200"
        leaveFrom="opacity-100 w-[50vw]"
        leaveTo="opacity-0 w-[0vw]"
      >
        <div className="absolute inset-y-0 right-0 w-[50vw] h-full overflow-hidden shadow-md">
          {/* item content for product item */}
          { selectedAccountPairItem &&
            <div className="flex-shrink-0 flex flex-col w-[50vw] h-full px-6 pt-6 pb-10 bg-white">
              {/* right side slider header */}
              <div className="flex-shrink-0 w-full flex flex-col gap-y-3">
                {selectedAccountPairItem.status === "IN_COLLECT" &&
                  <div className="relative flex-shrink-0 w-full bg-orange-100 rounded-md border border-orange-400 overflow-hidden">
                    <div className="w-full flex items-center justify-between py-2 px-4 bg-orange-400 text-xs font-semibold text-white">
                      <div className="flex items-center gap-x-1">
                        <ExclamationTriangleIcon className="h-4 w-4" />
                        <span>
                          {tos("detailModal.accountPair.message.initialWarning")}
                        </span>
                      </div>
                    </div>
                    <div className="grow relative flex items-center w-full py-2 gap-x-4 overflow-y-scroll text-xs text-gray-500 divide-x divide-orange-400">
                      <div className="pl-4 flex items-center gap-x-1">
                        {
                          tos.rich("detailModal.accountPair.message.dateDifference",{
                            value: getDateDifference(new Date(selectedAccountPairItem.updated_datetime), new Date()),
                            first: (chunks) => <div className="font-semibold text-lg text-orange-500">{chunks}</div>,
                            second: (chunks) => <div className="pt-1">{chunks}</div>
                          })
                        }
                      </div>
                    </div>
                  </div>
                }
                <div className="flex-shrink-0 w-full flex items-center justify-between">
                  <button onClick={handleAccountPairViewCloseClick}>
                    <XMarkIcon className="h-5 w-5 text-gray-500 hover:text-gray-800" />
                  </button>
                  <div className="flex items-center gap-x-4 py-3">
                    <AccountStatus accountItem={selectedAccountPairItem}/>
                    <div></div>
                  </div>
                </div>
              </div>
              <div className="grow relative flex flex-col w-full min-h-0">
                <div className="flex-shrink-0">
                  <div className="relative flex items-center pt-2 pb-6 border-b border-gray-100">
                    <div className="relative w-1/2 h-full flex flex-col items-start justify-between rounded-lg bg-blue-100/40 border border-blue-600 overflow-hidden">
                      <div className="flex-shrink-0 w-full py-2 px-4 bg-blue-600 text-white text-xs font-semibold">
                        {selectedAccountPairItem.sp_account_type === "vendor"
                          ? "Vendor Account"
                          : "Seller Account"
                        }
                      </div>
                      <div className="grow w-full min-h-0 flex flex-col justify-center px-4 py-6">
                        <div className="">
                          <div className="text-xs text-gray-400 font-semibold">Seller Partner ID</div>
                          <div className="text-base text-gray-600 font-semibold">{selectedAccountPairItem.selling_partner_id}</div>
                        </div>
                        {/* <div className="mt-2">
                          <div className="text-xs text-gray-400 font-semibold">Region</div>
                          <div className="text-base text-gray-600 font-semibold">{getRegionName(selectedAccountPairItem.sp_country_code)}</div>
                        </div> */}
                      </div>
                    </div>
                    <div className="flex-shrink-0 relative w-[60px] h-full flex items-start z-[1]">
                      <div className="relative w-1/2 h-1/2 border-b-2 border-blue-400 border-dashed">
                        <div className="absolute bottom-0 left-0 -translate-x-1/2 translate-y-1/2 inline-flex items-center justify-center border border-blue-600 p-1 rounded-full bg-white">
                          <span className="relative flex h-2 w-2">
                            <span className="absolute inline-flex h-full w-full rounded-full opacity-75 animate-ping bg-blue-600"></span>
                            <span className="relative inline-flex rounded-full h-2 w-2 bg-blue-600"></span>
                          </span>
                        </div>
                      </div>
                      <div className="relative w-1/2 h-1/2 border-b-2 border-purple-400 border-dashed">
                        <div className="absolute bottom-0 right-0 translate-x-1/2 translate-y-1/2 inline-flex items-center justify-center border border-purple-600 p-1 rounded-full bg-white">
                          <span className="relative flex h-2 w-2">
                            <span className="absolute inline-flex h-full w-full rounded-full opacity-75 animate-ping bg-purple-600"></span>
                            <span className="relative inline-flex rounded-full h-2 w-2 bg-purple-600"></span>
                          </span>
                        </div>
                      </div>
                    </div>
                    <div className="relative w-1/2 h-full flex flex-col items-start justify-between rounded-lg bg-purple-100/40 border border-purple-600 overflow-hidden">
                      <div className="flex-shrink-0 w-full py-2 px-4 bg-purple-600 text-white text-xs font-semibold">
                        Ads Account
                      </div>
                      <div className="grow w-full min-h-0 flex flex-col justify-center px-4 py-6">
                        <div className="">
                          <div className="text-xs text-gray-400 font-semibold">Profile Email</div>
                          <div className="text-base text-gray-600 font-semibold">{selectedAccountPairItem.ad_account_id}</div>
                        </div>
                        {/* <div className="mt-2">
                          <div className="text-xs text-gray-400 font-semibold">Region</div>
                          <div className="text-base text-gray-600 font-semibold">{getRegionName(selectedAccountPairItem.ad_country_code)}</div>
                        </div> */}
                      </div>
                    </div>
                  </div>
                </div>
                <div className="grow relative flex flex-col min-h-0 mt-6">
                  <TabGroup className="flex flex-col h-full">
                    <div className="flex-shrink-0">
                      <TabList className="inline-flex gap-x-1 bg-gray-100 p-1 rounded-lg">
                        <Tab
                          className="flex items-center gap-x-1 py-2 px-4 text-sm font-semibold text-gray-400 focus:outline-none data-[selected]:text-gray-500 data-[selected]:bg-white data-[selected]:shadow-md data-[selected]:data-[hover]:bg-white data-[hover]:bg-white/40 data-[focus]:outline-1 data-[focus]:outline-white rounded-lg"
                        >
                          {selectedAccountPairItem.sp_account_type === "vendor"
                            ? "Vendor Account"
                            : "Seller Account"
                          }
                        </Tab>
                        <Tab
                          className="flex items-center gap-x-1 py-2 px-4 text-sm font-semibold text-gray-400 focus:outline-none data-[selected]:text-gray-500 data-[selected]:bg-white data-[selected]:shadow-md data-[selected]:data-[hover]:bg-white data-[hover]:bg-white/40 data-[focus]:outline-1 data-[focus]:outline-white rounded-lg"
                        >
                          Ads Account
                        </Tab>
                      </TabList>
                    </div>
                    <TabPanels className="mt-6 grow relative flex flex-col min-h-0">
                      <TabPanel className="flex flex-col w-full h-full">
                        <div className="relative grow w-full min-h-0">
                          {spContent(selectedAccountPairItem)}
                        </div>
                      </TabPanel>
                      <TabPanel className="flex flex-col w-full h-full">
                        <div className="relative grow w-full min-h-0">
                          {adsContent(selectedAccountPairItem)}
                        </div>
                      </TabPanel>
                    </TabPanels>
                  </TabGroup>
                </div>
              </div>
            </div>
          }
        </div>
      </TransitionChild>
    </>
  )
}