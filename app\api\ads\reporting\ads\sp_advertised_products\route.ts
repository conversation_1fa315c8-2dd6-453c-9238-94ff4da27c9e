import { ErrorResponse } from "@/app/api/_models";
import { getServerApiHostUrl } from "@/utils/host";
import { NextRequest, NextResponse } from "next/server";

export type SPAdvertisedProductsResponse = any[];

export async function GET(
  request: NextRequest
): Promise<NextResponse<SPAdvertisedProductsResponse | ErrorResponse>> {
  const accessToken = request.headers.get("Authorization");
  if (!accessToken) {
    return NextResponse.json(
      { message: "Authorization header is missing" },
      { status: 400 }
    );
  }
  const profileId = request.nextUrl.searchParams.get("profile_id")
  if (!profileId) {
    return NextResponse.json(
      { message: "profile_id query is missing" },
      { status: 400 }
    );
  }
  const startDate = request.nextUrl.searchParams.get("start_date")
  if (!startDate) {
    return NextResponse.json(
      { message: "start_date query is missing" },
      { status: 400 }
    );
  }
  const endDate = request.nextUrl.searchParams.get("end_date")
  if (!endDate) {
    return NextResponse.json(
      { message: "end_date query is missing" },
      { status: 400 }
    );
  }
  const advertisedProductsResponse = await fetch(
    `${await getServerApiHostUrl()}/api/ads/reporting/ads/sp_advertised_products?profile_id=${profileId}&start_date=${startDate}&end_date=${endDate}`,
    {
      method: "GET",
      headers: {
        "Authorization": `Bearer ${accessToken}`,
        "Content-Type": "application/json",
      },
      cache: "no-cache",
    }
  ).then((res) => res.json());

  return NextResponse.json(advertisedProductsResponse, { status: 200 });
}
