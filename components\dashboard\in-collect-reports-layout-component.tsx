"use client"

import { api } from "@/utils/api"
import { cn, formatDate, formatUTCDate } from "@/utils/msc"
import { useSession } from "next-auth/react"
import { useSearchParams } from "next/navigation"
import { useLocale, useTranslations } from 'next-intl'
import { forwardRef, Fragment, useEffect, useRef, useState } from "react"
import { CSVLink } from "react-csv"
import DatePicker, { registerLocale } from "react-datepicker"
import { ko } from "date-fns/locale"
import "react-datepicker/dist/react-datepicker.css";
import { Popover, Transition } from '@headlessui/react'
import PinnedColTable, { AdvertisedProduct } from "@/components/dashboard/pinned-col-table"
import { ProfileOption } from "@/components/dashboard/profile-select"
import { MarketplaceOption } from "@/components/dashboard/marketplace-select"
import AttributionWindowSelect, { attributionWindowOptions } from "@/components/dashboard/attribution-window-select"
import { PortfolioListItem } from "./ad-portfolio-layout-component"
import OptimizationSetSelect from "./optimization-set-select"
import ComparisonSelect, { comparisonOptions } from "@/components/dashboard/comparison-select"
import LegendSelect from "@/components/dashboard/legend-select"
import { ArrowDownCircleIcon, CurrencyDollarIcon, ExclamationTriangleIcon, ArrowDownTrayIcon, PresentationChartLineIcon, QuestionMarkCircleIcon, XMarkIcon, CheckBadgeIcon, ClockIcon, ExclamationCircleIcon } from "@heroicons/react/20/solid"
import dynamic from "next/dynamic"
import Link from "next/link"
const PlotGraph = dynamic(() => import('@/components/dashboard/plot-graph'), { ssr: false })
registerLocale('ko', ko)

interface DashboardLayoutProps {
  mopUserData: any;
  selectedProfile: ProfileOption;
  selectedMarketplace: MarketplaceOption;
}

export default function InCollectReportsLayoutComponent({
  mopUserData,
  selectedProfile,
  selectedMarketplace
}: DashboardLayoutProps) {
  const t = useTranslations('DashboardPage')
  const locale = useLocale()
  const { data: session, status } = useSession()
  const searchParams = useSearchParams()
  const fetchCounter = useRef(0)
  const [mainProducts, setMainProducts] = useState<any[]>([])
  const [comparedProducts, setComparedProducts] = useState<any[]>([])
  const [mainProductsDailyData, setMainProductsDailyData] = useState<any[]>([])
  const [comparedProductsDailyData, setComparedProductsDailyData] = useState<any[]>([])
  const [reportByAsin, setReportByAsin] = useState<any[]>([])
  const [reportByDate, setReportByDate] = useState<any[]>([])
  const [comparedReportByDate, setComparedReportByDate] = useState<any[]>([])
  const [totalSalesReturned, setTotalSalesReturned] = useState(0)
  const [totalSales, setTotalSales] = useState(0)
  const [comparedTotalSales, setComparedTotalSales] = useState(0)
  const [totalFee, setTotalFee] = useState(0)
  const [comparedTotalFee, setComparedTotalFee] = useState(0)
  const [adSales, setAdSales] = useState(0)
  const [comparedAdSales, setComparedAdSales] = useState(0)
  const [adCost, setAdCost] = useState(0)
  const [comparedAdCost, setComparedAdCost] = useState(0)
  const [selectedAttributionWindow, setSelectedAttributionWindow] = useState(attributionWindowOptions[1])
  const [portfolioListItems, setPortfolioListItems] = useState<PortfolioListItem[]>([])
  const [selectedPortfolioItems, setSelectedPortfolioItems] = useState<PortfolioListItem[]>([])
  const [selectedComparison, setSelectedComparison] = useState(comparisonOptions[0])
  // legend options
  const legendOptions = [
    // total performance
    {
      id: 1,
      name: t('graph.legendSelect.sales'),
      category: 'total',
      type: 'sales',
      color: '#a855f7',
    },
    {
      id: 2,
      name: t('graph.legendSelect.comparedSales'),
      category: 'total',
      type: 'compared-sales',
      color: 'rgba(168, 85, 247, 0.8)',
    },
    {
      id: 3,
      name: t('graph.legendSelect.pageViews'),
      category: 'total',
      type: 'pageviews',
      color: '#ec4899',
    },
    {
      id: 4,
      name: t('graph.legendSelect.comparedPageViews'),
      category: 'total',
      type: 'compared-pageviews',
      color: 'rgba(236, 72, 153, 0.8)',
    },
    {
      id: 5,
      name: t('graph.legendSelect.sessions'),
      category: 'total',
      type: 'sessions',
      color: '#dc2626',
    },
    {
      id: 6,
      name: t('graph.legendSelect.comparedSessions'),
      category: 'total',
      type: 'compared-sessions',
      color: 'rgba(220, 38, 38, 0.8)',
    },
    {
      id: 7,
      name: t('graph.legendSelect.estimatedMargin'),
      category: 'total',
      type: 'estimated-margin',
      color: '#f59e0b',
    },
    {
      id: 8,
      name: t('graph.legendSelect.comparedEstimatedMargin'),
      category: 'total',
      type: 'compared-estimated-margin',
      color: 'rgba(245, 158, 11, 0.8)',
    },
    // ad performance (sp)
    {
      id: 9,
      name: t('graph.legendSelect.adCost'),
      category: 'sp',
      type: 'ad-cost',
      color: '#1d4ed8',
    },
    {
      id: 10,
      name: t('graph.legendSelect.comparedAdCost'),
      category: 'sp',
      type: 'compared-ad-cost',
      color: 'rgba(29, 78, 216, 0.8)',
    },
    {
      id: 11,
      name: t('graph.legendSelect.adSales'),
      category: 'sp',
      type: 'ad-sales',
      color: '#6366f1',
    },
    {
      id: 12,
      name: t('graph.legendSelect.comparedAdSales'),
      category: 'sp',
      type: 'compared-ad-sales',
      color: 'rgba(99, 102, 241, 0.8)',
    },
    {
      id: 13,
      name: t('graph.legendSelect.adSalesSameSku'),
      category: 'sp',
      type: 'ad-sales-same-sku',
      color: '#66d9e8',
    },
    {
      id: 14,
      name: t('graph.legendSelect.comparedAdSalesSameSku'),
      category: 'sp',
      type: 'compared-ad-sales-same-sku',
      color: 'rgba(102, 217, 232, 0.8)',
    },
    {
      id: 15,
      name: t('graph.legendSelect.clicks'),
      category: 'sp',
      type: 'clicks',
      color: '#f97316',
    },
    {
      id: 16,
      name: t('graph.legendSelect.comparedClicks'),
      category: 'sp',
      type: 'compared-clicks',
      color: 'rgba(249, 115, 22, 0.8)',
    },
    {
      id: 17,
      name: t('graph.legendSelect.impressions'),
      category: 'sp',
      type: 'impressions',
      color: '#c53030',
    },
    {
      id: 18,
      name: t('graph.legendSelect.comparedImpressions'),
      category: 'sp',
      type: 'compared-impressions',
      color: 'rgba(197, 48, 48, 0.8)',
    },
    {
      id: 19,
      name: t('graph.legendSelect.roas'),
      category: 'sp',
      type: 'roas',
      color: '#22c55e',
    },
    {
      id: 20,
      name: t('graph.legendSelect.comparedRoas'),
      category: 'sp',
      type: 'compared-roas',
      color: 'rgba(34, 197, 94, 0.8)',
    },
    // ad performance (sd)
    {
      id: 21,
      name: t('graph.legendSelect.adCost'),
      category: 'sd',
      type: 'sd-cost',
      color: '#d8a71d',
    },
    {
      id: 22,
      name: t('graph.legendSelect.comparedAdCost'),
      category: 'sd',
      type: 'compared-sd-cost',
      color: 'rgba(216, 167, 29, 0.8)',
    },
    {
      id: 23,
      name: t('graph.legendSelect.adSales'),
      category: 'sd',
      type: 'sd-sales',
      color: '#f1ee63',
    },
    {
      id: 24,
      name: t('graph.legendSelect.comparedAdSales'),
      category: 'sd',
      type: 'compared-sd-sales',
      color: 'rgba(241, 238, 99, 0.8)',
    },
    {
      id: 25,
      name: t('graph.legendSelect.sdSalesPromotedClick'),
      category: 'sd',
      type: 'sd-sales-promoted-clicks',
      color: '#e87566',
    },
    {
      id: 26,
      name: t('graph.legendSelect.comparedSdSalesPromotedClick'),
      category: 'sd',
      type: 'compared-sd-sales-promoted-clicks',
      color: 'rgba(232, 117, 102, 0.8)',
    },
    {
      id: 27,
      name: t('graph.legendSelect.clicks'),
      category: 'sd',
      type: 'sd-clicks',
      color: '#169cf9',
    },
    {
      id: 28,
      name: t('graph.legendSelect.comparedClicks'),
      category: 'sd',
      type: 'compared-sd-clicks',
      color: 'rgba(22, 156, 249, 0.8)',
    },
    {
      id: 29,
      name: t('graph.legendSelect.impressions'),
      category: 'sd',
      type: 'sd-impressions',
      color: '#30c5c5',
    },
    {
      id: 30,
      name: t('graph.legendSelect.comparedImpressions'),
      category: 'sd',
      type: 'compared-sd-impressions',
      color: 'rgba(48, 197, 197, 0.8)',
    },
    {
      id: 31,
      name: t('graph.legendSelect.roas'),
      category: 'sd',
      type: 'sd-roas',
      color: '#c52289',
    },
    {
      id: 32,
      name: t('graph.legendSelect.comparedRoas'),
      category: 'sd',
      type: 'compared-sd-roas',
      color: 'rgba(197, 34, 137, 0.8)',
    },
  ]
  const [selectedLegend, setSelectedLegend] = useState([legendOptions[0], legendOptions[2], legendOptions[4], legendOptions[6]])
  const [selectedProduct, setSelectedProduct] = useState<AdvertisedProduct | null>(null)
  const [dateRange, setDateRange] = useState<Date[]>([new Date(new Date().getFullYear(), new Date().getMonth(), 1), new Date()])
  const [startDate, endDate] = dateRange
  // endDate of comparedDateRange is determined by the time span between startDate and endDate
  const [comparedDateRange, setComparedDateRange] = useState<Date[]>([new Date(new Date().getFullYear(), new Date().getMonth() - 1, 1), new Date()])
  const [comparedStartDate, comparedEndDate] = comparedDateRange
  const DateRangeInput = forwardRef(({ value, onClick }: { value: string, onClick: () => void }, ref) => (
    // @ts-ignore
    <button className="w-auto mt-1 rounded-lg bg-white shadow-md overflow-hidden cursor-pointer text-sm text-gray-600 focus:outline-none" onClick={onClick} ref={ref}>
      <div className="min-w-[200px] inline-flex py-2 px-4 bg-white hover:bg-gray-100/20">
        {value}
      </div>
    </button>
  ));
  DateRangeInput.displayName = 'DateRangeInput'
  const [collectionStatus, setCollectionStatus] = useState({
    overall_progress: 0
  })

  const fetchCollectionStatus = async () => {
    const collectionStatusResponse = await api.getCollectionStatus(
      selectedProfile.account_id,
      selectedMarketplace.marketplace_id,
      (session?.user as any).access_token
    )
    collectionStatusResponse && setCollectionStatus(collectionStatusResponse)
  }
  useEffect(() => {
    selectedProfile && selectedMarketplace && fetchCollectionStatus()
  }, [selectedProfile, selectedMarketplace])

  return (
    <div className="relative size-full py-4 sm:py-6 px-8 sm:px-12 overflow-hidden">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl text-gray-800 font-medium">{t("title")}</h1>
        <CSVLink
          data={
            reportByDate.map((report: any) => {
              return {
                // CSV Columns
                "Date": formatUTCDate(report.date),
                "Total Sales": report.ordered_product_sales_amount,
                "Page Views": report.browser_page_views,
                "Sessions": report.sessions,
                "Total Amazon Fee": report.total_fee,
                "Estimated Profit": report.ordered_product_sales_amount - report.total_fee - report.ad_cost - report.sd_cost,
                "Estimated Margin %": report.estimated_margin,
                "Total Ad Spend": report.ad_cost + report.sd_cost,
                "Total AD Sales": report.ad_sales + report.sd_sales,
                "Total AD Sales Same SKU": report.ad_sales_same_sku + report.sd_sales_promoted_clicks,
                "Total Impressions": report.impressions + report.sd_impressions,
                "Total Clicks": report.clicks + report.sd_clicks,
                "Total ROAS": report.ad_cost + report.sd_cost === 0
                  ? 0
                  : ((report.ad_sales + report.sd_sales) / (report.ad_cost + report.sd_cost) * 100).toFixed(2),
                "SP AD Spend": report.ad_cost,
                "SP AD Sales": report.ad_sales,
                "SP AD Sales Same SKU": report.ad_sales_same_sku,
                "SP AD Impressions": report.impressions,
                "SP AD Clicks": report.clicks,
                "SP ROAS": report.roas,
                "SD AD Spend": report.sd_cost,
                "SD AD Sales": report.sd_sales,
                "SD AD Promoted Click Sales": report.sd_sales_promoted_clicks,
                "SD AD Impressions": report.sd_impressions,
                "SD AD Clicks": report.sd_clicks,
                "SD ROAS": report.sd_roas
              }
            })
          }
          filename={selectedProfile.account_name + "_" + selectedMarketplace.country_code + "_data_" + formatDate(new Date()) + ".csv"}
          target="_blank">
          <div
            className={cn(
              "flex items-center gap-x-2 p-3 cursor-pointer rounded-md focus:outline-none text-sm overflow-hidden bg-blue-100 hover:bg-blue-200 text-blue-500 font-semibold",
              (fetchCounter.current > 0 || reportByDate.length <= 0) && "cursor-not-allowed"
            )}
          >
            {fetchCounter.current > 0
              ? <svg className="animate-spin h-4 w-4 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              : <ArrowDownTrayIcon
                className="flex-shrink-0 h-4 w-4"
                aria-hidden="true"
              />
            }
            <div>Export</div>
          </div>
        </CSVLink>
      </div>
      {/* filters */}
      <div className="mt-3 flex items-center gap-x-3">
        <div>
          <div className="text-xs text-gray-400 font-semibold">{t("filter.dateRange.label")} *</div>
          <DatePicker
            selectsRange={true}
            minDate={new Date('2023-01-01')}
            maxDate={new Date()}
            startDate={startDate}
            endDate={endDate}
            onChange={(update) => {
              setDateRange(update as Date[])
            }}
            dateFormat="yyyy.MM.dd"
            calendarClassName="dashboard-date-range"
            // @ts-ignore
            customInput={<DateRangeInput />}
            locale={locale}
          />
        </div>
        <div>
          <div className="text-xs text-gray-400 font-semibold">{t("filter.attributionWindow.label")}</div>
          <AttributionWindowSelect
            className="mt-1"
            selected={selectedAttributionWindow}
            setSelected={setSelectedAttributionWindow}
          />
        </div>
        {portfolioListItems.length > 0 &&
          <div>
            <div className="flex items-center gap-x-2 text-xs text-gray-400 font-semibold">
              {t("filter.optimizationSet.label")}
              {selectedPortfolioItems.length > 0 &&
                <div className="flex items-center justify-center text-xs font-semibold text-blue-400">
                  {selectedPortfolioItems.length}
                </div>
              }
            </div>
            <OptimizationSetSelect
              className="mt-1"
              selected={selectedPortfolioItems}
              setSelected={setSelectedPortfolioItems}
              options={portfolioListItems}
              multiple={true}
            />
          </div>
        }
        {selectedProduct &&
          <div>
            <div className="text-xs text-gray-400 font-semibold">{t("filter.productFilter.label")}</div>
            <div className="mt-1 relative w-full max-w-[400px] flex items-center gap-x-3 px-3 py-2 cursor-pointer bg-white text-left text-gray-500 text-xs font-normal rounded-md shadow-md">
              <div className="grow relative flex items-center gap-x-4 overflow-hidden">
                {selectedProduct.image
                  ? (<img src={selectedProduct.image} alt="Item Image" className="flex-shrink-0 w-5 h-5 rounded-full" />)
                  : (<div className="flex-shrink-0 flex items-center justify-center w-5 h-5 bg-gray-100 rounded-full">
                    <ExclamationTriangleIcon className="h-2 w-2 text-gray-300" />
                  </div>)
                }
                <div className="flex-1 flex flex-col gap-y-0.5 overflow-hidden">
                  <div className="flex items-center gap-x-2">
                    <div className="flex-shrink-0 text-xs text-red-400 font-semibold">${selectedProduct.lowest_price}</div>
                    <div className="text-xs text-gray-500 text-left font-semibold truncate">
                      {selectedProduct.item_name
                        ? selectedProduct.item_name
                        : "No Title"
                      }
                    </div>
                  </div>
                </div>
              </div>
              <button onClick={() => setSelectedProduct(null)}>
                <XMarkIcon className="h-4 w-4 text-gray-400 hover:text-gray-500" />
              </button>
            </div>
          </div>
        }
        <div>
          <div className="text-xs text-gray-400 font-semibold">{t("filter.compareWith.label")}</div>
          <ComparisonSelect
            className="mt-1"
            selected={selectedComparison}
            setSelected={setSelectedComparison}
          />
        </div>
        {selectedComparison.type === 'custom' &&
          <div>
            <div className="text-xs text-gray-400 font-semibold">{t("filter.customPeriod.label")}</div>
            <DatePicker
              selectsRange={true}
              startDate={comparedStartDate}
              endDate={comparedEndDate}
              onChange={(update) => {
                const newStartDate = update[0] as Date
                const timeSpan = endDate.getTime() - startDate.getTime();
                const newEndDate = new Date(newStartDate.getTime() + timeSpan);
                setComparedDateRange([newStartDate, newEndDate])
              }}
              dateFormat="yyyy.MM.dd"
              calendarClassName="dashboard-date-range"
              // @ts-ignore
              customInput={<DateRangeInput />}
              locale={locale}
            />
          </div>
        }
      </div>
      {/* graph */}
      <div className="mt-4 relative h-[380px] flex items-center gap-x-3">
        <div className="flex-shrink-0 grid grid-rows-2 gap-y-3 h-full">
          <div className="flex flex-col justify-center gap-y-3 py-3 px-3 bg-white shadow-md rounded-lg">
            {/* to be developed */}
            <div className="relative w-full flex flex-col items-start gap-x-3 pb-1 px-3">
              <div className="flex-shrink-0 flex items-center gap-x-1 text-lg text-purple-900/60 font-semibold text-center">
                <CurrencyDollarIcon className="w-5 h-5" />
                {t("keyMetrics.totalMetrics.estMargin")}
                <Popover className="relative flex items-center justify-center">
                  {({ open }) => (
                    <>
                      <Popover.Button
                        className={cn(
                          "inline-flex items-center justify-center rounded-full text-gray-300 hover:text-gray-400 focus:outline-none",
                        )}
                      >
                        <QuestionMarkCircleIcon className="w-5 h-5" />
                      </Popover.Button>
                      <Transition
                        as={Fragment}
                        enter="transition ease-out duration-200"
                        enterFrom="opacity-0 translate-y-1"
                        enterTo="opacity-100 translate-y-0"
                        leave="transition ease-in duration-150"
                        leaveFrom="opacity-100 translate-y-0"
                        leaveTo="opacity-0 translate-y-1"
                      >
                        <Popover.Panel className="absolute top-full left-1/2 z-10 mt-2 -translate-x-1/2 transform">
                          <div className="w-[200px] p-2 rounded-lg shadow-lg bg-gray-900/90 text-xs text-white font-normal">
                            {t("keyMetrics.metricsTooltip.estimatedMargin")}
                          </div>
                        </Popover.Panel>
                      </Transition>
                    </>
                  )}
                </Popover>
              </div>
              {fetchCounter.current > 0
                ? <div className="animate-pulse pt-1">
                  <div className="w-[120px] h-8 rounded-md bg-gray-100" />
                </div>
                : <div className="mt-1 w-full flex items-center justify-between text-2xl text-gray-600 font-semibold text-center">
                  {totalSales === 0 ? '-' : (((totalSales - totalFee - adCost) / totalSales) * 100).toLocaleString(undefined, { maximumFractionDigits: 2 })}%
                  {selectedComparison.type !== 'none' &&
                    <div
                      className={cn(
                        "mt-1 flex items-center justify-center gap-x-1",
                        totalSales === 0 || comparedTotalSales === 0
                          ? "text-gray-400"
                          : ((totalSales - totalFee - adCost) / totalSales) > ((comparedTotalSales - comparedTotalFee - comparedAdCost) / comparedTotalSales)
                            ? "text-red-400"
                            : "text-blue-400"
                      )}
                    >
                      <ArrowDownCircleIcon
                        className={cn(
                          "h-4 w-4 inline-block",
                          ((totalSales - totalFee - adCost) / totalSales) < ((comparedTotalSales - comparedTotalFee - comparedAdCost) / comparedTotalSales)
                            ? ""
                            : "transform rotate-180"
                        )}
                      />
                      <span className="text-xs">
                        {totalSales === 0 || comparedTotalSales === 0
                          ? '0%'
                          : `${((((totalSales - totalFee - adCost) / totalSales) - ((comparedTotalSales - comparedTotalFee - comparedAdCost) / comparedTotalSales)) / ((comparedTotalSales - comparedTotalFee - comparedAdCost) / comparedTotalSales) * 100).toLocaleString(undefined, { maximumFractionDigits: 2 })}%`
                        }
                      </span>
                    </div>
                  }
                </div>
              }
            </div>
            <div className="relative w-full grid grid-cols-2 gap-x-3">
              <div className="flex-shrink-0 w-full relative flex flex-col items-start justify-start px-3 rounded-lg bg-white/50">
                <div className="flex-shrink-0 flex items-center justify-center text-sm text-gray-400 font-semibold text-center">
                  {t('keyMetrics.totalMetrics.totalSales')}
                  <Popover className="relative flex items-center justify-center pl-1">
                    {({ open }) => (
                      <>
                        <Popover.Button
                          className={cn(
                            "inline-flex items-center justify-center rounded-full text-gray-300 hover:text-gray-400 focus:outline-none",
                          )}
                        >
                          <QuestionMarkCircleIcon className="w-5 h-5" />
                        </Popover.Button>
                        <Transition
                          as={Fragment}
                          enter="transition ease-out duration-200"
                          enterFrom="opacity-0 translate-y-1"
                          enterTo="opacity-100 translate-y-0"
                          leave="transition ease-in duration-150"
                          leaveFrom="opacity-100 translate-y-0"
                          leaveTo="opacity-0 translate-y-1"
                        >
                          <Popover.Panel className="absolute top-full left-1/2 z-10 mt-2 -translate-x-1/2 transform">
                            {t.rich("keyMetrics.metricsTooltip.totalSales", {
                              value: totalSalesReturned.toLocaleString(undefined, { maximumFractionDigits: 0 }),
                              first: (chunks) => <div className="w-[200px] p-2 rounded-lg shadow-lg bg-gray-900/90 text-xs text-white font-normal">{chunks}</div>
                            })}
                          </Popover.Panel>
                        </Transition>
                      </>
                    )}
                  </Popover>
                </div>
                {fetchCounter.current > 0
                  ? <div className="animate-pulse pt-1">
                    <div className="w-[120px] h-6 rounded-md bg-gray-100" />
                  </div>
                  : <div className="text-lg text-gray-600 font-bold text-center">
                    ${totalSales.toLocaleString(undefined, { maximumFractionDigits: 0 })}
                    {selectedComparison.type !== 'none' &&
                      <div
                        className={cn(
                          "mt-1 flex items-center justify-center gap-x-1",
                          totalSales === comparedTotalSales
                            ? "text-gray-400"
                            : totalSales > comparedTotalSales
                              ? "text-red-400"
                              : "text-blue-400"
                        )}
                      >
                        <ArrowDownCircleIcon
                          className={cn(
                            "h-4 w-4 inline-block",
                            totalSales < comparedTotalSales
                              ? ""
                              : "transform rotate-180"
                          )}
                        />
                        <span className="text-xs">
                          {((totalSales - comparedTotalSales) / comparedTotalSales * 100).toLocaleString(undefined, { maximumFractionDigits: 2 })}%
                        </span>
                      </div>
                    }
                  </div>
                }
              </div>
              <div className="flex-shrink-0 w-full relative flex flex-col items-start justify-start px-3 rounded-lg bg-white/50">
                <div className="flex-shrink-0 flex items-center justify-center text-sm text-gray-400 font-semibold text-center">
                  {t("keyMetrics.totalMetrics.estFee")}
                  <Popover className="relative flex items-center justify-center pl-1">
                    {({ open }) => (
                      <>
                        <Popover.Button
                          className={cn(
                            "inline-flex items-center justify-center rounded-full text-gray-300 hover:text-gray-400 focus:outline-none",
                          )}
                        >
                          <QuestionMarkCircleIcon className="w-5 h-5" />
                        </Popover.Button>
                        <Transition
                          as={Fragment}
                          enter="transition ease-out duration-200"
                          enterFrom="opacity-0 translate-y-1"
                          enterTo="opacity-100 translate-y-0"
                          leave="transition ease-in duration-150"
                          leaveFrom="opacity-100 translate-y-0"
                          leaveTo="opacity-0 translate-y-1"
                        >
                          <Popover.Panel className="absolute top-full left-1/2 z-10 mt-2 -translate-x-1/2 transform">
                            <div className="w-[200px] p-2 rounded-lg shadow-lg bg-gray-900/90 text-xs text-white font-normal">
                              {t("keyMetrics.metricsTooltip.estFee")}
                            </div>
                          </Popover.Panel>
                        </Transition>
                      </>
                    )}
                  </Popover>
                </div>
                {fetchCounter.current > 0
                  ? <div className="animate-pulse pt-1">
                    <div className="w-[120px] h-6 rounded-md bg-gray-100" />
                  </div>
                  : <div className="text-lg text-gray-600 font-bold text-center">
                    ${totalFee.toLocaleString(undefined, { maximumFractionDigits: 0 })}
                    {selectedComparison.type !== 'none' &&
                      <div
                        className={cn(
                          "mt-1 flex items-center justify-center gap-x-1",
                          totalFee === comparedTotalFee
                            ? "text-gray-400"
                            : totalFee > comparedTotalFee
                              ? "text-red-400"
                              : "text-blue-400"
                        )}
                      >
                        <ArrowDownCircleIcon
                          className={cn(
                            "h-4 w-4 inline-block",
                            totalFee < comparedTotalFee
                              ? ""
                              : "transform rotate-180"
                          )}
                        />
                        <span className="text-xs">
                          {((totalFee - comparedTotalFee) / comparedTotalFee * 100).toLocaleString(undefined, { maximumFractionDigits: 2 })}%
                        </span>
                      </div>
                    }
                  </div>
                }
              </div>
            </div>
          </div>
          <div className="flex flex-col justify-center gap-y-3 py-3 px-3 bg-white shadow-md rounded-lg">
            {/* to be developed */}
            <div className="relative w-full flex flex-col items-start gap-x-3 pb-1 px-3">
              <div className="flex-shrink-0 flex items-center gap-x-1 text-lg text-blue-900/60 font-semibold text-center">
                <PresentationChartLineIcon className="w-5 h-5" />
                {t("keyMetrics.adMetrics.roas")}
                <Popover className="relative flex items-center justify-center">
                  {({ open }) => (
                    <>
                      <Popover.Button
                        className={cn(
                          "inline-flex items-center justify-center rounded-full text-gray-300 hover:text-gray-400 focus:outline-none",
                        )}
                      >
                        <QuestionMarkCircleIcon className="w-5 h-5" />
                      </Popover.Button>
                      <Transition
                        as={Fragment}
                        enter="transition ease-out duration-200"
                        enterFrom="opacity-0 translate-y-1"
                        enterTo="opacity-100 translate-y-0"
                        leave="transition ease-in duration-150"
                        leaveFrom="opacity-100 translate-y-0"
                        leaveTo="opacity-0 translate-y-1"
                      >
                        <Popover.Panel className="absolute top-full left-1/2 z-10 mt-2 -translate-x-1/2 transform">
                          <div className="w-[200px] p-2 rounded-lg shadow-lg bg-gray-900/90 text-xs text-white font-normal">
                            {t("keyMetrics.metricsTooltip.roas")}
                          </div>
                        </Popover.Panel>
                      </Transition>
                    </>
                  )}
                </Popover>
              </div>
              {fetchCounter.current > 0
                ? <div className="animate-pulse pt-1">
                  <div className="w-[120px] h-8 rounded-md bg-gray-100" />
                </div>
                : <div className="mt-1 w-full flex items-center justify-between text-2xl text-gray-600 font-semibold text-center">
                  {adCost === 0 ? '-' : ((adSales / adCost) * 100).toLocaleString(undefined, { maximumFractionDigits: 2 })}%
                  {selectedComparison.type !== 'none' &&
                    <div
                      className={cn(
                        "mt-1 flex items-center justify-center gap-x-1",
                        adCost === 0 || comparedAdCost === 0
                          ? "text-gray-400"
                          : (adSales / adCost) > (comparedAdSales / comparedAdCost)
                            ? "text-red-400"
                            : "text-blue-400"
                      )}
                    >
                      <ArrowDownCircleIcon
                        className={cn(
                          "h-4 w-4 inline-block",
                          (adSales / adCost) < (comparedAdSales / comparedAdCost)
                            ? ""
                            : "transform rotate-180"
                        )}
                      />
                      <span className="text-xs">
                        {adCost === 0 || comparedAdCost === 0
                          ? '0%'
                          : `${(((adSales / adCost) - (comparedAdSales / comparedAdCost)) / (comparedAdSales / comparedAdCost) * 100).toLocaleString(undefined, { maximumFractionDigits: 2 })}%`
                        }
                      </span>
                    </div>
                  }
                </div>
              }
            </div>
            <div className="relative w-full grid grid-cols-2 gap-x-3">
              <div className="flex-shrink-0 w-full relative flex flex-col items-start justify-start px-3 rounded-lg bg-white/50">
                <div className="text-sm text-gray-400 font-semibold text-center">
                  {t("keyMetrics.adMetrics.adSales")}
                </div>
                {fetchCounter.current > 0
                  ? <div className="animate-pulse pt-1">
                    <div className="w-[120px] h-6 rounded-md bg-gray-100" />
                  </div>
                  : <div className="text-lg text-gray-600 font-bold text-center">
                    ${adSales.toLocaleString(undefined, { maximumFractionDigits: 0 })}
                    {selectedComparison.type !== 'none' &&
                      <div
                        className={cn(
                          "mt-1 flex items-center justify-center gap-x-1",
                          adSales === comparedAdSales
                            ? "text-gray-400"
                            : adSales > comparedAdSales
                              ? "text-red-400"
                              : "text-blue-400"
                        )}
                      >
                        <ArrowDownCircleIcon
                          className={cn(
                            "h-4 w-4 inline-block",
                            adSales < comparedAdSales
                              ? ""
                              : "transform rotate-180"
                          )}
                        />
                        <span className="text-xs">
                          {((adSales - comparedAdSales) / comparedAdSales * 100).toLocaleString(undefined, { maximumFractionDigits: 2 })}%
                        </span>
                      </div>
                    }
                  </div>
                }
              </div>
              <div className="flex-shrink-0 w-full relative flex flex-col items-start justify-start px-3 rounded-lg bg-white/50">
                <div className="flex-shrink-0 flex items-center justify-center text-sm text-gray-400 font-semibold text-center">
                  {t("keyMetrics.adMetrics.adSpend")}
                </div>
                {fetchCounter.current > 0
                  ? <div className="animate-pulse pt-1">
                    <div className="w-[120px] h-6 rounded-md bg-gray-100" />
                  </div>
                  : <div className="text-lg text-gray-600 font-bold text-center">
                    ${adCost.toLocaleString(undefined, { maximumFractionDigits: 0 })}
                    {selectedComparison.type !== 'none' &&
                      <div
                        className={cn(
                          "mt-1 flex items-center justify-center gap-x-1",
                          adCost === comparedAdCost
                            ? "text-gray-400"
                            : adCost > comparedAdCost
                              ? "text-red-400"
                              : "text-blue-400"
                        )}
                      >
                        <ArrowDownCircleIcon
                          className={cn(
                            "h-4 w-4 inline-block",
                            adCost < comparedAdCost
                              ? ""
                              : "transform rotate-180"
                          )}
                        />
                        <span className="text-xs">
                          {((adCost - comparedAdCost) / comparedAdCost * 100).toLocaleString(undefined, { maximumFractionDigits: 2 })}%
                        </span>
                      </div>
                    }
                  </div>
                }
              </div>
            </div>
          </div>
        </div>
        {fetchCounter.current > 0
          ? <div className="grow flex flex-col h-full p-3 rounded-lg bg-white shadow-md overflow-hidden">
            <div className="animate-pulse relative flex flex-col w-full h-full">
              <div className="w-[260px] h-[38px] rounded-md bg-gray-100">
              </div>
              <div className="mt-4 grow w-full rounded-md bg-gray-100">
              </div>
            </div>
          </div>
          : <div className="grow flex flex-col h-full p-3 rounded-lg bg-white shadow-md overflow-hidden">
            <LegendSelect
              className="mb-1"
              compared={selectedComparison.type !== 'none'}
              legendOptions={legendOptions}
              selected={selectedLegend}
              setSelected={setSelectedLegend}
            />
            <PlotGraph selectedLegend={selectedLegend} data={reportByDate} comparedData={comparedReportByDate} />
          </div>
        }
      </div>
      {/* table */}
      <div className="w-full mt-3">
        <div
          className={cn(
            'rounded-lg bg-white shadow-md overflow-hidden',
          )}
        >
          {fetchCounter.current > 0
            ? <div className="animate-pulse w-full p-6">
              <div className="flex-1 space-y-6 py-1">
                <div className="h-[38px] bg-gray-100 rounded-md"></div>
                <div className="space-y-3">
                  <div className="grid grid-cols-3 gap-4">
                    <div className="h-[38px] bg-gray-100 rounded-md col-span-1"></div>
                    <div className="h-[38px] bg-gray-100 rounded-md col-span-2"></div>
                    <div className="h-[38px] bg-gray-100 rounded-md col-span-1"></div>
                    <div className="h-[38px] bg-gray-100 rounded-md col-span-2"></div>
                    <div className="h-[38px] bg-gray-100 rounded-md col-span-1"></div>
                    <div className="h-[38px] bg-gray-100 rounded-md col-span-2"></div>
                    <div className="h-[38px] bg-gray-100 rounded-md col-span-1"></div>
                    <div className="h-[38px] bg-gray-100 rounded-md col-span-2"></div>
                  </div>
                </div>
              </div>
            </div>
            : <div className="">
              <PinnedColTable
                data={reportByAsin}
                selectedAttributionWindow={selectedAttributionWindow}
                selectedProduct={selectedProduct}
                setSelectedProduct={setSelectedProduct}
                selectedMarketplace={selectedMarketplace} />
            </div>
          }
        </div>
      </div>
      <div className="absolute inset-0 size-full px-4 flex items-center justify-center bg-gray-100/60 backdrop-blur-sm z-[5]">
        <div className="w-full max-w-lg flex flex-col items-center">
          <div className="text-center text-gray-800 font-semibold text-lg">
            We are collecting the essential data to initialize Optapex.
            <br />
            Please wait 1 to 2 days after subscribing to the service.
            <div className="mt-8 mb-4 flex items-center justify-center gap-x-4">
              <div className="relative w-full h-3 bg-gray-300 rounded-full overflow-hidden">
                <div
                  className="absolute top-0 left-0 h-full bg-gradient-to-r from-purple-500 to-blue-500 rounded-full animate-pulse"
                  style={{
                    width: `${collectionStatus?.overall_progress * 100}%`
                  }}></div>
              </div>
              <div className="text-sm text-gray-600 text-right">
                {(collectionStatus?.overall_progress * 100).toFixed(2)}%
              </div>
            </div>
            <ul className="w-full text-left text-gray-700 divide-y divide-gray-200">
              {Object.entries(collectionStatus)
                .filter(([key]) => key !== "overall_status" && key !== "overall_progress")
                .map(([key, value]: [string, any]) => (
                <li key={key} className="py-4 w-full flex items-center justify-between">
                  <div className="">
                    <div className="text-sm font-semibold capitalize">{key.replace(/_/g, ' ')}</div>
                    <div className="mt-1 text-xs text-gray-500 font-normal">
                      {value.message}
                    </div>
                  </div>
                  {value.status === "COMPLETED"
                    ? <div className="flex items-center gap-x-1 text-xs text-gray-600">
                        <CheckBadgeIcon className="h-4 w-4 text-green-500" />
                        COMPLETED
                      </div>
                    : value.status === "IN_PROGRESS"
                      ? <div className="flex items-center gap-x-1 text-xs text-gray-600">
                          <ClockIcon className="h-4 w-4 text-blue-500" />
                          IN PROGRESS
                        </div>
                      : value.status === "PENDING"
                        ? <div className="flex items-center gap-x-1 text-xs text-gray-600">
                            <ExclamationCircleIcon className="h-4 w-4 text-gray-400" />
                            PENDING
                          </div>
                        : <div className="flex items-center gap-x-1 text-xs text-gray-600">
                            <ExclamationCircleIcon className="h-4 w-4 text-gray-400" />
                            {value.status}
                          </div>
                  }
                </li>
              ))}
            </ul>
          </div>
        </div>
      </div>
    </div>
  )
}
