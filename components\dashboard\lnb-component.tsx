"use client"

import { cn } from "@/utils/msc"
import { useSession } from "next-auth/react"
import { api } from "@/utils/api"
import { useEffect, useState } from "react"
import { useSearchParams } from "next/navigation"
import Link from "next/link"
import Image from "next/image"
import { BoltIcon, BellIcon, Cog8ToothIcon, ChartPieIcon, ChevronLeftIcon, ChevronUpDownIcon, GlobeAmericasIcon, HomeIcon, UsersIcon, CreditCardIcon, UserPlusIcon, HashtagIcon, ChartBarIcon, FolderArrowDownIcon, ClockIcon, ShieldCheckIcon } from "@heroicons/react/20/solid"
import DocsButton from "../ui/docs-button"
import { ProfileOption } from "@/components/dashboard/profile-select"
import { MarketplaceOption } from "@/components/dashboard/marketplace-select"
import { useTranslations } from "next-intl"

interface LNBProps {
  mopUserData: any;
  selectedProfile: ProfileOption | null;
  selectedMarketplace: MarketplaceOption | null;
  accountButton: JSX.Element
}

export default function LNBComponent({
  mopUserData,
  selectedProfile,
  selectedMarketplace,
  accountButton
}: LNBProps) {
  const { data: session, status } = useSession()
  const [isLNBExpanded, setIsLNBExpanded] = useState(true)
  const searchParams = useSearchParams()
  const tab = searchParams.get('tab')
  const t = useTranslations('component')

  const [selectedProfilePair, setSelectedProfilePair] = useState<any>(null)
  const fetchProfilePairs = async (selectedProfile: ProfileOption, selectedMarketplace: MarketplaceOption) => {
    setSelectedProfilePair(null)
    const availableProfilesResponse = await api.getAvailableProfiles((session?.user as any).access_token)
    availableProfilesResponse?.length && availableProfilesResponse?.forEach((adAccount: any) => {
      adAccount?.profiles?.forEach((profile: any) => {
        if (profile?.account_id === selectedProfile.account_id
          && profile?.marketplace_id === selectedMarketplace.marketplace_id
          && profile?.customer_id
        ) {
          setSelectedProfilePair(profile)
          return
        }
      })
    })
  }
  useEffect(() => {
    if (session?.user && selectedProfile && selectedMarketplace) {
      fetchProfilePairs(selectedProfile, selectedMarketplace)
    }    
  }, [session, selectedProfile, selectedMarketplace])

  return (
    <section
      className={cn(
        "relative flex-shrink-0 w-auto h-full bg-blue-800"
      )}
    >
      {/* <button
        className="group absolute top-[124px] -right-4 hidden sm:flex items-center justify-center w-8 h-8 rounded-full bg-white border border-gray-200 shadow-md z-[10] focus:outline-none"
        onClick={() => setIsLNBExpanded((isLNBExpanded) => !isLNBExpanded)}
      >
        <ChevronLeftIcon
          className={cn(
            "h-5 w-5 text-gray-400 group-hover:text-gray-500 transition-transform ease-in-out duration-150",
            isLNBExpanded ? "" : "rotate-180"
          )}
        />
      </button> */}
      <div
        className={cn(
          "px-3 h-full flex flex-col justify-between transition-width ease-in-out delay-150",
          isLNBExpanded ? "w-[190px]" : "w-[60px]"
        )}
      >
        <div className="w-full">
          <div className="flex items-center justify-left w-full h-16 pl-3.5">
            <Link href="/">
              <Image
                src="/logo/optapex-logo-white.svg"
                alt="optapex Logo"
                className="w-auto h-5"
                style={{ filter: "drop-shadow(rgba(255, 255, 255, 0.8) 0px 0px 4px)" }}
                width={120}
                height={32}
              />
            </Link>
          </div>
          <div className="">
            {/* Home */}
            {/* <Link
              href={{
                pathname: '/dashboard',
                search: '?tab=home'
              }}
              className={cn(
                "group relative flex items-center gap-x-2 px-2 cursor-pointer rounded-lg h-9 py-1.5 focus:outline-none text-sm overflow-hidden border-2 border-transparent",
                "transition-width ease-in-out delay-150",
                isLNBExpanded
                  ? tab === 'home' ? "w-[165px] bg-blue-700" : "w-[165px]"
                  : tab === 'home' ? "w-[36px] bg-blue-700 border-white/50" : "w-[36px] bg-blue-700 hover:bg-blue-600"
              )}
            >
              <HomeIcon
                className={cn(
                  "flex-shrink-0 h-5 w-5 text-white/50 group-hover:text-white/75",
                  tab === 'home' ? "text-white" : ""
                )}
                aria-hidden="true"
              />
              { isLNBExpanded
                ? <span
                    className={cn(
                      "flex-shrink-0 block text-white/50 group-hover:text-white/75",
                      tab === 'home' ? "text-white" : ""
                    )}
                  >
                    {t("lnb.menu.home")}
                  </span>
                : ""
              }
            </Link> */}
            {/* Reports */}
            <Link
              href={{
                pathname: '/dashboard',
                search: '?tab=reports'
              }}
              className={cn(
                "group relative flex items-center gap-x-2 px-2 cursor-pointer rounded-lg h-9 py-1.5 focus:outline-none text-sm overflow-hidden border-2 border-transparent",
                "transition-width ease-in-out delay-150",
                isLNBExpanded
                  ? tab === 'reports' ? "w-[165px] bg-blue-700" : "w-[165px]"
                  : tab === 'reports' ? "w-[36px] bg-blue-700 border-white/50" : "w-[36px] bg-blue-700 hover:bg-blue-600"
              )}
            >
              <ChartBarIcon
                className={cn(
                  "flex-shrink-0 h-5 w-5 text-white/50 group-hover:text-white/75",
                  tab === 'reports' ? "text-white" : ""
                )}
                aria-hidden="true"
              />
              { isLNBExpanded
                ? <span
                    className={cn(
                      "flex-shrink-0 block text-white/50 group-hover:text-white/75",
                      tab === 'reports' ? "text-white" : ""
                    )}
                  >
                    {t("lnb.menu.reports")}
                  </span>
                : ""
              }
            </Link>
            {/* Ad Settings */}
            <Link
              href={{
                pathname: '/dashboard',
                search: '?tab=ad-portfolio'
              }}
              className={cn(
                "mt-4 group relative flex items-center gap-x-2 px-2 cursor-pointer rounded-lg h-9 py-1.5 focus:outline-none text-sm overflow-hidden border-2 border-transparent",
                "transition-width ease-in-out delay-150",
                isLNBExpanded
                  ? tab === 'ad-portfolio' ? "w-[165px] bg-blue-700" : "w-[165px]"
                  : tab === 'ad-portfolio' ? "w-[36px] bg-blue-700 border-white/50" : "w-[36px] bg-blue-700 hover:bg-blue-600"
              )}
            >
              <BoltIcon
                className={cn(
                  "flex-shrink-0 h-5 w-5 text-white/50 group-hover:text-white/75",
                  tab === 'ad-portfolio' ? "text-white" : ""
                )}
                aria-hidden="true"
              />
              { isLNBExpanded
                ? <span
                    className={cn(
                      "flex-shrink-0 block text-white/50 group-hover:text-white/75",
                      tab === 'ad-portfolio' ? "text-white" : ""
                    )}
                  >
                    {t("lnb.menu.optimizationSets")}
                  </span>
                : ""
              }
            </Link>
            {/* Competition */}
            <Link
              href={{
                pathname: '/dashboard',
                search: '?tab=competition'
              }}
              className={cn(
                "mt-4 group relative flex items-center gap-x-2 px-2 cursor-pointer rounded-lg h-9 py-1.5 focus:outline-none text-sm overflow-hidden border-2 border-transparent",
                "transition-width ease-in-out delay-150",
                isLNBExpanded
                  ? tab === 'competition' ? "w-[165px] bg-blue-700" : "w-[165px]"
                  : tab === 'competition' ? "w-[36px] bg-blue-700 border-white/50" : "w-[36px] bg-blue-700 hover:bg-blue-600"
              )}
            >
              <ShieldCheckIcon
                className={cn(
                  "flex-shrink-0 h-5 w-5 text-white/50 group-hover:text-white/75",
                  tab === 'competition' ? "text-white" : ""
                )}
                aria-hidden="true"
              />
              { isLNBExpanded
                ? <span
                    className={cn(
                      "flex-shrink-0 block text-white/50 group-hover:text-white/75",
                      tab === 'competition' ? "text-white" : ""
                    )}
                  >
                    {t("lnb.menu.competition")}
                  </span>
                : ""
              }
            </Link>
            {/* Realtime */}
            <Link
              href={{
                pathname: '/dashboard',
                search: '?tab=realtime'
              }}
              className={cn(
                "mt-4 group relative flex items-center gap-x-2 px-2 cursor-pointer rounded-lg h-9 py-1.5 focus:outline-none text-sm overflow-hidden border-2 border-transparent",
                "transition-width ease-in-out delay-150",
                isLNBExpanded
                  ? tab === 'realtime' ? "w-[165px] bg-blue-700" : "w-[165px]"
                  : tab === 'realtime' ? "w-[36px] bg-blue-700 border-white/50" : "w-[36px] bg-blue-700 hover:bg-blue-600"
              )}
            >
              <ClockIcon
                className={cn(
                  "flex-shrink-0 h-5 w-5 text-white/50 group-hover:text-white/75",
                  tab === 'realtime' ? "text-white" : ""
                )}
                aria-hidden="true"
              />
              { isLNBExpanded
                ? <span
                    className={cn(
                      "flex-shrink-0 block text-white/50 group-hover:text-white/75",
                      tab === 'realtime' ? "text-white" : ""
                    )}
                  >
                    {t("lnb.menu.realtime")}
                  </span>
                : ""
              }
            </Link>
            {/* Report Download */}            
            <Link
              href={{
                pathname: '/dashboard',
                search: '?tab=downloads'
              }}
              className={cn(
                "mt-4 group relative flex items-center gap-x-2 px-2 cursor-pointer rounded-lg h-9 py-1.5 focus:outline-none text-sm overflow-hidden border-2 border-transparent",
                "transition-width ease-in-out delay-150",
                isLNBExpanded
                  ? tab === 'downloads' ? "w-[165px] bg-blue-700" : "w-[165px]"
                  : tab === 'downloads' ? "w-[36px] bg-blue-700 border-white/50" : "w-[36px] bg-blue-700 hover:bg-blue-600"
              )}
            >
              <FolderArrowDownIcon
                className={cn(
                  "flex-shrink-0 h-5 w-5 text-white/50 group-hover:text-white/75",
                  tab === 'downloads' ? "text-white" : ""
                )}
                aria-hidden="true"
              />
              { isLNBExpanded
                ? <span
                    className={cn(
                      "flex-shrink-0 block text-white/50 group-hover:text-white/75",
                      tab === 'downloads' ? "text-white" : ""
                    )}
                  >
                    {t("lnb.menu.downloads")}
                  </span>
                : ""
              }
            </Link>
            {/* Notifications */}
            {/* <Link
              href={{
                pathname: '/dashboard',
                search: '?tab=notifications'
              }}
              className={cn(
                "mt-4 group relative flex items-center gap-x-2 px-2 cursor-pointer rounded-lg h-9 py-1.5 focus:outline-none text-sm overflow-hidden border-2 border-transparent",
                "transition-width ease-in-out delay-150",
                isLNBExpanded
                  ? tab === 'notifications' ? "w-[165px] bg-blue-700" : "w-[165px]"
                  : tab === 'notifications' ? "w-[36px] bg-blue-700 border-white/50" : "w-[36px] bg-blue-700 hover:bg-blue-600"
              )}
            >
              <BellIcon
                className={cn(
                  "flex-shrink-0 h-5 w-5 text-white/50 group-hover:text-white/75",
                  tab === 'notifications' ? "text-white" : ""
                )}
                aria-hidden="true"
              />
              { isLNBExpanded
                ? <span
                    className={cn(
                      "flex-shrink-0 block text-white/50 group-hover:text-white/75",
                      tab === 'notifications' ? "text-white" : ""
                    )}
                  >
                    {t("lnb.menu.notifications")}
                  </span>
                : ""
              }
            </Link> */}
            
            {/* Billings */}
            {tab !== "manage-profile" && selectedProfilePair && selectedProfilePair.customer_id && (
              <button
                onClick={async (e) => {
                  e.stopPropagation()
                  const response = await api.createStripeCustomerPortalSession(
                    {
                      customer_id: selectedProfilePair.customer_id,
                    },
                    (session?.user as any).access_token
                  )
                  if (response.url) {
                    window.location.href = response.url
                  }
                }}
                className={cn(
                  "mt-4 group relative flex items-center gap-x-2 px-2 cursor-pointer rounded-lg h-9 py-1.5 focus:outline-none text-sm overflow-hidden border-2 border-transparent",
                  "transition-width ease-in-out delay-150",
                  isLNBExpanded
                    ? "w-[165px]"
                    : "w-[36px] bg-blue-700 hover:bg-blue-600"
                )}
              >
                <CreditCardIcon
                  className={cn(
                    "flex-shrink-0 h-5 w-5 text-white/50 group-hover:text-white/75",
                    tab === 'manage-billing' ? "text-white" : ""
                  )}
                  aria-hidden="true"
                />
                { isLNBExpanded
                  ? <span
                      className={cn(
                        "flex-shrink-0 block text-white/50 group-hover:text-white/75",
                        tab === 'manage-billing' ? "text-white" : ""
                      )}
                    >
                      {t("lnb.menu.manageBillings")} 
                    </span>
                  : ""
                }
              </button>
            )}
          </div>
        </div>
        <div className="w-full flex flex-col gap-y-3 py-4">
          {/* <DocsButton /> */}
          <div className="border-gray-100/10">
            {accountButton}
          </div>
        </div>
      </div>
    </section>
  )
}
