"use client"

import Plot from "react-plotly.js"
import { cn, stringToColor } from "@/utils/msc"
import { useState, useEffect, useMemo } from "react"
import { Fragment } from "react"
import { Listbox, Transition } from "@headlessui/react"
import { ChevronUpDownIcon } from "@heroicons/react/20/solid"
import { Check } from "@/components/ui/check"
import { ProductListItem } from './ad-portfolio-layout-component'
import AdvancedFilterSelect from "./advanced-filter-select"
import { MarketplaceOption } from "./marketplace-select"
import { useTranslations } from 'next-intl'

// The API returns an object keyed by ASIN → { timestamp: qty }
export interface InventoryApiResponse {
  [asin: string]: {
    daily: {
      ad_clicks: number,
      ad_impressions: number,
      ad_orders: number,
      ad_sales: number,
      ad_sales_same_sku: number,
      ad_spend: number,
      glance_views: number,
      ordered_units: number,
      sales: number
    }
    inventory: {
      [timestamp: string]: number
    }
    product: any
  }
}

interface AsinOption {
  asin: string
  color: string
}

export default function InventoryPlotGraph({ data, selectedMarketplace, productListItemCard }: { data: InventoryApiResponse, selectedMarketplace: MarketplaceOption, productListItemCard: (item: any, mode: string) => JSX.Element }) {
  const t = useTranslations('RealtimePage')
  const td = useTranslations('DashboardPage')

  // product share legend options
  const productShareLegendOptions = [
    // total performance
    {
      id: 1,
      name: td('graph.legendSelect.glanceViews'),
      category: 'total',
      type: 'glance-views',
    },
    {
      id: 2,
      name: td('graph.legendSelect.orderedUnits'),
      category: 'total',
      type: 'ordered-units',
    },
    {
      id: 3,
      name: td('graph.legendSelect.revenue'),
      category: 'total',
      type: 'revenue',
    },
    // ads performance
    {
      id: 4,
      name: td('graph.legendSelect.impressions'),
      category: 'ad',
      type: 'impressions',
    },
    {
      id: 5,
      name: td('graph.legendSelect.clicks'),
      category: 'ad',
      type: 'clicks',
    },
    {
      id: 6,
      name: td('graph.legendSelect.adOrders'),
      category: 'ad',
      type: 'ad-orders',
    },
    {
      id: 7,
      name: td('graph.legendSelect.adSales'),
      category: 'ad',
      type: 'ad-sales',
    },
    {
      id: 8,
      name: td('graph.legendSelect.adCost'),
      category: 'ad',
      type: 'ad-spends',
    },
  ]

  // advanced filter options
  const advancedFilterOptions = [
    // low inventory
    {
      id: 1,
      name: td('filter.advancedFilter.content.low30d'),
      category: 'low-inventory',
      type: 'low30d',
    },
    {
      id: 2,
      name: td('filter.advancedFilter.content.low60d'),
      category: 'low-inventory',
      type: 'low60d',
    },
    {
      id: 3,
      name: td('filter.advancedFilter.content.low90d'),
      category: 'low-inventory',
      type: 'low90d',
    },
    // excess inventory
    {
      id: 4,
      name: td('filter.advancedFilter.content.excess180d'),
      category: 'excess-inventory',
      type: 'excess180d',
    },
    {
      id: 5,
      name: td('filter.advancedFilter.content.excess270d'),
      category: 'excess-inventory',
      type: 'excess270d',
    },
    {
      id: 6,
      name: td('filter.advancedFilter.content.excess365d'),
      category: 'excess-inventory',
      type: 'excess365d',
    },
    // recommended ASIN
    // {
    //   id: 7,
    //   name: td('filter.advancedFilter.content.growth'),
    //   category: 'recommendation',
    //   type: 'GROWTH',
    // },
    // {
    //   id: 8,
    //   name: td('filter.advancedFilter.content.efficiency'),
    //   category: 'recommendation',
    //   type: 'EFFICIENCY',
    // }
  ].filter(option => {
    if (selectedMarketplace?.subscription_features?.inventory_warning === "limited") {
      const restrictedTypes = ['low30d', 'low60d', 'excess180d', 'excess270d'];
      return !restrictedTypes.includes(option.type);
    }
    return true;
  });

  const asinKeys = Object.keys(data || {})
  const [selectedProductShareLegend, setSelectedProductShareLegend] = useState(productShareLegendOptions[0])
  const [selectedAdvancedOptions, setSelectedAdvancedOptions] = useState<any>([])

  // Create ASIN options for selection (memoized to prevent recreation)
  const asinOptions: AsinOption[] = useMemo(() => 
    asinKeys.map((asin) => ({
      asin,
      color: stringToColor(asin)
    })), 
    [asinKeys.join(',')]
  )

  // State for selected ASINs (default: all selected)
  const [selectedAsins, setSelectedAsins] = useState<AsinOption[]>([])

  // Update selected ASINs when data changes (only once)
  useEffect(() => {
    setSelectedAsins(asinOptions)
  }, [asinKeys.join(',')])

  if (asinKeys.length === 0) {
    return (
      <div className="flex items-center justify-center w-full h-full text-gray-400 text-sm">
        No data
      </div>
    )
  }

  // Custom handler for ASIN selection (toggle behavior)
  const handleAsinToggle = (clickedOption: AsinOption) => {
    setSelectedAsins(prev => {
      const isSelected = prev.some(item => item.asin === clickedOption.asin)
      if (isSelected) {
        // Remove from selection
        return prev.filter(item => item.asin !== clickedOption.asin)
      } else {
        // Add to selection
        return [...prev, clickedOption]
      }
    })
  }
  
  // Filter data based on selected ASINs
  const filteredData = Object.fromEntries(
    Object.entries(data).filter(([asin, item]) => {
      // filter by advanced options
      if (selectedAdvancedOptions.length > 0) {
        const lowInventory = selectedAdvancedOptions.find((option: any) => option.category === 'low-inventory')
        const excessInventory = selectedAdvancedOptions.find((option: any) => option.category === 'excess-inventory')
        if (lowInventory) {
          if (lowInventory.type === "low30d") {
            return item.product?.estimated_days_of_supply && typeof item.product?.estimated_days_of_supply === "number" && item.product?.estimated_days_of_supply <= 30
          } else if (lowInventory.type === "low60d") {
            return item.product?.estimated_days_of_supply && typeof item.product?.estimated_days_of_supply === "number" && item.product?.estimated_days_of_supply <= 60
          } else if (lowInventory.type === "low90d") {
            return item.product?.estimated_days_of_supply && typeof item.product?.estimated_days_of_supply === "number" && item.product?.estimated_days_of_supply <= 90
          }
        }
        if (excessInventory) {
          if (excessInventory.type === "excess180d") {
            return item.product?.estimated_days_of_supply && typeof item.product?.estimated_days_of_supply === "number" && item.product?.estimated_days_of_supply >= 180
          } else if (excessInventory.type === "excess270d") {
            return item.product?.estimated_days_of_supply && typeof item.product?.estimated_days_of_supply === "number" && item.product?.estimated_days_of_supply >= 270
          } else if (excessInventory.type === "excess365d") {
            return item.product?.estimated_days_of_supply && typeof item.product?.estimated_days_of_supply === "number" && item.product?.estimated_days_of_supply >= 365
          }
        }
      }
      return selectedAsins.some(selected => selected.asin === asin)
    }).sort(([, itemA], [, itemB]) => { // sort by selected legend
      if (selectedProductShareLegend.type === 'glance-views') {
        return (itemB.daily?.glance_views || 0) - (itemA.daily?.glance_views || 0)
      } else if (selectedProductShareLegend.type === 'ordered-units') {
        return (itemB.daily?.ordered_units || 0) - (itemA.daily?.ordered_units || 0)
      } else if (selectedProductShareLegend.type === 'revenue') {
        return (itemB.daily?.sales || 0) - (itemA.daily?.sales || 0)
      } else if (selectedProductShareLegend.type === 'impressions') {
        return (itemB.daily?.ad_impressions || 0) - (itemA.daily?.ad_impressions || 0)
      } else if (selectedProductShareLegend.type === 'clicks') {
        return (itemB.daily?.ad_clicks || 0) - (itemA.daily?.ad_clicks || 0)
      } else if (selectedProductShareLegend.type === 'ad-orders') {
        return (itemB.daily?.ad_orders || 0) - (itemA.daily?.ad_orders || 0)
      } else if (selectedProductShareLegend.type === 'ad-sales') {
        return (itemB.daily?.ad_sales || 0) - (itemA.daily?.ad_sales || 0)
      } else if (selectedProductShareLegend.type === 'ad-spends') {
        return (itemB.daily?.ad_spend || 0) - (itemA.daily?.ad_spend || 0)
      }
      return 0
    }).filter(([asin]) => selectedAsins.some(selected => selected.asin === asin))
  )

  const filteredAsinKeys = Object.keys(filteredData)

  // 1. Build unified timeline keys (ascending) from all data
  const timelineSet = new Set<string>()
  asinKeys.forEach((asin) => {
    Object.keys(data[asin] || {}).forEach((ts) => timelineSet.add(ts))
  })
  const allTimestamps = Array.from(timelineSet).sort()

  // 2. Prepare traces per selected ASIN (sparse data - only actual data points)
  const traces = filteredAsinKeys.map((asin) => {
    const asinData = filteredData[asin]
    const timestamps = Object.keys(asinData?.inventory || {}).sort()
    const values = timestamps.map(ts => asinData?.inventory[ts])
    const color = stringToColor(asin)
    return {
      x: timestamps.map(ts => new Date(ts.replace(' ', 'T') + ':00Z').toISOString()),
      y: values,
      type: "scatter",
      mode: "lines",
      line: {
        shape: "linear",
        color: color
      },
      connectgaps: true,
      name: asin,
      hovertemplate: "%{y} units",
    }
  })

  // 3. Build tick labels – show date label at 00:00, fallback to first/last
  const tickVals: string[] = []
  const tickText: string[] = []
  allTimestamps.forEach((lbl) => {
    if (lbl.slice(11, 16) === "00:00") {
      tickVals.push(lbl)
      tickText.push(lbl.slice(0, 10))
    }
  })
  if (tickVals.length === 0 && allTimestamps.length > 0) {
    tickVals.push(allTimestamps[0])
    tickText.push(allTimestamps[0].slice(0, 10))
  }

  return (
    <div className="flex flex-col size-full">
      {/* Filter Section */}
      <div className="flex-shrink-0 flex flex-wrap items-center gap-3 mb-3">
        {/* Sorting Selection */}
        <div>
          <div className="text-xs text-gray-400 font-semibold">
            Sort By
          </div>
          <div className="mt-1 relative w-[200px] w-fit">
            <Listbox value={selectedProductShareLegend} onChange={setSelectedProductShareLegend}>
              <div className="relative min-w-[200px] max-w-full w-fit">
                <Listbox.Button className="relative w-full inline-flex items-center gap-x-6 cursor-pointer rounded-lg bg-white text-gray-600 py-2 pl-3 pr-10 text-left border border-gray-100 focus:outline-none focus-visible:border-indigo-500 focus-visible:ring-2 focus-visible:ring-white/75 focus-visible:ring-offset-2 focus-visible:ring-offset-orange-300 sm:text-sm truncate">
                  <span className="block truncate">{selectedProductShareLegend.name}</span>
                  <span className="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-2 bg-white">
                    <ChevronUpDownIcon
                      className="h-5 w-5 text-gray-300"
                      aria-hidden="true"
                    />
                  </span>
                </Listbox.Button>
                <Transition
                  as={Fragment}
                  leave="transition ease-in duration-100"
                  leaveFrom="opacity-100"
                  leaveTo="opacity-0"
                >
                  <Listbox.Options
                    className={cn(
                      "z-[3] absolute flex flex-col items-start divide-y divide-gray-100 mt-1 min-w-[400px] max-h-80 w-fit overflow-auto rounded-md bg-white text-sm shadow-lg ring-1 ring-black/5 focus:outline-none",
                    )}
                  >
                    <div className="relative w-full flex flex-col xl:flex-row items-start divide-y xl:divide-x divide-gray-100">
                      <div className="w-full xl:w-1/2 py-2">
                        <div className="flex items-center gap-x-2 py-2 px-4 text-xs text-gray-400 font-semibold">
                          Total Performance
                        </div>
                        {productShareLegendOptions.filter(option => option.category === 'total').map((option) => (
                          <Listbox.Option
                            key={option.id}
                            className={({ active }) =>
                              `relative cursor-pointer select-none py-2 pl-4 pr-10 ${
                                active ? 'bg-blue-100/40 text-blue-900' : 'text-gray-600'
                              }`
                            }
                            value={option}
                          >
                            {({ selected }) => (
                              <>
                                <div
                                  className={`flex items-center gap-x-3 truncate ${
                                    selected ? 'font-medium' : 'font-normal'
                                  }`}
                                >
                                  {option.name}
                                </div>
                                {selected ? (
                                  <div className="absolute inset-y-0 right-0 flex items-center pr-3">
                                    <div className="shrink-0 flex items-center justify-center w-5 h-5 rounded-full">
                                      <Check className="w-4 h-4 stroke-blue-400" aria-hidden="true" />
                                    </div>
                                  </div>
                                ) : null}
                              </>
                            )}
                          </Listbox.Option>
                        ))}
                      </div>
                      <div className="w-full xl:w-1/2 py-2">
                        <div className="flex items-center gap-x-2 py-2 px-4 text-xs text-gray-400 font-semibold">
                          Ad Performance
                        </div>
                        {productShareLegendOptions.filter(option => option.category === 'ad').map((option) => (
                          <Listbox.Option
                            key={option.id}
                            className={({ active }) =>
                              `relative cursor-pointer select-none py-2 pl-4 pr-10 ${
                                active ? 'bg-blue-100/40 text-blue-900' : 'text-gray-600'
                              }`
                            }
                            value={option}
                          >
                            {({ selected }) => (
                              <>
                                <div
                                  className={`flex items-center gap-x-3 truncate ${
                                    selected ? 'font-medium' : 'font-normal'
                                  }`}
                                >
                                  {option.name}
                                </div>
                                {selected ? (
                                  <div className="absolute inset-y-0 right-0 flex items-center pr-3">
                                    <div className="shrink-0 flex items-center justify-center w-5 h-5 rounded-full">
                                      <Check className="w-4 h-4 stroke-blue-400" aria-hidden="true" />
                                    </div>
                                  </div>
                                ) : null}
                              </>
                            )}
                          </Listbox.Option>
                        ))}
                      </div>
                    </div>
                  </Listbox.Options>
                </Transition>
              </div>
            </Listbox>
          </div>
        </div>
        {/* Advanced Filter */}
        <div>
          <div className="text-xs text-gray-400 font-semibold">{td("filter.advancedFilter.label")}</div>
          <AdvancedFilterSelect
            className="mt-1 border border-gray-100 rounded-lg"
            advancedFilterOptions={advancedFilterOptions}
            selected={selectedAdvancedOptions}
            setSelected={setSelectedAdvancedOptions}
          />
        </div>
        {/* ASIN Selection */}
        <div>
          <div className="text-xs text-gray-400 font-semibold">
            ASINs
          </div>
          <div className="relative min-w-[200px] max-w-full w-fit">
            <Listbox value={selectedAsins} onChange={() => {}} multiple>
              <Listbox.Button className="relative w-full inline-flex items-center gap-x-6 cursor-pointer rounded-lg bg-white text-gray-600 py-2 pl-3 pr-10 text-left border border-gray-100 focus:outline-none focus-visible:border-indigo-500 focus-visible:ring-2 focus-visible:ring-white/75 focus-visible:ring-offset-2 focus-visible:ring-offset-orange-300 sm:text-sm truncate">
                <span className="block truncate">
                  {selectedAsins.length === 0 
                    ? "Select ASINs" 
                    : selectedAsins.length === asinOptions.length 
                      ? "All ASINs" 
                      : `${selectedAsins.length} ASIN${selectedAsins.length > 1 ? 's' : ''} selected`
                  }
                </span>
                <span className="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-2 bg-white">
                  <ChevronUpDownIcon
                    className="h-5 w-5 text-gray-300"
                    aria-hidden="true"
                  />
                </span>
              </Listbox.Button>
              <Transition
                as={Fragment}
                leave="transition ease-in duration-100"
                leaveFrom="opacity-100"
                leaveTo="opacity-0"
              >
                <Listbox.Options className="z-[2] absolute flex flex-col items-start mt-1 min-w-[300px] max-h-60 w-fit overflow-auto rounded-md bg-white text-sm shadow-lg ring-1 ring-black/5 focus:outline-none">
                  <div className="w-full py-2">
                    <div className="flex items-center justify-between py-2 px-4 text-xs text-gray-400 font-semibold border-b border-gray-100">
                      <span>Inventory by ASIN</span>
                      <div className="flex gap-2">
                        <button 
                          onClick={(e) => {
                            e.stopPropagation()
                            setSelectedAsins(asinOptions)
                          }}
                          className="text-blue-300 hover:text-blue-500"
                        >
                          All
                        </button>
                        <button 
                          onClick={(e) => {
                            e.stopPropagation()
                            setSelectedAsins([])
                          }}
                          className="text-gray-300 hover:text-gray-500"
                        >
                          None
                        </button>
                      </div>
                    </div>
                    {asinOptions.map((option) => {
                      const isSelected = selectedAsins.some(item => item.asin === option.asin)
                      return (
                        <div
                          key={option.asin}
                          className="relative cursor-pointer select-none py-2 pl-4 pr-10 hover:bg-blue-100/40 text-gray-600"
                          onClick={() => handleAsinToggle(option)}
                        >
                          <div
                            className={`flex items-center gap-x-3 truncate ${
                              isSelected ? 'font-medium' : 'font-normal'
                            }`}
                          >
                            <div
                              className="relative w-6 h-0 border border-solid"
                              style={{ borderColor: option.color }}
                            >
                              <div
                                className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-1.5 h-1.5 rounded-full"
                                style={{ backgroundColor: option.color }}
                              ></div>
                            </div>
                            {option.asin}
                          </div>
                          {isSelected ? (
                            <div className="absolute inset-y-0 right-0 flex items-center pr-3">
                              <div className="shrink-0 flex items-center justify-center w-5 h-5 rounded-full">
                                <Check className="w-4 h-4 stroke-blue-400" aria-hidden="true" />
                              </div>
                            </div>
                          ) : null}
                        </div>
                      )
                    })}
                  </div>
                </Listbox.Options>
              </Transition>
            </Listbox>
          </div>
        </div>
      </div>
      <div className="relative grow w-full min-h-0 flex flex-col-reverse xl:flex-row items-center gap-3">
        <div className="relative flex-1 flex flex-col min-w-0 min-h-0 size-full">
          {/* Product List Filtered */}
          <div className="relative flex-1 h-full min-h-0 rounded-lg bg-white border border-gray-100 overflow-y-scroll">
            <div className="sticky inset-x-0 top-0 z-[1] flex items-center gap-x-3 py-3 text-center text-sm text-gray-400 font-bold bg-gray-100/40 backdrop-blur-md">
              <div className="flex-shrink-0 w-[100px] text-center">
                Legend
              </div>
              <div className="grow px-8">
                {t("productShare.table.header.productInfo")}
              </div>
            </div>
            {!filteredData ? (
              <div className="py-8 flex justify-center">
                <div className="animate-pulse">
                  <div className="w-[300px] h-6 rounded-md bg-gray-100"/>
                </div>
              </div>
            ) : (
              <ul className="divide-y divide-gray-100">
                {Object.keys(filteredData).length === 0 ? (
                  <li className="py-8 text-center text-gray-400">
                    No data available
                  </li>
                ) : (
                  Object.entries(filteredData).map(([asin, data], index: number) => {
                    return (<li
                      className="relative flex items-center gap-x-3 py-6 cursor-pointer hover:bg-gray-100/50 text-center text-gray-500 text-sm"
                      key={asin}
                      onClick={() => {}}
                    >
                      <div className="relative flex-shrink-0 flex flex-col items-start justify-center gap-x-2 gap-y-2 w-[100px] pl-8">
                        <div
                          className="relative w-6 h-0 border border-solid"
                          style={{ borderColor: stringToColor(asin) }}
                        >
                          <div
                            className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-1.5 h-1.5 rounded-full"
                            style={{ backgroundColor: stringToColor(asin) }}
                          ></div>
                        </div>
                      </div>
                      {productListItemCard(data.product, "light")}
                    </li>)
                  })
                )}
              </ul>
            )}
          </div>
        </div>
        <div className="flex-shrink-0 relative w-full xl:w-1/2 xl:max-w-[500px] h-[200px] xl:h-full">
          {/* Chart Container - 고정 높이로 설정 */}
          <div className="relative size-full">
            {filteredAsinKeys.length === 0 ? (
              <div className="absolute inset-0 flex items-center justify-center text-gray-400 text-sm bg-gray-50 rounded">
                <div className="text-center">
                  <div>No ASIN selected.</div>
                  <div className="">Choose from dropdown above.</div>
                </div>
              </div>
            ) : (
              <Plot
                data={traces as any}
                layout={{
                  margin: { l: 0, r: 0, b: 0, t: 0 },
                  width: undefined,
                  height: undefined,
                  autosize: true,
                  showlegend: false,
                  // showlegend: filteredAsinKeys.length <= 10, // 10개 이하일 때만 legend 표시
                  // legend: {
                  //   orientation: "h",
                  //   y: -0.15,
                  //   x: 0.5,
                  //   xanchor: 'center',
                  //   font: { size: 10 }
                  // },
                  xaxis: {
                    type: "date",
                    tickformat: '%Y-%m-%d',
                    hoverformat: "%Y-%m-%d %H:%M",
                    tickfont: { size: 10, color: "#9CA3AF" },
                    tickangle: 0,
                    zerolinecolor: "#e5e7eb",
                    automargin: true,
                    fixedrange: true,
                  },
                  yaxis: {
                    rangemode: "tozero",
                    tickformat: ",",
                    tickfont: { size: 10, color: "#9CA3AF" },
                    gridcolor: "#f3f4f6",
                    zerolinecolor: "#e5e7eb",
                    automargin: true,
                    fixedrange: true,
                  },
                  hovermode: "x unified",
                  hoverlabel: {
                    bgcolor: "rgba(17, 24, 39, 0.9)",
                    font: { size: 10, color: "#e5e7eb" },
                  },
                  dragmode: false,
                }}
                config={{ 
                  displayModeBar: false,
                  responsive: true
                }}
                useResizeHandler
                className="w-full h-full"
                style={{ width: '100%', height: '100%' }}
              />
            )}
          </div>
        </div>
      </div>
    </div>
  )
} 