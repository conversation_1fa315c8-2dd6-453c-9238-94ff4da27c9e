"use client"

import { api } from "@/utils/api"
import { useSession } from "next-auth/react"
import { forwardRef, Fragment, useEffect, useRef, useState } from 'react'
import DatePicker, { registerLocale } from "react-datepicker"
import { ko } from "date-fns/locale"
import { Dialog, DialogPanel, Listbox, Popover, PopoverButton, PopoverPanel, Transition, TransitionChild } from '@headlessui/react'
import { ProfileOption } from "@/components/dashboard/profile-select"
import { MarketplaceOption } from "@/components/dashboard/marketplace-select"
import { cn, currencyFormat, formatDate } from "@/utils/msc"
import { ExclamationTriangleIcon, QuestionMarkCircleIcon, XMarkIcon, ChevronUpDownIcon } from "@heroicons/react/20/solid"
import { Check } from "@/components/ui/check"

import { useLocale, useTranslations } from 'next-intl'
import dynamic from "next/dynamic"
import Plot from "react-plotly.js"

registerLocale('ko', ko)

const KeywordBrandShareGraph = dynamic(() => import('@/components/dashboard/keyword-brand-share-graph'), { ssr: false })

interface CompetitionWeeklyFunnelSliderProps {
  isOpen: boolean;
  onClose: () => void;
  selectedProfile: ProfileOption;
  selectedMarketplace: MarketplaceOption;
  selectedDate: Date;
}

export default function CompetitionWeeklyFunnelSlider({
  isOpen,
  onClose,
  selectedProfile,
  selectedMarketplace,
  selectedDate,
}: CompetitionWeeklyFunnelSliderProps) {
  const t = useTranslations("component")
  const tos = useTranslations('optimizationSets')
  const tk = useTranslations('CompetitionPage')
  const locale = useLocale()
  const { data: session, status } = useSession()  
  const funnelDetailFetchCounter = useRef(0)
  const tableListRef = useRef<HTMLDivElement | null>(null)
  const [brandCompetitionFunnelDataDetail, setBrandCompetitionFunnelDataDetail] = useState<any>(null)
  const [targetKeywords, setTargetKeywords] = useState<any[]>([])
  
  const DateRangeInput = forwardRef(({ value, onClick }: { value: string, onClick: () => void }, ref) => (
    // @ts-ignore
    <button className="min-w-[200px] overflow-hidden cursor-pointer rounded-lg bg-gray-100/10 hover:bg-gray-100/30 text-gray-300 py-2 px-3 text-left shadow-sm focus:outline-none focus-visible:border-indigo-500 focus-visible:ring-2 focus-visible:ring-white/75 focus-visible:ring-offset-2 focus-visible:ring-offset-orange-300 sm:text-sm" onClick={onClick} ref={ref}>
      <div className="inline-flex">
        {value}
      </div>
    </button>
  ));
  DateRangeInput.displayName = 'DateRangeInput'

  const [selectedKeywords, setSelectedKeywords] = useState<any[]>([])

  const productListItemCard = (item: any, mode: string) => {
    // items = [
    //   {
    //     "asin": "B0DSCV9HGJ",
    //     "product": "Razer Viper V3 Pro Wireless Esports Gaming Mouse: Symmetrical - 54g Lightweight - 8K Polling - 35K DPI Optical Sensor - Gen3 Optical Switches - 8 Programmable Controls - 95 Hr Battery - Faker Edition",
    //     "competitor_type": "top-tier",
    //     "brand": "Razer",
    //     "price": 179.99,
    //     "sales_rank": 122,
    //     "click_share": 0.2188,
    //     "click_share_rank": 2
    //   }
    // ]
    return (
      <div className="grow relative flex items-center gap-x-4 px-4 overflow-hidden">
        {/* { item.image
        ? (<img src={item.image} alt="Item Image" className="flex-shrink-0 w-10 h-10 rounded" />)
        : (<div className="flex-shrink-0 flex items-center justify-center w-10 h-10 bg-gray-100 rounded">
            <ExclamationTriangleIcon className="h-5 w-5 text-gray-300" />
          </div>)
        } */}
        <div className="flex-1 flex flex-col gap-y-0.5 overflow-hidden">
          <div
            className={cn(
              "text-xs text-left font-semibold truncate",
              mode === "dark" ? "text-gray-200" : "text-gray-500"
            )}
            title={item.product || "No Title"}
          >
            {item.product
              ? item.product
              : "No Title"
            }
          </div>
          <div className={cn(
            "flex items-center gap-x-2 divide-x divide-gray-100",
            mode === "dark" ? "divide-gray-600" : "divide-gray-100"
          )}>
            <div className="text-xs text-red-400 font-semibold">{currencyFormat.format(item.price || 0)}</div>
            {item.competitor_type &&
              <div className="pl-2 text-xs text-blue-400 font-semibold">{item.competitor_type}</div>
            }
          </div>
          <div className={cn(
            "mt-1 flex items-center gap-x-2 divide-x",
            mode === "dark" ? "divide-gray-600" : "divide-gray-100"
          )}>
            <div className="text-[10px] text-gray-400 truncate">{tos("detailModal.optSet.budgetTab.table.content.productInfo.asin")}: {item.asin}</div>
          </div>
          <div className={cn(
            "flex items-center gap-x-2 divide-x",
            mode === "dark" ? "divide-gray-600" : "divide-gray-100"
          )}>
            <div className="text-[10px] text-gray-400 truncate">
              Click Share: {Array.isArray(item.click_share) ? item.click_share[0] : item.click_share}
            </div>
            <div className="pl-2 text-[10px] text-gray-400 truncate">
              Click Share Rank: {Array.isArray(item.click_share_rank) ? item.click_share_rank[0] : item.click_share_rank}
            </div>
          </div>
          <div className={cn(
            "flex items-center gap-x-2 divide-x",
            mode === "dark" ? "divide-gray-600" : "divide-gray-100"
          )}>
            
            {Array.isArray(item.classification_title)
              ? (<Popover className="relative inline-block ml-auto">
                <PopoverButton className="inline-flex items-center gap-x-0.5 text-gray-400 hover:text-gray-200 text-xs font-semibold focus:outline-none">
                  <span>Show more</span>
                  <ChevronUpDownIcon className="w-4 h-4" />
                </PopoverButton>
                <PopoverPanel
                  anchor={{ to: 'bottom end' }}
                  className="z-10 max-h-60 min-w-[200px] overflow-auto rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black/5 focus:outline-none sm:text-xs divide-y divide-gray-200"
                >
                  {item.classification_title.map((title: any, idx: number) => (
                    <div key={idx} className="px-3 py-2 space-y-2">
                      <div>
                        <div className="text-[10px] text-gray-400">Classification Title</div>
                        <div className="text-xs text-gray-600">{title}</div>
                      </div>
                      <div>
                        <div className="text-[10px] text-gray-400">Sales Rank</div>
                        <div className="text-xs text-gray-600">{typeof item.sales_rank[idx] === "number" ? item.sales_rank[idx] : "N/A"}</div>
                      </div>
                    </div>
                  ))}
                </PopoverPanel>
              </Popover>)
              : <div className="relative pl-2 text-[10px] text-gray-400 truncate">
                Sales Rank: {item.sales_rank}
              </div>
            }
          </div>
        </div>
      </div>
    )
  }

  const fetchBrandCompetitionFunnelDataDetail = async (signal: AbortSignal) => {
    console.log('fetchBrandCompetitionFunnelDataDetail')
    if (!(session?.user as any).access_token) {
      console.log('access token is missing in the session.')
    }
    funnelDetailFetchCounter.current += 1
    let brandCompetitionFunnelDataDetailResponse = await api.getBrandCompetitionFunnelDataDetail(
      selectedProfile.account_id,
      selectedMarketplace.marketplace_id,
      formatDate(selectedDate, "-"),
      selectedProfile.account_type,
      (session?.user as any).access_token,
      signal
    )
    funnelDetailFetchCounter.current -= 1
    // brandCompetitionFunnelDataDetailResponse = {
    //   "table": [
    //     {
    //       "search_term": "pulsar sleeve",
    //       "impressions_share": 3.***************,
    //       "click_share": 41.**************,
    //       "cart_add_share": 40.**************,
    //       "purchase_share": 31.***************,
    //       "impression_count": 273,
    //       "click_count": 41,
    //       "cart_add_count": 1,
    //       "purchase_count": 0,
    //       "my_brand_impressions_share": 3.***************,
    //       "my_brand_click_share": 41.**************,
    //       "my_brand_cart_add_share": 40.**************,
    //       "my_brand_purchase_share": 31.***************,
    //       "my_brand_impression_count": 273,
    //       "my_brand_click_count": 41,
    //       "my_brand_cart_add_count": 1,
    //       "my_brand_purchase_count": 0,
    //       "top_asins": [
    //         {
    //           "asin": "B0CB3WCH3F",
    //           "brand": "Pulsar Gaming Gears",
    //           "search_term_type": "my brand keyword",
    //           "click_share": 38.38
    //         },
    //         {
    //           "asin": "B0CB3PPQGC",
    //           "brand": "Pulsar Gaming Gears",
    //           "search_term_type": "my brand keyword",
    //           "click_share": 1.01
    //         },
    //         {
    //           "asin": "B0CB3RS8ML",
    //           "brand": "Pulsar Gaming Gears",
    //           "search_term_type": "my brand keyword",
    //           "click_share": 1.01
    //         }
    //       ]
    //     },
    //   ],
    //   "total": 1,
    //   "start_date": "2025-07-20",
    //   "end_date": "2025-07-26",
    //   "classification_date": "2025-08-05"
    // }
    if (brandCompetitionFunnelDataDetailResponse && 'table' in brandCompetitionFunnelDataDetailResponse) {
      setBrandCompetitionFunnelDataDetail(brandCompetitionFunnelDataDetailResponse)
      setTargetKeywords(brandCompetitionFunnelDataDetailResponse.table as any[])
      setSelectedKeywords(brandCompetitionFunnelDataDetailResponse.table as any[])
    }
  }

  useEffect(() => {
    if (isOpen && selectedDate) {
      const controller = new AbortController()
      fetchBrandCompetitionFunnelDataDetail(controller.signal)
      return () => controller.abort()
    }
  }, [])

  return (
    <Transition appear show={isOpen}>
      <Dialog as="div" className="relative z-10 focus:outline-none" onClose={() => onClose()}>
        <div className="fixed inset-0 bg-black/30 backdrop-blur-sm" aria-hidden="true" />
        <div className="fixed inset-0 z-10 w-screen overflow-y-auto">
          <div className="flex min-h-full items-center justify-center p-4">
            <TransitionChild
              enter="ease-out duration-300"
              enterFrom="opacity-0 transform-[scale(95%)]"
              enterTo="opacity-100 transform-[scale(100%)]"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 transform-[scale(100%)]"
              leaveTo="opacity-0 transform-[scale(95%)]"
            >
              <DialogPanel className="relative w-[1080px] h-[600px] flex flex-col p-6 bg-gray-900/90 rounded-xl">
                <div className="flex-shrink-0 w-full">
                  <div className="flex items-center justify-end">
                    <button onClick={() => onClose()}>
                      <XMarkIcon className="h-5 w-5 text-gray-500 hover:text-gray-200" />
                    </button>
                  </div>
                </div>
                {funnelDetailFetchCounter.current > 0
                  ? (<div className="animate-pulse flex-1 flex items-center gap-x-6 pt-4">
                    <div className="relative w-2/3 h-full rounded-md bg-gray-100/10 shadow-sm overflow-hidden"></div>
                    <div className="relative w-1/3 h-full rounded-md bg-gray-100/10 shadow-sm overflow-hidden"></div>
                  </div>)
                  : brandCompetitionFunnelDataDetail?.table
                    ? (<div className="relative grow min-h-0 flex flex-col gap-y-3">
                        <div className="flex-shrink-0 flex flex-col justify-between">
                          <div className="px-6">
                            <h1 className="flex items-center gap-x-4 text-2xl text-gray-200 font-medium">
                              <div className="flex items-center gap-x-1">
                                {tk("weeklyFunnel.modalTitle")}
                                {/* <Popover className="relative flex items-center justify-center">
                                  {({ open }) => (
                                    <>
                                      <PopoverButton
                                        className={cn(
                                          "inline-flex items-center justify-center rounded-full text-gray-300 hover:text-gray-400 focus:outline-none"
                                        )}
                                      >
                                        <QuestionMarkCircleIcon className="w-5 h-5"/>
                                      </PopoverButton>
                                      <Transition
                                        as={Fragment}
                                        enter="transition ease-out duration-200"
                                        enterFrom="opacity-0 translate-y-1"
                                        enterTo="opacity-100 translate-y-0"
                                        leave="transition ease-in duration-150"
                                        leaveFrom="opacity-100 translate-y-0"
                                        leaveTo="opacity-0 translate-y-1"
                                      >
                                        <PopoverPanel className="absolute left-full top-0 z-10 ml-2 w-screen max-w-xs translate-y-0 transform px-4 sm:px-0">
                                          <div className="overflow-hidden rounded-lg shadow-lg">
                                            <div className="relative p-4 bg-gray-900/90 text-xs text-white font-normal text-left">
                                              {tk.rich("tooltip.keywordModalTitle",{
                                                enter: () =>  <br/>
                                              })}
                                            </div>
                                          </div>
                                        </PopoverPanel>
                                      </Transition>
                                    </>
                                  )}
                                </Popover> */}
                              </div>
                              {brandCompetitionFunnelDataDetail && brandCompetitionFunnelDataDetail.classification_date && (
                                <span className="flex items-center gap-x-1 text-[10px] text-gray-400 font-normal">
                                  <ExclamationTriangleIcon className="h-3 w-3 text-gray-400" />
                                  Last updated at {formatDate(new Date(brandCompetitionFunnelDataDetail.classification_date), ".")}
                                </span>
                              )}
                            </h1>
                            {brandCompetitionFunnelDataDetail?.table && (
                              <div className="flex items-center gap-x-3 mt-3">
                                <div className="flex-shrink-0">
                                  <div className="text-xs text-gray-400 font-semibold">{tk("weeklyFunnel.targetPeriod")}</div>
                                  <div className="mt-1 relative w-[200px] overflow-hidden rounded-lg bg-gray-100/10 text-gray-300 py-2 px-3 text-left shadow-sm focus:outline-none focus-visible:border-indigo-500 focus-visible:ring-2 focus-visible:ring-white/75 focus-visible:ring-offset-2 focus-visible:ring-offset-orange-300 sm:text-sm">
                                    <div className="inline-flex">
                                      {formatDate(new Date(brandCompetitionFunnelDataDetail.start_date), ".")} - {formatDate(new Date(brandCompetitionFunnelDataDetail.end_date), ".")}
                                    </div>
                                  </div>
                                </div>
                                <div className="flex-1 min-w-0">
                                  <div className="text-xs text-gray-400 font-semibold">{tk("weeklyFunnel.targetKeywords")} *</div>
                                  <div className="mt-1 relative w-full">
                                    <Listbox
                                      value={selectedKeywords}
                                      onChange={setSelectedKeywords}
                                      multiple
                                      by="search_term"
                                      disabled={!true}
                                    >
                                      <div className="relative w-full">
                                        <Listbox.Button
                                          className={cn(
                                            "relative w-full rounded-lg py-2 pl-3 pr-10 text-left shadow-md focus:outline-none focus-visible:border-indigo-500 focus-visible:ring-2 focus-visible:ring-white/75 focus-visible:ring-offset-2 focus-visible:ring-offset-orange-300 sm:text-sm",
                                            targetKeywords.length > 0
                                              ? "cursor-pointer bg-gray-100/10 hover:bg-gray-100/30 text-gray-300"
                                              : "cursor-not-allowed bg-gray-100/10 text-gray-500 opacity-60"
                                          )}
                                          disabled={!true}
                                        >
                                          <span className="block truncate">
                                            { selectedKeywords.length === 0
                                              ? "none"
                                              : selectedKeywords.length === targetKeywords.length
                                                ? "all"
                                                : selectedKeywords.map((keyword) => keyword.search_term).join(", ")
                                            }
                                          </span>
                                          <span className="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-2">
                                            <ChevronUpDownIcon
                                              className="h-4 w-4 text-gray-300"
                                              aria-hidden="true"
                                            />
                                          </span>
                                        </Listbox.Button>
                                        <Transition
                                          as={Fragment}
                                          leave="transition ease-in duration-100"
                                          leaveFrom="opacity-100"
                                          leaveTo="opacity-0"
                                        >
                                          <Listbox.Options
                                            className={cn(
                                              "absolute z-[3] mt-1 max-h-60 w-full overflow-auto rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black/5 focus:outline-none sm:text-sm",
                                            )}
                                          >
                                            {targetKeywords.map((option: any, optionIdx: number) => (
                                              <Listbox.Option
                                                key={option.search_term}
                                                className={({ active }) =>
                                                  `relative cursor-pointer select-none py-2 pl-4 pr-10 ${
                                                    active ? 'bg-blue-100/40 text-blue-900' : 'text-gray-600'
                                                  }`
                                                }
                                                value={option}
                                              >
                                                {({ selected }) => (
                                                  <>
                                                    <span
                                                      className={`block truncate ${
                                                        selected ? 'font-medium' : 'font-normal'
                                                      }`}
                                                    >
                                                      {option.search_term}
                                                    </span>
                                                    {selected ? (
                                                      <div className="absolute inset-y-0 right-0 flex items-center pr-3">
                                                        <div className="shrink-0 flex items-center justify-center w-5 h-5 rounded-full">
                                                          <Check className="w-4 h-4 stroke-blue-400" aria-hidden="true" />
                                                        </div>
                                                      </div>
                                                    ) : null}
                                                  </>
                                                )}
                                              </Listbox.Option>
                                            ))}
                                          </Listbox.Options>
                                        </Transition>
                                      </div>
                                    </Listbox>
                                  </div>
                                </div>
                              </div>
                            )}
                          </div>
                        </div>
                        <div className="relative grow min-h-0 flex items-center gap-x-6 px-6 py-3">
                          <div className="relative w-1/3 h-full">
                            <div className="relative size-full flex flex-col">
                              <div className="flex items-center justify-start gap-x-4">
                                <div className="flex items-center gap-x-1.5 text-gray-400 text-xs font-semibold">
                                  {tk("weeklyFunnel.myBrandShare")}
                                  <div className="w-2 h-2 rounded-full bg-blue-400"></div>
                                </div>
                                <div className="flex items-center gap-x-1.5 text-gray-400 text-xs font-semibold">
                                  {tk("weeklyFunnel.competitorShare")}
                                  <div className="w-2 h-2 rounded-full bg-yellow-400"></div>
                                </div>
                              </div>
                              <div className="relative size-full flex items-center">
                                <div className="flex-shrink-0 w-min h-full">
                                  <div className="grid grid-rows-4 h-full">
                                    {['Impressions', 'Clicks', 'Add to Cart', 'Purchases'].map((stage) => (
                                      <div key={stage} className="flex flex-col justify-center">
                                        <span className="text-xs text-gray-400 font-semibold">{stage}</span>
                                      </div>
                                    ))}
                                  </div>
                                </div>
                                <Plot
                                  data={[
                                    {
                                      type: 'funnel',
                                      name: 'My Brand',
                                      y: ['Impressions', 'Clicks', 'Add to Cart', 'Purchases'],
                                      x: [
                                        selectedKeywords?.map((item: any) => Math.max(item.my_brand_impression_count || 0, 0)).reduce((a, b) => a + b, 0),
                                        selectedKeywords?.map((item: any) => Math.max(item.my_brand_click_count || 0, 0)).reduce((a, b) => a + b, 0),
                                        selectedKeywords?.map((item: any) => Math.max(item.my_brand_cart_add_count || 0, 0)).reduce((a, b) => a + b, 0),
                                        selectedKeywords?.map((item: any) => Math.max(item.my_brand_purchase_count || 0, 0)).reduce((a, b) => a + b, 0)
                                      ],
                                      orientation: 'h',
                                      textinfo: 'value+percent',
                                      marker: { color: '#60A5FA' },
                                    },
                                    {
                                      type: 'funnel',
                                      name: 'Competitors',
                                      y: ['Impressions', 'Clicks', 'Add to Cart', 'Purchases'],
                                      x: [
                                        selectedKeywords?.map((item: any) => Math.max((item.impression_count || 0) - Math.max(item.my_brand_impression_count || 0, 0), 0)).reduce((a, b) => a + b, 0),
                                        selectedKeywords?.map((item: any) => Math.max((item.click_count || 0) - Math.max(item.my_brand_click_count || 0, 0), 0)).reduce((a, b) => a + b, 0),
                                        selectedKeywords?.map((item: any) => Math.max((item.cart_add_count || 0) - Math.max(item.my_brand_cart_add_count || 0, 0), 0)).reduce((a, b) => a + b, 0),
                                        selectedKeywords?.map((item: any) => Math.max((item.purchase_count || 0) - Math.max(item.my_brand_purchase_count || 0, 0), 0)).reduce((a, b) => a + b, 0)
                                      ],
                                      orientation: 'h',
                                      textinfo: 'value+percent',
                                      marker: { color: '#FACC15' },
                                    },
                                  ]}
                                  layout={{
                                    margin: { l: 0, r: 0, t: 0, b: 0 },
                                    plot_bgcolor: 'rgba(0,0,0,0)',
                                    paper_bgcolor: 'rgba(0,0,0,0)',
                                    font: { color: '#fff' },
                                    showlegend: false,
                                  }}
                                  config={{ displayModeBar: false }}
                                  className="size-full min-h-0"
                                />
                              </div>
                            </div>
                          </div>
                          <div className="relative w-2/3 h-full flex flex-col justify-end">
                            {/* Keyword List in Weekly Funnel */}
                            <div
                              className="relative size-full mt-3 rounded-lg bg-gray-100/10 overflow-y-auto"
                              ref={tableListRef}
                            >
                              <div className="sticky inset-x-0 top-0 z-[1] flex items-center gap-x-3 py-3 text-left text-sm text-gray-400 font-bold bg-gray-100/10 backdrop-blur-md">
                                <div className="grow pl-4">
                                  {tk("weeklyFunnel.table.header.keyword")}
                                </div>
                                <div className="flex-shrink-0 w-[180px] px-4">
                                  {tk("weeklyFunnel.table.header.impressionShare")}
                                </div>
                                <div className="flex-shrink-0 w-[300px] pr-4">
                                  {tk("weeklyFunnel.table.header.leadingProducts")}
                                </div>
                              </div>
                              <ul className="divide-y divide-gray-600">
                                {funnelDetailFetchCounter.current > 0 && brandCompetitionFunnelDataDetail?.table?.length === 0 ? (
                                  Array.from({ length: 8 }).map((_, idx) => (
                                    <li
                                      key={idx}
                                      className="relative animate-pulse h-[112px] p-4"
                                    >
                                      <div className="size-full rounded-md bg-gray-100"></div>
                                    </li>
                                  ))
                                ) : (
                                  (() => {
                                    return selectedKeywords?.length === 0
                                      ? <div className="absolute inset-0 flex items-center justify-center w-full text-gray-300 text-sm font-normal">
                                          No keywords found.
                                        </div>
                                      : <>
                                          {selectedKeywords?.map((item: any, index: number) => {
                                            // item = {
                                            //   "search_term": "game gear",
                                            //   "impressions_share": -2.0299999713897705,
                                            //   "click_share": -3,
                                            //   "cart_add_share": -3,
                                            //   "purchase_share": -4,
                                            //   "impression_count": 54,
                                            //   "click_count": -3,
                                            //   "cart_add_count": -3,
                                            //   "purchase_count": -3,
                                            //   "my_brand_impressions_share": -2.0299999713897705,
                                            //   "my_brand_click_share": -3,
                                            //   "my_brand_cart_add_share": -3,
                                            //   "my_brand_purchase_share": -4,
                                            //   "my_brand_impression_count": 54,
                                            //   "my_brand_click_count": -3,
                                            //   "my_brand_cart_add_count": -3,
                                            //   "my_brand_purchase_count": -3,
                                            //   "top_asins": [
                                            //     {
                                            //       "asin": "B0CLGT7JMM",
                                            //       "brand": "Pulsar Gaming Gears",
                                            //       "search_term_type": "my brand keyword",
                                            //       "click_share": 0
                                            //     },
                                            //     {
                                            //       "asin": "B000GSQ6QS",
                                            //       "brand": "",
                                            //       "search_term_type": "my brand keyword",
                                            //       "click_share": -1
                                            //     },
                                            //     {
                                            //       "asin": "B000H12RG2",
                                            //       "brand": "",
                                            //       "search_term_type": "my brand keyword",
                                            //       "click_share": -1
                                            //     }
                                            //   ]
                                            // }
                                            return item && (
                                              <li
                                                className="relative flex items-center gap-x-3 cursor-pointer text-center text-gray-500 text-sm"
                                                key={index}
                                              >
                                                <div className="flex-1 flex items-center py-8 gap-x-3 overflow-hidden">
                                                  <div className="grow relative flex flex-col gap-y-0.5 pl-4 overflow-hidden">
                                                    <div className="text-base text-gray-300 text-left font-semibold truncate" title={item.search_term}>
                                                      {item.search_term}
                                                    </div>
                                                  </div>
                                                  <div className="flex-shrink-0 relative w-[180px] flex flex-col items-center justify-start gap-y-0.5 px-4 overflow-hidden">
                                                    <div className="w-full flex items-center justify-between text-xs text-gray-300 font-semibold">
                                                      <div className="">My Brand</div>
                                                      <div className="">
                                                        {item.my_brand_impressions_share && item.my_brand_impressions_share > 0
                                                          ? item.my_brand_impressions_share.toFixed(2)
                                                          : 0
                                                        }%
                                                      </div>
                                                    </div>
                                                    <div className="relative w-full h-1 bg-gray-400 rounded-sm overflow-hidden">
                                                      <div
                                                        className="h-full bg-blue-400"
                                                        style={{
                                                          width: `${item.my_brand_impressions_share && item.my_brand_impressions_share > 0
                                                            ? item.my_brand_impressions_share.toFixed(2)
                                                            : 0}%`
                                                        }}
                                                      ></div>
                                                    </div>
                                                    <div className="w-full flex items-center justify-between text-xs">
                                                      <div className="text-gray-400">0</div>
                                                      <div className="text-gray-400">100%</div>
                                                    </div>
                                                  </div>
                                                  <div className="flex-shrink-0 w-[300px] pr-4">
                                                    {item.top_asins && item.top_asins.length > 0 && (
                                                      <div className="flex flex-col gap-y-2">
                                                        {item.top_asins.map((competitor: any, index: number) => (
                                                          <div key={index} className="flex items-center gap-x-2">
                                                            <div className="text-[10px] text-blue-400 font-semibold">rank {index + 1}</div>
                                                            <div className="text-xs text-gray-400">
                                                              {competitor.brand}
                                                            </div>
                                                            <div className="border-l border-gray-200 pl-2 text-xs text-gray-300">
                                                              {competitor.asin}
                                                            </div>
                                                          </div>
                                                        ))}
                                                      </div>
                                                    )}
                                                  </div>
                                                </div>
                                              </li>
                                            )
                                          })}
                                        </>
                                  })()
                                )}
                              </ul>
                            </div>
                          </div>
                        </div>
                      </div>
                    )
                    : ""
                }
              </DialogPanel>
            </TransitionChild>
          </div>
        </div>
      </Dialog>
    </Transition>
  )
}
