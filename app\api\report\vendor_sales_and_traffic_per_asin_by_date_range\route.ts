import { ErrorResponse } from "@/app/api/_models";
import { getServerApiHostUrl } from "@/utils/host";
import { NextRequest, NextResponse } from "next/server";

export type JoinedVendorReportByAsinAndDateResponse = any[];

export async function GET(
  request: NextRequest
): Promise<NextResponse<JoinedVendorReportByAsinAndDateResponse | ErrorResponse>> {
  const accessToken = request.headers.get("Authorization");
  if (!accessToken) {
    return NextResponse.json(
      { message: "Authorization header is missing" },
      { status: 400 }
    );
  }
  const accountId = request.nextUrl.searchParams.get("account_id")
  if (!accountId) {
    return NextResponse.json(
      { message: "account_id query is missing" },
      { status: 400 }
    );
  }
  const marketplaceId = request.nextUrl.searchParams.get("marketplace_id")
  if (!marketplaceId) {
    return NextResponse.json(
      { message: "marketplace_id query is missing" },
      { status: 400 }
    );
  }
  const startDate = request.nextUrl.searchParams.get("start_date")
  if (!startDate) {
    return NextResponse.json(
      { message: "start_date query is missing" },
      { status: 400 }
    );
  }
  const endDate = request.nextUrl.searchParams.get("end_date")
  if (!endDate) {
    return NextResponse.json(
      { message: "end_date query is missing" },
      { status: 400 }
    );
  }
  const joinedVendorReportByAsinAndDateResponse = await fetch(
    `${await getServerApiHostUrl()}/api/report/vendor_sales_and_traffic_per_asin_by_date_range?account_id=${accountId}&marketplace_id=${marketplaceId}&start_date=${startDate}&end_date=${endDate}`,
    {
      method: "GET",
      headers: {
        "Authorization": `Bearer ${accessToken}`,
        "Content-Type": "application/json",
      },
      cache: "force-cache",
      next: { revalidate: 600 }, // 10 minutes
    }
  ).then((res) => res.json());

  return NextResponse.json(joinedVendorReportByAsinAndDateResponse, { status: 200 });
}
