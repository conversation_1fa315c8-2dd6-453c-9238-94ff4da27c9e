import { ErrorResponse } from "@/app/api/_models";
import { getServerApiHostUrl } from "@/utils/host";
import { NextRequest, NextResponse } from "next/server";

export type HourlyInventoryResponse = {
  [asin: string]: {
    [timestamp: string]: number;
  };
};

export async function GET(
  request: NextRequest
): Promise<NextResponse<HourlyInventoryResponse | ErrorResponse>> {
  const accessToken = request.headers.get("Authorization");
  if (!accessToken) {
    return NextResponse.json(
      { message: "Authorization header is missing" },
      { status: 400 }
    );
  }
  const accountId = request.nextUrl.searchParams.get("account_id");
  if (!accountId) {
    return NextResponse.json(
      { message: "account_id query is missing" },
      { status: 400 }
    );
  }
  const marketplaceId = request.nextUrl.searchParams.get("marketplace_id");
  if (!marketplaceId) {
    return NextResponse.json(
      { message: "marketplace_id query is missing" },
      { status: 400 }
    );
  }
  
  const optimizationId = request.nextUrl.searchParams.get("optimization_id");
  if (!optimizationId) {
    return NextResponse.json(
      { message: "optimization_id query is required for inventory API" },
      { status: 400 }
    );
  }
  
  const apiUrl = `${await getServerApiHostUrl()}/api/hourly_report/hourly/inventory?account_id=${accountId}&marketplace_id=${marketplaceId}&optimization_id=${optimizationId}`;
  
  const hourlyInventoryResponse = await fetch(apiUrl, {
    method: "GET",
    headers: {
      "Authorization": `Bearer ${accessToken}`,
      "Content-Type": "application/json",
    },
    cache: "force-cache",
    next: { revalidate: 300 }, // 5 minutes
  }).then((res) => res.json());

  return NextResponse.json(hourlyInventoryResponse, { status: 200 });
} 