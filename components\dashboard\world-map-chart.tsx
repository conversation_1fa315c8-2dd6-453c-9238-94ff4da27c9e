import React from "react";
import {
  ComposableMap,
  Geographies,
  Geography,
  Graticule,
  Line,
  Point,
  Sphere
} from "react-simple-maps";

const geoUrl = "/map-features.json";

// https://www.worldatlas.com/articles/top-coffee-producing-countries.html
const highlighted = [
  "BRA",
  "VNM",
  "COL",
  "IDN",
  "ETH",
  "HND",
  "IND",
  "UGA",
  "MEX",
  "GTM",
  "PER",
  "NIC",
  "CHN",
  "CIV",
  "CRI",
  "KEN",
  "PNG",
  "TZA",
  "SLV",
  "ECU",
  "CMR",
  "LAO",
  "MDG",
  "GAB",
  "THA",
  "VEN",
  "DOM",
  "HTI",
  "COD",
  "RWA",
  "BDI",
  "PHL",
  "TGO",
  "GIN",
  "YEM",
  "CUB",
  "PAN",
  "BOL",
  "TLS",
  "CAF",
  "NGA",
  "GHA",
  "SLE",
  "AGO",
  "J<PERSON>",
  "<PERSON>Y",
  "MW<PERSON>",
  "TTO",
  "ZWE",
  "LBR"
];

function generateCircle(deg: number): Point[] {
  if (!deg)
    return [
      [-180, 0],
      [-90, 0],
      [0, 0],
      [90, 0],
      [180, 0]
    ];
  return new Array(361).fill(1).map((d, i) => {
    return [-180 + i, deg];
  });
}

const WorldMapChart = ({countryCode}: {countryCode:string}) => {
  return (
    <ComposableMap
      projectionConfig={{
        rotate: [-10, 0, 0],
        scale: 147
      }}
      width={800}
      height={390}
    >
      <Sphere stroke="#E4E5E6" id={""} fill="transparent" strokeWidth={0.5} />
      <Graticule stroke="#E4E5E6" strokeWidth={0.5} />
      <Geographies geography={geoUrl} stroke="#FFF" strokeWidth={0.5}>
        {({ geographies }) =>
          geographies.map((geo) => {
            // const isHighlighted = highlighted.indexOf(geo.id) !== -1;
            const country = countryCode === "CA"
              ? "CAN"
              : countryCode === "US"
                ? "USA"
                : countryCode === "MX"
                  ? "MEX"
                  : countryCode;
            const isHighlighted = geo.id === country;
            return (
              <Geography
                key={geo.rsmKey}
                geography={geo}
                fill={isHighlighted ? "#eff6ff" : "#4b5563"}
                // onClick={() => console.log(geo.properties.name)}
              />
            );
          })
        }
      </Geographies>
      {/* <Line coordinates={generateCircle(0)} stroke="#F53" strokeWidth={2} />
      <Line
        coordinates={generateCircle(23)}
        stroke="#776865"
        strokeWidth={1}
        strokeDasharray="5, 5"
      />
      <Line
        coordinates={generateCircle(-24)}
        stroke="#776865"
        strokeWidth={1}
        strokeDasharray="5, 5"
      /> */}
    </ComposableMap>
  );
};

export default WorldMapChart;
