"use client"

import { Fragment } from "react"
import { cn } from "@/utils/msc"
import { Listbox, Transition } from "@headlessui/react"
import { ChevronUpDownIcon } from "@heroicons/react/20/solid"
import { Check } from "@/components/ui/check"
import { useTranslations } from "next-intl"

type ComparisonOption = {
  name: string;
  type: string;
}

export const comparisonOptions = [
  {
    name: 'component.filter.compareWith.content.null',
    type: 'none'
  },
  {
    name: 'component.filter.compareWith.content.previousMonth',
    type: 'month'
  },
  {
    name: 'component.filter.compareWith.content.previousYear',
    type: 'year'
  },
  {
    name: 'component.filter.compareWith.content.customPeriod',
    type: 'custom'
  }
]

interface ComparisonSelectProps extends React.HTMLAttributes<HTMLDivElement> {
  listboxClassName?: string;
  selected: ComparisonOption;
  setSelected: (option: ComparisonOption) => void
}

export default function ComparisonSelect({
  className,
  listboxClassName,
  selected,
  setSelected,
  ...props
}: ComparisonSelectProps) {
  const t = useTranslations()
  
  return (
    <div
      className={cn(
        "relative w-40",
        className
      )}
      {...props}
    >
      <Listbox value={selected} onChange={setSelected}>
        <div className="relative">
          <Listbox.Button className="relative w-full cursor-pointer rounded-lg bg-white text-gray-600 py-2 pl-3 pr-10 text-left focus:outline-none focus-visible:border-indigo-500 focus-visible:ring-2 focus-visible:ring-white/75 focus-visible:ring-offset-2 focus-visible:ring-offset-orange-300 sm:text-sm">
            <span className="block truncate">{t(selected.name)}</span>
            <span className="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-2">
              <ChevronUpDownIcon
                className="h-5 w-5 text-gray-400"
                aria-hidden="true"
              />
            </span>
          </Listbox.Button>
          <Transition
            as={Fragment}
            leave="transition ease-in duration-100"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
            <Listbox.Options
              className={cn(
                "absolute z-[1] mt-1 max-h-60 w-full overflow-auto rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black/5 focus:outline-none sm:text-sm",
                listboxClassName
              )}
            >
              {comparisonOptions.map((option, optionIdx) => (
                <Listbox.Option
                  key={optionIdx}
                  className={({ active }) =>
                    `relative cursor-pointer select-none py-2 pl-4 pr-10 ${
                      active ? 'bg-blue-100/40 text-blue-900' : 'text-gray-600'
                    }`
                  }
                  value={option}
                >
                  {({ selected }) => (
                    <>
                      <span
                        className={`block truncate ${
                          selected ? 'font-medium' : 'font-normal'
                        }`}
                      >
                        {t(option.name)}
                      </span>
                      {selected ? (
                        <div className="absolute inset-y-0 right-0 flex items-center pr-3">
                          <div className="shrink-0 flex items-center justify-center w-5 h-5 rounded-full">
                            <Check className="w-4 h-4 stroke-blue-400" aria-hidden="true" />
                          </div>
                        </div>
                      ) : null}
                    </>
                  )}
                </Listbox.Option>
              ))}
            </Listbox.Options>
          </Transition>
        </div>
      </Listbox>
    </div>
  )
}
