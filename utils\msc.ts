import { type ClassValue, clsx } from "clsx"
import { RefObject } from "react"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

export function getDateDifference(date1: Date, date2: Date) {
  const oneDay = 24 * 60 * 60 * 1000 // Number of milliseconds in a day
  const diffDays = Math.round(Math.abs((date1.getTime() - date2.getTime()) / oneDay))
  return diffDays
}

export function formatDateTime(date: Date|string|null|undefined, delimiter: string = "-") {
  if (!date) return "-"; // Return a default value for null or undefined
  const d = typeof date === 'string' ? new Date(date) : date
  let month = '' + (d.getMonth() + 1)
  let day = '' + d.getDate()
  let year = d.getFullYear()
  let hour = '' + d.getHours()
  let minute = '' + d.getMinutes()
  let second = '' + d.getSeconds()

  if (month.length < 2) 
      month = '0' + month;
  if (day.length < 2) 
      day = '0' + day;
  if (hour.toString().length < 2) 
      hour = '0' + hour;
  if (minute.toString().length < 2) 
      minute = '0' + minute;
  if (second.toString().length < 2) 
      second = '0' + second;

  return [year, month, day].join(delimiter) + " " + [hour, minute, second].join(":");
}

export function formatLocalDateTime(date: Date|string|null|undefined, delimiter: string = "-") {
  if (!date) return "-";
  
  let utcDate: Date;
  if (typeof date === 'string') {    
    if (!date.includes('Z') && !date.includes('+') && !date.includes('-', 10)) {
      utcDate = new Date(date + 'Z'); 
    } else {
      utcDate = new Date(date);
    }
  } else {
    utcDate = date;
  } 
  
  const year = utcDate.getFullYear();
  const month = String(utcDate.getMonth() + 1).padStart(2, '0');
  const day = String(utcDate.getDate()).padStart(2, '0');
  const hour = String(utcDate.getHours()).padStart(2, '0');
  const minute = String(utcDate.getMinutes()).padStart(2, '0');
  const second = String(utcDate.getSeconds()).padStart(2, '0');

  return [year, month, day].join(delimiter) + " " + [hour, minute, second].join(":");
}

export function formatDate(date: Date|string|null|undefined, delimiter: string = "-") {
  if (!date) return "-"; // Return a default value for null or undefined
  const d = typeof date === 'string' ? new Date(date) : date
  let month = '' + (d.getMonth() + 1)
  let day = '' + d.getDate()
  let year = d.getFullYear()

  if (month.length < 2) 
      month = '0' + month;
  if (day.length < 2) 
      day = '0' + day;

  return [year, month, day].join(delimiter);
}

export function formatUTCDate(date: Date|string|null|undefined, delimiter: string = "-") {
  if (!date) return "-"; // Return a default value for null or undefined
  const d = typeof date === 'string' ? new Date(date) : date
  let month = '' + (d.getUTCMonth() + 1)
  let day = '' + (d.getUTCDate())
  let year = d.getUTCFullYear()

  if (month.length < 2) 
      month = '0' + month;
  if (day.length < 2) 
      day = '0' + day;

  return [year, month, day].join(delimiter);
}

export function formatHour(hour: number) {
  if (hour < 0 || hour > 23) {
    console.error("Hour must be between 0 and 23");
  }
  return hour.toString().padStart(2, '0') + ':00:00';
}

export function formatISODateTime(date: Date|string|null|undefined): string {
  if (!date) return "-";
  
  const d = typeof date === 'string' ? new Date(date) : date;
  
  // Get the ISO string (which will be in the format YYYY-MM-DDTHH:mm:ss.sssZ)
  const isoString = d.toISOString();
  
  // Convert the Z timezone to +00:00 format
  return isoString.replace(/\.\d{3}Z$/, '+00:00');
}

export function formatLocalDateAsISO(date: Date | string | null | undefined): string {
  if (!date) return "-";
  const d = typeof date === 'string' ? new Date(date) : date;
  const year = d.getFullYear();
  const month = String(d.getMonth() + 1).padStart(2, '0');
  const day = String(d.getDate()).padStart(2, '0');
  return `${year}-${month}-${day}`;
}


export function formatISODate(date: Date|string|null|undefined): string {
  if (!date) return "-";
  
  const d = typeof date === 'string' ? new Date(date) : date;
  
  // Get the ISO string (which will be in the format YYYY-MM-DD)
  const isoString = d.toISOString();
  
  return isoString.split('T')[0];
}


export const currencyFormat = new Intl.NumberFormat("en-US", {
  style: "currency",
  currency: "USD",
  maximumFractionDigits: 2
})

export const integerCurrencyFormat = new Intl.NumberFormat("en-US", {
  style: "currency",
  currency: "USD",
  maximumFractionDigits: 0
})

export const colorSetList = [
  {
    "bg": "bg-red-100",
    "bg_hex": "#FEE2E2",
    "hoverbg": "hover:bg-red-200",
    "border": "border-red-500",
    "border_hex": "#EF4444",
    "text": "text-red-500",
    "text_hex": "#F87171",
    "hovertext": "hover:text-red-600"
  },
  {
    "bg": "bg-orange-100",
    "bg_hex": "#ffedd5",
    "hoverbg": "hover:bg-orange-200",
    "border": "border-orange-500",
    "border_hex": "#F59E0B",
    "text": "text-orange-500",
    "text_hex": "#FBBF24",
    "hovertext": "hover:text-orange-600"
  },
  {
    "bg": "bg-yellow-100",
    "bg_hex": "#fef9c3",
    "hoverbg": "hover:bg-yellow-200",
    "border": "border-yellow-500",
    "border_hex": "#FCD34D",
    "text": "text-yellow-500",
    "text_hex": "#FCD34D",
    "hovertext": "hover:text-yellow-600"
  },
  {
    "bg": "bg-lime-100",
    "bg_hex": "#ECFCCB",
    "hoverbg": "hover:bg-lime-200",
    "border": "border-lime-500",
    "border_hex": "#84CC16",
    "text": "text-lime-500",
    "text_hex": "#84CC16",
    "hovertext": "hover:text-lime-600"
  },
  {
    "bg": "bg-emerald-100",
    "bg_hex": "#D1FAE5",
    "hoverbg": "hover:bg-emerald-200",
    "border": "border-emerald-500",
    "border_hex": "#10b981",
    "text": "text-emerald-500",
    "text_hex": "#10b981",
    "hovertext": "hover:text-emerald-600"
  },
  {
    "bg": "bg-cyan-100",
    "bg_hex": "#cffafe",
    "hoverbg": "hover:bg-cyan-200",
    "border": "border-cyan-500",
    "border_hex": "#06b6d4",
    "text": "text-cyan-500",
    "text_hex": "#06b6d4",
    "hovertext": "hover:text-cyan-600"
  },
  {
    "bg": "bg-indigo-100",
    "bg_hex": "#E0E7FF",
    "hoverbg": "hover:bg-indigo-200",
    "border": "border-indigo-500",
    "border_hex": "#6366f1",
    "text": "text-indigo-500",
    "text_hex": "#6366f1",
    "hovertext": "hover:text-indigo-600"
  }
]

function fallbackCopyTextToClipboard(text: string) {
  var textArea = document.createElement("textarea");
  textArea.value = text;
  
  // Avoid scrolling to bottom
  textArea.style.top = "0";
  textArea.style.left = "0";
  textArea.style.position = "fixed";

  document.body.appendChild(textArea);
  textArea.focus();
  textArea.select();

  try {
    var successful = document.execCommand('copy');
    var msg = successful ? 'successful' : 'unsuccessful';
    console.log('Fallback: Copying text command was ' + msg);
  } catch (err) {
    console.error('Fallback: Oops, unable to copy', err);
  }

  document.body.removeChild(textArea);
}
export function copyTextToClipboard(text: string) {
  if (!navigator.clipboard) {
    fallbackCopyTextToClipboard(text);
    return;
  }
  navigator.clipboard.writeText(text).then(function() {
    console.log('Async: Copying to clipboard was successful!');
  }, function(err) {
    console.error('Async: Could not copy text: ', err);
  });
}

export function stringToColor(str: string) {
  let hash = 0;
  str.split('').forEach(char => {
    hash = char.charCodeAt(0) + ((hash << 5) - hash)
  })
  let color = '#'
  for (let i = 0; i < 3; i++) {
    const value = (hash >> (i * 8)) & 0xff
    color += value.toString(16).padStart(2, '0')
  }
  return color
}

export function hexToComplimentary(hex: any) {
  // Convert hex to rgb
  var rgb = 'rgb(' + (hex = hex.replace('#', '')).match(new RegExp('(.{' + hex.length/3 + '})', 'g')).map(function(l: any) { return parseInt(hex.length%2 ? l+l : l, 16); }).join(',') + ')' as any;

  // Get array of RGB values
  rgb = rgb.replace(/[^\d,]/g, '').split(',');

  var r = rgb[0], g = rgb[1], b = rgb[2];

  // Convert RGB to HSL
  r /= 255.0;
  g /= 255.0;
  b /= 255.0;
  var max = Math.max(r, g, b);
  var min = Math.min(r, g, b);
  var h, s, l = (max + min) / 2.0 as number;

  if(max == min) {
      h = s = 0;  //achromatic
  } else {
      var d = max - min;
      s = (l > 0.5 ? d / (2.0 - max - min) : d / (max + min));

      if(max == r && g >= b) {
          h = 1.0472 * (g - b) / d ;
      } else if(max == r && g < b) {
          h = 1.0472 * (g - b) / d + 6.2832;
      } else if(max == g) {
          h = 1.0472 * (b - r) / d + 2.0944;
      } else if(max == b) {
          h = 1.0472 * (r - g) / d + 4.1888;
      }
  }

  h = h || 0;
  h = h / 6.2832 * 360.0 + 0;

  // Shift hue to opposite side of wheel and convert to [0-1] value
  h+= 180;
  if (h > 360) { h -= 360; }
  h /= 360;

  if(s === 0){
      r = g = b = l; // achromatic
  } else {
      var hue2rgb = function hue2rgb(p: any, q: any, t: any){
          if(t < 0) t += 1;
          if(t > 1) t -= 1;
          if(t < 1/6) return p + (q - p) * 6 * t;
          if(t < 1/2) return q;
          if(t < 2/3) return p + (q - p) * (2/3 - t) * 6;
          return p;
      };

      var q = l < 0.5 ? l * (1 + s) : l + s - l * s;
      var p = 2 * l - q;

      r = hue2rgb(p, q, h + 1/3);
      g = hue2rgb(p, q, h);
      b = hue2rgb(p, q, h - 1/3);
  }

  r = Math.round(r * 255);
  g = Math.round(g * 255); 
  b = Math.round(b * 255);

  // Convert r b and g values to hex
  rgb = b | (g << 8) | (r << 16); 
  return "#" + (0x1000000 | rgb).toString(16).substring(1);
}

export function hexToRgbA(hex: string, opacity: number) {
  var c;
  if(/^#([A-Fa-f0-9]{3}){1,2}$/.test(hex)){
      c= hex.substring(1).split('');
      if(c.length== 3){
          c= [c[0], c[0], c[1], c[1], c[2], c[2]];
      }
      c= '0x'+c.join('');
      return 'rgba('+[(Number(c)>>16)&255, (Number(c)>>8)&255, Number(c)&255].join(',')+','+opacity+')';
  }
  throw new Error('Bad Hex');
}

export function getRegionName(regionCode: string) {
  return {
    "AP": "Asia Pacific",
    "EU": "Europe",
    "NA": "North America",
    "SA": "South America",
    "AF": "Africa",
    "OC": "Oceania",
    "AN": "Antarctica"
  }[regionCode]
}

export const useScrollToBottom = (ref: RefObject<HTMLDivElement>) => {
  const scrollToBottom = () => {
    if (ref.current) {
        ref.current.style.scrollBehavior = 'smooth';
        ref.current.scrollTop = ref.current.scrollHeight;
    }
  };
  return {
    scrollToBottom,
  }
}

export const downloadTextFile = (content: string, filename: string) => {
  const blob = new Blob([content], { type: 'text/plain' });
  const url = URL.createObjectURL(blob);
  const a = document.createElement('a');
  a.href = url;
  a.download = filename;
  document.body.appendChild(a); // Append to body to make it clickable
  a.click();
  document.body.removeChild(a); // Clean up
  URL.revokeObjectURL(url);
}