"use client"

import { Fragment, useMemo } from "react"
import { cn } from "@/utils/msc"
import { Listbox, Transition } from "@headlessui/react"
import { ChevronUpDownIcon } from "@heroicons/react/20/solid"
import { Check } from "@/components/ui/check"

type LegendOption = {
  id: number;
  name: string;
  category: string;
  type: string;
  color: string;
}

interface VendorLegendSelectProps extends React.HTMLAttributes<HTMLDivElement> {
  listboxClassName?: string;
  compared?: boolean;
  legendOptions: LegendOption[];
  selected: LegendOption[];
  setSelected: (option: LegendOption[]) => void
}

export default function VendorLegendSelect({
  className,
  listboxClassName,
  compared = false,
  legendOptions,
  selected,
  setSelected,
  ...props
}: VendorLegendSelectProps) {
  const selectedOptions = useMemo(() => {
    return selected.map((option) => legendOptions.find((legend) => legend.id === option.id))
  }, [legendOptions])
  return (
    <div
      className={cn(
        "relative",
        className
      )}
      {...props}
    >
      <Listbox value={selected} onChange={(target) => target.length > 0 && setSelected(target)} multiple>
        <div className="relative min-w-[260px] max-w-full w-fit">
          <Listbox.Button className="relative w-full inline-flex items-center gap-x-6 cursor-pointer rounded-lg bg-white text-gray-600 py-2 pl-3 pr-10 text-left border border-gray-100 focus:outline-none focus-visible:border-indigo-500 focus-visible:ring-2 focus-visible:ring-white/75 focus-visible:ring-offset-2 focus-visible:ring-offset-orange-300 sm:text-sm truncate">
            {selectedOptions.map((option, idx) => {
              return option && (
                <div key={idx} className="flex items-center gap-x-3 flex-shrink-0">
                  <div
                    className={cn(
                      "relative w-6 h-0 border",
                      option.type.includes('compared') ? 'border-dashed' : 'border-solid'
                    )}
                    style={{ borderColor: option.color }}
                  >
                    <div
                      className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-1.5 h-1.5 rounded-full"
                      style={{ backgroundColor: option.color }}
                    ></div>
                  </div>
                  {option.name}
                </div>
              )
            })}
            <span className="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-2 bg-white">
              <ChevronUpDownIcon
                className="h-5 w-5 text-gray-400"
                aria-hidden="true"
              />
            </span>
          </Listbox.Button>
          <Transition
            as={Fragment}
            leave="transition ease-in duration-100"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
            <Listbox.Options
              className={cn(
                "z-[1] absolute flex flex-col items-start divide-y divide-gray-100 mt-1 max-h-80 w-fit overflow-auto rounded-md bg-white text-sm shadow-lg ring-1 ring-black/5 focus:outline-none",
                listboxClassName
              )}
            >
              <div className="w-full py-2">
                <div className="py-2 px-4 text-xs text-gray-400 font-semibold">Total Performance</div>
                {legendOptions.filter(option => option.category === 'total').filter((option) => {
                  return (compared
                    ? true
                    : !option.type.includes('compared'))
                  }).map((option) => (
                  <Listbox.Option
                    key={option.id}
                    className={({ active }) =>
                      `relative cursor-pointer select-none py-2 pl-4 pr-10 ${
                        active ? 'bg-blue-100/40 text-blue-900' : 'text-gray-600'
                      }`
                    }
                    value={option}
                  >
                    {({ selected }) => (
                      <>
                        <div
                          className={`flex items-center gap-x-3 truncate ${
                            selected ? 'font-medium' : 'font-normal'
                          }`}
                        >
                          <div
                            className={cn(
                              "relative w-6 h-0 border",
                              option.type.includes('compared') ? 'border-dashed' : 'border-solid'
                            )}
                            style={{ borderColor: option.color }}
                          >
                            <div
                              className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-1.5 h-1.5 rounded-full"
                              style={{ backgroundColor: option.color }}
                            ></div>
                          </div>
                          {option.name}
                        </div>
                        {selected ? (
                          <div className="absolute inset-y-0 right-0 flex items-center pr-3">
                            <div className="shrink-0 flex items-center justify-center w-5 h-5 rounded-full">
                              <Check className="w-4 h-4 stroke-blue-400" aria-hidden="true" />
                            </div>
                          </div>
                        ) : null}
                      </>
                    )}
                  </Listbox.Option>
                ))}
              </div>
              <div className="relative w-full flex flex-col xl:flex-row items-start divide-y xl:divide-x divide-gray-100">
                <div className="w-full xl:w-1/2 py-2">
                  <div className="flex items-center gap-x-2 py-2 px-4 text-xs text-gray-400 font-semibold">
                    <div className="py-0.25 px-1.5 border border-gray-400 bg-white text-gray-500 text-[10px] rounded-md">SP</div>
                    Ad Performance
                  </div>
                  {legendOptions.filter(option => option.category === 'sp').filter((option) => {
                    return (compared
                      ? true
                      : !option.type.includes('compared'))
                    }).map((option) => (
                    <Listbox.Option
                      key={option.id}
                      className={({ active }) =>
                        `relative cursor-pointer select-none py-2 pl-4 pr-10 ${
                          active ? 'bg-blue-100/40 text-blue-900' : 'text-gray-600'
                        }`
                      }
                      value={option}
                    >
                      {({ selected }) => (
                        <>
                          <div
                            className={`flex items-center gap-x-3 truncate ${
                              selected ? 'font-medium' : 'font-normal'
                            }`}
                          >
                            <div
                              className={cn(
                                "relative w-6 h-0 border",
                                option.type.includes('compared') ? 'border-dashed' : 'border-solid'
                              )}
                              style={{ borderColor: option.color }}
                            >
                              <div
                                className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-1.5 h-1.5 rounded-full"
                                style={{ backgroundColor: option.color }}
                              ></div>
                            </div>
                            {option.name}
                          </div>
                          {selected ? (
                            <div className="absolute inset-y-0 right-0 flex items-center pr-3">
                              <div className="shrink-0 flex items-center justify-center w-5 h-5 rounded-full">
                                <Check className="w-4 h-4 stroke-blue-400" aria-hidden="true" />
                              </div>
                            </div>
                          ) : null}
                        </>
                      )}
                    </Listbox.Option>
                  ))}
                </div>
                <div className="w-full xl:w-1/2 py-2">
                  <div className="flex items-center gap-x-2 py-2 px-4 text-xs text-gray-400 font-semibold">
                    <div className="py-0.25 px-1.5 border border-gray-400 bg-white text-gray-500 text-[10px] rounded-md">SD</div>
                    Ad Performance
                  </div>
                  {legendOptions.filter(option => option.category === 'sd').filter((option) => {
                    return (compared
                      ? true
                      : !option.type.includes('compared'))
                    }).map((option) => (
                    <Listbox.Option
                      key={option.id}
                      className={({ active }) =>
                        `relative cursor-pointer select-none py-2 pl-4 pr-10 ${
                          active ? 'bg-blue-100/40 text-blue-900' : 'text-gray-600'
                        }`
                      }
                      value={option}
                    >
                      {({ selected }) => (
                        <>
                          <div
                            className={`flex items-center gap-x-3 truncate ${
                              selected ? 'font-medium' : 'font-normal'
                            }`}
                          >
                            <div
                              className={cn(
                                "relative w-6 h-0 border",
                                option.type.includes('compared') ? 'border-dashed' : 'border-solid'
                              )}
                              style={{ borderColor: option.color }}
                            >
                              <div
                                className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-1.5 h-1.5 rounded-full"
                                style={{ backgroundColor: option.color }}
                              ></div>
                            </div>
                            {option.name}
                          </div>
                          {selected ? (
                            <div className="absolute inset-y-0 right-0 flex items-center pr-3">
                              <div className="shrink-0 flex items-center justify-center w-5 h-5 rounded-full">
                                <Check className="w-4 h-4 stroke-blue-400" aria-hidden="true" />
                              </div>
                            </div>
                          ) : null}
                        </>
                      )}
                    </Listbox.Option>
                  ))}
                </div>
              </div>
            </Listbox.Options>
          </Transition>
        </div>
      </Listbox>
    </div>
  )
}
