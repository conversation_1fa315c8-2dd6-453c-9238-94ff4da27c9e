import React from 'react'
import { render, screen } from '@testing-library/react'
import '@testing-library/jest-dom'
import { describe, test, expect, jest } from '@jest/globals'

jest.mock('next/link', () => ({
  __esModule: true,
  default: ({ href, children, ...rest }: any) => (
    <a href={href} {...rest}>{children}</a>
  )
}))

import CustomLink from '@/components/custom-link'

describe('components/CustomLink', () => {
  test('renders internal link as anchor', () => {
    render(<CustomLink href="/about">About</CustomLink>)
    const a = screen.getByRole('link', { name: /about/i }) as HTMLAnchorElement
    expect(a).toBeInTheDocument()
    expect(a.getAttribute('href') || '').toMatch(/\/about$/)
  })

  test('renders external link with target and rel', () => {
    render(<CustomLink href="https://example.com">Docs</CustomLink>)
    const a = screen.getByRole('link', { name: /docs/i }) as HTMLAnchorElement
    expect(a).toBeInTheDocument()
    expect(a).toHaveAttribute('target', '_blank')
    expect(a).toHaveAttribute('rel', 'noopener noreferrer')
  })
}) 