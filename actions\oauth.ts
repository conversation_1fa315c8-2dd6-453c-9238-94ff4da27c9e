"use server";

import { getServerApiHostUrl } from "@/utils/host";

export async function getAdLoginLink() {
  const oauthLoginResponse = await fetch(
    `${await getServerApiHostUrl()}/api/oauth/login-link/ad?region=NA`,
    {
      method: "GET",
      cache: "no-cache",
    }
  ).then((res) => res.json());

  return oauthLoginResponse;
}

export async function getExAdLoginLink(regionCode: string) {
  const oauthLoginResponse = await fetch(
    `${await getServerApiHostUrl()}/api/ex/oauth/login-link/ad?region=${regionCode}`,
    {
      method: "GET",
      cache: "no-cache",
    }
  ).then((res) => res.json());

  return oauthLoginResponse;
}

export async function getSpLoginLink(countryCode: string) {
  const oauthLoginResponse = await fetch(
    `${await getServerApiHostUrl()}/api/oauth/login-link/sp?country=${countryCode}&state=${countryCode}_seller`,
    {
      method: "GET",
      cache: "no-cache",
    }
  ).then((res) => res.json());

  return oauthLoginResponse;
}

export async function getExSpLoginLink(countryCode: string) {
  const oauthLoginResponse = await fetch(
    `${await getServerApiHostUrl()}/api/ex/oauth/login-link/sp?country=${countryCode}&state=${countryCode}_seller`,
    {
      method: "GET",
      cache: "no-cache",
    }
  ).then((res) => res.json());

  return oauthLoginResponse;
}

export async function getSpVendorLoginLink(countryCode: string) {
  const oauthLoginResponse = await fetch(
    `${await getServerApiHostUrl()}/api/oauth/login-link/sp-vendor?country=${countryCode}&state=${countryCode}_vendor`,
    {
      method: "GET",
      cache: "no-cache",
    }
  ).then((res) => res.json());

  return oauthLoginResponse;
}

export async function getExSpVendorLoginLink(countryCode: string) {
  const oauthLoginResponse = await fetch(
    `${await getServerApiHostUrl()}/api/ex/oauth/login-link/sp-vendor?country=${countryCode}&state=${countryCode}_vendor`,
    {
      method: "GET",
      cache: "no-cache",
    }
  ).then((res) => res.json());

  return oauthLoginResponse;
}

export type OAuthIntegrateLwaResponse = {
  cognito_sub: string;
  id: number;
  profiles: any[];
};

// TODO: region selectable (currently NA fixed)
export async function integrateLwaAd({
  lwaCode,
  accessToken,
}: {
  lwaCode: string;
  accessToken: string;
}): Promise<OAuthIntegrateLwaResponse> {
  const oauthIntegrateLwaResponse = await fetch(
    `${await getServerApiHostUrl()}/api/oauth/integrate-lwa/ad?region=NA`,
    {
      method: "POST",
      headers: {
        "X-Lwa-Oauth-Code": lwaCode,
        "Authorization": `Bearer ${accessToken}`,
      },
      cache: "no-cache",
    }
  ).then((res) => res.json());

  return oauthIntegrateLwaResponse;
}
export async function integrateExLwaAd({
  lwaCode,
  accessToken,
}: {
  lwaCode: string;
  accessToken: string;
}): Promise<OAuthIntegrateLwaResponse> {
  const oauthIntegrateLwaResponse = await fetch(
    `${await getServerApiHostUrl()}/api/ex/oauth/integrate-lwa/ad?region=NA`,
    {
      method: "POST",
      headers: {
        "X-Lwa-Oauth-Code": lwaCode,
        "Authorization": `Bearer ${accessToken}`,
      },
      cache: "no-cache",
    }
  ).then((res) => res.json());

  return oauthIntegrateLwaResponse;
}

export async function integrateLwaSp({
  country,
  sellingPartnerId,
  lwaOAuthCode,
  accessToken,
}: {
  country: string;
  sellingPartnerId: string;
  lwaOAuthCode: string;
  accessToken: string;
}): Promise<OAuthIntegrateLwaResponse> {
  const oauthIntegrateLwaResponse = await fetch(
    `${await getServerApiHostUrl()}/api/oauth/integrate-lwa/sp?country=${country}&selling_partner_id=${sellingPartnerId}`,
    {
      method: "POST",
      headers: {
        "X-Lwa-Oauth-Code": lwaOAuthCode,
        "Authorization": `Bearer ${accessToken}`,
      },
      cache: "no-cache",
    }
  ).then((res) => res.json());

  return oauthIntegrateLwaResponse;
}

export async function integrateExLwaSp({
  country,
  sellingPartnerId,
  lwaOAuthCode,
  accessToken,
}: {
  country: string;
  sellingPartnerId: string;
  lwaOAuthCode: string;
  accessToken: string;
}): Promise<OAuthIntegrateLwaResponse> {
  const oauthIntegrateLwaResponse = await fetch(
    `${await getServerApiHostUrl()}/api/ex/oauth/integrate-lwa/sp?country=${country}&selling_partner_id=${sellingPartnerId}`,
    {
      method: "POST",
      headers: {
        "X-Lwa-Oauth-Code": lwaOAuthCode,
        "Authorization": `Bearer ${accessToken}`,
      },
      cache: "no-cache",
    }
  ).then((res) => res.json());

  return oauthIntegrateLwaResponse;
}

export async function integrateLwaSpVendor({
  country,
  sellingPartnerId,
  lwaOAuthCode,
  accessToken,
}: {
  country: string;
  sellingPartnerId: string;
  lwaOAuthCode: string;
  accessToken: string;
}): Promise<OAuthIntegrateLwaResponse> {
  const oauthIntegrateLwaResponse = await fetch(
    `${await getServerApiHostUrl()}/api/oauth/integrate-lwa/sp-vendor?country=${country}&selling_partner_id=${sellingPartnerId}`,
    {
      method: "POST",
      headers: {
        "X-Lwa-Oauth-Code": lwaOAuthCode,
        "Authorization": `Bearer ${accessToken}`,
      },
      cache: "no-cache",
    }
  ).then((res) => res.json());

  return oauthIntegrateLwaResponse;
}

export async function integrateExLwaSpVendor({
  country,
  sellingPartnerId,
  lwaOAuthCode,
  accessToken,
}: {
  country: string;
  sellingPartnerId: string;
  lwaOAuthCode: string;
  accessToken: string;
}): Promise<OAuthIntegrateLwaResponse> {
  const oauthIntegrateLwaResponse = await fetch(
    `${await getServerApiHostUrl()}/api/ex/oauth/integrate-lwa/sp-vendor?country=${country}&selling_partner_id=${sellingPartnerId}`,
    {
      method: "POST",
      headers: {
        "X-Lwa-Oauth-Code": lwaOAuthCode,
        "Authorization": `Bearer ${accessToken}`,
      },
      cache: "no-cache",
    }
  ).then((res) => res.json());

  return oauthIntegrateLwaResponse;
}
