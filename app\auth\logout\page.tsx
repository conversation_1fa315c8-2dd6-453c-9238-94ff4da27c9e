"use client";

import { useEffect } from "react"
import { signOut } from "next-auth/react"
import { getEnvValue } from "@/utils/host"

export default async function LogOutWithRedirectionPage() {
  useEffect(() => {
    const signOutUser = async () => {
      const authDomain = await getEnvValue('AUTH_DOMAIN')
      const cognitoClientId = await getEnvValue('COGNITO_CLIENT_ID')
      const nextAuthUrl = await getEnvValue('NEXTAUTH_URL')
      await signOut({
        redirect: false
      }).then(() => {
        window.location.href = `https://${authDomain}/logout?client_id=${cognitoClientId}&logout_uri=${nextAuthUrl}/auth/login&redirect_uri=${nextAuthUrl}/auth/login&response_type=code`
      });
    };
    signOutUser();
  }, []);

  return (
    <div className="flex items-center justify-center overflow-hidden relative w-full h-full">
      <svg className="animate-spin h-10 w-10 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
      </svg>
    </div>
  );
}