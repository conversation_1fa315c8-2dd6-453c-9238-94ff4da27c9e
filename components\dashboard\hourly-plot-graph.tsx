"use client"

import Plot from "react-plotly.js"
import { formatHour } from "@/utils/msc"
import { getCurrencySymbol, getCurrencySymbolUnicode } from "@/utils/currency"

type GraphData = {
  hour: number;
  sales: number;
  pageviews: number;
  ad_cost: number;
  ad_sales: number;
  ad_sales_same_sku: number;
  ad_orders: number;
  clicks: number;
  impressions: number;
  sd_cost: number;
  sd_sales: number;
  sd_sales_same_sku: number;
  sd_clicks: number;
  sd_impressions: number;
}

export default function HourlyPlotGraph({ selectedLegend, data, currencyCode = 'USD' }: { selectedLegend: any[], data: GraphData[], currencyCode?: string }) {
  const dataSet = []
  const currencySymbol = getCurrencySymbol(currencyCode)
  const currencySymbolUnicode = getCurrencySymbolUnicode(currencyCode)
  const currencyPrefix = currencySymbolUnicode === currencySymbol ? `${currencySymbol} ` : `${currencySymbolUnicode} `
  const salesSelected = selectedLegend.find((legend) => legend.type === 'sales')
  salesSelected && dataSet.push({
    x: data.map((d) => formatHour(d.hour)),
    y: data.map((d) => d.sales),
    customdata: data.map((d) => formatHour(d.hour)),
    type: 'scatter',
    mode: 'lines+markers',
    xperiodalignment: 'start',
    hovertemplate: '%{fullData.name} : ' + currencyPrefix + '%{y:,.0f} (%{customdata})<extra></extra>',
    marker: {color: '#a855f7'},
    name: salesSelected.name,
  })
  const pageViewsSelected = selectedLegend.find((legend) => legend.type === 'pageviews')
  pageViewsSelected && dataSet.push({
    x: data.map((d) => formatHour(d.hour)),
    y: data.map((d) => d.pageviews),
    customdata: data.map((d) => formatHour(d.hour)),
    yaxis: 'y2',
    type: 'scatter',
    mode: 'lines+markers',
    xperiodalignment: 'start',
    hovertemplate: '%{fullData.name} : %{y} (%{customdata})<extra></extra>',
    marker: {color: '#ec4899'},
    name: pageViewsSelected.name,
  })
  const adCostSelected = selectedLegend.find((legend) => legend.type === 'ad-cost')
  adCostSelected && dataSet.push({
    x: data.map((d) => formatHour(d.hour)),
    y: data.map((d) => d.ad_cost),
    customdata: data.map((d) => formatHour(d.hour)),
    type: 'scatter',
    mode: 'lines+markers',
    xperiodalignment: 'start',
    hovertemplate: '%{fullData.name} : ' + currencyPrefix + '%{y:,.0f} (%{customdata})<extra></extra>',
    marker: {color: '#1d4ed8'},
    name: adCostSelected.name,
  })
  const adSalesSelected = selectedLegend.find((legend) => legend.type === 'ad-sales')
  adSalesSelected && dataSet.push({
    x: data.map((d) => formatHour(d.hour)),
    y: data.map((d) => d.ad_sales),
    customdata: data.map((d) => formatHour(d.hour)),
    type: 'scatter',
    mode: 'lines+markers',
    xperiodalignment: 'start',
    hovertemplate: '%{fullData.name} : ' + currencyPrefix + '%{y:,.0f} (%{customdata})<extra></extra>',
    marker: {color: '#6366f1'},
    name: adSalesSelected.name,
  })
  const adSalesSameSkuSelected = selectedLegend.find((legend) => legend.type === 'ad-sales-same-sku')
  adSalesSameSkuSelected && dataSet.push({
    x: data.map((d) => formatHour(d.hour)),
    y: data.map((d) => d.ad_sales_same_sku),
    customdata: data.map((d) => formatHour(d.hour)),
    type: 'scatter',
    mode: 'lines+markers',
    xperiodalignment: 'start',
    hovertemplate: '%{fullData.name} : ' + currencyPrefix + '%{y:,.0f} (%{customdata})<extra></extra>',
    marker: {color: '#66d9e8'},
    name: adSalesSameSkuSelected.name,
  })
  const clicksSelected = selectedLegend.find((legend) => legend.type === 'clicks')
  clicksSelected && dataSet.push({
    x: data.map((d) => formatHour(d.hour)),
    y: data.map((d) => d.clicks),
    customdata: data.map((d) => formatHour(d.hour)),
    yaxis: 'y2',
    type: 'scatter',
    mode: 'lines+markers',
    xperiodalignment: 'start',
    hovertemplate: '%{fullData.name} : %{y} (%{customdata})<extra></extra>',
    marker: {color: '#f97316'},
    name: clicksSelected.name,
  })
  const impressionsSelected = selectedLegend.find((legend) => legend.type === 'impressions')
  impressionsSelected && dataSet.push({
    x: data.map((d) => formatHour(d.hour)),
    y: data.map((d) => d.impressions),
    customdata: data.map((d) => formatHour(d.hour)),
    yaxis: 'y2',
    type: 'scatter',
    mode: 'lines+markers',
    xperiodalignment: 'start',
    hovertemplate: '%{fullData.name} : %{y} (%{customdata})<extra></extra>',
    marker: {color: '#c53030'},
    name: impressionsSelected.name,
  })
  // const roasSelected = selectedLegend.find((legend) => legend.type === 'roas')
  // roasSelected && dataSet.push({
  //   x: data.map((d) => formatHour(d.hour)),
  //   y: data.map((d) => d.roas),
  //   customdata: data.map((d) => formatHour(d.hour)),
  //   yaxis: 'y2',
  //   type: 'scatter',
  //   mode: 'lines+markers',
  //   xperiodalignment: 'start',
  //   hovertemplate: '%{y}% (%{customdata})',
  //   marker: {color: '#22c55e'},
  //   name: roasSelected.name,
  // })
  const sdCostSelected = selectedLegend.find((legend) => legend.type === 'sd-cost')
  sdCostSelected && dataSet.push({
    x: data.map((d) => formatHour(d.hour)),
    y: data.map((d) => d.sd_cost),
    customdata: data.map((d) => formatHour(d.hour)),
    type: 'scatter',
    mode: 'lines+markers',
    xperiodalignment: 'start',
    hovertemplate: '%{fullData.name} : ' + currencyPrefix + '%{y:,.0f} (%{customdata})<extra></extra>',
    marker: {color: '#d8a71d'},
    name: sdCostSelected.name,
  })
  const sdSalesSelected = selectedLegend.find((legend) => legend.type === 'sd-sales')
  sdSalesSelected && dataSet.push({
    x: data.map((d) => formatHour(d.hour)),
    y: data.map((d) => d.sd_sales),
    customdata: data.map((d) => formatHour(d.hour)),
    type: 'scatter',
    mode: 'lines+markers',
    xperiodalignment: 'start',
    hovertemplate: '%{fullData.name} : ' + currencyPrefix + '%{y:,.0f} (%{customdata})<extra></extra>',
    marker: {color: '#f1ee63'},
    name: sdSalesSelected.name,
  })
  const sdClicksSelected = selectedLegend.find((legend) => legend.type === 'sd-clicks')
  sdClicksSelected && dataSet.push({
    x: data.map((d) => formatHour(d.hour)),
    y: data.map((d) => d.sd_clicks),
    customdata: data.map((d) => formatHour(d.hour)),
    yaxis: 'y2',
    type: 'scatter',
    mode: 'lines+markers',
    xperiodalignment: 'start',
    hovertemplate: '%{fullData.name} : %{y} (%{customdata})<extra></extra>',
    marker: {color: '#169cf9'},
    name: sdClicksSelected.name,
  })
  const sdImpressionsSelected = selectedLegend.find((legend) => legend.type === 'sd-impressions')
  sdImpressionsSelected && dataSet.push({
    x: data.map((d) => formatHour(d.hour)),
    y: data.map((d) => d.sd_impressions),
    customdata: data.map((d) => formatHour(d.hour)),
    yaxis: 'y2',
    type: 'scatter',
    mode: 'lines+markers',
    xperiodalignment: 'start',
    hovertemplate: '%{fullData.name} : %{y} (%{customdata})<extra></extra>',
    marker: {color: '#30c5c5'},
    name: sdImpressionsSelected.name,
  })
  const sdSalesSameSkuSelected = selectedLegend.find((legend) => legend.type === 'sd-sales-same-sku')
  sdSalesSameSkuSelected && dataSet.push({
    x: data.map((d) => formatHour(d.hour)),
    y: data.map((d) => d.sd_sales_same_sku),
    customdata: data.map((d) => formatHour(d.hour)),
    type: 'scatter',
    mode: 'lines+markers',
    xperiodalignment: 'start',
    hovertemplate: '%{fullData.name} : ' + currencyPrefix + '%{y:,.0f} (%{customdata})<extra></extra>',
    marker: {color: '#e87566'},
    name: sdSalesSameSkuSelected.name,
  })

  const PlotComponent = Plot as any
  return (
    <PlotComponent
      data={dataSet as any}
      layout={{
        margin: {
          l: 20,
          r: 20,
          b: 20,
          t: 20,
          pad: 0
        },
        autosize: true,
        showlegend: false,
        // legend: {
        //   font: {
        //     size: 10,
        //     color: '#6B7280'
        //   }
        // },
        xaxis: {
          tickfont: {
            size: 10,
            color: '#9CA3AF'
          },
          tickformat: '%H:%M:%S',
          zerolinecolor: '#e5e7eb',
          tickmode: 'auto',
          nticks: data.length > 10 ? 10 : data.length,
        },
        yaxis: {
          tickformat: ',.0f',
          tickprefix: currencyPrefix,
          tickfont: {
            size: 10,
            color: '#9CA3AF'
          },
          gridcolor: '#f3f4f6',
          zerolinecolor: '#e5e7eb',
          automargin: true,
        },
        yaxis2: {
          tickformat: ',',
          tickfont: {
            size: 10,
            color: '#9CA3AF'
          },
          gridcolor: '#f3f4f6',
          zerolinecolor: '#e5e7eb',
          overlaying: 'y',
          side: 'right',
          automargin: true,
        },
        hovermode: 'x unified',
        hoverlabel: {
          bgcolor: 'rgba(17, 24, 39, 0.9)',
          font: {
            size: 10,
            color: '#e5e7eb'
          },
        },
        dragmode: false,
      }}
      config={{
        displayModeBar: false,
      }}
      useResizeHandler={true}
      className="w-full grow"
    />
  )
}
