default:
  image: 621436759906.dkr.ecr.ap-northeast-2.amazonaws.com/gitlab-runner-image-aws:1.5.7
  tags:
    - shared

variables:
  AWS_ROLE_SESSION_NAME: "gitlab-$CI_PIPELINE_ID-$CI_JOB_NAME"
  SERVICE_NAME: "global-mop-fe" # not change
  SYSTEM_NAME: "gmop-fe"
  SCHEMA: "gmop"
  BRANCH: $CI_CI_COMMIT_REF_NAME
  DOMAIN: "fe"
  ROOT_DOMAIN: "gmopapp.net"
  GITPJT_GROUP: "global-mop"
  CACHE_FALLBACK_KEY: "develop-gradle-2"
  FF_USE_FASTZIP: "true"
  CACHE_COMPRESSION_LEVEL: "fast"
  ARTIFACT_COMPRESSION_LEVEL: "fastest"
  ENV: $ENV
  DOMAIN_NAME: "studio.${ENV}.optapex.com"
  DOMAIN_NAME_PRD: "studio.optapex.com"
  DEPLOY_STG: "true"

include:
  - project: "global-mop/global-mop-module"
    file:
      - "/gitlab/.template-before-script.yml"
      - "/gitlab/.template-workflow.yml"
      - "/gitlab/.template-infra.yml"
      - "/gitlab/.template-app.yml"

stages:
  - dev-npm-build
  - dev-unit-test
  - dev-docker-build
  - dev-eks-deploy
  # - e2e-test
  - dev-smoke-test
  - dev-e2e-test
  - stg-npm-build
  - stg-docker-build
  - stg-eks-deploy
  - stg-smoke-test
  # - stg-e2e-test
  - confirm
  - prd-npm-build
  - prd-docker-build
  - prd-eks-deploy
  - prd-smoke-test

# dev-unit-test:
#   stage: dev-unit-test
#   environment:
#     name: $ENV
#   image: cypress/included:12.17.4
#   script:
#     # - export AUTH_TRUST_HOST=true
#     - export NEXTAUTH_URL=http://localhost:3000
#     - export NODE_TLS_REJECT_UNAUTHORIZED=0
#     - export AUTH_DOMAIN=auth.dev.optapex.com
#     - export AUTH_SECRET="$E2E_AUTH_SECRET"
#     - export COGNITO_CLIENT_ID="$COGNITO_CLIENT_ID"
#     - export COGNITO_CLIENT_SECRET="$COGNITO_CLIENT_SECRET"
#     - export COGNITO_ISSUER="$COGNITO_ISSUER"
#     - export BE_API_ENDPOINT=https://api.dev.optapex.com
#     - npm install
#     - npm run test

#     - npm run dev &
#     - npm run test:smoke
#     - npm run test:module
#   # artifacts:
#   #   paths:
#   #     - .next
#   #     - .env
#   #   expire_in: 1 day
#   except:
#     refs:
#       - master

dev-npm-build:
  stage: dev-npm-build
  # cache:
  #   key: "node_modules-${CI_COMMIT_REF_SLUG}-${checksum:package-lock.json}"
  #   paths:
  #     - node_modules/
  #     - .next/cache/
  #   policy: pull-push
  environment:
    name: $ENV
  image: node:18
  before_script:
    - apt-get update && apt-get install -y unzip curl jq
    - curl "https://awscli.amazonaws.com/awscli-exe-linux-x86_64.zip" -o "awscliv2.zip"
    - unzip awscliv2.zip
    - ./aws/install
    - aws --version
    - !reference [.template-get-branch, before_script]
    - !reference [.template-aws-assume, before_script]
  script:
    - cp ".env.${ENV}" .env
    - echo "" >> .env
    - export FE_COGNITO_SECRET=$(aws secretsmanager get-secret-value --secret-id sm-us-east-1-${ENV}-gmop-cognito-fe --query SecretString --output text | jq -r '{cognito_client_id, cognito_client_secret, cognito_issuer}')
    - export FE_COGNITO_AUTH_SECRET=$(aws secretsmanager get-secret-value --secret-id sm-us-east-1-${ENV}-gmop-cognito-fe-auth --query SecretString --output text | jq -r '{nextauth_url, auth_secret, auth_domain}')
    - export FE_API_SECRET=$(aws secretsmanager get-secret-value --secret-id fe-api-endpoint --query SecretString --output text | jq -r '.')
    - export FE_ENV_SECRET=$(aws secretsmanager get-secret-value --secret-id sm-us-east-1-${ENV}-gmop-fe-env --query SecretString --output text | jq -r '.')

    - echo "$FE_COGNITO_SECRET" | jq -r 'to_entries | .[] | "\(.key | ascii_upcase)=\(.value)"' >> .env
    - echo "$FE_COGNITO_AUTH_SECRET" | jq -r 'to_entries | .[] | "\(.key | ascii_upcase)=\(.value)"' >> .env
    - echo "$FE_API_SECRET" | jq -r 'to_entries | .[] | "\(.key | ascii_upcase)=\(.value)"' >> .env
    - echo "$FE_ENV_SECRET" | jq -r 'to_entries | .[] | "\(.key | ascii_upcase)=\(.value)"' >> .env
    - sed -i 's/^COGNITO_ISSUER=/COGNITO_ISSUER=https:\/\//' .env
    - cat .env

    - npm install
    - npm run build
  artifacts:
    paths:
      - .next
      - .env
    expire_in: 1 day
  except:
    refs:
      - master

dev-unit-test:
  stage: dev-unit-test
  image: node:18
  rules:
    - if: $CI_COMMIT_REF_NAME == "develop"
    - when: never
  script:
    - npm install
    - npm run test


# e2e_test:
#   stage: e2e-test
#   environment:
#     name: $ENV
#   # image: cypress/included:9.7.0
#   image: cypress/base:16.14.2-slim
#   # needs:
#   #   - optional: true
#   artifacts:
#     when: on_failure
#     paths:
#       - tests/module/screenshots/**/*.png
#     expire_in: 1 day
#   script:
#     - >-
#       if [ "$IS_FEATURE" == "true" ]; then
#         domainName="https://${BRANCH}.${DOMAIN_NAME}.s3-website.ap-northeast-2.amazonaws.com"
#       else
#         domainName="https://${DOMAIN_NAME}"
#       fi
#     - yarn run cypress install
#     - echo ${domainName}
#     # - yarn run wait-on ${domainName}
#     - export CYPRESS_BASE_URL=${domainName}
#     - npm run test:module:default
#   # variables:
#   #   runCommand: "yarn run test:module:default"
#   # extends: .template-e2e-test
#   except:
#     refs:
#       - master

dev-docker-build:
  stage: dev-docker-build
  environment:
    name: $ENV
  extends: .template-docker-build
  dependencies:
    - dev-npm-build
  except:
    refs:
      - master

dev-eks-deploy:
  stage: dev-eks-deploy
  environment:
    name: $ENV
  extends: .template-eks-deploy
  except:
    refs:
      - master

dev-smoke-test:
  stage: dev-smoke-test
  image: cypress/included:12.17.4
  needs: ["dev-eks-deploy"]
  rules:
    - if: '$CI_COMMIT_REF_NAME == "develop"'
      when: on_success
    - when: never
  script:
    - domainName="https://${DOMAIN_NAME}"
    - echo "Target(smoke) - ${domainName}"
    - npm install
    - CYPRESS_BASE_URL=${domainName} npm run test:smoke:remote
  artifacts:
    when: on_failure
    paths:
      - tests/module/screenshots/**/*.png
    expire_in: 1 day

dev-e2e-test:
  stage: dev-e2e-test
  image: cypress/included:12.17.4
  needs: ["dev-eks-deploy"]
  rules:
    - if: '$CI_COMMIT_REF_NAME == "develop"'
      when: on_success
    - when: never
  script:
    - domainName="https://${DOMAIN_NAME}"
    - echo "Target(module) - ${domainName}"
    - npm install
    - CYPRESS_BASE_URL=${domainName} npm run test:module:default
  artifacts:
    when: on_failure
    paths:
      - tests/module/screenshots/**/*.png
    expire_in: 1 day

stg-npm-build:
  stage: stg-npm-build
  # cache:
  #   key: "node_modules-${CI_COMMIT_REF_SLUG}-${checksum:package-lock.json}"
  #   paths:
  #     - node_modules/
  #     - .next/cache/
  #   policy: pull-push
  environment:
    name: $ENV
  image: node:18
  before_script:
    - apt-get update && apt-get install -y unzip curl jq
    - curl "https://awscli.amazonaws.com/awscli-exe-linux-x86_64.zip" -o "awscliv2.zip"
    - unzip awscliv2.zip
    - ./aws/install
    - aws --version
    - !reference [.template-get-branch, before_script]
    - !reference [.template-aws-assume, before_script]
  script:
    - cp ".env.${ENV}" .env
    - echo "" >> .env
    - export FE_COGNITO_SECRET=$(aws secretsmanager get-secret-value --secret-id sm-us-east-1-${ENV}-gmop-cognito-fe --query SecretString --output text | jq -r '{cognito_client_id, cognito_client_secret, cognito_issuer}')
    - export FE_COGNITO_AUTH_SECRET=$(aws secretsmanager get-secret-value --secret-id sm-us-east-1-${ENV}-gmop-cognito-fe-auth --query SecretString --output text | jq -r '{nextauth_url, auth_secret, auth_domain}')
    - export FE_API_SECRET=$(aws secretsmanager get-secret-value --secret-id fe-api-endpoint --query SecretString --output text | jq -r '.')
    - export FE_ENV_SECRET=$(aws secretsmanager get-secret-value --secret-id sm-us-east-1-${ENV}-gmop-fe-env --query SecretString --output text | jq -r '.')

    - echo "$FE_COGNITO_SECRET" | jq -r 'to_entries | .[] | "\(.key | ascii_upcase)=\(.value)"' >> .env
    - echo "$FE_COGNITO_AUTH_SECRET" | jq -r 'to_entries | .[] | "\(.key | ascii_upcase)=\(.value)"' >> .env
    - echo "$FE_API_SECRET" | jq -r 'to_entries | .[] | "\(.key | ascii_upcase)=\(.value)"' >> .env
    - echo "$FE_ENV_SECRET" | jq -r 'to_entries | .[] | "\(.key | ascii_upcase)=\(.value)"' >> .env
    - sed -i 's/^COGNITO_ISSUER=/COGNITO_ISSUER=https:\/\//' .env

    - npm install
    - npm run build
  artifacts:
    paths:
      - .next
      - .env
    expire_in: 1 day
  rules:
    - if: $CI_COMMIT_REF_NAME == "master" && $DEPLOY_STG == "true"

stg-docker-build:
  stage: stg-docker-build
  environment:
    name: $ENV
  extends: .template-docker-build
  dependencies:
    - stg-npm-build
  rules:
    - if: $CI_COMMIT_REF_NAME == "master" && $DEPLOY_STG == "true"

stg-eks-deploy:
  stage: stg-eks-deploy
  environment:
    name: $ENV
  extends: .template-eks-deploy
  rules:
    - if: $CI_COMMIT_REF_NAME == "master" && $DEPLOY_STG == "true"

stg-smoke-test:
  stage: stg-smoke-test
  image: cypress/included:12.17.4
  needs: ["stg-eks-deploy"]
  rules:
    - if: '$CI_COMMIT_REF_NAME == "master" && $DEPLOY_STG == "true"'
  script:
    - domainName="https://${DOMAIN_NAME}"
    - echo "Target(smoke) - ${domainName}"
    - npm install
    - CYPRESS_BASE_URL=${domainName} npm run test:smoke:remote

# stg-e2e-test:
#   stage: stg-e2e-test
#   image: cypress/included:12.17.4
#   needs: ["stg-eks-deploy"]
#   rules:
#     - if: '$CI_COMMIT_REF_NAME == "master" && $DEPLOY_STG == "true"'
#   script:
#     - domainName="https://${DOMAIN_NAME}"
#     - echo "Target(module) - ${domainName}"
#     - npm install
#     - CYPRESS_BASE_URL=${domainName} npm run test:module:default

confirm-master:
  stage: confirm
  when: manual
  allow_failure: false
  script:
    - echo "confirmed by" $GITLAB_USER_NAME
  rules:
    - if: $CI_COMMIT_REF_NAME == "master" && $DEPLOY_STG == "true"

prd-npm-build:
  stage: prd-npm-build
  environment:
    name: $ENV
  variables:
    ENV: prd
  image: node:18
  before_script:
    - apt-get update && apt-get install -y unzip curl jq
    - curl "https://awscli.amazonaws.com/awscli-exe-linux-x86_64.zip" -o "awscliv2.zip"
    - unzip awscliv2.zip
    - ./aws/install
    - aws --version
    - !reference [.template-get-branch, before_script]
    - !reference [.template-aws-assume, before_script]
  script:
    - cp ".env.${ENV}" .env
    - echo "" >> .env
    - export FE_COGNITO_SECRET=$(aws secretsmanager get-secret-value --secret-id sm-us-east-1-${ENV}-gmop-cognito-fe --query SecretString --output text | jq -r '{cognito_client_id, cognito_client_secret, cognito_issuer}')
    - export FE_COGNITO_AUTH_SECRET=$(aws secretsmanager get-secret-value --secret-id sm-us-east-1-${ENV}-gmop-cognito-fe-auth --query SecretString --output text | jq -r '{nextauth_url, auth_secret, auth_domain}')
    - export FE_API_SECRET=$(aws secretsmanager get-secret-value --secret-id fe-api-endpoint --query SecretString --output text | jq -r '.')
    - export FE_ENV_SECRET=$(aws secretsmanager get-secret-value --secret-id sm-us-east-1-${ENV}-gmop-fe-env --query SecretString --output text | jq -r '.')

    - echo "$FE_COGNITO_SECRET" | jq -r 'to_entries | .[] | "\(.key | ascii_upcase)=\(.value)"' >> .env
    - echo "$FE_COGNITO_AUTH_SECRET" | jq -r 'to_entries | .[] | "\(.key | ascii_upcase)=\(.value)"' >> .env
    - echo "$FE_API_SECRET" | jq -r 'to_entries | .[] | "\(.key | ascii_upcase)=\(.value)"' >> .env
    - echo "$FE_ENV_SECRET" | jq -r 'to_entries | .[] | "\(.key | ascii_upcase)=\(.value)"' >> .env
    - sed -i 's/^COGNITO_ISSUER=/COGNITO_ISSUER=https:\/\//' .env

    - npm install
    - npm run build
  artifacts:
    paths:
      - .next
      - .env
    expire_in: 1 day
  rules:
    - if: $CI_COMMIT_REF_NAME == "master"

prd-docker-build:
  stage: prd-docker-build
  environment:
    name: $ENV
  variables:
    ENV: prd
  extends: .template-docker-build
  dependencies:
    - prd-npm-build
  rules:
    - if: $CI_COMMIT_REF_NAME == "master"

prd-eks-deploy:
  stage: prd-eks-deploy
  environment:
    name: $ENV
  variables:
    ENV: prd
  extends: .template-eks-deploy
  rules:
    - if: $CI_COMMIT_REF_NAME == "master"

prd-smoke-test:
  stage: prd-smoke-test
  image: cypress/included:12.17.4
  needs: ["prd-eks-deploy"]
  rules:
    - if: '$CI_COMMIT_REF_NAME == "master"'
  script:
    - domainName="https://${DOMAIN_NAME_PRD}"
    - npm install
    - CYPRESS_BASE_URL=${domainName} npm run test:smoke:remote