"use client"

import { forwardRef, useEffect, useRef, useState } from "react"
import { useSession } from "next-auth/react"
import { useLocale, useTranslations } from 'next-intl'
import { Tab, TabGroup, TabList, TabPanel, TabPanels } from '@headlessui/react'
import { registerLocale } from "react-datepicker"
import { ko } from "date-fns/locale"
import "react-datepicker/dist/react-datepicker.css"
import { formatDate } from "@/utils/msc"
import { api } from "@/utils/api"
import { ProfileOption } from "./profile-select"
import { MarketplaceOption } from "./marketplace-select"
import CompetitionDailyLayoutComponent from "./competition-daily-layout-component"
import CompetitionWeeklyLayoutComponent from "./competition-weekly-layout-component"
registerLocale('ko', ko)

interface CompetitionLayoutProps {
  mopUserData: any;
  selectedProfile: ProfileOption;
  selectedMarketplace: MarketplaceOption;
}

export default function CompetitionLayoutComponent({
  mopUserData,
  selectedProfile,
  selectedMarketplace
}: CompetitionLayoutProps) {
  const t = useTranslations('component')
  const tk = useTranslations('CompetitionPage')
  const locale = useLocale()
  const { data: session, status } = useSession()
  const overviewFetchCounter = useRef(0)
  
  const [brandCompetitionOverview, setBrandCompetitionOverview] = useState<any>(null)
  
  const fetchBrandCompetitionOverview = async (signal: AbortSignal) => {
    if (!(session?.user as any).access_token) {
      console.log('access token is missing in the session.')
    }
    overviewFetchCounter.current += 1
    let brandCompetitionOverviewResponse = await api.getBrandCompetitionOverview(
      selectedProfile.account_id,
      selectedMarketplace.marketplace_id,
      "",
      selectedProfile.account_type,
      (session?.user as any).access_token,
      signal
    )
    // brandCompetitionOverviewResponse = {
    //   "avg_rank": 167773.***********,
    //   "avg_rank_change": -11566.************,
    //   "avg_relative_rank": 0.****************,
    //   "avg_relative_rank_change": -0.033362769370987255,
    //   "avg_top3_click_prop": 0.****************,
    //   "avg_top3_click_prop_change": 0.*****************,
    //   "daily": [
    //     {
    //       "date": "2025-04-27",
    //       "avg_rank": 199902.4,
    //       "avg_relative_rank": 0.****************,
    //       "avg_top3_click_prop": 0.***************
    //     },
    //     {
    //       "date": "2025-04-28",
    //       "avg_rank": 190922.6,
    //       "avg_relative_rank": 0.****************,
    //       "avg_top3_click_prop": 0.****************
    //     },
    //   ],
    //   "weekly": [
    //     {
    //       "date": "2025-05-07",
    //       "avg_rank": 179339.**********,
    //       "avg_relative_rank": 0.49691783821298957,
    //       "avg_top3_click_prop": 0.7674603281986146
    //     },
    //     {
    //       "date": "2025-05-14",
    //       "avg_rank": 167773.***********,
    //       "avg_relative_rank": 0.****************,
    //       "avg_top3_click_prop": 0.****************
    //     }
    //   ]
    // }
    overviewFetchCounter.current -= 1
    brandCompetitionOverviewResponse && setBrandCompetitionOverview(brandCompetitionOverviewResponse)
  }

  useEffect(() => {
    const controller = new AbortController()
    fetchBrandCompetitionOverview(controller.signal)
    return () => controller.abort()
  }, [selectedProfile, selectedMarketplace])

  return selectedProfile && selectedMarketplace
    ? <div className="relative size-full flex flex-col bg-white">
      <TabGroup className="relative size-full">
        <TabPanels className="relative size-full">
          <div className="flex-shrink-0 absolute top-[16px] right-[24px] z-[1]">
            <TabList className="inline-flex gap-x-1 bg-gray-200 p-1 rounded-lg">
              <Tab
                className="flex items-center gap-x-1 py-2 px-4 text-sm font-semibold text-gray-400 focus:outline-none data-[selected]:text-gray-500 data-[selected]:bg-white data-[selected]:shadow-md data-[selected]:data-[hover]:bg-white data-[hover]:bg-white/40 data-[focus]:outline-1 data-[focus]:outline-white rounded-lg"
              >
                {tk("weekly")}
              </Tab>
              <Tab
                className="flex items-center gap-x-1 py-2 px-4 text-sm font-semibold text-gray-400 focus:outline-none data-[selected]:text-gray-500 data-[selected]:bg-white data-[selected]:shadow-md data-[selected]:data-[hover]:bg-white data-[hover]:bg-white/40 data-[focus]:outline-1 data-[focus]:outline-white rounded-lg"
              >
                {tk("daily")}
              </Tab>
            </TabList>
          </div>
          <TabPanel className="flex flex-col w-full h-full">
            <div className="relative grow w-full min-h-0">
              <CompetitionWeeklyLayoutComponent
                selectedProfile={selectedProfile}
                selectedMarketplace={selectedMarketplace}
                brandCompetitionOverview={brandCompetitionOverview}
              />
            </div>
          </TabPanel>
          <TabPanel className="flex flex-col w-full h-full">
            <div className="relative grow w-full min-h-0">
              <CompetitionDailyLayoutComponent
                selectedProfile={selectedProfile}
                selectedMarketplace={selectedMarketplace}
                brandCompetitionOverview={brandCompetitionOverview}
              />
            </div>
          </TabPanel>
        </TabPanels>
      </TabGroup>
    </div>
    : ""
}
