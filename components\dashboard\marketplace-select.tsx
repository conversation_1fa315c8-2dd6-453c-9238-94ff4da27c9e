"use client"

import { Fragment, useState } from "react"
import { cn } from "@/utils/msc"
import { useTranslations } from "next-intl"
import { Listbox, Transition } from "@headlessui/react"
import { ChevronUpDownIcon, ExclamationTriangleIcon, GlobeAmericasIcon, ShoppingBagIcon } from "@heroicons/react/20/solid"
import { Check } from "@/components/ui/check"
import { CircleFlag } from "react-circle-flags"

export type MarketplaceOption = {
  id: string;
  marketplace_id: string;
  marketplace_name: string;
  status: string;
  lwa_account_id: number;
  country_code: string;
  creation_datetime: string;
  created_datetime: string;
  subscription_start_date?: string;
  subscription_active_date?: string;
  last_update_datetime?: string;
  deleted_datetime?: string;
  default_currency_code: string;
  default_language_code: string;
  domain_name?: string;
  profile?: any;
  subscription_yn?: string;
  subscription_features?: any;
  subscription_product_type?: string;
  ad_lwa_validation_yn?: string;
  sp_lwa_validation_yn?: string;
}
interface MarketplaceSelectProps extends React.HTMLAttributes<HTMLDivElement> {
    listboxClassName?: string;
    marketplaceOptions: MarketplaceOption[];
    isLNBExpanded: boolean;
    selected: MarketplaceOption;
    setSelected: (option: MarketplaceOption | null) => void
}
export default function MarketplaceSelect({
    className,
    listboxClassName,
    marketplaceOptions,
    isLNBExpanded,
    selected,
    setSelected,
    ...props
}: MarketplaceSelectProps) {
  const t = useTranslations('component')
  return (
    <div
      className={cn(
        "relative",
        className
      )}
      {...props}
    >
      <Listbox value={selected} onChange={setSelected}>
        {({ open }) => (
        <div className="relative">
          <Listbox.Button
            className={cn(
              "group relative flex items-center gap-x-2 pl-3 pr-2 w-full h-9 py-1.5 cursor-pointer rounded-lg text-left focus:outline-none text-sm overflow-hidden border border-gray-200 hover:border-gray-300",
              open
                ? "bg-gray-100"
                : ""
            )}
          >
            <span className="flex items-center gap-x-2">
              <GlobeAmericasIcon
                className="flex-shrink-0 h-5 w-5 text-gray-500 group-hover:text-gray-600"
                aria-hidden="true"
              />
              <span className="flex-shrink-0 block truncate text-gray-500 group-hover:text-gray-600">
                { isLNBExpanded
                  ? selected.country_code + " (" + selected.default_currency_code + ")"
                  : ""
                }
              </span>
              <div className="flex items-center gap-x-1 text-gray-500 group-hover:text-gray-600 text-xs border-l border-gray-200">
                <div className="px-3 flex-shrink-0">
                  <div className="flex items-center gap-x-1">
                    <span className={cn(
                      "inline-flex rounded-full h-1.5 w-1.5",
                      selected.ad_lwa_validation_yn === "N"
                        ? "bg-red-500"
                        : selected.ad_lwa_validation_yn === "Y"
                          ? "bg-green-500"
                          : "bg-gray-300"
                    )}></span>
                    Ads
                  </div>
                </div>
                <div className="pl-3 flex-shrink-0 flex items-center gap-x-1.5 border-l border-gray-200">
                  <div className="flex items-center gap-x-1">
                    <span className={cn(
                      "inline-flex rounded-full h-1.5 w-1.5",
                      selected.sp_lwa_validation_yn === "N"
                        ? "bg-red-500"
                        : selected.sp_lwa_validation_yn === "Y"
                          ? "bg-green-500"
                          : "bg-gray-300"
                    )}></span>
                    {(selected.profile?.account_type ?? "seller") === "vendor"
                      ? "Vendor"
                      : "Seller"
                    }
                  </div>
                </div>
                {selected.subscription_product_type &&
                <div className="pl-3 flex-shrink-0 flex items-center">
                  <span className={cn(
                    "inline-flex rounded-md px-1 py-0.5 text-[10px] text-white font-semibold",
                    selected.subscription_product_type === "LITE"
                      ? "bg-gray-400 group-hover:bg-gray-500"
                      : selected.subscription_product_type === "PRO"
                        ? "bg-purple-400 group-hover:bg-purple-500"
                        : "bg-gray-300"
                  )}>{selected.subscription_product_type}</span>
                </div>
                }
              </div>
            </span>
            <div className="pointer-events-none flex items-center ml-auto">
              { isLNBExpanded
                ? (
                  <ChevronUpDownIcon
                    className="h-5 w-5 text-gray-300 group-hover:text-gray-400"
                    aria-hidden="true"
                  />
                )
                : (
                  <></>
                )
              }
            </div>
          </Listbox.Button>
          <Transition
            as={Fragment}
            leave="transition ease-in duration-100"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
            <Listbox.Options
              className={cn(
                listboxClassName
              )}
              >
              {marketplaceOptions.map((option, optionIdx) => (
                <Listbox.Option
                  key={optionIdx}
                  className={({ active }) =>
                    `group relative cursor-pointer select-none py-2 pl-4 pr-10 ${
                      active ? 'bg-blue-100/40 text-blue-900' : 'text-gray-600'
                    }`
                  }
                  value={option}
                >
                  {() => (
                    <>
                      <div>
                        {option.subscription_yn === "Y"
                          ? ""
                          : (
                            <div className="w-full flex items-center justify-between pt-1.5 px-2 text-orange-400 text-xs font-semibold">
                              <div className="flex items-center gap-x-1">
                                <ExclamationTriangleIcon className="h-4 w-4" />
                                <span>
                                  subscription required
                                </span>
                              </div>
                            </div>
                          )
                        }
                        <div className="py-3 px-2">
                          <div className="flex items-center gap-x-2">
                            <div className="flex-shrink-0 flex items-center gap-x-2">
                              <div className="w-4 h-4">
                                <CircleFlag countryCode={option.country_code.toLowerCase()} />
                              </div>
                              <div
                                className={`${
                                selected.id === option.id ? 'font-medium' : 'font-normal'
                              }`}>{option.country_code + " (" + option.default_currency_code + ")"}</div>
                            </div>

                            <div className="flex items-center gap-x-1 text-gray-500 group-hover:text-gray-600 text-xs border-l border-gray-200">
                              <div className="px-3 flex-shrink-0">
                                <div className="flex items-center gap-x-1">
                                  <span className={cn(
                                    "inline-flex rounded-full h-1.5 w-1.5",
                                    option.ad_lwa_validation_yn === "N"
                                      ? "bg-red-500"
                                      : option.ad_lwa_validation_yn === "Y"
                                        ? "bg-green-500"
                                        : "bg-gray-300"
                                  )}></span>
                                  Ads
                                </div>
                              </div>
                              <div className="pl-3 flex-shrink-0 flex items-center gap-x-1.5 border-l border-gray-200">
                                <div className="flex items-center gap-x-1">
                                  <span className={cn(
                                    "inline-flex rounded-full h-1.5 w-1.5",
                                    option.sp_lwa_validation_yn === "N"
                                      ? "bg-red-500"
                                      : option.sp_lwa_validation_yn === "Y"
                                        ? "bg-green-500"
                                        : "bg-gray-300"
                                  )}></span>
                                  {(option.profile?.account_type ?? "seller") === "vendor"
                                    ? "Vendor"
                                    : "Seller"
                                  }
                                </div>
                              </div>
                              {option.subscription_product_type &&
                              <div className="pl-3 flex-shrink-0 flex items-center">
                                <span className={cn(
                                  "inline-flex rounded-md px-1 py-0.5 text-[10px] text-white font-semibold",
                                  option.subscription_product_type === "LITE"
                                    ? "bg-gray-400 group-hover:bg-gray-500"
                                    : option.subscription_product_type === "PRO"
                                      ? "bg-purple-400 group-hover:bg-purple-500"
                                      : "bg-gray-300"
                                )}>{option.subscription_product_type}</span>
                              </div>
                              }
                            </div>
                          </div>
                          {/* <div className="mt-2 flex-shrink-0 flex items-center gap-x-0.5 text-gray-300 group-hover:text-gray-400">
                            <ShoppingBagIcon className="flex-shrink-0 h-3 w-3"/>
                            <div className="text-[10px]">
                              {option.marketplace_name}
                            </div>
                          </div> */}
                        </div>
                      </div>
                      {selected.id === option.id ? (
                        <div className="absolute inset-y-0 right-0 flex items-center pr-5">
                          <div className="shrink-0 flex items-center justify-center w-5 h-5 rounded-full bg-blue-400">
                            <Check className="w-4 h-4 stroke-white" aria-hidden="true" />
                          </div>
                        </div>
                      ) : null}
                    </>
                  )}
                </Listbox.Option>
              ))}
            </Listbox.Options>
          </Transition>
        </div>
        )}
      </Listbox>
    </div>
  )
}
