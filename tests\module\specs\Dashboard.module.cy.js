const sessionData = {
    sub: '94a82418-a051-70b6-4c7d-db8cdcbf8f8a',
    name: '김테스트',
    email: '<EMAIL>',
    access_token: '*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************',
};

describe('Dashboard', () => {
    it.guide('38 - 처음 접속하면, 대시보드가 보인다.', {
        actionFunc: () => {
            cy.login();
            cy.intercept('GET', '/api/oauth/availableProfiles', { 
                body: [{
                    "id": "acc_1PeUKDGMi5qS2Zp1",
                    "name": "Test Profile",
                    "email": "<EMAIL>",
                    "created_at": "2024-01-01T00:00:00.000Z",
                    "updated_at": "2024-01-01T00:00:00.000Z",
                    "profiles": []
                }] 
            }).as('getAvailableProfiles');
            cy.visit('/dashboard?tab=manage-profile');
        },
        assertFunc: () => {
            cy.wait('@getAvailableProfiles');
            cy.get('body').contains('Manage Your Amazon Accounts');        
        },
        afterFunc: () => {
            cy.clearCookies();
        }
    });
});
  