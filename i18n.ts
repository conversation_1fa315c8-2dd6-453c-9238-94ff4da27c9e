import { getRequestConfig } from 'next-intl/server';
import { getUserLocale } from './utils/locale';

// Deep merge 함수 - 중첩된 객체의 키들도 fallback 처리
function deepMerge(fallback: any, primary: any): any {
  if (typeof primary !== 'object' || primary === null) {
    return primary;
  }
  
  if (typeof fallback !== 'object' || fallback === null) {
    return primary;
  }
  
  const result = { ...fallback };
  
  for (const key in primary) {
    if (primary.hasOwnProperty(key)) {
      if (typeof primary[key] === 'object' && primary[key] !== null && 
          typeof fallback[key] === 'object' && fallback[key] !== null) {
        result[key] = deepMerge(fallback[key], primary[key]);
      } else {
        result[key] = primary[key];
      }
    }
  }
  
  return result;
}

export default getRequestConfig(async () => {
  const locale = await getUserLocale();

  // 기본 메시지 로드
  const messages = (await import(`./messages/${locale}.json`)).default;
  
  // 중국어인 경우 영어 메시지를 fallback으로 사용
  if (locale === 'cn') {
    const fallbackMessages = (await import(`./messages/en.json`)).default;
    return {
      locale,
      messages: deepMerge(fallbackMessages, messages)
    };
  }

  return {
    locale,
    messages
  };
});