"use client"

import Image from "next/image"
import { usePathname } from 'next/navigation'
import { cn } from "@/utils/msc"
import CustomLink from "./custom-link"
import {
  NavigationMenu,
  NavigationMenuContent,
  NavigationMenuItem,
  NavigationMenuLink,
  NavigationMenuList,
  NavigationMenuTrigger,
  navigationMenuTriggerStyle,
} from "./ui/navigation-menu"
import React from "react"
import { Button } from "./ui/button"

export function MainNav() {
  const pathName = usePathname()
  return (
    <div className="flex items-center space-x-6 lg:space-x-10">
      <CustomLink href="/">
        <Button variant="ghost" className="px-0 py-0 hover:bg-white">
          <Image
            src="/logo/optapex-logo.svg"
            alt="optapex Logo"
            className="w-auto h-6 text-gray-900"
            width={120}
            height={32}
          />
        </Button>
      </CustomLink>
      <NavigationMenu>
        <NavigationMenuList>
          {/* <NavigationMenuItem>
            <NavigationMenuTrigger>Server Side</NavigationMenuTrigger>
            <NavigationMenuContent>
              <ul className="grid gap-3 p-6 md:w-[400px] lg:w-[500px] lg:grid-cols-[.75fr_1fr]">
                <ListItem href="/server-example" title="RSC Example">
                  Protecting React Server Component.
                </ListItem>
                <ListItem href="/middleware-example" title="Middleware Example">
                  Using Middleware to protect pages & APIs.
                </ListItem>
                <ListItem href="/api-example" title="Route Handler Example">
                  Getting the session inside an API Route.
                </ListItem>
              </ul>
            </NavigationMenuContent>
          </NavigationMenuItem> */}
          {/* <NavigationMenuItem className="hidden sm:inline-flex mr-3">
            <NavigationMenuLink
              href="/dashboard?tab=reports"
              className={navigationMenuTriggerStyle()}
              {...(pathName === "/dashboard" ? { "data-active": true } : {})}
            >
              Dashboard
            </NavigationMenuLink>
          </NavigationMenuItem> */}
          {/* <NavigationMenuItem className="hidden sm:inline-flex mr-3">
            <NavigationMenuLink
              href="/team"
              className={navigationMenuTriggerStyle()}
              {...(pathName === "/team" ? { "data-active": true } : {})}
            >
              Team
            </NavigationMenuLink>
          </NavigationMenuItem> */}
        </NavigationMenuList>
      </NavigationMenu>
    </div>
  )
}

const ListItem = React.forwardRef<
  React.ElementRef<"a">,
  React.ComponentPropsWithoutRef<"a">
>(({ className, title, children, ...props }, ref) => {
  return (
    <li>
      <NavigationMenuLink asChild>
        <a
          ref={ref}
          className={cn(
            "block select-none space-y-1 rounded-md p-3 leading-none no-underline outline-none transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground",
            className
          )}
          {...props}
        >
          <div className="text-sm font-medium leading-none">{title}</div>
          <p className="text-sm leading-snug line-clamp-2 text-muted-foreground">
            {children}
          </p>
        </a>
      </NavigationMenuLink>
    </li>
  )
})
ListItem.displayName = "ListItem"
