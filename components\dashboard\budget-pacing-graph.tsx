"use client"

import { useEffect, useState } from 'react'
import { useCurrencyFormatter, getCurrencySymbol, getCurrencySymbolUnicode } from "@/utils/currency"

// @ts-ignore
import Plot from "react-plotly.js"

interface BudgetPacingData {
  optimization_id: number
  total_budget: number
  start_date: string
  end_date: string
  spent_budget: number
  remaining_budget: number
  remaining_days: number
  daily_recommended_budget: number
  daily_spending_history: Record<string, number>
  budget_usage_predictions?: {
    MAX: number
    MIN: number
    TARGET: number
    estimated_budget_state: string
  }
  is_ended: boolean
  custom_date_range: boolean
  ad_budget_type: string
}

interface BudgetPacingGraphProps {
  data: BudgetPacingData[]
  serverTimeInfo?: {
    last_updated_date_time: string | null
    timezone: string | null
  }
  accountTimeInfo?: {
    time: Date
    timezone: string | null
    timeString: string | null
  }
  currencyCode?: string
}

export default function BudgetPacingGraph({ data, serverTimeInfo, accountTimeInfo, currencyCode = 'USD' }: BudgetPacingGraphProps) {
  const { formatCurrency } = useCurrencyFormatter()
  const [isClient, setIsClient] = useState(false)
  const currencySymbol = getCurrencySymbol(currencyCode)
  const currencySymbolUnicode = getCurrencySymbolUnicode(currencyCode)

  useEffect(() => {
    setIsClient(true)
  }, [])

  // Helper: safely parse "YYYY-MM-DD" to avoid timezone issues
  const parseDateTime = (dateTimeStr: string): Date => {
    const [year, month, day] = dateTimeStr.split('T')[0].split('-').map(Number);
    return new Date(year, month - 1, day);
  }

  // Helper: format for tooltip (date only)
  const formatDateTimeForHover = (date: Date): string => {
    const yyyy = date.getFullYear()
    const mm = String(date.getMonth() + 1).padStart(2, '0')
    const dd = String(date.getDate()).padStart(2, '0')
    return `${yyyy}-${mm}-${dd}`
  }

  if (!isClient) {
    return (
      <div className="w-full h-64 flex items-center justify-center text-gray-400">
        Loading...
      </div>
    )
  }

  if (!data || data.length === 0) {
    return (
      <div className="w-full h-64 flex items-center justify-center text-gray-400">
        No budget pacing data available
      </div>
    )
  }

  // Aggregate all spending data for all optimization sets (keep hourly data separate)
  let totalBudget = 0
  let startDate: Date | undefined
  let endDate: Date | undefined
  const dailySpendingMap = new Map<string, number>()

  // 데이터에서 가장 마지막 날짜를 찾는다. (미래 날짜 데이터 핸들링)
  let maxDateFromHistory: Date | undefined;
  data.forEach(item => {
    if (item.daily_spending_history && typeof item.daily_spending_history === 'object') {
      Object.keys(item.daily_spending_history).forEach(dateStr => {
        const d = parseDateTime(dateStr);
        if (!maxDateFromHistory || d > maxDateFromHistory) {
          maxDateFromHistory = d;
        }
      });
    }
  });

  // Process data for all optimization sets
  data.forEach(item => {
    totalBudget += item.total_budget
    
    let itemStartDate = parseDateTime(item.start_date)
    let itemEndDate = parseDateTime(item.end_date)
    
    // MONTHLYRECURRING이거나 end_date가 9999-12-31인 경우 현재 달 기준으로 날짜 설정
    if (item.ad_budget_type === "MONTHLYRECURRING" || item.end_date.startsWith("9999-12-31")) {
      // 우선순위: 1) 서버 시간 정보, 2) 계정 타임존, 3) 로컬 시간
      let systemDate: Date;
      if (serverTimeInfo?.last_updated_date_time && serverTimeInfo?.timezone) {
        const [datePart] = serverTimeInfo.last_updated_date_time.split(' ');
        const [year, month, day] = datePart.split('-').map(Number);
        systemDate = new Date(year, month - 1, day); // month는 0-based
      } else if (accountTimeInfo?.time) {
        systemDate = new Date(accountTimeInfo.time);
      } else {
        systemDate = new Date();
      }
      
      const baseDate = (maxDateFromHistory && maxDateFromHistory > systemDate) ? maxDateFromHistory : systemDate;
      
      // 현재 달의 마지막날 + 1일을 end_date로 설정 (다음달 1일 00:00:00)
      const nextMonthFirstDay = new Date(baseDate.getFullYear(), baseDate.getMonth() + 1, 1)
      itemEndDate = nextMonthFirstDay
      
      // MONTHLYRECURRING인 경우에만 start_date도 현재 달의 1일로 설정
      if (item.ad_budget_type === "MONTHLYRECURRING") {
        const firstDayOfMonth = new Date(baseDate.getFullYear(), baseDate.getMonth(), 1)
        itemStartDate = firstDayOfMonth
      }
    }
    
    if (!startDate || itemStartDate < startDate) startDate = itemStartDate
    if (!endDate || itemEndDate > endDate) endDate = itemEndDate

    // Add spending data - keep all hourly data points separate
    if (item.daily_spending_history && typeof item.daily_spending_history === 'object') {
      Object.entries(item.daily_spending_history).forEach(([dateStr, spending]) => {
        const dateKey = dateStr.split('T')[0] // Use YYYY-MM-DD as key
        const currentSpending = dailySpendingMap.get(dateKey) || 0
        dailySpendingMap.set(dateKey, currentSpending + spending)
      })
    }
  })

  if (!startDate || !endDate) {
    return (
      <div className="w-full h-64 flex items-center justify-center text-gray-400">
        Invalid date range
      </div>
    )
  }

  // Create data points from aggregated spending
  const spendingPoints: Array<{ date: Date, spending: number, originalDateTimeStr: string }> = []
  dailySpendingMap.forEach((spending, dateStr) => {
    spendingPoints.push({
      date: parseDateTime(dateStr),
      spending,
      originalDateTimeStr: dateStr
    })
  })

  // Ensure start_date has a data point (with 0 spending if none exists)
  const startDateStr = formatDateTimeForHover(startDate)
  if (!dailySpendingMap.has(startDateStr)) {
    spendingPoints.push({
      date: startDate,
      spending: 0,
      originalDateTimeStr: startDateStr
    })
  }

  // Sort by date and calculate cumulative spending
  spendingPoints.sort((a, b) => a.date.getTime() - b.date.getTime())

  let cumulativeSpendingVal = 0
  const cumulativeSpendingData = spendingPoints.map(point => {
    cumulativeSpendingVal += point.spending
    return {
      date: point.date,
      spending: point.spending,
      cumulativeSpending: cumulativeSpendingVal,
      originalDateTimeStr: point.originalDateTimeStr
    }
  })

  // Sort by date - a-cumulative calculation is no longer needed
  const cumulative = cumulativeSpendingData.length > 0 ? cumulativeSpendingData[cumulativeSpendingData.length - 1].cumulativeSpending : 0

  // 최근 3일 판별을 위한 기준일 계산 (마지막 실제 데이터의 날짜 기준)
  const lastActualDate = cumulativeSpendingData.length ? cumulativeSpendingData[cumulativeSpendingData.length - 1].date : undefined
  const recentThreshold = lastActualDate
    ? new Date(lastActualDate.getFullYear(), lastActualDate.getMonth(), lastActualDate.getDate() - 2)
    : undefined

  const spentBudget = data.reduce((total, item) => total + item.spent_budget, 0)
  
  // 오늘 기준점: 데이터의 마지막 날짜(마지막 기록 시점) 사용
  let today: Date;
  if (cumulativeSpendingData.length > 0) {
    // allSpendingData는 날짜 기준으로 정렬되어 있으므로 마지막 요소가 가장 최근 날짜
    today = cumulativeSpendingData[cumulativeSpendingData.length - 1].date;
  } else if (serverTimeInfo?.last_updated_date_time && serverTimeInfo?.timezone) {
    const [datePart] = serverTimeInfo.last_updated_date_time.split(' ');
    const [year, month, day] = datePart.split('-').map(Number);
    today = new Date(year, month - 1, day); // month는 0-based
  } else if (accountTimeInfo?.time) {
    // 계정 타임존 기준 시간 사용
    today = new Date(accountTimeInfo.time);
  } else {
    today = new Date();
  }

  // Prepare data for Plotly
  const traces: any[] = []

  // Cumulative spending line (with per-point hover text)
  if (cumulativeSpendingData.length > 0) {
    // 각 데이터 포인트에 대해 최근 3일 여부 판별
    const cumulativeHover = cumulativeSpendingData.map(d => {
      const isRecentThreeDays = recentThreshold ? d.date >= recentThreshold : false
      return formatDateTimeForHover(d.date)
    })

    traces.push({
      x: cumulativeSpendingData.map(d => d.date),
      y: cumulativeSpendingData.map(d => d.cumulativeSpending),
      type: 'scatter',
      mode: 'lines',
      name: 'Cumulative Spending',
      line: { color: '#3B82F6', width: 3 },
      hovertemplate: `Cumulative Spending: %{y:${currencySymbolUnicode},.0f}<extra></extra>`,
    })
  }

  // Total budget line (horizontal dashed)
  traces.push({
    x: [startDate, endDate],
    y: [totalBudget, totalBudget],
    type: 'scatter',
    mode: 'lines',
    name: 'Total Budget',
    line: { color: '#EF4444', width: 2, dash: 'dash' },
    hoverinfo: 'skip',
  })

  // Spent budget line (horizontal dashed)
  if (spentBudget > 0) {
    traces.push({
      x: [startDate, endDate],
      y: [spentBudget, spentBudget],
      type: 'scatter',
      mode: 'lines',
      name: 'Spent Budget',
      line: { color: '#10B981', width: 2, dash: 'dash' },
      hoverinfo: 'skip',
    })
  }

  // Trend line (prediction from last data point) - 별도 trace로 분리
  if (cumulativeSpendingData.length > 0) {
    let trendStartPoint: { date: Date, cumulativeSpending: number };

    // '오늘'의 기준을 accountTimeInfo나 serverTimeInfo에서 가져온다.
    let systemNow: Date;
    if (accountTimeInfo?.time) {
      systemNow = new Date(accountTimeInfo.time);
    } else if (serverTimeInfo?.last_updated_date_time) {
      systemNow = parseDateTime(serverTimeInfo.last_updated_date_time);
    } else {
      systemNow = new Date();
    }
    
    const lastDataPoint = cumulativeSpendingData[cumulativeSpendingData.length - 1];
    const lastDataDate = lastDataPoint.date;

    // 마지막 데이터 날짜와 시스템의 '오늘' 날짜가 같은지 확인 (시간은 무시)
    const isLastDataFromToday = 
        lastDataDate.getFullYear() === systemNow.getFullYear() &&
        lastDataDate.getMonth() === systemNow.getMonth() &&
        lastDataDate.getDate() === systemNow.getDate();

    if (isLastDataFromToday && cumulativeSpendingData.length > 1) {
      // 마지막 데이터가 오늘 것이고, 데이터가 2개 이상이면 어제 데이터부터 trend 시작
      trendStartPoint = cumulativeSpendingData[cumulativeSpendingData.length - 2];
    } else {
      // 아니면 마지막 데이터부터 trend 시작
      trendStartPoint = lastDataPoint;
    }
    
    const trendStartDate = trendStartPoint.date;
    const trendStartSpending = trendStartPoint.cumulativeSpending;
    
    // Calculate average daily TARGET prediction from all optimization sets
    const totalDailyTarget = data.reduce((sum, item) => {
      return sum + (item.budget_usage_predictions?.TARGET || item.daily_recommended_budget || 0)
    }, 0)
    
    if (totalDailyTarget > 0) {
      // Create trend line data points from trend start point
      const trendData: Array<{ x: Date, y: number }> = []
      
      // 첫 번째 포인트: 트렌드 시작점
      trendData.push({
        x: new Date(trendStartDate),
        y: trendStartSpending
      })
      
      // 다음날 00:00부터 일별로 예측 추가
      let currentDate = new Date(trendStartDate.getFullYear(), trendStartDate.getMonth(), trendStartDate.getDate() + 1)
      let currentSpending = trendStartSpending
      
      // 일별 예측 추가
      while (currentDate <= endDate) {
        currentSpending += totalDailyTarget

        trendData.push({
          x: new Date(currentDate),
          y: currentSpending
        })

        currentDate.setDate(currentDate.getDate() + 1)
      }
      
      if (trendData.length > 1) {
        traces.push({
          x: trendData.map(d => d.x),
          y: trendData.map(d => d.y),
          type: 'scatter',
          mode: 'lines',
          name: 'Predicted Spending', // 'Predicted Trend'에서 'Predicted Spending'으로 변경
          line: { color: '#FBBF24', width: 2, dash: 'solid' },
          hovertemplate: `Predicted Spending: %{y:${currencySymbolUnicode},.0f}<extra></extra>`,
        })
      }
    }
  }

  // Today line (vertical dashed)
  if (today.getTime() >= startDate.getTime() && today.getTime() <= endDate.getTime()) {
    const maxY = Math.max(totalBudget, cumulative) * 1.1
    traces.push({
      x: [today, today],
      y: [0, maxY],
      type: 'scatter',
      mode: 'lines',
      name: 'Today',
      line: { color: '#9CA3AF', width: 2, dash: 'dot' },
      hoverinfo: 'skip',
    })
  }

  // Annotation for budget lines
  const annotations: any[] = []
  annotations.push({
    x: 1,
    xref: 'paper',
    xanchor: 'right',
    xshift: -5,
    y: totalBudget,
    yref: 'y',
    yanchor: 'middle',
    yshift: +12,
    text: `Total Budget: ${formatCurrency(totalBudget, currencyCode)}`,
    showarrow: false,
    font: { color: '#EF4444', size: 12 }
  })
  if (spentBudget > 0) {
    annotations.push({
      x: 1,
      xref: 'paper',
      xanchor: 'right',
      xshift: -5,
      y: spentBudget,
      yref: 'y',
      yanchor: 'middle',
      yshift: +12,
      text: `Spent Budget: ${formatCurrency(spentBudget, currencyCode)}`,
      showarrow: false,
      font: { color: '#10B981', size: 12 }
    })
  }

  const layout = {
    title: {
      text: '',
      font: { size: 16 }
    },
    xaxis: {
      showgrid: true,
      gridcolor: '#E5E7EB',
      gridwidth: 1,
      tickfont: { size: 10, color: '#9CA3AF' },
      tickangle: 0,
      range: [startDate, endDate],
      tickformat: '%Y-%m-%d',
      hoverformat: '%Y-%m-%d',
      zerolinecolor: "#e5e7eb",
      automargin: true,
      fixedrange: true,
    },
    yaxis: {
      rangemode: "tozero" as const,
      showgrid: true,
      gridcolor: "#f3f4f6",
      zerolinecolor: "#e5e7eb",
      tickfont: { size: 10, color: "#9CA3AF" },
      tickformat: ",.0f",
      tickprefix: currencySymbolUnicode === currencySymbol ? `${currencySymbol} ` : `${currencySymbolUnicode} `,
      automargin: true,
      fixedrange: true,
    },
    plot_bgcolor: 'white',
    paper_bgcolor: 'white',
    margin: { l: 0, r: 0, t: 0, b: 0 },
    hovermode: 'x unified' as const,
    showlegend: false,
    annotations
  }

  const config = {
    displayModeBar: false,
    responsive: true
  }

  return (
    <div className="size-full">
      <Plot
        data={traces}
        layout={layout}
        config={config}
        style={{ width: '100%', height: '100%' }}
      />
    </div>
  )
} 