import { ErrorResponse } from "@/app/api/_models";
import { getServerApiHostUrl } from "@/utils/host";
import { NextRequest, NextResponse } from "next/server";

export interface ReportListResponse {
  reports: Array<{
    id: string;
    status: string;
    report_type: string;
    report_name: string;
    report_title: string;
    requested_datetime: string;
    file_creation_datetime: string | null;
    account_id: string;
    marketplace_id: string;
    created_by: number;
    presigned_url_expired_at: string;
  }>;
}

export type CustomErrorResponse = ErrorResponse & {
  detail?: string;
};

export async function GET(
  request: NextRequest
): Promise<NextResponse<ReportListResponse | CustomErrorResponse>> {
  const accessToken = request.headers.get("Authorization");
  if (!accessToken) {
    return NextResponse.json(
      { message: "Authorization header is missing" },
      { status: 400 }
    );
  }

  const accountId = request.nextUrl.searchParams.get("account_id");
  if (!accountId) {
    return NextResponse.json(
      { message: "account_id parameter is missing" },
      { status: 400 }
    );
  }
  const marketplaceId = request.nextUrl.searchParams.get("marketplace_id");
  if (!marketplaceId) {
    return NextResponse.json(
      { message: "marketplace_id parameter is missing" },
      { status: 400 }
    );
  }

  try {    
    const reportListResponse = await fetch(
      `${await getServerApiHostUrl()}/api/custom_report/get_report_list?account_id=${accountId}&marketplace_id=${marketplaceId}`,
      {
        method: "GET",
        headers: {
          "Authorization": `Bearer ${accessToken}`,
          "Content-Type": "application/json",
        },
        cache: "no-store",
      }
    );

    if (!reportListResponse.ok) {
      const errorData = await reportListResponse.json();
      if (errorData.detail === "Token has expired") {
        return NextResponse.json(
          { detail: "Token has expired" } as CustomErrorResponse,
          { status: 401 }
        );
      }
      return NextResponse.json(
        { message: "Failed to get report list", error: errorData } as CustomErrorResponse,
        { status: reportListResponse.status }
      );
    }

    const result = await reportListResponse.json();
    return NextResponse.json(result, { status: 200 });
  } catch (error) {
    console.error("Error getting report list:", error);
    return NextResponse.json(
      { message: "Internal server error", error: String(error) } as CustomErrorResponse,
      { status: 500 }
    );
  }
} 