"use client"

import Plot from "react-plotly.js"
import { formatDate, getDateDifference } from "@/utils/msc"
import { useEffect, useMemo, useState } from "react"

type GraphData = {
  date: string;
  clickShare: number;
  adClickShare: number;
}

export default function WeeklyBrandShareGraph({ data, targetDate, setTargetDate }: { data: GraphData[], targetDate: Date, setTargetDate: (date: Date) => void }) {
  const initialDataset: any[] = useMemo(() => {
    const dataset = []
    dataset.push({
      x: data.map((d) => new Date(d.date)),
      y: data.map((d) => 0),
      customdata: data.map((d) => {
        const startDate = new Date(d.date);
        const endDate = new Date(startDate);
        endDate.setDate(startDate.getDate() + 6); // 7 days period (including start date)
        return `${formatDate(startDate, '.')} - ${formatDate(endDate, '.')}`;
      }),
      hovertemplate: '%{y:.2f}% (%{customdata})',
      type: 'bar',
      marker: {
        color: data.map((d, index) => index === data.length - 1 ? 'rgba(59, 130, 246, 1)' : 'rgba(59, 130, 246, 0.2)'),
      },
      name: 'click share',
    })
    dataset.push({
      x: data.map((d) => new Date(d.date)),
      y: data.map((d) => 0),
      customdata: data.map((d) => {
        const startDate = new Date(d.date);
        const endDate = new Date(startDate);
        endDate.setDate(startDate.getDate() + 6); // 7 days period (including start date)
        return `${formatDate(startDate, '.')} - ${formatDate(endDate, '.')}`;
      }),
      hovertemplate: '%{y:.2f}% (%{customdata})',
      type: 'scatter',
      mode: 'lines',
      line: { shape: 'spline', smoothing: 1.3 },
      xperiodalignment: 'start',
      marker: {
        color: '#eab308',
      },
      name: 'ad click share',
    })

    return dataset
  }, [data])

  const changedDataset = useMemo(() => {
    const dataset = []
    dataset.push({
      x: data.map((d) => new Date(d.date)),
      y: data.map((d) => d.clickShare),
      customdata: data.map((d) => {
        const startDate = new Date(d.date);
        const endDate = new Date(startDate);
        endDate.setDate(startDate.getDate() + 6); // 7 days period (including start date)
        return `${formatDate(startDate, '.')} - ${formatDate(endDate, '.')}`;
      }),
      hovertemplate: '%{y:.2f}% (%{customdata})',
      type: 'bar',
      marker: {
        color: data.map((d, index) => index === data.length - 1 ? 'rgba(59, 130, 246, 1)' : 'rgba(59, 130, 246, 0.2)'),
      },
      name: 'click share',
    })
    dataset.push({
      x: data.map((d) => new Date(d.date)),
      y: data.map((d) => d.adClickShare),
      customdata: data.map((d) => {
        const startDate = new Date(d.date);
        const endDate = new Date(startDate);
        endDate.setDate(startDate.getDate() + 6); // 7 days period (including start date)
        return `${formatDate(startDate, '.')} - ${formatDate(endDate, '.')}`;
      }),
      hovertemplate: '%{y:.2f}% (%{customdata})',
      type: 'scatter',
      mode: 'lines',
      line: { shape: 'spline', smoothing: 1.3 },
      xperiodalignment: 'start',
      marker: {
        color: '#eab308',
      },
      name: 'ad click share',
    })

    return dataset
  }, [data])

  const layout = {
    transition: {
      duration: 2000,
      easing: 'cubic-in-out',
    },
    margin: {
      l: 0,
      r: 0,
      b: 20,
      t: 60,
      pad: 0
    },
    paper_bgcolor: 'rgba(0,0,0,0)',
    plot_bgcolor: 'rgba(0,0,0,0)',
    autosize: true,
    showlegend: false,
    xaxis: {
      tickfont: {
        size: 10,
        color: '#9CA3AF'
      },
      tickformat: '%y.%m.%d',
      ticks: 'outside',
      tickcolor: 'rgba(0,0,0,0)',
      gridcolor: '#e5e7eb',
      zerolinecolor: '#d1d5db',
      tickmode: 'auto',
      
    },
    yaxis: {
      tickformat: '.0f', // Format as number with 0 decimal places
      ticksuffix: '%',   // Add % suffix to the ticks
      tickfont: {
        size: 10,
        color: '#9CA3AF'
      },
      ticks: 'outside',
      tickcolor: 'rgba(0,0,0,0)',
      gridcolor: '#e5e7eb',
      zerolinecolor: '#d1d5db',
      automargin: true,
      range: [0, 100],
    },
    hovermode: 'x unified',
    hoverlabel: {
      bgcolor: 'rgba(17, 24, 39, 0.9)',
      font: {
        size: 10,
        color: '#e5e7eb'
      },
    },
    dragmode: false,
  }
  const [graphInfo, setGraphInfo] = useState<any>({
    dataset: initialDataset,
    layout: layout,
  })

  const handleBarClick = (event: any) => {
    const clickedIndex = event.points[0].pointIndex;
    const clickedDate = data[clickedIndex]?.date;
    if (clickedDate) {
      setTargetDate(new Date(clickedDate));
    }
    const updatedDataset = graphInfo.dataset.map((trace: any) => {
      if (trace.type === 'bar') {
        return {
          ...trace,
          marker: {
            ...trace.marker,
            color: trace.marker.color.map((color: string, index: number) =>
              index === clickedIndex ? 'rgba(59, 130, 246, 1)' : 'rgba(59, 130, 246, 0.2)'
            ),
          },
        };
      }
      return trace;
    });
    setGraphInfo({ ...graphInfo, dataset: updatedDataset });
  };
      
  useEffect(() => {
    setTimeout(() => {
      setGraphInfo({
        dataset: changedDataset,
        layout: {
          ...layout,
        }
      })
    }, 100)
  }, [])

  // Update bar color when targetDate changes
  useEffect(() => {
    if (!targetDate) return;
    const clickedIndex = data.findIndex(d => {
      const dDate = new Date(d.date);
      return dDate.getFullYear() === targetDate.getFullYear() &&
        dDate.getMonth() === targetDate.getMonth() &&
        dDate.getDate() === targetDate.getDate();
    });
    if (clickedIndex === -1) return;
    setGraphInfo((prev: { dataset: any[] }) => {
      const updatedDataset = prev.dataset.map((trace: any) => {
        if (trace.type === 'bar') {
          return {
            ...trace,
            marker: {
              ...trace.marker,
              color: trace.marker.color.map((color: string, index: number) =>
                index === clickedIndex ? 'rgba(59, 130, 246, 1)' : 'rgba(59, 130, 246, 0.2)'
              ),
            },
          };
        }
        return trace;
      });
      return { ...prev, dataset: updatedDataset };
    });
  }, [targetDate, data]);
  return (
    <Plot
      data={graphInfo.dataset}
      layout={graphInfo.layout}
      config={{
        displayModeBar: false,
      }}
      useResizeHandler={true}
      onClick={handleBarClick}
      className="w-full h-full"
    />
  )
}
