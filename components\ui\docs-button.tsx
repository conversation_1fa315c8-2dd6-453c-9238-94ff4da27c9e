"use client"

import { useState } from "react"
import { useLocale } from 'next-intl';
import { cn } from "@/utils/msc"
import { Dialog, DialogBackdrop, DialogPanel, Transition, TransitionChild } from '@headlessui/react'
import { XMarkIcon, BookOpenIcon } from "@heroicons/react/20/solid"

export default function DocsButton() {
  const locale = useLocale();
  const [showDocsModal, setShowDocsModal] = useState(false);

  return (
    <>
      <button
        className={cn(
          "rounded-lg p-2 h-8 text-xs transition-colors hover:bg-accent text-slate-600 hover:text-slate-900",
          "cursor-pointer"
        )}
        onClick={() => setShowDocsModal(true)}
        >
        Docs
      </button>
      {showDocsModal && (
        <Transition appear show={showDocsModal}>
          <Dialog as="div" className="relative z-10 focus:outline-none" onClose={() => setShowDocsModal(false)}>
            <DialogBackdrop className="fixed inset-0 bg-black/30 backdrop-blur-sm" aria-hidden="true" />
            <div className="fixed inset-0 z-10 w-screen overflow-y-auto">
              <div className="flex min-h-full items-center justify-center py-4 px-12">
                <TransitionChild
                  enter="ease-out duration-300"
                  enterFrom="opacity-0 transform-[scale(95%)]"
                  enterTo="opacity-100 transform-[scale(100%)]"
                  leave="ease-in duration-200"
                  leaveFrom="opacity-100 transform-[scale(100%)]"
                  leaveTo="opacity-0 transform-[scale(95%)]"
                >
                  <DialogPanel className="w-full rounded-xl bg-white overflow-hidden shadow-lg">
                    <div className="px-6 pt-4 text-right">
                      <button onClick={() => setShowDocsModal(false)}>
                        <XMarkIcon className="h-5 w-5 text-gray-500 hover:text-gray-800" />
                      </button>
                    </div>
                    <div className="mt-2">
                      <iframe
                        src={
                          locale === "en"
                            ? "https://help.optapex.com/"
                            : "https://help-kr.optapex.com/"
                        }
                        title="Documentation"
                        className="w-full h-[600px] border-none"
                      />
                    </div>
                  </DialogPanel>
                </TransitionChild>
              </div>
            </div>
          </Dialog>
        </Transition>
      )}
    </>
  );
}