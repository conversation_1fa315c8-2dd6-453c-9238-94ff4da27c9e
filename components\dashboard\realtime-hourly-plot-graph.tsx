"use client"

import Plot from "react-plotly.js"
import { formatHour } from "@/utils/msc"
import { getCurrencySymbol, getCurrencySymbolUnicode } from "@/utils/currency"

// If `timestamp` is provided, it represents the exact key value returned from the
// API (e.g. "2024-12-27 13:00"). When absent we gracefully fall back to `hour`.
export type GraphData = {
  // full key (yyyy-MM-dd HH:mm) returned from API
  timestamp?: string;
  // numeric hour (0~23) for backward-compatibility
  hour?: number;

  // total performance
  sales: number;
  pageviews: number;
  ordered_units?: number;

  // sponsored products performance
  ad_cost: number;
  ad_sales: number;
  ad_sales_same_sku: number;
  ad_orders: number;
  clicks: number;
  impressions: number;

  // sponsored display performance
  sd_cost: number;
  sd_sales: number;
  sd_sales_same_sku: number;
  sd_clicks: number;
  sd_impressions: number;
}

interface RealtimeHourlyPlotGraphProps {
  selectedLegend: any[];
  data: GraphData[];
  onAfterPlot?: () => void;
  currencyCode?: string;
}

// This component is identical to HourlyPlotGraph but placed separately so that
// real-time page can import an isolated copy without affecting other pages.
export default function RealtimeHourlyPlotGraph({ selectedLegend, data, onAfterPlot, currencyCode = 'USD' }: RealtimeHourlyPlotGraphProps) {
  const dataSet: any[] = []

  // x-axis labels – prefer full timestamp, otherwise fallback to HH format
  const xLabels = data.map((d) => {
    if (d.timestamp) return d.timestamp
    if (typeof d.hour === 'number') return formatHour(d.hour)
    return ''
  })

  // Show tick label only when time is 00:00 → use date part as label
  const tickVals: string[] = []
  const tickText: string[] = []
  xLabels.forEach((lbl) => {
    // lbl 형식: yyyy-MM-dd HH:mm
    if (lbl.slice(11, 16) === '00:00') {
      tickVals.push(lbl)
      tickText.push(lbl.slice(0, 10)) // yyyy-MM-dd
    }
  })
  // 만약 00:00 값이 하나도 없다면 첫번째/마지막 라벨이라도 표시
  if (tickVals.length === 0 && xLabels.length > 0) {
    tickVals.push(xLabels[0])
    tickText.push(xLabels[0].slice(0, 10))
  }

  const currencySymbol = getCurrencySymbol(currencyCode)
  const currencySymbolUnicode = getCurrencySymbolUnicode(currencyCode)

  const pushTrace = (
    key: string,
    yAccessor: (d: GraphData) => number,
    options: { color: string; yAxis2?: boolean; isCurrency?: boolean }
  ) => {
    const legend = selectedLegend.find((l) => l.type === key)
    if (!legend) return

    // 통화 관련 메트릭인지 확인
    const currencyMetrics = ['sales', 'ad-cost', 'ad-sales', 'ad-sales-same-sku', 'sd-cost', 'sd-sales', 'sd-sales-same-sku']
    const isCurrencyMetric = currencyMetrics.includes(key) || options.isCurrency

    dataSet.push({
      x: xLabels,
      y: data.map(yAccessor),
      customdata: xLabels,
      type: 'scatter',
      mode: 'lines',
      line: { color: options.color },
      hovertemplate: isCurrencyMetric ? `%{y:${currencySymbolUnicode},.0f}` : '%{y:,.0f}',
      name: legend.name,
      ...(options.yAxis2 ? { yaxis: 'y2' } : {}),
    })
  }

  // total performance
  pushTrace('sales', (d) => d.sales, { color: '#a855f7', isCurrency: true })
  pushTrace('pageviews', (d) => d.pageviews, { color: '#ec4899', yAxis2: true })
  pushTrace('ordered-units', (d) => d.ordered_units ?? 0, { color: '#10b981', yAxis2: true })

  // ad performance (sp)
  pushTrace('ad-cost', (d) => d.ad_cost, { color: '#1d4ed8', isCurrency: true })
  pushTrace('ad-sales', (d) => d.ad_sales, { color: '#6366f1', isCurrency: true })
  pushTrace('ad-sales-same-sku', (d) => d.ad_sales_same_sku, { color: '#66d9e8', isCurrency: true })
  pushTrace('clicks', (d) => d.clicks, { color: '#f97316', yAxis2: true })
  pushTrace('impressions', (d) => d.impressions, { color: '#c53030', yAxis2: true })
  pushTrace('ad-orders', (d) => d.ad_orders, { color: '#10b981', yAxis2: true })

  // ad performance (sd)
  pushTrace('sd-cost', d => d.sd_cost, { color: '#d8a71d', isCurrency: true })
  pushTrace('sd-sales', d => d.sd_sales, { color: '#f1ee63', isCurrency: true })
  pushTrace('sd-sales-same-sku', d => d.sd_sales_same_sku, { color: '#e87566', isCurrency: true })
  pushTrace('sd-clicks', d => d.sd_clicks, { color: '#169cf9', yAxis2: true })
  pushTrace('sd-impressions', d => d.sd_impressions, { color: '#30c5c5', yAxis2: true })

  // Y축별 메트릭 확인
  const hasLeftAxisCurrencyMetric = selectedLegend.some(legend => 
    ['sales', 'ad-cost', 'ad-sales', 'ad-sales-same-sku', 'sd-cost', 'sd-sales', 'sd-sales-same-sku'].includes(legend.type)
  )
  
  const hasRightAxisMetric = selectedLegend.some(legend => 
    ['pageviews', 'ordered-units', 'clicks', 'impressions', 'ad-orders', 'sd-clicks', 'sd-impressions'].includes(legend.type)
  )

  return (
    <Plot
      data={dataSet as any}
      layout={{
        margin: { l: 60, r: 60, b: 30, t: 20, pad: 0 },
        autosize: true,
        showlegend: false,
        xaxis: {
          type: 'category',
          tickfont: { size: 10, color: '#9CA3AF' },
          tickangle: 0,
          zerolinecolor: '#e5e7eb',
          tickmode: 'array',
          tickvals: tickVals,
          ticktext: tickText,
          fixedrange: true,
          range: [-0.5, xLabels.length - 0.5],
        },
        yaxis: {
          rangemode: 'tozero',
          tickformat: ',~s',
          tickprefix: hasLeftAxisCurrencyMetric ? (currencySymbolUnicode === currencySymbol ? `${currencySymbol} ` : `${currencySymbolUnicode} `) : '',
          tickfont: { size: 10, color: '#9CA3AF' },
          gridcolor: '#f3f4f6',
          zerolinecolor: '#e5e7eb',
          automargin: true,
          fixedrange: true,
        },
        yaxis2: {
          rangemode: 'tozero',
          tickformat: ',~s',
          tickprefix: '', // 오른쪽 Y축에는 통화 기호 표시하지 않음
          tickfont: { size: 10, color: '#9CA3AF' },
          gridcolor: '#f3f4f6',
          zerolinecolor: '#e5e7eb',
          overlaying: 'y',
          side: 'right',
          automargin: true,
          fixedrange: true,
        },
        hovermode: 'x unified',
        hoverlabel: {
          bgcolor: 'rgba(17, 24, 39, 0.9)',
          font: { size: 10, color: '#e5e7eb' },
        },
        dragmode: false,
      }}
      config={{ 
        displayModeBar: false,
        scrollZoom: false,
        doubleClick: false,
        editable: false,
        staticPlot: false,
        responsive: true,
      }}
      useResizeHandler
      className="w-full h-full"
      style={{ width: '100%', height: '100%' }}
      onAfterPlot={onAfterPlot}
    />
  )
} 