"use client"

import Plot from "react-plotly.js"
import { formatDate, getDateDifference } from "@/utils/msc"
import { useEffect, useMemo, useState } from "react"

type GraphData = {
  date: string;
  value: number;
}

export default function BrandShareGraph({ data, targetDate, setTargetDate }: { data: GraphData[], targetDate: Date, setTargetDate: (date: Date) => void }) {
  const initialDataset: any[] = useMemo(() => {
    const dataset = []
    dataset.push({
      x: data.map((d) => new Date(d.date)),
      y: data.map((d) => 0),
      customdata: data.map((d) => formatDate(d.date, '.')),
      hovertemplate: '%{y:.2f} (%{customdata})',
      type: 'bar',
      marker: {
        color: data.map((d, index) => index === data.length - 1 ? 'rgba(59, 130, 246, 1)' : 'rgba(59, 130, 246, 0.2)'),
      },
      name: 'score',
    })
    dataset.push({
      x: data.map((d) => new Date(d.date)),
      y: data.map((d) => 0),
      customdata: data.map((d) => formatDate(d.date, '.')),
      type: 'scatter',
      mode: 'lines',
      line: { shape: 'spline', smoothing: 1.3 },
      xperiodalignment: 'start',
      marker: {
        color: 'rgba(255, 255, 255, 0.9)',
      },
      name: 'brand share line',
      hoverinfo: 'skip', // Hide label in tooltip
    })

    return dataset
  }, [data])

  const changedDataset = useMemo(() => {
    const dataset = []
    dataset.push({
      x: data.map((d) => new Date(d.date)),
      y: data.map((d) => d.value),
      customdata: data.map((d) => formatDate(d.date, '.')),
      hovertemplate: '%{y:.2f} (%{customdata})',
      type: 'bar',
      marker: {
        color: data.map((d, index) => index === data.length - 1 ? 'rgba(59, 130, 246, 1)' : 'rgba(59, 130, 246, 0.2)'),
      },
      name: 'score',
    })
    dataset.push({
      x: data.map((d) => new Date(d.date)),
      y: data.map((d) => d.value),
      customdata: data.map((d) => formatDate(d.date, '.')),
      type: 'scatter',
      mode: 'lines',
      line: { shape: 'spline', smoothing: 1.3 },
      xperiodalignment: 'start',
      marker: {
        color: 'rgba(255, 255, 255, 0.9)',
      },
      name: 'brand share line',
      hoverinfo: 'skip', // Hide label in tooltip
    })

    return dataset
  }, [data])

  const layout = {
    transition: {
      duration: 2000,
      easing: 'cubic-in-out',
    },
    margin: {
      l: 0,
      r: 0,
      b: 20,
      t: 60,
      pad: 0
    },
    paper_bgcolor: 'rgba(0,0,0,0)',
    plot_bgcolor: 'rgba(0,0,0,0)',
    autosize: true,
    showlegend: false,
    xaxis: {
      tickfont: {
        size: 10,
        color: '#9CA3AF'
      },
      tickformat: '%y.%m.%d',
      ticks: 'outside',
      tickcolor: 'rgba(0,0,0,0)',
      gridcolor: '#e5e7eb',
      zerolinecolor: '#d1d5db',
      tickmode: 'auto',
      nticks: data.length > 10 ? 10 : data.length,
      range: [
        data.length > 0 ? data[0].date : new Date().toISOString(),
        data.length > 0
          ? new Date(new Date(data[data.length - 1].date).setDate(new Date(data[data.length - 1].date).getDate() + 1))
          : new Date(new Date().setDate(new Date().getDate() + 1)).toISOString(),
      ],
    },
    yaxis: {
      tickformat: '.2f', // Show floats with 2 decimal places
      tickfont: {
        size: 10,
        color: '#9CA3AF'
      },
      ticks: 'outside',
      tickcolor: 'rgba(0,0,0,0)',
      gridcolor: '#e5e7eb',
      zerolinecolor: '#d1d5db',
      automargin: true,
      range: [0, 1],
    },
    hovermode: 'x unified',
    hoverlabel: {
      bgcolor: 'rgba(17, 24, 39, 0.9)',
      font: {
        size: 10,
        color: '#e5e7eb'
      },
    },
    dragmode: false,
  }
  const [graphInfo, setGraphInfo] = useState<any>({
    dataset: initialDataset,
    layout: layout,
  })

  const handleBarClick = (event: any) => {
    const clickedIndex = event.points[0].pointIndex;
    const clickedDate = data[clickedIndex]?.date;
    if (clickedDate) {
      setTargetDate(new Date(clickedDate));
    }
    const updatedDataset = graphInfo.dataset.map((trace: any) => {
      if (trace.type === 'bar') {
        return {
          ...trace,
          marker: {
            ...trace.marker,
            color: trace.marker.color.map((color: string, index: number) =>
              index === clickedIndex ? 'rgba(59, 130, 246, 1)' : 'rgba(59, 130, 246, 0.2)'
            ),
          },
        };
      }
      return trace;
    });
    setGraphInfo({ ...graphInfo, dataset: updatedDataset });
  };
      
  useEffect(() => {
    setTimeout(() => {
      setGraphInfo({
        dataset: changedDataset,
        layout: {
          ...layout,
        }
      })
    }, 100)
  }, [])

  // Update bar color when targetDate changes
  useEffect(() => {
    if (!targetDate) return;
    const clickedIndex = data.findIndex(d => {
      const dDate = new Date(d.date);
      return dDate.getFullYear() === targetDate.getFullYear() &&
        dDate.getMonth() === targetDate.getMonth() &&
        dDate.getDate() === targetDate.getDate();
    });
    if (clickedIndex === -1) return;
    setGraphInfo((prev: { dataset: any[] }) => {
      const updatedDataset = prev.dataset.map((trace: any) => {
        if (trace.type === 'bar') {
          return {
            ...trace,
            marker: {
              ...trace.marker,
              color: trace.marker.color.map((color: string, index: number) =>
                index === clickedIndex ? 'rgba(59, 130, 246, 1)' : 'rgba(59, 130, 246, 0.2)'
              ),
            },
          };
        }
        return trace;
      });
      return { ...prev, dataset: updatedDataset };
    });
  }, [targetDate, data]);
  return (
    <Plot
      data={graphInfo.dataset}
      layout={graphInfo.layout}
      config={{
        displayModeBar: false,
      }}
      useResizeHandler={true}
      onClick={handleBarClick}
      className="w-full h-full"
    />
  )
}
