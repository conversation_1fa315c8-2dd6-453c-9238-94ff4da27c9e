{"AppLayout": {"home": "Home", "logout": "로그아웃", "profile": "프로필"}, "HomePage": {"title": "Home"}, "LocaleSwitcher": {"ko": "한국어", "en": "English", "cn": "中文", "label": "언어"}, "LoginPage": {"description": "Please enter your details.", "email": "이메일 주소", "invalidCredentials": "Please check your credentials.", "invalidEmail": "유효한 이메일 주소를 입력해주세요.", "invalidPassword": "비밀번호를 입력해주세요.", "login": "로그인", "password": "비밀번호", "title": "Welcome back"}, "component": {"gnb": {"logo": "OptApex", "account": {"logout": "로그아웃", "withdraw": "계정 삭제"}}, "accounts": {"title": "나의 아마존 계정 관리", "description": "전체 기능을 사용하려면 아마존 계정 연동이 필요합니다. SP와 Ads 계정 또는 Vendor와 Ads 계정의 조합으로 연동하세요.", "noProductData": "연결된 상품 데이터 없음", "productData": "상품 데이터", "authorizeAds": "광고 계정 인증", "authorizeSP": "SP 권한 활성화", "authorizeVendor": "Vendor 권한 활성화", "selectRegion": "지역 선택", "selectRegionDescription": "아마존 광고 계정을 인증하려면 지역을 선택해야 합니다.", "chooseProductData": "연결할 상품 데이터를 선택하세요.", "productDataAvailable": "개 상품 선택 가능", "type": "유형", "actions": "작업", "cancel": "취소", "confirm": "확인", "chooseProductType": "인증할 상품 데이터 유형을 선택하세요", "authorizationDescription": "아마존에서 SP 또는 Vendor 데이터를 인증하면 추가 인증 없이 각 마켓플레이스를 광고 프로필과 연결할 수 있습니다.", "areSeller": "Seller 입니까?", "areVendor": "Vendor 입니까?", "notConnected": "연결되지 않음"}, "lnb": {"profile": {"region": "지역", "profileEmail": "프로필 이메일", "profileName": "프로필명", "profileId": "프로필 ID"}, "marketplace": {"country": {"usa": "US", "canada": "CA", "mexico": "MX", "japan": "JP", "china": "CN"}, "currency": {"USD": "USD", "CAD": "CAD", "MXN": "MXN", "JPY": "JPY", "CNY": "CNY"}, "title": {"usMarketTitle": "{{marketplace.country.usa}} {{marketplace.currency.USD}}", "caMarketTitle": "{{marketplace.country.canada}} {{marketplace.currency.CAD}}", "mxMarketTitle": "{{marketplace.country.mexico}} {{marketplace.currency.MXN}}", "jpMarketTitle": "{{marketplace.country.japan}} {{marketplace.currency.JPY}}", "cnMarketTitle": "{{marketplace.country.china}} {{marketplace.currency.CNY}}"}, "marketplaceName": {"label": "마켓플레이스명", "content": {"usMarketplace": "Amazon.com", "caMarketplace": "Amazon.ca", "mxMarketplace": "Amazon.com.mx", "jpMarketplace": "Amazon.co.jp", "cnMarketplace": "Amazon.cn"}}, "marketplaceId": {"label": "마켓플레이스 ID"}}, "menu": {"accounts": "Accounts", "home": "Home", "competition": "Brand Protection", "reports": "Reports", "optimizationSets": "Optimization Set", "realtime": "Real-time", "downloads": "Downloads", "notifications": "Notifications", "manageAccounts": "Accounts", "manageBillings": "<PERSON><PERSON>"}}, "calendar": {"month": ["1월", "2월", "3월", "4월", "5월", "6월", "7월", "8월", "9월", "10월", "11월", "12월"], "days": ["일", "월", "화", "수", "목", "금", "토"]}, "eligibility": {"eligible": "판매 가능", "ineligible": "판매 불가능", "unknown": "확인 불가"}, "productStatus": {"mopAuto": "최적화 중", "amazonAuto": "Amazon Auto", "ineligible": "광고 불가", "initializing": "Initializing", "processing": "Processing", "paused": "일시 중지", "active": "운영 중", "expired": "만료됨", "warning": "경고", "error": "오류"}, "campaignType": {"auto": "자동 타겟팅", "manual": "수동 타겟팅"}, "portfolioStatus": {"initializing": "Initializing", "processing": "Processing", "paused": "일시 중지", "active": "운영 중", "expired": "만료됨"}, "downloadStatus": {"creating": "생성 중", "created": "생성 완료", "error": "오류 발생", "expired": "기간 만료"}, "adStatus": {"enabled": "운영 중", "paused": "일시 중지"}, "accountStatus": {"initializing": "Initializing", "processing": "Processing", "paused": "일시 중지", "active": "운영 중"}, "filter": {"dateRange": {"label": "조회 기간"}, "compareWith": {"label": "기간 비교", "content": {"null": "미선택", "previousMonth": "지난달", "previousYear": "작년", "customPeriod": "직접 선택"}}}, "status": {"notAvailable": "사용 불가", "connected": "connected", "activated": "Activated", "activate": "Activate"}, "billing": {"title": "나의 결제 관리", "table": {"header": {"status": "상태", "accountsPair": "계정 연동", "updatedDate": "갱신일자", "pairActions": "연동 관리", "billing": "결제"}}}}, "CompetitionPage": {"daily": "일별", "weekly": "주별", "topBannerTitle": "브랜드 보호 점수", "tableTitle": "내 브랜드 키워드 보호 현황", "weeklyFunnel": {"topButton": "주간 퍼널", "modalTitle": "주간 퍼널", "targetPeriod": "대상 기간", "targetKeywords": "타겟 키워드", "myBrandShare": "내 브랜드 점유율", "competitorShare": "경쟁사 점유율", "table": {"header": {"keyword": "키워드", "impressionShare": "노출 점유율", "leadingProducts": "주요 제품"}}}, "keywordModalTitle": "키워드 경쟁 현황", "overview": {"weeklyAvgKeywordRank": "주간 평균 키워드 순위", "weeklyAvgRelKeywordRank": "주간 평균 상대 키워드 순위", "weeklyBrandKeywordLeadershipRate": "주간 브랜드 키워드 리더십 비율"}, "searchBar": {"placeholder": "키워드로 검색하세요."}, "tooltip": {"brandProtectionScore": "브랜드 보호 점수는 귀하의 브랜드 키워드가 경쟁으로부터 얼마나 잘 보호되고 있는지에 대한 인사이트를 제공합니다. 점수는 0에서 1까지로, 점수가 높을수록 보호가 강하다는 의미입니다.<enter></enter><enter></enter>이 점수는 LG Optapex의 분류 알고리즘이 선정한 브랜드 키워드의 클릭 점유율과 경쟁자 수를 기반으로 산출됩니다.", "keywordModalTitle": "브랜드의 클릭 점유율 리더십 추이를 주요 경쟁사와 비교하여 확인할 수 있습니다.<enter></enter><enter></enter>주요 경쟁사는 LG Optapex의 알고리즘을 기반으로 제품 순위, 가격, 리스팅 정보, 내 브랜드 제품과의 키워드 관계를 활용해 선정됩니다."}, "table": {"header": {"queryVolumeRank": "검색량 순위", "myBrandKeyword": "내 브랜드 키워드", "myBrandClickShare": "내 브랜드 클릭 점유율", "leadingProducts": "선도 제품"}}, "keywordModal": {"myBrandTotalClickShare": "내 브랜드 총 클릭 점유율", "keyCompetitorsTotalClickShare": "주요 경쟁사 총 클릭 점유율"}}, "DashboardPage": {"title": "아마존 퍼포먼스", "filter": {"dateRange": {"label": "조회 기간"}, "attributionWindow": {"label": "어트리뷰션 기간", "content": {"1d": "1일", "7d": "7일", "14d": "14일", "30d": "30일"}}, "optimizationSet": {"label": "최적화 세트", "content": {"optSet": "최적화 세트", "null": "미선택"}}, "productFilter": {"label": "선택 제품"}, "compareWith": {"label": "기간 비교", "content": {"null": "미선택", "previousMonth": "지난달", "previousYear": "작년", "customPeriod": "직접 선택"}}, "customPeriod": {"label": "비교 기간"}, "advancedFilter": {"label": "고급 필터", "subLabel": {"lowInventory": "재고 부족", "excessInventory": "재고 과다", "recommendedAsin": "추천 ASIN"}, "content": {"null": "미선택", "low30d": "제품 공급 일수 < 30일", "low60d": "제품 공급 일수 < 60일", "low90d": "제품 공급 일수 < 90일", "excess180d": "제품 공급 일수 > 180일", "excess270d": "제품 공급 일수 > 270일", "excess365d": "제품 공급 일수 > 365일", "growth": "성장 잠재", "efficiency": "효율화 잠재"}}}, "keyMetrics": {"totalMetrics": {"totalSales": "총 매출", "estFee": "예상 아마존 수수료", "estMargin": "예상 총 이익률", "totalCogsRevenue": "벤더 매출", "totalRevenue": "매출"}, "adMetrics": {"adSales": "광고 매출", "adSpend": "광고 비용", "roas": "ROAS"}, "metricsTooltip": {"totalSales": "<first>환불된 ${value}는 포함되지 않았습니다.</first>", "estFee": "주문 처리 수수료(FBA Fee) + 판매 수수료(Referral Fee) + 보관 수수료(Storage Fee)", "estimatedMargin": "(총 매출 - 반품액 - 예상 판매 수수료 - 광고비 - 프로모션 할인액) / 총 매출 * 100", "totalCogsRevenue": "<first>벤더 매출은 Amazon이 지급하는 공급업체의 수익입니다. Amazon 판매 매출 {value}는 고객에게 판매된 실제 가격의 합계입니다.</first>", "totalRevenue": "수익은 고객이 제품에 대해 실제로 지불한 가격의 합계입니다.", "vendorEstimatedMargin": "(벤더 매출 - 반품액 - 예상 판매 수수료 - 광고비) / 벤더 매출 * 100", "roas": "(SP 매출 + SD 매출) / (SP 광고 비용 + SD 광고 비용) * 100"}}, "graph": {"tab": {"daily": "일별", "hourly": "시간별", "tabTooltip": {"hourly": "조회 기간 동안 특정 시간의 평균값을 표시합니다."}}, "totalMetrics": {"absolute": {"totalSales": "총 매출", "estMargin": "예상 총 이익률", "pageViews": "총 클릭"}, "compared": {"totalSales": "총 매출 (비교기간)", "estMargin": "예상 총 이익률 (비교기간)", "pageViews": "총 클릭 (비교기간)"}}, "adMetrics": {"absolute": {"adSales": "광고 매출", "roas": "ROAS", "adClicks": "광고 클릭"}, "compared": {"adSales": "광고 매출 (비교기간)", "roas": "ROAS (비교기간)", "adClicks": "광고 클릭 (비교기간)"}}, "legendSelect": {"sales": "총 매출", "adSales": "광고 매출", "adSalesSameSku": "동일 SKU 광고 매출", "sdSalesPromotedClick": "SD 광고 클릭", "pageViews": "총 클릭", "clicks": "광고 클릭", "impressions": "노출", "sessions": "세션 수", "roas": "ROAS", "estimatedMargin": "예상 총 이익률", "comparedSales": "총 매출 (비교기간)", "comparedAdSales": "광고 매출 (비교기간)", "comparedAdSalesSameSku": "동일 SKU 광고 매출 (비교기간)", "comparedSdSalesPromotedClick": "SD 광고 클릭 (비교기간)", "comparedPageViews": "총 클릭 (비교기간)", "comparedClicks": "광고 클릭 (비교기간)", "comparedImpressions": "노출 (비교기간)", "comparedSessions": "세션 수 (비교기간)", "comparedRoas": "ROAS (비교기간)", "comparedEstimatedMargin": "예상 총 이익률 (비교기간)", "revenue": "매출", "cogsRevenue": "벤더 매출", "adCost": "광고 비용", "glanceViews": "Glance Views", "comparedRevenue": "매출 (비교기간)", "comparedCogsRevenue": "벤더 매출 (비교기간)", "comparedAdCost": "광고 비용 (비교기간)", "comparedGlanceViews": "Glance Views (비교기간)", "orderedUnits": "주문 수량", "adOrders": "Ad Orders"}}, "table": {"header": {"products": "제품", "totalPerformance": "통합 퍼포먼스", "adPerformance": "광고 퍼포먼스"}, "subHeader": {"productsAdInfo": "제품 & 광고 정보"}, "content": {"products": {"rank": "<first>{value2} 내 <second>#{value1}</second>위</first>", "fee": "주문당 수수료", "points": "포인트", "inventory": "재고", "inventoryType": {"available": "available", "unfulfillable": "unfulfillable", "reserved": "reserved", "inbound": "inbound", "openPurchase": "아마존 구매 예정 수량"}, "asin": "ASIN", "sku": "SKU"}, "totalMetrics": {"cogsRevenue": "벤더 매출", "totalRevenue": "아마존 판매 매출", "sales": "총 매출", "estFee": "예상 아마존 수수료", "shippedUnits": "배송 개수", "unitsSold": "판매 수량", "unitsReturned": "환불 수량", "salesReturned": "환불 금액", "sessions": "세션 수", "buyBoxPercentage": "Buy Box 점유율", "glanceViews": "Glance View", "pageViews": "총 클릭", "cvrTotal": "CVR (전체)"}, "adMetrics": {"adSpend": "광고 비용", "adSales": "광고 매출", "adUnitsSold": "SP 광고 판매 수량", "attributedSalesSameSku": "SP 광고 매출 (동일 SKU)", "unitsSoldSameSku": "SP 광고 판매 수량 (동일 SKU)", "tacos": "TACOS", "profit": "Profit", "acos": "ACOS", "spAcos": "SP ACOS", "roas": "ROAS", "impressions": "노출", "spImpressions": "SP 노출", "clicks": "광고 클릭", "spClicks": "SP 광고 클릭", "ctr": "CTR", "spCtr": "SP CTR", "avgCPC": "평균 CPC", "spAvgCPC": "SP 평균 CPC", "cvrAds": "CVR (광고)", "spCvr": "SP 전환율", "spCost": "SP 광고 비용", "spSales": "SP 광고 매출", "spRoas": "SP ROAS", "sdSales": "SD 광고 매출", "sdSalesPromotedClicks": "SD 광고 클릭", "sdCost": "SD 광고 비용", "sdUnitsSold": "SD 광고 판매 수량", "sdClicks": "SD 광고 클릭", "sdImpressions": "SD 노출", "sdImpressionsViews": "SD 노출 뷰", "sdCumulativeReach": "SD 누적 도달", "sdDetailPageViews": "SD 상세 페이지 뷰", "sdNewToBrandDetailPageViews": "SD 신규 고객 상세 페이지 뷰", "sdNewToBrandSales": "SD 신규 고객 매출", "sdRoas": "SD ROAS", "sdAcos": "SD ACOS", "sdConvRate": "SD 전환율", "sdViewClickThroughRate": "SD 뷰 클릭율", "sdNewToBrandSalesRate": "SD 신규 고객 매출 비율", "sdImpressionFrequencyAverage": "SD 평균 노출 빈도", "sdViewabilityRate": "SD 뷰어빌리티 비율", "sdNewToBrandDetailPageViewRate": "SD 신규 고객 상세 페이지 뷰 비율"}}, "adDetailRow": {"header": {"status": "상태", "runBy": "운영", "campaign": "캠페인"}, "content": {"status": {"enabled": "운영 중", "paused": "일시 중지", "archived": "아카이브"}, "runBy": {"amazon": "Amazon", "mop": "OptApex"}, "campaign": {"id": "ID", "budget": "예산", "type": {"auto": "자동 타겟팅", "manual": "수동 타겟팅"}}}}}, "viewSetting": {"rowsPerPage": "페이지당 결과수", "currentRow": "{value1} / {value2}개 결과 "}}, "optimizationSets": {"title": "Optimization Set", "topButton": {"addNew": "신규 생성"}, "searchBar": {"placeholder": "최적화 세트명으로 검색하세요."}, "optSetList": {"header": {"status": "상태", "name": "세트명", "period": "최적화 기간", "budgetPacing": "예산 집행 현황", "creationDate": "생성일"}, "content": {"optSetRow": {"status": {"active": "운영 중", "paused": "일시 중지"}, "statusAlert": {"abnormal": "이상 감지", "warning": "주의 필요"}, "name": {"optSet": "최적화 세트", "topProduct": "대표 제품"}, "period": {"optObjective": {"maxSales": "총 매출 극대화", "maxProfit": "총 이익 극대화", "maxAdSales": "광고 매출 극대화", "maxRoas": "ROAS 극대화", "vendorMaxProfit": "벤더 이익 극대화", "vendorMaxSales": "벤더 매출 극대화"}}, "budgetPacing": {"totalBudget": "총 예산", "monthlyBudget": "총 예산"}, "searchResultNull": "검색된 최적화 세트가 없습니다.", "optSetNull": "현재 생성된 최적화 세트가 없습니다."}, "productRow": {"statusAlert": {"abnormal": "이상 감지", "warning": "주의 필요"}, "asin": "ASIN", "sku": "SKU"}}}, "detailModal": {"accountPair": {"message": {"dateDifference": "<first>{value}</first> <second>일 전에 계정 연동이 완료되었습니다.</second>", "initialWarning": "아마존으로부터 데이터를 받아오는 데까지 일반적으로 계정 연동 완료 후 1~2 주 이상 소요됩니다."}}, "optSet": {"topButton": {"edit": "편집", "resume": "재시작", "pause": "일시 중지", "delete": "세트 삭제"}, "topStatus": {"active": "운영 중", "paused": "일시 중지"}, "message": {"ineligibleDetails": "<first>{value}</first><second>개의 <red>판매 불가능한 제품</red>이 존재합니다.</second>", "totalBudget": "<second>총 예산:</second> <first>{value}</first>", "expectedUsage": " <second>(예상) 총 예산 소진:</second> <first>{value}</first>", "feasibleBudgetRange": "<second>실현 가능한 예산 범위:</second> <first>{value}</first>", "and": " 그리고 ", "ineligible": "판매 불가능한 제품이 존재합니다.", "budgetDecrease": "예산 하향 조정이 필요합니다.", "budgetIncrease": "예산 상향 조정이 필요합니다."}, "budgetSensitivityGraph": {"title": "예산 시뮬레이터", "cta": "최적의 예산 찾기", "tooltip": {"budgetGuideline": "예산 가이드는 현재 최적화 세트의 상태를 기반으로 최근 성과와 트렌드를 분석하여 최적의 예산 범위를 제안합니다."}, "warning": "향후 7일간의 예상 성과를 확인하세요.", "maximum": "Maximum", "minimum": "Minimum", "optimalBudget": "Optimal Budget", "optimizationTarget": "최적화 목표", "optimizationNotStarted": "최적의 예산을 찾기 위해 시뮬레이션을 시작하세요.", "startSimulation": "시뮬레이션 시작", "totalProfit": {"label": "Total Profit"}, "totalSales": {"label": "Total Sales"}, "totalCost": {"label": "Total Cost"}, "adSpend": {"label": "Ad Spend"}, "adSales": {"label": "Ad Sales"}, "adSalesSameSku": {"label": "Ad Sales Same SKU"}}, "budgetPaceGraph": {"title": {"daterangeBudget": "예산 집행 현황", "monthlyBudget": "예산 집행 현황"}, "budgetType": {"noTracking": {"label": "미추적", "details": "예상 소진량 미추적"}, "abnormal": {"label": "조정 필요", "underAbnormal": {"details": "예산 과소 소진", "action": "예산을 줄여주세요."}, "overAbnormal": {"details": "예산 과다 소진", "action": "예산을 늘려주세요."}}, "normal": {"label": "정상", "details": "예산 정상 소진"}}, "metricType": {"usage": {"daterange": "(누적) 예산 소진", "monthly": "(누적) 예산 소진"}, "usageExpected": "(예상) 총 예산 소진", "budget": {"daterange": "전체 예산 규모", "monthly": "전체 예산 규모"}}}, "optProfile": {"label": "최적화 세트", "optSetName": "최적화 세트 {value}", "optSetObjective": {"label": "목표", "objectiveType": {"maxSales": "총 매출 극대화", "maxProfit": "총 이익 극대화", "maxAdSales": "광고 매출 극대화", "maxRoas": "ROAS 극대화", "vendorMaxProfit": "벤더 이익 극대화", "vendorMaxSales": "벤더 매출 극대화"}, "option": {"default": "부스트 기능 미선택", "minInventory": "재고 최소화 옵션", "boostImpression": "노출 강화 옵션"}}, "optSetPeriod": {"label": "기간", "policy": {"daterange": "날짜 지정", "monthly": "매월 반복"}, "createdDate": "생성일"}}, "budgetTab": {"tabLabel": "예산 최적화 현황", "searchBar": {"placeholder": "제품명 혹은 ASIN으로 검색하세요."}, "table": {"header": {"status": "상태", "productInfo": "제품 정보", "totalBudgetUsage": "예산 사용율"}, "content": {"statusAlert": {"warning": "주의 필요", "abnormal": "이상 감지"}, "productInfo": {"asin": "ASIN", "sku": "SKU"}, "searchResultNull": "검색된 최적화 세트가 없습니다", "optSetNull": "현재 생성된 최적화 세트가 없습니다."}}}, "historyTab": {"tabLabel": "변경 이력", "filter": {"dateRange": {"label": "조회 기간"}}, "table": {"header": {"actionType": "변경 유형", "actionDetails": "세부 변경 사항", "dateTime": "변경 일시"}, "content": {"actionType": {"create": "최적화 세트 생성", "edit": "최적화 세트 수정", "pauseResume": "최적화 세트 운영 중지/재개"}, "actionDetails": {"budgetPolicy": "예산 규칙", "budgetAmount": "예산 규모", "budgetStartDate": "최적화 시작일", "budgetEndDate": "최적화 종료일"}, "null": "변경 이력이 없습니다."}}}}, "product": {"message": {"abnormal": {"ineligibleAlert": "해당 제품은 현재 판매 불가능한 상태입니다.", "action": "<first><red>Amazon Seller Central</red> 확인 후 조치가 필요합니다.</first>"}, "warning": {"lowStockAlert": "해당 제품은 재고 부족 상태입니다.", "estimatedSold": "<first>하루 예상 판매량: <orange1>{value}</orange1> <orange2>개</orange2></first>", "available": "<first>판매 가능: <orange1>{value}</orange1> <orange2>개</orange2></first>", "estimatedDaysLeft": "<first>(예상) 품절까지 <orange1>{value}</orange1> <orange2>일</orange2></first>"}}, "productProfile": {"label": {"optSet": "최적화 세트", "asin": "ASIN", "sku": "SKU"}}, "campaignTab": {"tabLabel": "캠페인", "tabTooltip": "처음 2주는 아마존의 추천 입찰 및 예산 운영으로 집행됩니다.<enter></enter><enter></enter>2주간의 아마존 자동 광고 운영 성과를 기반으로 OptApex가 정해진 예산 하에서 설정 목표에 도달할 수 있도록 입찰가를 자동 최적화합니다.", "cumulatedBudgetUsage": "누적 예산 소진", "table": {"header": {"status": "상태", "campaignInfo": "캠페인 정보", "dailyBudgetUsage": "하루 예산 소진"}, "content": {"status": {"enabled": "운영 중", "paused": "일시 중지"}, "campaignInfo": {"id": "ID", "startDate": "시작일"}}}, "donutChart": {"dailyBudgetUsage": "하루 예산 소진"}}, "productHistoryTab": {"tabLabel": "제품 이력", "filter": {"history": {"label": "변경 유형", "type": {"operation": "운영", "budget": "예산"}}, "date": {"label": "조회 기간"}}, "table": {"header": {"actionType": "Action Type", "from": "From", "to": "To", "datetime": "Date Time"}, "content": {"operation": {"actionType": {}, "itemChanged": {}}, "budget": {"actionType": {}, "itemChanged": {}}, "null": "변경 이력이 없습니다."}}}, "targetTab": {"tabLabel": "타겟팅 이력", "filter": {"date": {"label": "조회 기간"}}, "table": {"header": {"actionType": "Action Type", "from": "From", "to": "To", "datetime": "Date Time"}, "content": {"actionType": {}, "itemChanged": {}, "null": "변경 이력이 없습니다."}}}, "bidHistoryTab": {"tabLabel": "입찰 이력", "filter": {"date": {"label": "조회 기간"}}, "table": {"header": {"actionType": "Action Type", "from": "From", "to": "To", "datetime": "Date Time"}, "content": {"actionType": {}, "itemChanged": {}, "null": "변경 이력이 없습니다."}}}}}, "addEditModal": {"topButton": {"save": "저장"}, "bulkSelection": {"openModal": "일괄 선택", "description": "ASIN 또는 SKU가 포함된 csv 또는 txt 파일을 붙여넣어 제품을 일괄 선택합니다.", "select": "선택", "textarea": {"placeholder": "ASIN을 쉼표, 공백 또는 줄바꿈으로 구분하여 입력하세요."}, "import": {"placeholder": "CSV 또는 TXT 파일 가져오기", "tooltip": "여기를 클릭하여 파일을 선택할 수 있습니다. 샘플 파일을 다운로드하여 참고하세요.", "sampleFile": "샘플 파일 다운로드"}, "matchedProducts": "일치하는 제품"}, "optSetName": {"label": "최적화 세트명", "placeholder": "최적화 세트명을 입력해주세요.", "duplicateError": "이미 사용 중인 최적화 세트명입니다.", "limitReachedMessage": "LITE 버전에서는 최적화 개수가 최대 1개로 제한됩니다."}, "product": {"searchBar": {"placeholder": "제품명 혹은 ASIN으로 검색하세요."}, "targetCandidates": {"label": "세트 추가 가능 제품", "asin": "ASIN", "null": "해당 제품이 없습니다."}, "targetProducts": {"label": "선택 제품", "asin": "ASIN", "null": "선택된 제품이 없습니다."}}, "objective": {"label": "목표", "subLabel": "최적화 목표", "optObjectiveType": {"maxSales": {"label": "총 매출 극대화", "dataSource": "SP data + Ad data", "details": "판매 데이터와 광고 데이터를 함께 사용하여 총 매출을 극대화합니다.", "option": "재고 최소화", "vendorDataSource": "Vendor data + Ads data", "vendorLabel": "벤더 매출 극대화", "vendorDetails": "벤더 데이터와 광고 데이터를 함께 사용하여 총 매출을 극대화합니다."}, "maxProfit": {"label": "총 이익 극대화", "dataSource": "SP data + Ad data", "details": "판매 데이터와 광고 데이터를 함께 사용하여 총 이익을 극대화합니다.", "option": "재고 최소화", "vendorDataSource": "Vendor data + Ads data", "vendorDetails": "벤더 데이터와 광고 데이터를 함께 사용하여 총 이익을 극대화합니다.", "vendorLabel": "벤더 이익 극대화"}, "maxAdSales": {"label": "광고 매출 극대화", "dataSource": "Ad data", "details": "광고 데이터를 사용하여 광고 매출을 극대화합니다.", "option": "노출 강화"}, "maxRoas": {"label": "ROAS 극대화", "dataSource": "Ad data", "details": "광고 데이터를 사용하여 ROAS를 극대화합니다", "option": "노출 강화"}}, "advancedOptions": {"label": "고급 옵션", "competitionTactic": {"label": "경쟁 전략", "option": {"none": "없음", "brandDefense": "브랜드 보호", "competitorConquesting": "경쟁사 공략"}}, "salesTargeting": {"label": "판매 타겟팅 전략", "option": "동일 상품(SKU)만 타겟팅"}, "tooltip": {"competitionTactic": "Competition Tactic offers an advanced strategy that optimizes your ads based on your marketing objectives.<enter></enter><enter></enter>Brand Defense focuses on strengthening click share for brand keywords selected by LG Optapex's proprietary algorithm.<enter></enter><enter></enter>Competitor Conquesting focuses on increasing click share for key competitors identified by LG Optapex's proprietary algorithm."}}}, "adType": {"label": "광고 유형", "subLabel": "광고 범위", "optAdType": {"displayN": {"label": "Sponsored Product", "target": "SP only", "details": "SP 광고 최적화"}, "displayY": {"label": "Sponsored Product + Sponsored Display", "target": "SP + SD", "details": "SP와 SD 광고 최적화"}}}, "scheduleBudget": {"label": "최적화 기간 & 예산", "creationDateLabel": "생성일", "schedule": {"label": "최적화 기간", "overview": {"monthlyInfinite": "매월 반복", "monthlyEnd": "{value}까지 매월 반복", "daterangeInfinite": "시작일: {value}"}, "checkbox": {"daterange": {"label": "선택 기간", "details": "선택한 기간에 맞추어 예산을 설정합니다."}, "monthly": {"label": "매월 반복", "details": "매달 1일 새롭게 자동 갱신되는 한 달 예산을 설정합니다.", "toggle": {"label": "종료일 설정"}}}}, "budget": {"daterange": {"label": "총 광고 예산", "input": "총 광고 예산을 입력해주세요."}, "monthly": {"label": "월 광고 예산", "input": "월 광고 예산을 입력해주세요."}}, "limitCpc": {"label": "스마트 CPC 가드", "input": "스마트 CPC 가드를 입력해주세요.", "tooltip": "스마트 CPC 가드는 예산 집행의 원활함을 위해 희망하는 한도에 CPC가 근접하도록 제어합니다. 단, 엄격한 제한은 적용되지 않습니다."}}, "confirmModal": {"titleCreate": "최적화 세트 생성", "titleEdit": "최적화 세트 수정", "descriptionCreate": "새로운 최적화 세트를 생성하시겠습니까?", "descriptionEdit": "최적화 세트를 수정하시겠습니까?", "cancel": "취소", "save": "저장"}, "closeConfirmModal": {"title": "변경 사항을 저장하지 않고 닫기", "description": "저장하지 않은 변경 사항이 있습니다. 정말로 닫으시겠습니까?", "cancel": "취소", "discard": "변경 사항 버리기"}}}, "RealtimePage": {"title": {"todayOverview": "오늘의 개요", "realTimePerformance": "실시간 성과", "optsetMonitoring": "최적화 세트 모니터링"}, "filter": {"attributionWindow": {"label": "어트리뷰션 기간", "content": {"1d": "1일", "7d": "7일", "14d": "14일", "30d": "30일"}}, "optimizationSet": {"label": "최적화 세트", "content": {"optSet": "최적화 세트", "null": "없음"}}}, "keyMetrics": {"totalMetrics": {"totalSales": "총 매출", "totalRevenue": "매출", "totalCogsRevenue": "벤더 매출", "estFee": "예상 수수료", "estMargin": "예상 이익률"}, "adMetrics": {"adSales": "광고 매출", "adSpend": "광고 비용", "roas": "ROAS"}, "metricsTooltip": {"estimatedMargin": "(총 매출 - 환불액 - 예상 수수료 - 광고비) / 총 매출 * 100", "vendorEstimatedMargin": "(벤더 매출 - 환불액 - 예상 수수료 - 광고비) / 벤더 매출 * 100", "totalSales": "<first>환불된 ${value}는 포함되지 않았습니다.</first>", "totalCogsRevenue": "<first>벤더 매출은 Amazon이 지급하는 공급업체의 수익입니다. {value}의 배송 매출은 고객에게 실제로 판매된 가격의 합계입니다.</first>", "totalRevenue": "매출은 고객이 제품에 실제로 지불한 가격의 합계입니다.", "estFee": "FBA 수수료 + 판매 수수료 + 보관 수수료 (기간에 따라 다름)", "roas": "(SP 매출 + SD 매출) / (SP 광고비 + SD 광고비) * 100"}}, "productShare": {"title": "제품 점유율", "searchBar": {"placeholder": "제품명 또는 ASIN으로 검색"}, "table": {"header": {"rank": "순위", "productInfo": "제품", "share": "점유율"}, "content": {"statusAlert": {"warning": "주의", "abnormal": "이상"}, "productInfo": {"asin": "ASIN", "sku": "SKU"}, "searchResultNull": "검색 결과가 없습니다", "optSetNull": "생성된 최적화 세트가 없습니다"}}}, "graph": {"legendSelect": {"sales": "Total Sales", "adSales": "Ad Sales", "adSalesSameSku": "Ad Sales for Same SKU", "sdSalesPromotedClick": "SD Sales Promoted Clicks", "pageViews": "Pageviews", "clicks": "<PERSON>", "impressions": "Impressions", "sessions": "Sessions", "roas": "ROAS", "estimatedMargin": "Est. Profit", "comparedSales": "Total Sales (compared)", "comparedAdSales": "Ad Sales (compared)", "comparedAdSalesSameSku": "Ad Sales for Same SKU (compared)", "comparedSdSalesPromotedClick": "SD Sales Promoted Clicks (compared)", "comparedPageViews": "Pageviews (compared)", "comparedClicks": "<PERSON> (compared)", "comparedImpressions": "Impressions (compared)", "comparedSessions": "Sessions (compared)", "comparedRoas": "ROAS (compared)", "comparedEstimatedMargin": "Est. <PERSON>it (compared)", "revenue": "Revenue", "cogsRevenue": "Cogs Revenue", "adCost": "Ad Spend", "glanceViews": "Glance Views", "comparedRevenue": "Revenue (compared)", "comparedCogsRevenue": "Cogs Revenue (compared)", "comparedAdCost": "<PERSON>pend (compared)", "comparedGlanceViews": "Glance Views (compared)", "orderedUnits": "Ordered Units", "adOrders": "Ad Orders"}}}, "downloads": {"title": "Report Downloads", "topButton": {"addNew": "신규 생성", "remainingRequests": "오늘 잔여 요청 횟수"}, "downloadList": {"header": {"status": "상태", "reportTitle": "리포트 제목", "reportType": "리포트 유형", "requestTime": "요청 일시", "fileCreationTime": "파일 생성 일시", "download": "다운로드"}, "content": {"downloadRow": {"downloadNull": "No download result found"}}}, "addModal": {"topButton": {"request": "문서 요청"}, "reportType": {"label": "리포트 유형", "subLabel": "리포트 유형을 선택하세요.", "options": {"sp": {"label": "Seller Central Report", "details": "Seller Central Report is a report that is generated from Seller Central. It includes all the data related to your products, including sales, inventory, and performance metrics."}, "vp": {"label": "Vendor Retail Analytics Report", "details": "Vendor Retail Analytics Report is a report that is generated from Vendor Retail Analytics. It includes all the data related to your products, including sales, inventory, and performance metrics."}, "ad": {"label": "Ads Console Report", "details": "Ads Console Report is a report that is generated from Ads Console. It includes all the data related to your advertising campaigns, including impressions, clicks, and sales."}, "optapex": {"label": "Optapex Report", "details": "Optapex에서만 제공되는 리포트를 생성하고 다운로드할 수 있습니다.", "featured": "주요 기능"}}}, "reportName": {"label": "리포트명", "category": {"subLabel": "카테고리"}, "name": {"subLabel": "리포트명"}}, "reportTitle": {"label": "리포트 제목", "subLabel": "제목"}, "advancedSetting": {"label": "고급 설정", "filter": {"dateAggregationFilter": {"label": "집계 기준", "options": {"daily": "일별", "weekly": "주별", "monthly": "월별"}}, "asinAggregationFilter": {"label": "ASIN 집계 기준", "options": {"parent": "부모 ASIN", "child": "자식 ASIN", "sku": "SKU"}}, "locationAggregationFilter": {"label": "위치 집계 기준", "options": {"country": "국경별", "fc": "FC별"}}, "eventTypeFilter": {"label": "이벤트 유형", "options": {"adjustments": "Adjustments", "customerReturns": "Customer Returns", "receipts": "Receipts", "shipments": "Shipments", "vendorReturns": "<PERSON><PERSON><PERSON> Returns", "whseTransfers": "Warehouse Transfers"}}, "timeUnitFilter": {"label": "시간 단위", "options": {"daily": "일별", "summary": "요약"}}, "groupByFilter": {"label": "그룹 기준", "options": {"searchTerm": "Search Term", "targeting": "Targeting", "matchedTarget": "Matched Target", "advertiser": "Advertiser"}}, "distributorViewFilter": {"label": "Distributor View Filter", "options": {"manufacturing": "Manufacturing", "sourcing": "Sourcing"}}, "sellingProgramFilter": {"label": "Selling Program Filter", "options": {"retail": "Retail", "business": "Business", "fresh": "Fresh"}}, "reportPeriodFilter": {"label": "Report Period Filter", "options": {"day": "Day", "week": "Week", "month": "Month", "quarter": "Quarter", "year": "Year"}}}, "dateRestrictions": {"title": "날짜 제한", "maxFromNow": "현재일로부터 최대 조회 가능기간", "maxRange": "최대 조회 가능 기간"}}, "error": {"dateRangeRequired": "날짜 범위는 필수값입니다.", "startDateTooFar": "시작일이 오늘로부터 너무 멀리 설정되었습니다.", "dateRangeTooWide": "선택한 날짜 범위가 너무 넓습니다.", "recent": "최근", "maximum": "최대", "timeUnits": {"days": "일", "weeks": "주", "months": "개월", "quarters": "분기", "years": "년"}}}}}