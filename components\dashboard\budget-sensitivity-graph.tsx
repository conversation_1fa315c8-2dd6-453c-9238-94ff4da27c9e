"use client"

import Plot from "react-plotly.js"
import { useEffect, useMemo, useState } from "react"

type GraphData = {
  budget_ratio: string;
  value: number;
}

export default function BudgetSensitivityGraph({
  totalProfitData,
  totalSalesData,
  totalCostData,
  adSpendData,
  adSalesData,
  adSalesSameSkuData,
  optimized = false
}: {
  totalProfitData: GraphData[],
  totalSalesData: GraphData[],
  totalCostData: GraphData[],
  adSpendData: GraphData[],
  adSalesData: GraphData[],
  adSalesSameSkuData: GraphData[],
  optimized?: boolean
}) {
  const initialDataset: any[] = useMemo(() => {
    const dataset = []
    dataset.push({
      x: totalProfitData.map((d) => d.budget_ratio),
      y: totalProfitData.map((d) => 0),
      customdata: totalProfitData.map((d) => (Number(d.budget_ratio) * 100).toFixed(2)),
      type: 'scatter',
      mode: 'lines+markers',
      line: { shape: 'spline', smoothing: 1.3 },
      xperiodalignment: 'start',
      hovertemplate: '%{y} (%{customdata}%)',
      marker: {
        color: '#a855f7',
        size: 6
      },
      name: 'Total Profit',
    })
    dataset.push({
      x: totalSalesData.map((d) => d.budget_ratio),
      y: totalSalesData.map((d) => 0),
      customdata: totalSalesData.map((d) => (Number(d.budget_ratio) * 100).toFixed(2)),
      type: 'scatter',
      mode: 'lines+markers',
      line: { shape: 'spline', smoothing: 1.3 },
      xperiodalignment: 'start',
      hovertemplate: '%{y} (%{customdata}%)',
      marker: {
        color: '#ec4899',
        size: 6
      },
      name: 'Total Sales',
    })
    dataset.push({
      x: totalCostData.map((d) => d.budget_ratio),
      y: totalCostData.map((d) => 0),
      customdata: totalCostData.map((d) => (Number(d.budget_ratio) * 100).toFixed(2)),
      type: 'scatter',
      mode: 'lines+markers',
      line: { shape: 'spline', smoothing: 1.3 },
      xperiodalignment: 'start',
      hovertemplate: '%{y} (%{customdata}%)',
      marker: {
        color: '#dc2626',
        size: 6
      },
      name: 'Total Cost',
      yaxis: 'y2',
    })
    dataset.push({
      x: adSpendData.map((d) => d.budget_ratio),
      y: adSpendData.map((d) => 0),
      customdata: adSpendData.map((d) => (Number(d.budget_ratio) * 100).toFixed(2)),
      type: 'scatter',
      mode: 'lines+markers',
      line: { shape: 'spline', smoothing: 1.3 },
      xperiodalignment: 'start',
      hovertemplate: '%{y} (%{customdata}%)',
      marker: {
        color: '#f59e0b',
        size: 6
      },
      name: 'Ad Spend',
      yaxis: 'y2',
    })
    dataset.push({
      x: adSalesData.map((d) => d.budget_ratio),
      y: adSalesData.map((d) => 0),
      customdata: adSalesData.map((d) => (Number(d.budget_ratio) * 100).toFixed(2)),
      type: 'scatter',
      mode: 'lines+markers',
      line: { shape: 'spline', smoothing: 1.3 },
      xperiodalignment: 'start',
      hovertemplate: '%{y} (%{customdata}%)',
      marker: {
        color: '#6366f1',
        size: 6
      },
      name: 'Ad Sales',
      yaxis: 'y2',
    })
    dataset.push({
      x: adSalesSameSkuData.map((d) => d.budget_ratio),
      y: adSalesSameSkuData.map((d) => 0),
      customdata: adSalesSameSkuData.map((d) => (Number(d.budget_ratio) * 100).toFixed(2)),
      type: 'scatter',
      mode: 'lines+markers',
      line: { shape: 'spline', smoothing: 1.3 },
      xperiodalignment: 'start',
      hovertemplate: '%{y} (%{customdata}%)',
      marker: {
        color: '#66d9e8',
        size: 6
      },
      name: 'Ad Sales Same SKU',
      yaxis: 'y2',
    })

    return dataset
  }, [totalProfitData, totalSalesData, totalCostData, adSpendData, adSalesData, adSalesSameSkuData])

  const changedDataset = useMemo(() => {
    const dataset = []
    dataset.push({
      x: totalProfitData.map((d) => d.budget_ratio),
      y: totalProfitData.map((d) => d.value),
      customdata: totalProfitData.map((d) => (Number(d.budget_ratio) * 100).toFixed(2)),
      type: 'scatter',
      mode: 'lines+markers',
      line: { shape: 'spline', smoothing: 1.3 },
      xperiodalignment: 'start',
      hovertemplate: '%{y} (%{customdata}%)',
      marker: {
        color: '#a855f7',
        size: 6
      },
      name: 'Total Profit',
    })
    dataset.push({
      x: totalSalesData.map((d) => d.budget_ratio),
      y: totalSalesData.map((d) => d.value),
      customdata: totalSalesData.map((d) => (Number(d.budget_ratio) * 100).toFixed(2)),
      type: 'scatter',
      mode: 'lines+markers',
      line: { shape: 'spline', smoothing: 1.3 },
      xperiodalignment: 'start',
      hovertemplate: '%{y} (%{customdata}%)',
      marker: {
        color: '#ec4899',
        size: 6
      },
      name: 'Total Sales',
    })
    dataset.push({
      x: totalCostData.map((d) => d.budget_ratio),
      y: totalCostData.map((d) => d.value),
      customdata: totalCostData.map((d) => (Number(d.budget_ratio) * 100).toFixed(2)),
      type: 'scatter',
      mode: 'lines+markers',
      line: { shape: 'spline', smoothing: 1.3 },
      xperiodalignment: 'start',
      hovertemplate: '%{y} (%{customdata}%)',
      marker: {
        color: '#dc2626',
        size: 6
      },
      name: 'Total Cost',
      yaxis: 'y2',
    })
    dataset.push({
      x: adSpendData.map((d) => d.budget_ratio),
      y: adSpendData.map((d) => d.value),
      customdata: adSpendData.map((d) => (Number(d.budget_ratio) * 100).toFixed(2)),
      type: 'scatter',
      mode: 'lines+markers',
      line: { shape: 'spline', smoothing: 1.3 },
      xperiodalignment: 'start',
      hovertemplate: '%{y} (%{customdata}%)',
      marker: {
        color: '#f59e0b',
        size: 6
      },
      name: 'Ad Spend',
      yaxis: 'y2',
    })
    dataset.push({
      x: adSalesData.map((d) => d.budget_ratio),
      y: adSalesData.map((d) => d.value),
      customdata: adSalesData.map((d) => (Number(d.budget_ratio) * 100).toFixed(2)),
      type: 'scatter',
      mode: 'lines+markers',
      line: { shape: 'spline', smoothing: 1.3 },
      xperiodalignment: 'start',
      hovertemplate: '%{y} (%{customdata}%)',
      marker: {
        color: '#6366f1',
        size: 6
      },
      name: 'Ad Sales',
      yaxis: 'y2',
    })
    dataset.push({
      x: adSalesSameSkuData.map((d) => d.budget_ratio),
      y: adSalesSameSkuData.map((d) => d.value),
      customdata: adSalesSameSkuData.map((d) => (Number(d.budget_ratio) * 100).toFixed(2)),
      type: 'scatter',
      mode: 'lines+markers',
      line: { shape: 'spline', smoothing: 1.3 },
      xperiodalignment: 'start',
      hovertemplate: '%{y} (%{customdata}%)',
      marker: {
        color: '#66d9e8',
        size: 6
      },
      name: 'Ad Sales Same SKU',
      yaxis: 'y2',
    })

    return dataset
  }, [totalProfitData, totalSalesData, totalCostData, adSpendData, adSalesData, adSalesSameSkuData])

  const layout = {
    // transition: {
    //   duration: 2000,
    //   easing: 'cubic-in-out',
    // },
    margin: {
      l: 84,
      r: 84,
      b: 20,
      t: 0,
      pad: 0
    },
    paper_bgcolor: 'rgba(0,0,0,0)',
    plot_bgcolor: 'rgba(0,0,0,0)',
    autosize: true,
    showlegend: false,
    xaxis: {
      tickfont: {
        size: 10,
        color: '#4b5563'
      },
      tickformat: '.0%',
      ticks: 'outside',
      tickcolor: 'rgba(0,0,0,0)',
      gridcolor: '#374151',
      zerolinecolor: '#374151',
      tickmode: 'auto',
      nticks: totalProfitData.length > 10 ? 10 : totalProfitData.length,
      range: [
        Math.min(...totalProfitData.map(d => Number(d.budget_ratio) - 0.05)),
        Math.max(...totalProfitData.map(d => Number(d.budget_ratio) + 0.05))
      ],
    },
    yaxis: {
      tickformat: '$,',
      tickfont: {
        size: 10,
        color: '#4b5563'
      },
      ticks: 'outside',
      tickcolor: 'rgba(0,0,0,0)',
      gridcolor: '#374151',
      zerolinecolor: '#374151',
      automargin: true,
    },
    yaxis2: {
      overlaying: 'y',
      side: 'right',
      tickformat: '$,',
      tickfont: {
        size: 10,
        color: '#4b5563'
      },
      ticks: 'outside',
      tickcolor: 'rgba(0,0,0,0)',
      gridcolor: '#374151',
      zerolinecolor: '#374151',
      automargin: true,
    },
    hovermode: 'x unified',
    hoverlabel: {
      bgcolor: 'rgba(17, 24, 39, 0.9)',
      font: {
        size: 10,
        color: '#e5e7eb'
      },
    },
    dragmode: false,
  }
  const [graphInfo, setGraphInfo] = useState<any>({
    dataset: initialDataset,
    layout: layout,
  })
      
  useEffect(() => {
    if (optimized) {    
      setTimeout(() => {
        setGraphInfo({
          dataset: changedDataset,
          layout: {
            ...layout,
          }
        })
      }, 100)
    }
  }, [optimized])
  return (
    <Plot
      data={graphInfo.dataset}
      layout={graphInfo.layout}
      config={{
        displayModeBar: false,
      }}
      useResizeHandler={true}
      className="w-full h-full"
    />
  )
}
