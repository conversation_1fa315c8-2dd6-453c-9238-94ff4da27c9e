import { ErrorResponse } from "@/app/api/_models";
import { getServerApiHostUrl } from "@/utils/host";
import { NextRequest, NextResponse } from "next/server";

export type ChangePasswordResponse = {
  status: string;
  message: string;
};

export async function POST(
  request: NextRequest
): Promise<NextResponse<ChangePasswordResponse | ErrorResponse>> {
  const accessToken = request.headers.get("Authorization");
  const body = await request.json();

  if (!accessToken) {
    return NextResponse.json(
      { message: "Authorization header is missing" },
      { status: 400 }
    );
  }
  const changePasswordResponse = await fetch(
    `${await getServerApiHostUrl()}/api/cognito/change-password`,
    {
      method: "POST",
      headers: {
        "Authorization": `Bearer ${accessToken}`,
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        "password": body?.password,
      }),
      cache: "no-cache",
    }
  ).then((res) => res.json());

  return NextResponse.json(changePasswordResponse, { status: 200 });
}
