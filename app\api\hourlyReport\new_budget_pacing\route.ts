import { NextRequest, NextResponse } from "next/server"
import { getServerApiHostUrl } from "@/utils/host"

export interface BudgetPacingRequest {
  optimization_ids?: number[]
  account_id?: string
  marketplace_id?: string
}

export interface BudgetPacingResponse {
  optimization_id: number
  total_budget: number
  start_date: string
  end_date: string
  spent_budget: number
  remaining_budget: number
  remaining_days: number
  daily_recommended_budget: number
  daily_spending_history: Record<string, number>
  is_ended: boolean
  custom_date_range: boolean
  ad_budget_type: string
  budget_usage_predictions?: {
    MAX: number
    estimated_budget_state: string
    MIN: number
    TARGET: number
  }
}

export async function POST(request: NextRequest) {
  try {
    const body: BudgetPacingRequest = await request.json()
    const { optimization_ids, account_id, marketplace_id } = body
    const accessToken = request.headers.get("Authorization")

    if (!accessToken) {
      return NextResponse.json(
        { error: "Access token is required" },
        { status: 401 }
      )
    }

    const hasOptimizationIds = optimization_ids && Array.isArray(optimization_ids) && optimization_ids.length > 0
    const hasAccountInfo = account_id && marketplace_id

    if (!hasOptimizationIds && !hasAccountInfo) {
      return NextResponse.json(
        { error: "Either optimization_ids array or account_id/marketplace_id pair is required" },
        { status: 400 }
      )
    }
    if (hasOptimizationIds && hasAccountInfo) {
      return NextResponse.json(
        { error: "Cannot provide both optimization_ids and account_id/marketplace_id at the same time" },
        { status: 400 }
      )
    }
    if ((account_id && !marketplace_id) || (!account_id && marketplace_id)) {
      return NextResponse.json(
        { error: "account_id and marketplace_id must be provided together" },
        { status: 400 }
      )
    }

    const requestBody: any = {}
    if (hasOptimizationIds) {
      requestBody.optimization_ids = optimization_ids
    } else {
      requestBody.account_id = account_id
      requestBody.marketplace_id = marketplace_id
    }

    const response = await fetch(`${await getServerApiHostUrl()}/api/hourly_report/new_budget_pacing`, {
      method: "POST",
      headers: {
        "Authorization": `Bearer ${accessToken}`,
        "Content-Type": "application/json",
      },
      body: JSON.stringify(requestBody),
      cache: "no-store", // Disable caching to avoid the 2MB limit issue
    })

    if (!response.ok) {
      return NextResponse.json(
        { error: "Failed to fetch budget pacing data" },
        { status: response.status }
      )
    }

    const data: BudgetPacingResponse[] = await response.json()
    return NextResponse.json(data)
  } catch (error) {
    console.error("Budget pacing API error:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
} 