// ***********************************************
// This example commands.js shows you how to
// create various custom commands and overwrite
// existing commands.
//
// For more comprehensive examples of custom
// commands please read more here:
// https://on.cypress.io/custom-commands
// ***********************************************
//
//
// -- This is a parent command --
// Cypress.Commands.add('login', (email, password) => { ... })
//
//
// -- This is a child command --
// Cypress.Commands.add('drag', { prevSubject: 'element'}, (subject, options) => { ... })
//
//
// -- This is a dual command --
// Cypress.Commands.add('dismiss', { prevSubject: 'optional'}, (subject, options) => { ... })
//
//
// -- This will overwrite an existing command --
// Cypress.Commands.overwrite('visit', (originalFn, url, options) => { ... })
Cypress.Commands.add('isDisplayed', (elementSelectors) => {
    if (typeof elementSelectors === 'string') {
        elementSelectors = [elementSelectors];
    }
    elementSelectors.forEach((elementSelector) => {
        cy.get(elementSelector).should('be.visible');
    });
});

Cypress.Commands.add('clearAndType', (elementSelector, text) => {
    cy.get(elementSelector).clear();
    cy.get(elementSelector).type(text);
});

Cypress.Commands.add('clearAndTypeAndBlur', (elementSelector, text) => {
    cy.get(elementSelector).clear();
    cy.get(elementSelector).type(text).blur();
});

Cypress.Commands.add('clearAndBlur', (elementSelector) => {
    cy.get(elementSelector).clear();
    cy.get(elementSelector).focus().blur();
});

Cypress.Commands.overwrite('type', (originalFn, subject, chars, options) => {
    let defaultDelayValueInMilliSec = 1;
    if (typeof options !== 'object') {
        options = {};
    }
    if (!options.delay) {
        options.delay = defaultDelayValueInMilliSec;
    }
    return originalFn(subject, chars, options);
});

Cypress.Commands.overwriteQuery('get', function (originalFn, ...args) {
    let defaultCommandTimeout = 10000;
    this.set('timeout', args?.timeout || defaultCommandTimeout);
    return originalFn.apply(this, args);
});

Cypress.Commands.overwriteQuery('location', function (originalFn, ...args) {
    let defaultCommandTimeout = 10000;
    this.set('timeout', defaultCommandTimeout);
    return originalFn.apply(this, args);
});

Cypress.Commands.add('clearSession', () => {
    cy.window().then((win) => {
        win.sessionStorage.clear();
    });
});

Cypress.Commands.add('setViewport', (size) => {
    if (Cypress._.isArray(size)) {
        cy.viewport(size[0], size[1]);
    } else {
        cy.viewport(size);
    }
});

Cypress.Commands.overwrite('visit', (originalFn, url, options) => {
    if (typeof options !== 'object') {
        options = {};
    }
    if (!options.failOnStatusCode) {
        options.failOnStatusCode = false;
    }
    return originalFn(url, options);
});

let LOCAL_STORAGE_MEMORY = {};

Cypress.Commands.add('saveLocalStorage', () => {
    Object.keys(localStorage).forEach((key) => {
        LOCAL_STORAGE_MEMORY[key] = localStorage[key];
    });
});

Cypress.Commands.add('restoreLocalStorage', () => {
    Object.keys(LOCAL_STORAGE_MEMORY).forEach((key) => {
        localStorage.setItem(key, LOCAL_STORAGE_MEMORY[key]);
    });
});

Cypress.Commands.add('waitUrl', (url) => {
    cy.location('href').should('include', url);
});

Cypress.Commands.add('setSessionToken', (sessionData) => {
    const crypto = require("crypto");

    cy.request({
        method: 'POST',
        url: '/api/test/mock-session',
        body: {
            token: {
                user: {
                    name: 'test',
                    email: email,
                    image: null
                },
                access_token: 'eyJraWQiOiI2R0pnUXpaSGs4ZUQ1YVEzOVVGWFMzdEVJZ2JmcHg5aVFaWE1Ua215UnpFPSIsImFsZyI6IlJTMjU2In0.eyJzdWIiOiJjNDg4NDQ4OC05MDExLTcwNDQtMWM2OC1jODVlMGViMWVmOTMiLCJpc3MiOiJodHRwczpcL1wvY29nbml0by1pZHAudXMtZWFzdC0xLmFtYXpvbmF3cy5jb20cL3VzLWVhc3QtMV9DNUdlSlE0bzEiLCJ2ZXJzaW9uIjoyLCJjbGllbnRfaWQiOiI1aTExNG4wOWZ0NTcxcmI5bXJnZ21rOGJmbyIsIm9yaWdpbl9qdGkiOiJmMDAxYzhlYS0yNDJiLTQ0MGEtYWRkOC04NmQ2OWY4MTYyYTciLCJldmVudF9pZCI6IjQ3MWVlMjUxLTU3MzEtNGRlNC1hOWRjLTBiOTQ0ODlmNmUxMCIsInRva2VuX3VzZSI6ImFjY2VzcyIsInNjb3BlIjoib3BlbmlkIHByb2ZpbGUgZW1haWwiLCJhdXRoX3RpbWUiOjE3NDIzNjY2ODUsImV4cCI6MTc0MjM3MDI4NSwiaWF0IjoxNzQyMzY2Njg1LCJqdGkiOiJiOGQxMDhmNy0yZDE4LTQyYjItYjhhYi1iNWNkMWU2MTZjMzMiLCJ1c2VybmFtZSI6ImM0ODg4NDQ4OC05MDExLTcwNDQtMWM2OC1jODVlMGViMWVmOTMyIn0.E_G_9Wd-i-tG-rC3i-lD-c_r-o_c-k_t-o_k-e_n'
            }
        }
    });
});

Cypress.Commands.add(
  'login',
  (
    email = '<EMAIL>',
    password = 'Moptest123!',
    rememberUser = true
  ) => {
    cy.session(
      [email, password, rememberUser],
      () => {
        cy.request({
          method: 'POST',
          url: '/api/test/mock-session',
          body: {
            token: {
              name: 'test',
              email: email,
              sub: 'c4884488-9011-7044-1c68-c85e0eb1ef93',
              access_token: 'mock_access_token_for_cypress_test',
              is_cypress_test: true,
            }
          }
        })
      }
    )
  }
)
