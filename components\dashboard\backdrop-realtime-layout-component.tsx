"use client"

import { api } from "@/utils/api"
import { colorSetList, cn, stringToColor } from "@/utils/msc"
import { useCurrencyFormatter, getCurrencyCodeFromMarketplace } from "@/utils/currency"
import { useSession } from "next-auth/react"
import { useTranslations } from 'next-intl'
import { Fragment, useEffect, useRef, useState, useMemo } from "react"
import { registerLocale } from "react-datepicker"
import { ko } from "date-fns/locale"
import "react-datepicker/dist/react-datepicker.css";
import { Listbox, Transition } from '@headlessui/react'
import { Check } from "@/components/ui/check"
import { ProfileOption } from "@/components/dashboard/profile-select"
import { MarketplaceOption } from "@/components/dashboard/marketplace-select"
import AttributionWindowSelect, { attributionWindowOptions } from "@/components/dashboard/attribution-window-select"
import { PortfolioListItem, ProductListItem } from './ad-portfolio-layout-component'
import OptimizationSetSelect from "./optimization-set-select"
import LegendSelect from "@/components/dashboard/legend-select"
import RefreshButton from "@/components/ui/refresh-button"
import { ExclamationTriangleIcon, ChevronUpDownIcon, MoonIcon, SunIcon } from "@heroicons/react/20/solid"
import dynamic from "next/dynamic"
import ProductSharePieChart from "./product-share-pie-chart"
import { TruckCheck } from "@/components/ui/truck-check"
import { TruckRemove } from "@/components/ui/truck-remove"
import Link from "next/link"

registerLocale('ko', ko)

interface RealtimeLayoutProps {
    mopUserData: any;
}

export default function BackdropRealtimeLayoutComponent({
    mopUserData,
}: RealtimeLayoutProps) {
  const t = useTranslations('RealtimePage')
  const tos = useTranslations('optimizationSets')
  const td = useTranslations('DashboardPage')
  const tc = useTranslations("component")
  const { data: session, status } = useSession()
  
  const { formatCurrency } = useCurrencyFormatter()
  const currencyCode = useMemo(() => {
    return 'USD' // 기본값
  }, [])
  const fetchCounter = useRef(0)
  const hourlyfetchCounter = useRef(0)
  const [mainProductsHourlyData, setMainProductsHourlyData] = useState<any>({})
  const [reportByHour, setReportByHour] = useState<any[]>([])
  const [selectedAttributionWindow, setSelectedAttributionWindow] = useState(attributionWindowOptions[1])
  const [portfolioListItems, setPortfolioListItems] = useState<PortfolioListItem[]>([])
  const [selectedPortfolioItems, setSelectedPortfolioItems] = useState<PortfolioListItem[]>([])
  // product share legend options
  const productShareLegendOptions = [
    // total performance
    {
      id: 1,
      name: td('graph.legendSelect.glanceViews'),
      category: 'total',
      type: 'glance-views',
    },
    {
      id: 2,
      name: td('graph.legendSelect.orderedUnits'),
      category: 'total',
      type: 'ordered-units',
    },
    {
      id: 3,
      name: td('graph.legendSelect.revenue'),
      category: 'total',
      type: 'revenue',
    },
    // ads performance
    {
      id: 4,
      name: td('graph.legendSelect.impressions'),
      category: 'ad',
      type: 'impressions',
    },
    {
      id: 5,
      name: td('graph.legendSelect.clicks'),
      category: 'ad',
      type: 'clicks',
    },
    {
      id: 6,
      name: td('graph.legendSelect.adOrders'),
      category: 'ad',
      type: 'ad-orders',
    },
    {
      id: 7,
      name: td('graph.legendSelect.adSales'),
      category: 'ad',
      type: 'ad-sales',
    },
    {
      id: 8,
      name: td('graph.legendSelect.adCost'),
      category: 'ad',
      type: 'ad-spends',
    },
  ]
  // legend options for hourly graph
  const hourlyLegendOptions = [
    // total performance
    {
      id: 1,
      name: t('graph.legendSelect.revenue'),
      category: 'total',
      type: 'sales',
      color: '#a855f7',
    },
    {
      id: 2,
      name: t('graph.legendSelect.glanceViews'),
      category: 'total',
      type: 'pageviews',
      color: '#ec4899',
    },
    {
      id: 3,
      name: t('graph.legendSelect.orderedUnits'),
      category: 'total',
      type: 'ordered-units',
      color: '#10b981',
    },
    // ad performance (sp)
    {
      id: 9,
      name: t('graph.legendSelect.adCost'),
      category: 'sp',
      type: 'ad-cost',
      color: '#1d4ed8',
    },
    {
      id: 11,
      name: t('graph.legendSelect.adSales'),
      category: 'sp',
      type: 'ad-sales',
      color: '#6366f1',
    },
    {
      id: 13,
      name: t('graph.legendSelect.adSalesSameSku'),
      category: 'sp',
      type: 'ad-sales-same-sku',
      color: '#66d9e8',
    },
    {
      id: 15,
      name: t('graph.legendSelect.clicks'),
      category: 'sp',
      type: 'clicks',
      color: '#f97316',
    },
    {
      id: 17,
      name: t('graph.legendSelect.impressions'),
      category: 'sp',
      type: 'impressions',
      color: '#c53030',
    },
    {
      id: 19,
      name: t('graph.legendSelect.adOrders'),
      category: 'sp',
      type: 'ad-orders',
      color: '#10b981',
    }
  ]
  // legend option subsets for hourly charts (sales vs ads)
  const salesLegendOptions = hourlyLegendOptions.filter(option => option.category === 'total')
  // Desired order: impressions, clicks, ad-orders, ad-sales, ad-sales-same-sku, ad-cost
  const adsLegendOrder = ['impressions', 'clicks', 'ad-orders', 'ad-sales', 'ad-sales-same-sku', 'ad-cost']
  const adsLegendOptions = adsLegendOrder
    .map(type => hourlyLegendOptions.find(option => option.type === type && option.category === 'sp'))
    .filter((o): o is typeof hourlyLegendOptions[number] => Boolean(o))

  const [selectedProductShareLegend, setSelectedProductShareLegend] = useState(productShareLegendOptions[0])
  const [inventoryHourlyData, setInventoryHourlyData] = useState<any>({})
  const [adShareData, setAdShareData] = useState<any>({})
  const [adShareLoading, setAdShareLoading] = useState(false)
  const [budgetPacingData, setBudgetPacingData] = useState<any[]>([])
  const [budgetPacingLoading, setBudgetPacingLoading] = useState(false)
  
  const productListItemCard = (item: any, mode: string) => {
    if (!item) {
      return (
        <div className="grow relative flex items-center gap-x-4 px-4 overflow-hidden">
          <div className="flex-shrink-0 flex items-center justify-center w-10 h-10 bg-gray-100 rounded">
            <ExclamationTriangleIcon className="h-5 w-5 text-gray-300" />
          </div>
          <div className="flex-1 flex flex-col gap-y-0.5 overflow-hidden">
            <div className={cn(
              "text-xs text-left font-semibold truncate",
              mode === "dark" ? "text-gray-200" : "text-gray-500"
            )}>
              No Item
            </div>
          </div>
        </div>
      )
    }
    return (
      <div className="grow relative flex items-center gap-x-4 px-4 overflow-hidden">
        { item.image
        ? (<img src={item.image} alt="Item Image" className="flex-shrink-0 w-10 h-10 rounded" />)
        : (<div className="flex-shrink-0 flex items-center justify-center w-10 h-10 bg-gray-100 rounded">
            <ExclamationTriangleIcon className="h-5 w-5 text-gray-300" />
          </div>)
        }
        <div className="flex-1 flex flex-col gap-y-0.5 overflow-hidden">
          <div className={cn(
            "text-xs text-left font-semibold truncate",
            mode === "dark" ? "text-gray-200" : "text-gray-500"
          )}>
            {item.item_name
              ? item.item_name
              : "No Title"
            }
          </div>
          <div className={cn(
            "flex items-center gap-x-2 divide-x divide-gray-100",
            mode === "dark" ? "divide-gray-600" : "divide-gray-100"
          )}>
            <div className="text-xs text-red-400 font-semibold">{formatCurrency(item.listing_price || 0, currencyCode)}</div>
            <div className="flex items-center pl-2 text-xs font-semibold">
              {item.fulfillment_channel === "DEFAULT"
                ? <TruckRemove className="w-4 h-4 text-gray-400"/>
                : <TruckCheck className="w-4 h-4 text-orange-300"/>
              }
              {item.fulfillment_channel === "DEFAULT"
                ? <div className="pl-1 text-gray-400 font-semibold">FBM</div>
                : <div className="pl-1 text-orange-400 font-semibold">{formatCurrency(item.shipping_price || 0, currencyCode)}</div>
              }
            </div>
            {item.eligibility_status &&
              item.eligibility_status === "ELIGIBLE"
                ? <div className="pl-2 text-xs text-blue-400 font-semibold">{tc("eligibility.eligible")}</div>
                : item.eligibility_status === "INELIGIBLE"
                  ? <div className="pl-2 text-xs text-gray-400 font-semibold">{tc("eligibility.ineligible")}</div>
                  : <div className="pl-2 text-xs text-gray-400 font-semibold">{tc("eligibility.unknown")}</div>
            }
          </div>
          <div className={cn(
            "flex items-center gap-x-2 divide-x",
            mode === "dark" ? "divide-gray-600" : "divide-gray-100"
          )}>
            <div className="text-[10px] text-gray-400 truncate">{tos("detailModal.optSet.budgetTab.table.content.productInfo.asin")}: {item.asin}</div>
            <div className="pl-2 text-[10px] text-gray-400 truncate">{tos("detailModal.optSet.budgetTab.table.content.productInfo.sku")}: {item.sku}</div>
          </div>
        </div>
      </div>
    )
  }
  
  const getMetricForAdShare = (legendType: string) => {
    switch (legendType) {
      case 'glance-views':
        return 'glance_views'
      case 'ordered-units':
        return 'ordered_units'
      case 'revenue':
        return 'sales'
      case 'impressions':
        return 'ad_impressions'
      case 'clicks':
        return 'ad_clicks'
      case 'ad-orders':
        return 'ad_orders'
      case 'ad-sales':
        return 'ad_sales'
      case 'ad-spends':
        return 'ad_spend'
      default:
        return 'sales'
    }
  }
  
  const fetchAdShareData = async (signal: AbortSignal) => {
    if (!(session?.user as any)?.access_token) {
      console.log("access token is missing in the session.")
      return
    }
    
    setAdShareLoading(true)
    const targetMetric = getMetricForAdShare(selectedProductShareLegend.type)
    const optimizationId = selectedPortfolioItems.length === 1 ? selectedPortfolioItems[0].id : null
    const attributionWindow = Number(selectedAttributionWindow.type.replace('d', ''))
    
    try {
      // const response = await api.getHourlyAdShare(
      //   targetMetric,
      //   selectedProfile.account_id,
      //   selectedMarketplace.marketplace_id,
      //   optimizationId,
      //   attributionWindow,
      //   (session?.user as any).access_token,
      //   signal
      // )
      const response = {
        "sum": 0
      }
      
      if (response && Object.keys(response).length > 0) {
        setAdShareData(response)
      } else {
        // 빈값인 경우 초기화
        setAdShareData({})
      }
    } catch (error) {
      if (error instanceof Error && error.name !== 'AbortError') {
        console.error('Error fetching ad share data:', error)
        // 에러가 발생한 경우도 초기화
        setAdShareData({})
      }
    } finally {
      setAdShareLoading(false)
    }
  }

  const fetchBudgetPacingData = async (signal: AbortSignal) => {
    if (!(session?.user as any)?.access_token) {
      console.log("access token is missing in the session.")
      return
    }
    
    if (selectedPortfolioItems.length === 0) {
      setBudgetPacingData([])
      return
    }
    
    setBudgetPacingLoading(true)
    const optimizationIds = selectedPortfolioItems.map(item => item.id)
    
    try {
      const response = await api.getNewBudgetPacing(
        { optimization_ids: optimizationIds },
        (session?.user as any).access_token,
        signal
      )
      
      if (response && Array.isArray(response) && response.length > 0) {
        setBudgetPacingData(response)
      } else {
        // 빈값인 경우 초기화
        setBudgetPacingData([])
      }
    } catch (error) {
      if (error instanceof Error && error.name !== 'AbortError') {
        console.error('Error fetching budget pacing data:', error)
        // 에러가 발생한 경우도 초기화
        setBudgetPacingData([])
      }
    } finally {
      setBudgetPacingLoading(false)
    }
  }
    
  const calculateHourlyData = (hourlyDataResponse: any) => {
    if (!hourlyDataResponse || Object.keys(hourlyDataResponse).length === 0) {
      return { 
        aggregated: [], 
        summary: {
          sales: 0,
          ordered_units: 0,
          glance_views: 0,
          ad_spend: 0,
          ad_impressions: 0,
          ad_clicks: 0,
          ad_orders: 0,
          ad_sales: 0,
        }
      }
    }

    /* -------------------------------------------------------------------------- */
    /*   1) 최근 24시간 합산                                                         */
    /* -------------------------------------------------------------------------- */
    // 시간별 키(0~23)만 추출하여 그래프용 타임라인을 만들고,
    // 합계(sum)는 API에서 바로 제공되므로 그대로 사용한다.
    const hourKeys = Object.keys(hourlyDataResponse.sales || {})
      .filter((k) => k !== "sum")
      .sort((a, b) => {
        const numA = Number(a)
        const numB = Number(b)
        if (!isNaN(numA) && !isNaN(numB)) {
          return numA - numB
        }
        return a.localeCompare(b)
      })

    const getMetricSum = (metricObj: Record<string, number> | undefined) => {
      if (!metricObj) return 0
      return (metricObj as any).sum ?? 0
    }

    const summary = {
      sales: getMetricSum(hourlyDataResponse.sales),
      ordered_units: getMetricSum(hourlyDataResponse.ordered_units),
      glance_views: getMetricSum(hourlyDataResponse.glance_views),
      ad_spend: getMetricSum(hourlyDataResponse.ad_spend),
      ad_impressions: getMetricSum(hourlyDataResponse.ad_impressions),
      ad_clicks: getMetricSum(hourlyDataResponse.ad_clicks),
      ad_orders: getMetricSum(hourlyDataResponse.ad_orders),
      ad_sales: getMetricSum(hourlyDataResponse.ad_sales),
    }

    /* -------------------------------------------------------------------------- */
    /*   2) 타임라인(yyyy-MM-dd HH:mm) 기준 전체 데이터 생성                            */
    /* -------------------------------------------------------------------------- */
    // 판매(metricObj) 중 하나라도 있으면 해당 타임스탬프는 표시해야 하므로 sales 키를 기준으로 사용

    const getVal = (obj: Record<string, number> | undefined, k: string) =>
      obj && typeof obj[k] === 'number' ? obj[k] : 0

    const aggregated = hourKeys.map((ts) => ({
      timestamp: ts,
      // total performance
      sales: getVal(hourlyDataResponse.sales, ts),
      pageviews: getVal(hourlyDataResponse.glance_views, ts),
      ordered_units: getVal(hourlyDataResponse.ordered_units, ts),

      // sponsored products
      ad_cost: getVal(hourlyDataResponse.ad_spend, ts),
      ad_sales: getVal(hourlyDataResponse.ad_sales, ts),
      ad_sales_same_sku: getVal(hourlyDataResponse.ad_sales_same_sku, ts),
      ad_orders: getVal(hourlyDataResponse.ad_orders, ts),
      clicks: getVal(hourlyDataResponse.ad_clicks, ts),
      impressions: getVal(hourlyDataResponse.ad_impressions, ts),

      // sponsored display (존재하지 않으면 0)
      sd_cost: getVal(hourlyDataResponse.sd_ad_spend, ts),
      sd_sales: getVal(hourlyDataResponse.sd_ad_sales, ts),
      sd_sales_same_sku: getVal(hourlyDataResponse.sd_ad_sales_same_sku, ts),
      sd_clicks: getVal(hourlyDataResponse.sd_ad_clicks, ts),
      sd_impressions: getVal(hourlyDataResponse.sd_ad_impressions, ts),
    }))

    return { aggregated, summary }
  }

  const fetchOptimizationSets = async (signal: AbortSignal) => {
    if (!(session?.user as any).access_token) {
      console.log('access token is missing in the session.')
    }
    // let optimizationSetsResponse = await api.getListOptimizationsForReport(selectedProfile.account_id, selectedMarketplace.marketplace_id, (session?.user as any).access_token, signal)
    let optimizationSetsResponse = [
      {
        "id": 168,
        "account_id": "AJAUUX76KYQGG",
        "marketplace_id": "ATVPDKIKX0DER",
        "optimization_name": "kinigo chocolate",
        "ad_budget_type": "DATERANGE",
        "ad_budget_amount": 40,
        "ad_budget_start_date": "2025-01-15",
        "ad_budget_end_date": "2025-02-15",
        "optimization_range": "SPPLUS",
        "optimization_goal": "ROI",
        "optimization_option": "INVENTORY",
        "optimization_target_type": "NONE",
        "optimization_target_value": 0,
        "bid_yn": "Y",
        "use_yn": "Y",
        "created_by": 106,
        "creation_datetime": "2025-01-15T01:02:01",
        "updated_by": 106,
        "last_update_datetime": "2025-05-29T08:43:19",
        "request_status": "DONE",
        "portfolio_id": "**************",
        "account_type": "seller",
        "selling_partner_id": "AJAUUX76KYQGG",
        "display_yn": "Y",
        "competition_option": "NONE",
        "target_same_sku_only_yn": "N",
        "limit_cpc": -1,
        "target_products": [
          {
            "asin": "B0CW5NYS1S"
          }
        ]
      }
    ]
    setPortfolioListItems(optimizationSetsResponse as any || [])
  }

  useEffect(() => {
    const abortController = new AbortController()
    fetchOptimizationSets(abortController.signal)
    setSelectedPortfolioItems([])
    return () => {
      abortController.abort()
    }
  }, [])

  // Transform ad share data for UI consumption
  const transformAdShareData = () => {
    if (!adShareData || Object.keys(adShareData).length === 0) {
      return { products: [], chartData: [] }
    }

    // Extract ASIN data (exclude 'etc' and 'sum')
    const asinEntries = Object.entries(adShareData).filter(([key]) => !['etc', 'sum'].includes(key))
    
    // Sort by value descending
    const sortedProducts = asinEntries
      .map(([asin, data]: [string, any]) => ({
        ...data.product,
        value: data.value,
        share_percentage: adShareData.sum > 0 ? (data.value / adShareData.sum) * 100 : 0
      }))
      .sort((a, b) => b.value - a.value)

    // Prepare chart data
    const chartData = sortedProducts.map((item, index) => ({
      id: item.asin,
      name: item.item_name,
      share_percentage: item.share_percentage,
      value: item.value,
      border: colorSetList[index % colorSetList.length].border_hex,
      bg: colorSetList[index % colorSetList.length].bg_hex,
      text: colorSetList[index % colorSetList.length].text_hex
    }))

    // Add 'etc' if it exists
    if (adShareData.etc && adShareData.etc > 0) {
      const etcSharePercentage = adShareData.sum > 0 ? (adShareData.etc / adShareData.sum) * 100 : 0
      chartData.push({
        id: 'etc',
        name: 'Others',
        share_percentage: etcSharePercentage,
        value: adShareData.etc,
        border: '#9CA3AF',
        bg: '#F3F4F6',
        text: '#6B7280'
      })
    }

    return { products: sortedProducts, chartData }
  }

  const { products: transformedProducts, chartData: transformedChartData } = transformAdShareData()

  // refs for physical scroll sync
  const salesGraphScrollRef = useRef<HTMLDivElement>(null)
  const adsGraphScrollRef = useRef<HTMLDivElement>(null)

  // Handle scroll synchronization between charts
  const handleGraphScroll = (scrollLeft: number, sourceRef: string) => {
    if (sourceRef === 'sales' && adsGraphScrollRef.current) {
      adsGraphScrollRef.current.scrollLeft = scrollLeft
    } else if (sourceRef === 'ads' && salesGraphScrollRef.current) {
      salesGraphScrollRef.current.scrollLeft = scrollLeft
    }
  }

  // real-time hourly summary (최근 24시간)
  const [hourlySummary, setHourlySummary] = useState({
    sales: 0,
    ordered_units: 0,
    glance_views: 0,
    ad_spend: 0,
    ad_impressions: 0,
    ad_clicks: 0,
    ad_orders: 0,
    ad_sales: 0,
  })

  // API에서 제공하는 타임존 정보
  const [serverTimeInfo, setServerTimeInfo] = useState<{
    last_updated_date_time: string | null,
    timezone: string | null
  }>({
    last_updated_date_time: null,
    timezone: null
  })

  // 계정의 ad_timezone을 이용한 현재 시간 계산 함수
  const getCurrentTimeWithAccountTimezone = () => {
    return {
      "time": new Date("2025-09-11T19:00:00.000Z"),
      "timezone": "America/Los_Angeles",
      "timeString": "2025-09-11 19:00"
    }
  }

  // calculate hourly data onMainProductsChange (second instance)
  useEffect(() => {
    const { aggregated } = calculateHourlyData(mainProductsHourlyData || {}) as any
    if (aggregated && aggregated.length > 0) {
      setReportByHour(aggregated)
    }
  }, [mainProductsHourlyData])

  // fetch hourly today data for overview section
  useEffect(() => {
    const abortController = new AbortController()
    mopUserData && fetchHourlyReportToday(abortController.signal).then((data) => {
      if (data?.hourlyData) {
        const { summary } = calculateHourlyData(data.hourlyData as any)
        if (summary) {
          setHourlySummary(summary)
        } else {
          // 빈값인 경우 초기화
          setHourlySummary({
            sales: 0,
            ordered_units: 0,
            glance_views: 0,
            ad_spend: 0,
            ad_impressions: 0,
            ad_clicks: 0,
            ad_orders: 0,
            ad_sales: 0,
          })
        }
        
        // 서버에서 제공하는 시간 정보 저장
        if (data.hourlyData.last_updated_date_time && data.hourlyData.timezone) {
          setServerTimeInfo({
            last_updated_date_time: data.hourlyData.last_updated_date_time,
            timezone: data.hourlyData.timezone
          })
        }
      } else {
        // 응답이 없는 경우 초기화
        setHourlySummary({
          sales: 0,
          ordered_units: 0,
          glance_views: 0,
          ad_spend: 0,
          ad_impressions: 0,
          ad_clicks: 0,
          ad_orders: 0,
          ad_sales: 0,
        })
        setServerTimeInfo({
          last_updated_date_time: null,
          timezone: null
        })
      }
    })
    return () => abortController.abort()
  }, [selectedPortfolioItems, selectedAttributionWindow])

  // Scroll to end function for charts
  const scrollChartsToEnd = () => {
    if (salesGraphScrollRef.current) {
      const maxScrollLeft = salesGraphScrollRef.current.scrollWidth - salesGraphScrollRef.current.clientWidth
      salesGraphScrollRef.current.scrollLeft = Math.max(0, maxScrollLeft)
    }
    if (adsGraphScrollRef.current) {
      const maxScrollLeft = adsGraphScrollRef.current.scrollWidth - adsGraphScrollRef.current.clientWidth
      adsGraphScrollRef.current.scrollLeft = Math.max(0, maxScrollLeft)
    }
  }

  // Handle chart after plot event - triggered when chart is fully rendered
  const handleAfterPlot = () => {
    scrollChartsToEnd()
  }

  // state for sales / ads performance legends
  const [selectedSalesLegend, setSelectedSalesLegend] = useState(salesLegendOptions)
  const [selectedAdsLegend, setSelectedAdsLegend] = useState(adsLegendOptions)

  // 현재 화면에서 선택된 포트폴리오가 1개일 때만 optimization_id 유효
  const optimizationId = selectedPortfolioItems.length === 1 ? String(selectedPortfolioItems[0].id) : null

  const fetchHourlyReportToday = async (signal: AbortSignal) => {
    if (!(session?.user as any)?.access_token) {
      console.log("access token is missing in the session.")
    }
    hourlyfetchCounter.current += 1
    const optimizationId = selectedPortfolioItems.length === 1 ? String(selectedPortfolioItems[0].id) : null
    const attributionWindow = Number(selectedAttributionWindow.type.replace('d', ''))

    // const hourlyResp = await api.getHourlyReportToday(
    //   selectedProfile.account_id,
    //   selectedMarketplace.marketplace_id,
    //   optimizationId,
    //   attributionWindow,
    //   (session?.user as any).access_token,
    //   signal
    // )
    const hourlyResp ={
      "sales": {
        "sum": 0
      },
      "ordered_units": {
        "sum": 0
      },
      "glance_views": {
        "sum": 0
      },
      "ad_spend": {
        "0": 0,
        "1": 0,
        "2": 0,
        "3": 0,
        "4": 0,
        "5": 0,
        "6": 0,
        "7": 0,
        "8": 0,
        "9": 0,
        "10": 0,
        "11": 0,
        "12": 0,
        "13": 0,
        "14": 0,
        "15": 0,
        "16": 0,
        "17": 0,
        "18": 0,
        "19": 0,
        "sum": 0
      },
      "ad_impressions": {
        "0": 0,
        "1": 0,
        "2": 0,
        "3": 0,
        "4": 0,
        "5": 0,
        "6": 0,
        "7": 0,
        "8": 0,
        "9": 0,
        "10": 0,
        "11": 0,
        "12": 0,
        "13": 0,
        "14": 0,
        "15": 0,
        "16": 0,
        "17": 0,
        "18": 0,
        "19": 0,
        "sum": 0
      },
      "ad_clicks": {
        "0": 0,
        "1": 0,
        "2": 0,
        "3": 0,
        "4": 0,
        "5": 0,
        "6": 0,
        "7": 0,
        "8": 0,
        "9": 0,
        "10": 0,
        "11": 0,
        "12": 0,
        "13": 0,
        "14": 0,
        "15": 0,
        "16": 0,
        "17": 0,
        "18": 0,
        "19": 0,
        "sum": 0
      },
      "ad_orders": {
        "0": 0,
        "1": 0,
        "2": 0,
        "3": 0,
        "4": 0,
        "5": 0,
        "6": 0,
        "7": 0,
        "8": 0,
        "9": 0,
        "10": 0,
        "11": 0,
        "12": 0,
        "13": 0,
        "14": 0,
        "15": 0,
        "16": 0,
        "17": 0,
        "18": 0,
        "19": 0,
        "sum": 0
      },
      "ad_sales": {
        "0": 0,
        "1": 0,
        "2": 0,
        "3": 0,
        "4": 0,
        "5": 0,
        "6": 0,
        "7": 0,
        "8": 0,
        "9": 0,
        "10": 0,
        "11": 0,
        "12": 0,
        "13": 0,
        "14": 0,
        "15": 0,
        "16": 0,
        "17": 0,
        "18": 0,
        "19": 0,
        "sum": 0
      },
      "ad_sales_same_sku": {
        "0": 0,
        "1": 0,
        "2": 0,
        "3": 0,
        "4": 0,
        "5": 0,
        "6": 0,
        "7": 0,
        "8": 0,
        "9": 0,
        "10": 0,
        "11": 0,
        "12": 0,
        "13": 0,
        "14": 0,
        "15": 0,
        "16": 0,
        "17": 0,
        "18": 0,
        "19": 0,
        "sum": 0
      },
      "last_updated_date_time": "2025-09-11 19:07",
      "timezone": "America/Los_Angeles"
    }
    hourlyfetchCounter.current -= 1
    return { hourlyData: hourlyResp }
  }

  return (
    <div className="relative size-full min-w-[800px] pt-[82px] pb-4 sm:pb-6 px-8 sm:px-12 overflow-hidden">
      {/* filters */}
      <div className="fixed top-16 left-[190px] w-full px-8 py-2 sm:px-12 sm:py-3 z-[5] bg-white/60 backdrop-blur-md border-t border-b border-gray-200 shadow-sm flex items-center gap-x-4">
        <div className="flex flex-wrap items-center gap-3">
          <div>
            <div className="text-xs text-gray-400 font-semibold">
              {t("filter.attributionWindow.label")}
            </div>
            <AttributionWindowSelect
              className="mt-1 border border-gray-200 rounded-lg"
              listboxClassName="z-[3]"
              selected={selectedAttributionWindow}
              setSelected={setSelectedAttributionWindow}
            />
          </div>
          { portfolioListItems.length > 0 &&
          <div>
            <div className="text-xs text-gray-400 font-semibold">
              {t("filter.optimizationSet.label")}
            </div>
            <OptimizationSetSelect
              className="mt-1 border border-gray-200 rounded-lg"
              listboxClassName="z-[3]"
              selected={selectedPortfolioItems}
              setSelected={setSelectedPortfolioItems}
              options={portfolioListItems}
              multiple={false}
            />
          </div>
          }
        </div>
      </div>
      
      {/* today's overview title section */}
      <div className="flex items-center gap-x-3 pt-4 sm:pt-6">
        <h1 className="text-2xl text-gray-800 font-medium">{t("title.todayOverview")}</h1>
        <RefreshButton 
          onClick={() => {}}
          className="bg-white/40 hover:bg-blue-100/80 text-gray-500 hover:text-blue-500 border border-gray-200 hover:border-blue-200"
          isLoading={fetchCounter.current > 0 || adShareLoading}
          size="sm"
          aria-label="Refresh today's overview"
        />
      </div>
      
      {/* 24 hour time section */}
      <div className="mt-4 relative pt-9 pb-8 px-8 shadow-md">
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="rounded-l-lg w-1/2 h-full bg-gradient-to-r from-gray-900/90 from-40% via-purple-800/70 via-50% to-orange-200/20 to-90%"></div>
          <div className="rounded-r-lg w-1/2 h-full bg-gradient-to-l from-gray-900/90 from-40% via-purple-800/70 via-50% to-orange-200/20 to-90%"></div>
        </div>
        {/* 24 hour progress bar with "now" red line */}
        <div className="relative w-full h-8 flex items-center">
          {/* Background bar */}
          <div className="absolute left-0 top-1/2 -translate-y-1/2 w-full h-2 bg-gray-200 rounded-full" />
          {/* Progress bar */}
          {(() => {
            // 우선순위: 1) 서버 시간 정보, 2) 계정 타임존, 3) 숨김
            let now: Date;
            let lastUpdatedAt: Date | null = null;
            let currentTimezone: string | null = null;
            let shouldShowTime = false;
            
            if (serverTimeInfo.last_updated_date_time && serverTimeInfo.timezone) {
              shouldShowTime = true;
              // 서버 시간을 파싱 (YYYY-MM-DD HH:MM 형식)
              const [datePart, timePart] = serverTimeInfo.last_updated_date_time.split(' ');
              const [year, month, day] = datePart.split('-').map(Number);
              const [hour, minute] = timePart.split(':').map(Number);
              
              // 분을 10의 단위로 버림
              const roundedMinute = Math.floor(minute / 10) * 10;
              
              // 서버에서 받은 시간을 UTC 기준으로 설정 (실제로는 서버 타임존 시간)
              lastUpdatedAt = new Date();
              lastUpdatedAt.setUTCFullYear(year, month - 1, day); // month는 0-based
              lastUpdatedAt.setUTCHours(hour, roundedMinute, 0, 0);
              currentTimezone = serverTimeInfo.timezone;
            }
          
            // 계정의 ad_timezone을 사용
            const accountTimeInfo = getCurrentTimeWithAccountTimezone();
            now = accountTimeInfo.time;
            if (!currentTimezone) {
              currentTimezone = accountTimeInfo.timezone;
            }
            
            // 타임존이 있을 때만 시간 표시
            if (currentTimezone) {
              shouldShowTime = true;
            }
            
            // 타임존 정보가 없으면 시간 표시 하지 않음
            if (!shouldShowTime) {
              return (
                <div className="absolute inset-x-0 top-0">
                  <div className="absolute left-1/2 -translate-x-1/2 -top-[24px] animate-pulse">
                    <span className="text-sm text-gray-400">Waiting for timezone information...</span>
                  </div>
                  <div className="absolute left-0 -translate-x-1/2 top-[28px] animate-pulse">
                    <div className="w-5 h-5 rounded-md bg-gray-100/40"/>
                  </div>
                  <div className="absolute left-1/2 -translate-x-1/2 top-[28px] animate-pulse">
                    <div className="w-5 h-5 rounded-md bg-gray-400/40"/>
                  </div>
                  <div className="absolute left-full -translate-x-1/2 top-[28px] animate-pulse">
                    <div className="w-5 h-5 rounded-md bg-gray-100/40"/>
                  </div>
                </div>
              );
            }
            
            const startOfDay = new Date(now);
            startOfDay.setUTCHours(0, 0, 0, 0);
            const endOfDay = new Date(now);
            endOfDay.setUTCHours(23, 59, 59, 999);
            const totalMs = endOfDay.getTime() - startOfDay.getTime();
            const passedMs = now.getTime() - startOfDay.getTime();
            const percent = Math.max(0, Math.min(100, (passedMs / totalMs) * 100));
            // Generate hour marks (every 3 hours)
            const hourMarks = [];
            for (let h = 0; h <= 24; h += 3) {
              const left = (h / 24) * 100;
              hourMarks.push(
                <div
                  key={h}
                  className="absolute bottom-5 flex flex-col items-center"
                  style={{ left: `calc(${left}% - 14px)` }}
                >
                  <div className={`text-[10px] font-semibold mt-0.5 ${[9,12,15].includes(h) ? 'text-gray-600' : 'text-gray-200'}`}>
                    {`${h.toString().padStart(2, '0')}:00`}
                  </div>
                  <div className={`w-0.5 h-2 ${[9,12,15].includes(h) ? 'bg-gray-500' : 'bg-gray-300'}`} />
                </div>
              );
            }
            return (
              <>
                {/* Progress up to now */}
                <div
                  className="absolute left-0 top-1/2 -translate-y-1/2 h-2 bg-blue-400 rounded-full"
                  style={{ width: `${percent}%`, zIndex: 1 }}
                />
                {/* "Now" red line */}
                <div
                  className="absolute top-0 bottom-0"
                  style={{ left: `calc(${percent}% - 1px)`, zIndex: 2 }}
                >
                  <div className="relative flex flex-col items-center h-full">
                    <div className="w-0.5 h-[30px] bg-red-500 rounded-full" />
                    <div className="absolute -bottom-[22px] left-0 -translate-x-1/2 flex items-center gap-x-1.5 text-xs font-semibold text-red-600 whitespace-nowrap">
                      <span className="flex items-center gap-x-1.5 py-1 px-1.5 bg-red-500 text-white rounded-sm">
                        <span className="relative flex h-2 w-2">
                          <span className="absolute inline-flex h-full w-full rounded-full opacity-75 animate-ping bg-white"></span>
                          <span className="relative inline-flex rounded-full h-2 w-2 bg-white"></span>
                        </span>
                        <span className="px-1.5 text-[10px] rounded-sm bg-white/20">now</span>
                        {" "}
                        {`${now.getUTCHours().toString().padStart(2, '0')}:${now.getUTCMinutes().toString().padStart(2, '0')}`}
                      </span>
                    </div>
                    <div className="absolute -bottom-[32px] left-0 flex items-center gap-x-1.5 text-xs font-bold text-red-500 whitespace-nowrap">
                      {/* Upward triangle */}
                      <div
                        className="absolute left-1/2 -translate-x-1/2 -bottom-3"
                        style={{ width: 0, height: 0, borderLeft: '10px solid transparent', borderRight: '10px solid transparent', borderTop: '10px solid transparent', borderBottom: '10px solid', borderBottomColor: 'rgba(17,24,39,0.9)' }}
                      />
                    </div>
                  </div>
                </div>
                {currentTimezone && (
                  <div className="absolute w-full bottom-[45px] flex items-center justify-between">
                    <span className="-ml-[14px] flex items-center text-gray-100 text-[10px] font-semibold">
                      {currentTimezone.split('/')[1]?.replace('_', ' ') || currentTimezone}
                    </span>
                    {lastUpdatedAt && (
                      <span className="-mr-[14px] flex items-center text-gray-100 text-[10px] font-semibold">
                        last updated at {`${lastUpdatedAt.getUTCHours().toString().padStart(2, '0')}:${lastUpdatedAt.getUTCMinutes().toString().padStart(2, '0')}`}
                      </span>
                    )}
                  </div>
                )}
                <div className="absolute left-0 -translate-x-1/2 -bottom-[18px]">
                  <MoonIcon className="w-5 h-5 text-yellow-100" />
                </div>
                <div className="absolute left-1/2 -translate-x-1/2 -bottom-5">
                  <SunIcon className="w-6 h-6 text-orange-400" />
                </div>
                <div className="absolute right-0 translate-x-1/2 -bottom-[18px]">
                  <MoonIcon className="w-5 h-5 text-yellow-100" />
                </div>
                {/* Hour marks */}
                {hourMarks}
              </>
            );
          })()}
        </div>
      </div>
      {/* today's overview section */}
      <div className="relative flex flex-col items-center gap-x-3">
        <div className="mt-3 flex-shrink-0 space-y-3 w-full p-6 bg-gray-900/90 rounded-xl shadow-md">
          <div className="flex flex-col justify-start gap-y-1">
            {/* to be developed */}
            <div className="relative w-full flex flex-col items-start gap-x-3 pb-1">
              <div className="flex-shrink-0 flex items-center gap-x-1 text-lg text-purple-300 font-semibold text-center">
                Sales Overview
              </div>
            </div>
            <div className="relative w-full grid grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-3">
              <div className="p-3 rounded-md bg-gray-100/10 shadow-sm backdrop-blur-sm">
                <div className="flex items-center gap-x-1.5 text-gray-400 text-xs font-semibold">
                  {td('graph.legendSelect.glanceViews')}
                </div>
                {fetchCounter.current > 0
                  ? <div className="animate-pulse pt-1">
                      <div className="w-[120px] h-6 rounded-md bg-gray-100/40"/>
                    </div>
                  : <div className="mt-1 text-lg text-gray-100 font-semibold">
                      {hourlySummary.glance_views.toLocaleString(undefined, {maximumFractionDigits: 0})}
                    </div>
                }
              </div>
              <div className="p-3 rounded-md bg-gray-100/10 shadow-sm backdrop-blur-sm">
                <div className="flex items-center gap-x-1.5 text-gray-400 text-xs font-semibold">
                  {td('graph.legendSelect.orderedUnits')}
                </div>
                {fetchCounter.current > 0
                  ? <div className="animate-pulse pt-1">
                      <div className="w-[120px] h-6 rounded-md bg-gray-100/40"/>
                    </div>
                  : <div className="mt-1 text-lg text-gray-100 font-semibold">
                      {hourlySummary.ordered_units.toLocaleString(undefined, {maximumFractionDigits: 0})}
                    </div>
                }
              </div>
              <div className="p-3 rounded-md bg-gray-100/10 shadow-sm backdrop-blur-sm">
                <div className="flex items-center gap-x-1.5 text-gray-400 text-xs font-semibold">
                  {td('graph.legendSelect.revenue')}
                </div>
                {fetchCounter.current > 0
                  ? <div className="animate-pulse pt-1">
                      <div className="w-[120px] h-6 rounded-md bg-gray-100/40"/>
                    </div>
                  : <div className="mt-1 text-lg text-gray-100 font-semibold">
                      {formatCurrency(hourlySummary.sales, currencyCode)}
                    </div>
                }
              </div>
            </div>
          </div>
          <div className="flex flex-col justify-start gap-y-1">
            {/* to be developed */}
            <div className="relative w-full flex flex-col items-start gap-x-3 pb-1">
              <div className="flex-shrink-0 flex items-center gap-x-1 text-lg text-blue-300 font-semibold text-center">
                Ads Overview
              </div>
            </div>
            <div className="relative w-full grid grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-3">
              <div className="p-3 rounded-md bg-gray-100/10 shadow-sm backdrop-blur-sm">
                <div className="flex items-center gap-x-1.5 text-gray-400 text-xs font-semibold">
                  {td('graph.legendSelect.impressions')}
                </div>
                {fetchCounter.current > 0
                  ? <div className="animate-pulse pt-1">
                      <div className="w-[120px] h-6 rounded-md bg-gray-100/40"/>
                    </div>
                  : <div className="mt-1 text-lg text-gray-100 font-semibold">
                      {hourlySummary.ad_impressions.toLocaleString(undefined, {maximumFractionDigits: 0})}
                    </div>
                }
              </div>
              <div className="p-3 rounded-md bg-gray-100/10 shadow-sm backdrop-blur-sm">
                <div className="flex items-center gap-x-1.5 text-gray-400 text-xs font-semibold">
                  {td('graph.legendSelect.clicks')}
                </div>
                {fetchCounter.current > 0
                  ? <div className="animate-pulse pt-1">
                      <div className="w-[120px] h-6 rounded-md bg-gray-100/40"/>
                    </div>
                  : <div className="mt-1 text-lg text-gray-100 font-semibold">
                      {hourlySummary.ad_clicks.toLocaleString(undefined, {maximumFractionDigits: 0})}
                    </div>
                }
              </div>
              <div className="p-3 rounded-md bg-gray-100/10 shadow-sm backdrop-blur-sm">
                <div className="flex items-center gap-x-1.5 text-gray-400 text-xs font-semibold">
                  {td('graph.legendSelect.adOrders')}
                </div>
                {fetchCounter.current > 0
                  ? <div className="animate-pulse pt-1">
                      <div className="w-[120px] h-6 rounded-md bg-gray-100/40"/>
                    </div>
                  : <div className="mt-1 text-lg text-gray-100 font-semibold">
                      {hourlySummary.ad_orders.toLocaleString(undefined, {maximumFractionDigits: 0})}
                    </div>
                }
              </div>
              <div className="p-3 rounded-md bg-gray-100/10 shadow-sm backdrop-blur-sm">
                <div className="flex items-center gap-x-1.5 text-gray-400 text-xs font-semibold">
                  {td('graph.legendSelect.adSales')}
                </div>
                {fetchCounter.current > 0
                  ? <div className="animate-pulse pt-1">
                      <div className="w-[120px] h-6 rounded-md bg-gray-100/40"/>
                    </div>
                  : <div className="mt-1 text-lg text-gray-100 font-semibold">
                      {formatCurrency(hourlySummary.ad_sales, currencyCode)}
                    </div>
                }
              </div>
              <div className="p-3 rounded-md bg-gray-100/10 shadow-sm backdrop-blur-sm">
                <div className="flex items-center gap-x-1.5 text-gray-400 text-xs font-semibold">
                  {td('graph.legendSelect.adCost')}
                </div>
                {fetchCounter.current > 0
                  ? <div className="animate-pulse pt-1">
                      <div className="w-[120px] h-6 rounded-md bg-gray-100/40"/>
                    </div>
                  : <div className="mt-1 text-lg text-gray-100 font-semibold">
                      {formatCurrency(hourlySummary.ad_spend, currencyCode)}
                    </div>
                }
              </div>
            </div>
          </div>
        </div>
        <div className="mt-3 relative grow flex flex-col w-full min-w-0 xl:h-[424px] p-6 rounded-lg bg-white shadow-md">
          {fetchCounter.current > 0
            ? <div className="animate-pulse relative flex flex-col w-full h-full">
              <div className="w-[260px] h-[38px] rounded-md bg-gray-100">
              </div>
              <div className="mt-3 grow w-full rounded-md bg-gray-100">
              </div>
            </div>
            : <>
              <div className="relative w-[120px] mb-3">
                <Listbox value={selectedProductShareLegend} onChange={setSelectedProductShareLegend}>
                  <div className="relative min-w-[260px] max-w-full w-fit">
                    <Listbox.Button className="relative w-full inline-flex items-center gap-x-6 cursor-pointer rounded-lg bg-white text-gray-600 py-2 pl-3 pr-10 text-left border border-gray-100 focus:outline-none focus-visible:border-indigo-500 focus-visible:ring-2 focus-visible:ring-white/75 focus-visible:ring-offset-2 focus-visible:ring-offset-orange-300 sm:text-sm truncate">
                      <span className="block truncate">{selectedProductShareLegend.name}</span>
                      <span className="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-2 bg-white">
                        <ChevronUpDownIcon
                          className="h-5 w-5 text-gray-300"
                          aria-hidden="true"
                        />
                      </span>
                    </Listbox.Button>
                    <Transition
                      as={Fragment}
                      leave="transition ease-in duration-100"
                      leaveFrom="opacity-100"
                      leaveTo="opacity-0"
                    >
                      <Listbox.Options
                        className={cn(
                          "z-[3] absolute flex flex-col items-start divide-y divide-gray-100 mt-1 min-w-[400px] max-h-80 w-fit overflow-auto rounded-md bg-white text-sm shadow-lg ring-1 ring-black/5 focus:outline-none",
                        )}
                      >
                        <div className="relative w-full flex flex-col xl:flex-row items-start divide-y xl:divide-x divide-gray-100">
                          <div className="w-full xl:w-1/2 py-2">
                            <div className="flex items-center gap-x-2 py-2 px-4 text-xs text-gray-400 font-semibold">
                              Total Performance
                            </div>
                            {productShareLegendOptions.filter(option => option.category === 'total').map((option) => (
                              <Listbox.Option
                                key={option.id}
                                className={({ active }) =>
                                  `relative cursor-pointer select-none py-2 pl-4 pr-10 ${
                                    active ? 'bg-blue-100/40 text-blue-900' : 'text-gray-600'
                                  }`
                                }
                                value={option}
                              >
                                {({ selected }) => (
                                  <>
                                    <div
                                      className={`flex items-center gap-x-3 truncate ${
                                        selected ? 'font-medium' : 'font-normal'
                                      }`}
                                    >
                                      {option.name}
                                    </div>
                                    {selected ? (
                                      <div className="absolute inset-y-0 right-0 flex items-center pr-3">
                                        <div className="shrink-0 flex items-center justify-center w-5 h-5 rounded-full">
                                          <Check className="w-4 h-4 stroke-blue-400" aria-hidden="true" />
                                        </div>
                                      </div>
                                    ) : null}
                                  </>
                                )}
                              </Listbox.Option>
                            ))}
                          </div>
                          <div className="w-full xl:w-1/2 py-2">
                            <div className="flex items-center gap-x-2 py-2 px-4 text-xs text-gray-400 font-semibold">
                              Ad Performance
                            </div>
                            {productShareLegendOptions.filter(option => option.category === 'ad').map((option) => (
                              <Listbox.Option
                                key={option.id}
                                className={({ active }) =>
                                  `relative cursor-pointer select-none py-2 pl-4 pr-10 ${
                                    active ? 'bg-blue-100/40 text-blue-900' : 'text-gray-600'
                                  }`
                                }
                                value={option}
                              >
                                {({ selected }) => (
                                  <>
                                    <div
                                      className={`flex items-center gap-x-3 truncate ${
                                        selected ? 'font-medium' : 'font-normal'
                                      }`}
                                    >
                                      {option.name}
                                    </div>
                                    {selected ? (
                                      <div className="absolute inset-y-0 right-0 flex items-center pr-3">
                                        <div className="shrink-0 flex items-center justify-center w-5 h-5 rounded-full">
                                          <Check className="w-4 h-4 stroke-blue-400" aria-hidden="true" />
                                        </div>
                                      </div>
                                    ) : null}
                                  </>
                                )}
                              </Listbox.Option>
                            ))}
                          </div>
                        </div>
                      </Listbox.Options>
                    </Transition>
                  </div>
                </Listbox>
              </div>
              <div className="relative size-full flex flex-col-reverse xl:flex-row items-center justify-between gap-6 min-h-0">
                <div className="relative flex-1 min-w-0 w-full h-full">
                  {/* Product List in a Portfolio */}
                  <div className="relative size-full rounded-lg bg-white border border-gray-100 overflow-y-scroll">
                    <div className="sticky inset-x-0 top-0 z-[1] flex items-center gap-x-3 py-3 text-center text-sm text-gray-400 font-bold bg-gray-100/40 backdrop-blur-md">
                      <div className="flex-shrink-0 w-[80px] pl-8">
                        {t("productShare.table.header.rank")}
                      </div>
                      <div className="grow px-8">
                        {t("productShare.table.header.productInfo")}
                      </div>
                      <div className="flex-shrink-0 w-[180px] pr-6">
                        {t("productShare.table.header.share")}
                      </div>
                    </div>
                    {adShareLoading ? (
                      <div className="py-8 flex justify-center">
                        <div className="animate-pulse">
                          <div className="w-[300px] h-6 rounded-md bg-gray-100"/>
                        </div>
                      </div>
                    ) : (
                      <ul className="divide-y divide-gray-100">
                        {transformedProducts.length === 0 ? (
                          <li className="py-8 text-center text-gray-400">
                            No data available
                          </li>
                        ) : (
                          transformedProducts.map((item: any, index: number) => (
                            <li
                              className="relative flex items-center gap-x-3 py-6 cursor-pointer hover:bg-gray-100/50 text-center text-gray-500 text-sm"
                              key={item.asin}
                              onClick={() => {}}
                            >
                              <div className="relative flex-shrink-0 flex flex-col items-start justify-center gap-x-2 gap-y-2 w-[80px] pl-8">
                                Rank {index + 1}
                              </div>
                              {productListItemCard(item, "light")}
                              <div className="flex-shrink-0 w-[180px] pr-6">
                                <div className="flex items-start justify-center w-full gap-x-3">
                                  <div
                                    className={cn(
                                      "inline-flex items-center justify-end w-[70px] h-8 px-3 text-xs border font-semibold rounded-sm",
                                      `${colorSetList[index % colorSetList.length].border} ${colorSetList[index % colorSetList.length].bg} ${colorSetList[index % colorSetList.length].text}`
                                    )}
                                  >
                                    {item.share_percentage.toFixed(2)}%
                                  </div>
                                  <div className="mt-2 flex-shrink-0 text-xs text-gray-500 font-semibold">
                                    {['revenue', 'ad-sales', 'ad-spends'].includes(selectedProductShareLegend.type)
                                      ? formatCurrency(item.value || 0, currencyCode)
                                      : (item.value || 0).toLocaleString(undefined, {maximumFractionDigits: 0})
                                    }
                                  </div>
                                </div>
                              </div>
                            </li>
                          ))
                        )}
                      </ul>
                    )}
                  </div>
                </div>
                <div className="relative flex-shrink-0 w-[360px] h-[308px]">
                  <ProductSharePieChart
                    data={transformedChartData}
                    metricName={selectedProductShareLegend.name}
                    selectedLegendType={selectedProductShareLegend.type}
                  />
                  <div className="absolute inset-0 flex items-center justify-center pointer-events-none z-[1]">
                    <span className="text-base font-bold text-gray-400">{t("productShare.title")}</span>
                  </div>
                </div>
              </div>
            </>
          }
        </div>
      </div>
      <div className="absolute inset-0 size-full flex items-center justify-center bg-gray-100/60 backdrop-blur-sm z-[5]">
        <div className="flex flex-col items-center">
          <div className="text-center text-gray-800 font-semibold text-lg">
            Unlock the full potential of your data insights.
            <br />
            Subscribe now and take your business to the next level!
          </div>
          <Link
            href={{
              pathname: '/dashboard',
              search: '?tab=manage-profile'
            }}
            className="mt-4 inline-flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            subscribe
          </Link>
        </div>
      </div>
    </div>
  )
}
