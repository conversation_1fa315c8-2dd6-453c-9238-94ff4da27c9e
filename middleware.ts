import { type NextRequest, NextResponse } from "next/server";
import { auth } from "auth";
import { locales } from "./config";

export function middleware(request: NextRequest) {
  const { nextUrl } = request;
  const lang = nextUrl.searchParams.get("lang");

  if (lang && locales.includes(lang as any)) {
    const newUrl = new URL(nextUrl.pathname, request.url);
    nextUrl.searchParams.forEach((value, key) => {
      if (key !== "lang") {
        newUrl.searchParams.append(key, value);
      }
    });

    const response = NextResponse.redirect(newUrl);
    response.cookies.set("NEXT_LOCALE", lang);
    return response;
  }

  return auth(request as any);
}

// Read more: https://nextjs.org/docs/app/building-your-application/routing/middleware#matcher
export const config = {
  matcher: ["/((?!api|_next/static|_next/image|favicon.ico).*)"],
};