import { ErrorResponse } from "@/app/api/_models";
import { getServerApiHostUrl } from "@/utils/host";
import { NextRequest, NextResponse } from "next/server";

export type AddOptimizationTargetsResponse = any[];

export async function PUT(
  request: NextRequest
): Promise<NextResponse<AddOptimizationTargetsResponse | ErrorResponse>> {
  const accessToken = request.headers.get("Authorization");
  const body = await request.json();

  if (!accessToken) {
    return NextResponse.json(
      { message: "Authorization header is missing" },
      { status: 400 }
    );
  }
  const accountId = request.nextUrl.searchParams.get("account_id")
  if (!accountId) {
    return NextResponse.json(
      { message: "account_id query is missing" },
      { status: 400 }
    );
  }
  const marketplaceId = request.nextUrl.searchParams.get("marketplace_id")
  if (!marketplaceId) {
    return NextResponse.json(
      { message: "marketplace_id query is missing" },
      { status: 400 }
    );
  }
  const addOptimizationTargetsResponse = await fetch(
    `${await getServerApiHostUrl()}/api/optimization/add_targets?account_id=${accountId}&marketplace_id=${marketplaceId}`,
    {
      method: "PUT",
      headers: {
        "Authorization": `Bearer ${accessToken}`,
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        "optimization_id": body?.optimization_id,
        "target_products": body?.target_products,
        "integrate_existing_targets": body?.integrate_existing_targets,
      }),
      cache: "no-cache",
    }
  ).then((res) => res.json());

  return NextResponse.json(addOptimizationTargetsResponse, { status: 200 });
}
