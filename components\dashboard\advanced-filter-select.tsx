"use client"

import { Fragment, useMemo } from "react"
import { useTranslations } from 'next-intl'
import { cn } from "@/utils/msc"
import { Listbox, ListboxButton, ListboxOption, ListboxOptions, Transition } from "@headlessui/react"
import { ChevronUpDownIcon } from "@heroicons/react/20/solid"
import { Check } from "@/components/ui/check"

type AdvancedFilterOption = {
  id: number;
  name: string;
  category: string;
  type: string;
}

interface AdvancedFilterSelectProps extends React.HTMLAttributes<HTMLDivElement> {
  listboxClassName?: string;
  compared?: boolean;
  advancedFilterOptions: AdvancedFilterOption[];
  selected: AdvancedFilterOption[];
  setSelected: (option: AdvancedFilterOption[]) => void
}

export default function AdvancedFilterSelect({
  className,
  listboxClassName,
  compared = false,
  advancedFilterOptions,
  selected,
  setSelected,
  ...props
}: AdvancedFilterSelectProps) {
  const t = useTranslations('DashboardPage')
  const selectedOptions = useMemo(() => {
    return selected.map((option) => advancedFilterOptions.find((advancedFilter) => advancedFilter.id === option.id))
  }, [advancedFilterOptions])
  return (
    <div
      className={cn(
        "relative w-[200px]",
        className
      )}
      {...props}
    >
      <Listbox
        value={selected}
        onChange={(targets: AdvancedFilterOption[]) => {
          const sameCategorySelected = targets.filter(target => 
            targets.some(item => item.id !== target.id && item.category === target.category)
          )
          if (sameCategorySelected.length >= 2) {
            const alreadySelected = sameCategorySelected.find((item) => selected.some(selectedItem => selectedItem.id === item.id))
            setSelected(targets.filter(target => target.id !== alreadySelected?.id))
          } else {
            setSelected(targets);
          }
        }}
        multiple>
        <div className="relative">
          <ListboxButton className="relative w-full cursor-pointer rounded-lg bg-white text-gray-600 py-2 pl-3 pr-10 text-left focus:outline-none focus-visible:border-indigo-500 focus-visible:ring-2 focus-visible:ring-white/75 focus-visible:ring-offset-2 focus-visible:ring-offset-orange-300 sm:text-sm">
            <span className="block truncate">
							{selectedOptions.length > 0
								? selected.map(item => item.name).join(', ')
								: 'None'
							}
						</span>
            <span className="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-2">
              <ChevronUpDownIcon
                className="h-5 w-5 text-gray-400"
                aria-hidden="true"
              />
            </span>
          </ListboxButton>
          <Transition
            as={Fragment}
            leave="transition ease-in duration-100"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
            <ListboxOptions
              className={cn(
                "absolute z-[3] flex flex-col items-start divide-y divide-gray-100 mt-1 max-h-100 w-fit overflow-auto rounded-md bg-white text-sm shadow-lg ring-1 ring-black/5 focus:outline-none",
                listboxClassName
              )}
            >
              <div className="w-full py-2">
                <div className="flex items-center gap-x-1.5 py-2 px-4 text-xs text-gray-400 font-semibold">
                  {t('filter.advancedFilter.subLabel.lowInventory')}
                  <div className="flex-shrink-0 py-0.25 px-1.5 text-white bg-red-500 text-[10px] rounded-md">Low Stock</div>
                </div>
                {advancedFilterOptions.filter(option => option.category === 'low-inventory').map((option) => (
                  <ListboxOption
                    key={option.id}
                    className={({ active }) =>
                      `relative cursor-pointer select-none py-2 pl-4 pr-10 ${
                        active ? 'bg-blue-100/40 text-blue-900' : 'text-gray-600'
                      }`
                    }
                    value={option}
                  >
                    {({ selected }) => (
                      <>
                        <div
                          className={`flex items-center gap-x-3 truncate ${
                            selected ? 'font-medium' : 'font-normal'
                          }`}
                        >
                          {option.name}
                        </div>
                        {selected ? (
                          <div className="absolute inset-y-0 right-0 flex items-center pr-3">
                            <div className="shrink-0 flex items-center justify-center w-5 h-5 rounded-full">
                              <Check className="w-4 h-4 stroke-blue-400" aria-hidden="true" />
                            </div>
                          </div>
                        ) : null}
                      </>
                    )}
                  </ListboxOption>
                ))}
              </div>
              <div className="w-full py-2">
                <div className="flex items-center gap-x-1.5 py-2 px-4 text-xs text-gray-400 font-semibold">
                  {t('filter.advancedFilter.subLabel.excessInventory')}
                  <div className="flex-shrink-0 py-0.25 px-1.5 text-white bg-cyan-500 text-[10px] rounded-md">Excess Stock</div>
                </div>
                {advancedFilterOptions.filter(option => option.category === 'excess-inventory').map((option) => (
                  <ListboxOption
                    key={option.id}
                    className={({ active }) =>
                      `relative cursor-pointer select-none py-2 pl-4 pr-10 ${
                        active ? 'bg-blue-100/40 text-blue-900' : 'text-gray-600'
                      }`
                    }
                    value={option}
                  >
                    {({ selected }) => (
                      <>
                        <div
                          className={`flex items-center gap-x-3 truncate ${
                            selected ? 'font-medium' : 'font-normal'
                          }`}
                        >
                          {option.name}
                        </div>
                        {selected ? (
                          <div className="absolute inset-y-0 right-0 flex items-center pr-3">
                            <div className="shrink-0 flex items-center justify-center w-5 h-5 rounded-full">
                              <Check className="w-4 h-4 stroke-blue-400" aria-hidden="true" />
                            </div>
                          </div>
                        ) : null}
                      </>
                    )}
                  </ListboxOption>
                ))}
              </div>
              {advancedFilterOptions.some(option => option.category === 'recommendation') && (
              <div className="w-full py-2">
                <div className="py-2 px-4 text-xs text-gray-400 font-semibold">{t('filter.advancedFilter.subLabel.recommendedAsin')}</div>
                {advancedFilterOptions.filter(option => option.category === 'recommendation').map((option) => (
                  <ListboxOption
                    key={option.id}
                    className={({ active }) =>
                      `relative cursor-pointer select-none py-2 pl-4 pr-10 ${
                        active ? 'bg-blue-100/40 text-blue-900' : 'text-gray-600'
                      }`
                    }
                    value={option}
                  >
                    {({ selected }) => (
                      <>
                        <div
                          className={`flex items-center gap-x-3 truncate ${
                            selected ? 'font-medium' : 'font-normal'
                          }`}
                        >
                          <div className="flex items-center gap-x-1.5">
                            {option.name}
                            {option.type === "EFFICIENCY"
                              ? <div className="text-xs"><div className="flex-shrink-0 py-0.25 px-1.5 text-white bg-green-500 text-[10px] rounded-md">Efficiency</div></div>
                              : option.type === "GROWTH"
                                ? <div className="text-xs"><div className="flex-shrink-0 py-0.25 px-1.5 text-white bg-purple-500 text-[10px] rounded-md">Growth</div></div>
                                : ""
                            }
                          </div>
                        </div>
                        {selected ? (
                          <div className="absolute inset-y-0 right-0 flex items-center pr-3">
                            <div className="shrink-0 flex items-center justify-center w-5 h-5 rounded-full">
                              <Check className="w-4 h-4 stroke-blue-400" aria-hidden="true" />
                            </div>
                          </div>
                        ) : null}
                      </>
                    )}
                  </ListboxOption>
                ))}
              </div>
              )}
            </ListboxOptions>
          </Transition>
        </div>
      </Listbox>
    </div>
  )
}
