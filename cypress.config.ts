import { defineConfig } from 'cypress'

export default defineConfig({
  chromeWebSecurity: false,
  e2e: {
    // We've imported your old cypress plugins here.
    // You may want to clean this up later by importing these.
    setupNodeEvents(on, config) {
      return require('./tests/plugins')(on, config)
    },
    supportFile: 'tests/support',
    specPattern: 'tests/module/specs//**/*.cy.{js,jsx,ts,tsx}',
  },
})
