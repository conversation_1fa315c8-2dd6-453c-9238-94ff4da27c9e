"use client"

import { forwardRef, Fragment, JSXElementConstructor, Key, PromiseLikeOfReactNode, ReactElement, ReactNode, ReactPortal, useEffect, useRef, useState } from "react"
import { useSession } from "next-auth/react"
import { useLocale, useTranslations } from 'next-intl'
import { Dialog, DialogPanel, Popover, PopoverButton, PopoverPanel, Transition, TransitionChild } from '@headlessui/react'
import DatePicker, { registerLocale } from "react-datepicker"
import { ko } from "date-fns/locale"
import "react-datepicker/dist/react-datepicker.css"
import { cn, currencyFormat, formatDate } from "@/utils/msc"
import { api } from "@/utils/api"
import { ArrowDownCircleIcon, ExclamationTriangleIcon, MagnifyingGlassIcon, QuestionMarkCircleIcon, XMarkIcon } from "@heroicons/react/20/solid"
import { ProfileOption } from "./profile-select"
import { MarketplaceOption } from "./marketplace-select"
import BrandShareGraph from "./brand-share-graph"
import TrendGraph from "./trend-graph"
import CompetitionKeywordViewSlider from "./competition-keyword-view-slider"
registerLocale('ko', ko)

interface CompetitionDailyLayoutProps {
  selectedProfile: ProfileOption;
  selectedMarketplace: MarketplaceOption;
  brandCompetitionOverview: any;
}

export default function CompetitionDailyLayoutComponent({
  selectedProfile,
  selectedMarketplace,
  brandCompetitionOverview
}: CompetitionDailyLayoutProps) {
  const t = useTranslations('component')
  const tk = useTranslations('CompetitionPage')
  const locale = useLocale()
  const { data: session, status } = useSession()
  const chartFetchCounter = useRef(0)
  const overviewFetchCounter = useRef(0)
  const tableFetchCounter = useRef(0)
  
  const [brandCompetitionClickShareChart, setBrandCompetitionClickShareChart] = useState<any>(null)
  const [brandCompetitionClickShareTable, setBrandCompetitionClickShareTable] = useState<any>({
    total: 0,
    items: []
  })
  const [tablePage, setTablePage] = useState(1)
  const tablePageSize = 100
  const tableListRef = useRef<HTMLDivElement | null>(null)
  const [selectedKeywordItem, setSelectedKeywordItem] = useState<any>(null)
  const [isKeywordGraphModalOpen, setIsKeywordGraphModalOpen] = useState(false)
  const [keywordSearchText, setKeywordSearchText] = useState('')
  const [targetDate,  setTargetDate] = useState<Date>(new Date())
  const isLimitedCompetition = selectedMarketplace?.subscription_features?.competition === "limited";
  const DateRangeInput = forwardRef(({ value, onClick }: { value: string, onClick: () => void }, ref) => (
    // @ts-ignore
    <button className="w-auto rounded-lg bg-white border border-gray-100 overflow-hidden cursor-pointer text-sm text-gray-600 focus:outline-none" onClick={onClick} ref={ref}>
      <div className="min-w-[200px] inline-flex py-2 px-4 bg-white hover:bg-gray-100/20">
        {value}
      </div>
    </button>
  ));
  DateRangeInput.displayName = 'DateRangeInput'

  const fetchBrandCompetitionClickShareChart = async (signal: AbortSignal) => {
    if (!(session?.user as any).access_token) {
      console.log('access token is missing in the session.')
    }
    chartFetchCounter.current += 1
    let brandCompetitionClickShareChartResponse = await api.getBrandCompetitionClickShareChart(
      selectedProfile.account_id,
      selectedMarketplace.marketplace_id,
      formatDate(new Date(), "-"),
      selectedProfile.account_type,
      (session?.user as any).access_token,
      signal
    )
    // brandCompetitionClickShareChartResponse = {
    //   "wma": {
    //     "pulsar": 0.*****************,
    //     "pulsar mouse": 0.*****************,
    //     "pulsar tenz": 0.*****************,
    //     "pulsar x2": 0.*****************,
    //     "pulsar xlite v3 es": 0.***************,
    //     "pulsar x3": 0.****************,
    //     "pulsar x2 crazylight": 0.****************
    //   },
    //   "wi": {
    //     "pulsar": 19.***************,
    //     "pulsar mouse": 21.**************,
    //     "pulsar tenz": 11.**************,
    //     "pulsar x2": 7.***************,
    //     "pulsar xlite v3 es": 24.***************,
    //     "pulsar x3": 7.***************,
    //     "pulsar x2 crazylight": 7.***************
    //   },
    //   "overall_share": 51.**************,
    //   "chart": [
    //     {
    //       "date": "2025-05-01",
    //       "avg_click_share": 0.***********000006
    //     },
    //     {
    //       "date": "2025-05-02",
    //       "avg_click_share": 0.5
    //     },
    //     {
    //       "date": "2025-05-03",
    //       "avg_click_share": 0.4664
    //     },
    //     {
    //       "date": "2025-05-04",
    //       "avg_click_share": 0.39859999999999995
    //     },
    //     {
    //       "date": "2025-05-05",
    //       "avg_click_share": 0.50996
    //     },
    //     {
    //       "date": "2025-05-06",
    //       "avg_click_share": 0.52805
    //     },
    //     {
    //       "date": "2025-05-07",
    //       "avg_click_share": 0.38612499999999994
    //     },
    //     {
    //       "date": "2025-05-08",
    //       "avg_click_share": 0.48075
    //     },
    //     {
    //       "date": "2025-05-09",
    //       "avg_click_share": 0.58396
    //     },
    //     {
    //       "date": "2025-05-10",
    //       "avg_click_share": 0.362225
    //     }
    //   ]
    // }
    chartFetchCounter.current -= 1
    brandCompetitionClickShareChartResponse && setBrandCompetitionClickShareChart(brandCompetitionClickShareChartResponse)
  }
  const fetchBrandCompetitionClickShareTable = async (signal: AbortSignal, page: number, pageSize: number, append = false) => {
    console.log('fetchBrandCompetitionClickShareTable', page, pageSize, append)
    if (!(session?.user as any).access_token) {
      console.log('access token is missing in the session.')
    }
    tableFetchCounter.current += 1
    let brandCompetitionClickShareTableResponse = await api.getBrandCompetitionClickShareTable(
      selectedProfile.account_id,
      selectedMarketplace.marketplace_id,
      formatDate(targetDate, "-"),
      keywordSearchText,
      page,
      pageSize,
      selectedProfile.account_type,
      (session?.user as any).access_token,
      signal
    )
    // brandCompetitionClickShareTableResponse = {
    //   "total": 8,
    //   "items": [
    //     {
    //       "search_term": "pulsar",
    //       "search_frequency_rank": 128160,
    //       "relative_search_frequency_rank": 0.37299,
    //       "my_click_share": 0.3692,
    //       "competitor_top3": [
    //         {
    //           "brand": "My Brand",
    //           "asin": "B0CH9Y54NS",
    //           "click_share": 0.1538
    //         },
    //         {
    //           "brand": "My Brand",
    //           "asin": "B0DLWJKVK1",
    //           "click_share": 0.1077
    //         },
    //         {
    //           "brand": "My Brand",
    //           "asin": "B0DVXD49L8",
    //           "click_share": 0.1077
    //         }
    //       ]
    //     },
    //     {
    //       "search_term": "pulsar mouse",
    //       "search_frequency_rank": 146791,
    //       "relative_search_frequency_rank": 0.427212,
    //       "my_click_share": 0.2106,
    //       "competitor_top3": [
    //         {
    //           "brand": "My Brand",
    //           "asin": "B0DZGHZQ7V",
    //           "click_share": 0.1404
    //         },
    //         {
    //           "brand": "My Brand",
    //           "asin": "B0DVXD49L8",
    //           "click_share": 0.0702
    //         }
    //       ]
    //     },
    //     {
    //       "search_term": "pulsar tenz",
    //       "search_frequency_rank": 265827,
    //       "relative_search_frequency_rank": 0.773648,
    //       "my_click_share": 0.25,
    //       "competitor_top3": [
    //         {
    //           "brand": "My Brand",
    //           "asin": "B0CH9Z62J3",
    //           "click_share": 0.25
    //         },
    //         {
    //           "brand": "Razer",
    //           "asin": "B0DSCV9HGJ",
    //           "click_share": 0.2188
    //         },
    //         {
    //           "brand": "ENDGAME GEAR",
    //           "asin": "B0CDCBDGGT",
    //           "click_share": 0.0938
    //         }
    //       ]
    //     },
    //     {
    //       "search_term": "pulsar xlite v3 es",
    //       "search_frequency_rank": 132319,
    //       "relative_search_frequency_rank": 0.385094,
    //       "my_click_share": 0.6191,
    //       "competitor_top3": [
    //         {
    //           "brand": "My Brand",
    //           "asin": "B0D2QLWLTY",
    //           "click_share": 0.4762
    //         },
    //         {
    //           "brand": "My Brand",
    //           "asin": "B0D2QJNLNC",
    //           "click_share": 0.1429
    //         },
    //         {
    //           "brand": "Razer",
    //           "asin": "B0B6XZLNHQ",
    //           "click_share": 0.0794
    //         }
    //       ]
    //     },
    //     {
    //       "search_term": "pulsar",
    //       "search_frequency_rank": 128160,
    //       "relative_search_frequency_rank": 0.37299,
    //       "my_click_share": 0.3692,
    //       "competitor_top3": [
    //         {
    //           "brand": "My Brand",
    //           "asin": "B0CH9Y54NS",
    //           "click_share": 0.1538
    //         },
    //         {
    //           "brand": "My Brand",
    //           "asin": "B0DLWJKVK1",
    //           "click_share": 0.1077
    //         },
    //         {
    //           "brand": "My Brand",
    //           "asin": "B0DVXD49L8",
    //           "click_share": 0.1077
    //         }
    //       ]
    //     },
    //     {
    //       "search_term": "pulsar mouse",
    //       "search_frequency_rank": 146791,
    //       "relative_search_frequency_rank": 0.427212,
    //       "my_click_share": 0.2106,
    //       "competitor_top3": [
    //         {
    //           "brand": "My Brand",
    //           "asin": "B0DZGHZQ7V",
    //           "click_share": 0.1404
    //         },
    //         {
    //           "brand": "My Brand",
    //           "asin": "B0DVXD49L8",
    //           "click_share": 0.0702
    //         }
    //       ]
    //     },
    //     {
    //       "search_term": "pulsar tenz",
    //       "search_frequency_rank": 265827,
    //       "relative_search_frequency_rank": 0.773648,
    //       "my_click_share": 0.25,
    //       "competitor_top3": [
    //         {
    //           "brand": "My Brand",
    //           "asin": "B0CH9Z62J3",
    //           "click_share": 0.25
    //         },
    //         {
    //           "brand": "Razer",
    //           "asin": "B0DSCV9HGJ",
    //           "click_share": 0.2188
    //         },
    //         {
    //           "brand": "ENDGAME GEAR",
    //           "asin": "B0CDCBDGGT",
    //           "click_share": 0.0938
    //         }
    //       ]
    //     },
    //     {
    //       "search_term": "pulsar xlite v3 es",
    //       "search_frequency_rank": 132319,
    //       "relative_search_frequency_rank": 0.385094,
    //       "my_click_share": 0.6191,
    //       "competitor_top3": [
    //         {
    //           "brand": "My Brand",
    //           "asin": "B0D2QLWLTY",
    //           "click_share": 0.4762
    //         },
    //         {
    //           "brand": "My Brand",
    //           "asin": "B0D2QJNLNC",
    //           "click_share": 0.1429
    //         },
    //         {
    //           "brand": "Razer",
    //           "asin": "B0B6XZLNHQ",
    //           "click_share": 0.0794
    //         }
    //       ]
    //     }
    //   ]
    // }
    tableFetchCounter.current -= 1
    if (brandCompetitionClickShareTableResponse) {
      setBrandCompetitionClickShareTable((prev: any) => {
        if (append) {
          return {
            total: (brandCompetitionClickShareTableResponse as any)?.total ?? 0,
            items: [
              ...prev.items,
              ...((brandCompetitionClickShareTableResponse as any)?.items ?? [])
            ]
          }
        } else {
          return {
            total: (brandCompetitionClickShareTableResponse as any)?.total ?? 0,
            items: (brandCompetitionClickShareTableResponse as any)?.items ?? []
          }
        }
      })
    }
  }

  const handleKeywordItemClick = (item: any) => {
    setSelectedKeywordItem(item)
    setIsKeywordGraphModalOpen(true)
  }

  const handleKeywordViewCloseClick = () => {
    setSelectedKeywordItem(null)
    setIsKeywordGraphModalOpen(false)
  }

  useEffect(() => {
    const controller = new AbortController()
    fetchBrandCompetitionClickShareChart(controller.signal)
    return () => controller.abort()
  }, [selectedProfile, selectedMarketplace])

  useEffect(() => {
    if (brandCompetitionClickShareChart?.chart && brandCompetitionClickShareChart?.chart?.length > 0) {
      setTargetDate(new Date(brandCompetitionClickShareChart.chart[brandCompetitionClickShareChart.chart.length - 1].date))
    } else {
      setTargetDate(new Date)
    }
  }, [brandCompetitionClickShareChart])

  useEffect(() => {
    setTablePage(1)
    const controller = new AbortController()
    fetchBrandCompetitionClickShareTable(controller.signal, 1, isLimitedCompetition ? 3 : tablePageSize)
    return () => controller.abort()
  }, [targetDate])

  useEffect(() => {// Infinite scroll: fetch next page when tablePage increases
    if (tablePage === 1 || isLimitedCompetition) return
    const controller = new AbortController()
    fetchBrandCompetitionClickShareTable(controller.signal, tablePage, tablePageSize, true)
    return () => controller.abort()
  }, [tablePage])

  useEffect(() => {
    const controller = new AbortController()
    if (keywordSearchText) {
      const debounceTimeout = setTimeout(() => {
        fetchBrandCompetitionClickShareTable(controller.signal, 1, tablePageSize)
      }, 500)
      return () => {
        clearTimeout(debounceTimeout)
        controller.abort()
      }
    } else {
      // If search text is cleared, fetch all results immediately
      setTablePage(1)
      fetchBrandCompetitionClickShareTable(controller.signal, 1, tablePageSize)
      return () => {
        controller.abort()
      }
    }
  }, [keywordSearchText])

  return selectedProfile && selectedMarketplace
    ? <div className="relative size-full flex flex-col bg-white">
        <div className="relative flex-shrink-0 flex items-center w-full h-[372px] px-6 gap-x-6 bg-gray-100">
          <div className="relative grow h-full pt-6 pb-3 overflow-hidden">
            {chartFetchCounter.current > 0
              ? (<div className="animate-pulse">
                <div className="relative w-full h-full rounded-md bg-white shadow-md overflow-hidden"></div>
              </div>)
              : brandCompetitionClickShareChart?.chart?.length > 0
                ? <BrandShareGraph
                    data={brandCompetitionClickShareChart?.chart
                      ? brandCompetitionClickShareChart?.chart.map((item:any) => ({
                        date: item.date,
                        value: item.avg_click_share
                      }))
                      : []
                    }
                    targetDate={targetDate}
                    setTargetDate={setTargetDate}
                  />
                : <div className="absolute inset-0 flex items-center justify-center w-full text-gray-400 text-sm font-normal">Not enough data to analyze.</div>
            }
            <div className="absolute inset-x-0 top-0 flex flex-col justify-between">
              <h1 className="flex items-center gap-x-4 px-6 pt-6 text-2xl text-gray-800 font-medium">
                <div className="flex items-center gap-x-1">
                  {tk("topBannerTitle")}
                  <Popover className="relative flex items-center justify-center">
                    {({ open }) => (
                      <>
                        <PopoverButton
                          className={cn(
                            "inline-flex items-center justify-center rounded-full text-gray-300 hover:text-gray-400 focus:outline-none"
                          )}
                        >
                          <QuestionMarkCircleIcon className="w-5 h-5"/>
                        </PopoverButton>
                        <Transition
                          as={Fragment}
                          enter="transition ease-out duration-200"
                          enterFrom="opacity-0 translate-y-1"
                          enterTo="opacity-100 translate-y-0"
                          leave="transition ease-in duration-150"
                          leaveFrom="opacity-100 translate-y-0"
                          leaveTo="opacity-0 translate-y-1"
                        >
                          <PopoverPanel className="absolute left-full top-0 z-10 ml-2 w-screen max-w-xs translate-y-0 transform px-4 sm:px-0">
                            <div className="overflow-hidden rounded-lg shadow-lg">
                              <div className="relative p-4 bg-gray-900/90 text-xs text-white font-normal text-left">
                                {tk.rich("tooltip.brandProtectionScore",{
                                  enter: () =>  <br/>
                                })}
                              </div>
                            </div>
                          </PopoverPanel>
                        </Transition>
                      </>
                    )}
                  </Popover>
                </div>
                {brandCompetitionClickShareChart?.chart && brandCompetitionClickShareChart.chart.length > 0 && (
                  <span className="flex items-center gap-x-1 text-[10px] text-gray-400 font-normal">
                    <ExclamationTriangleIcon className="h-3 w-3 text-gray-400" />
                    Last updated at {formatDate(new Date(brandCompetitionClickShareChart.chart[brandCompetitionClickShareChart.chart.length - 1].date), ".")}
                  </span>
                )}
              </h1>
            </div>
          </div>
          {overviewFetchCounter.current > 0
            ? (<div className="animate-pulse flex-shrink-0 flex flex-col w-[280px] h-full pb-8 pt-[72px] gap-y-4">
              <div className="relative w-full h-1/3 pt-[50px] pb-1 rounded-md bg-white shadow overflow-hidden"></div>
              <div className="relative w-full h-1/3 pt-[50px] pb-1 rounded-md bg-white shadow overflow-hidden"></div>
              <div className="relative w-full h-1/3 pt-[50px] pb-1 rounded-md bg-white shadow overflow-hidden"></div>
            </div>)
            : <div className="flex-shrink-0 flex flex-col w-[280px] h-full pb-8 pt-[72px] gap-y-4">
              <div className="relative w-full h-1/3 pt-[50px] pb-1 rounded-md bg-white shadow overflow-hidden">
                <TrendGraph
                  key={`rank-${JSON.stringify(brandCompetitionOverview?.daily?.map((item: any) => item.avg_rank) || [])}`}
                  data={brandCompetitionOverview?.daily.sort((a: any, b: any) => new Date(a.date).getTime() - new Date(b.date).getTime()).map((item: any) => ({
                    date: item.date,
                    value: item.avg_rank
                  })) || []}
                />
                <div className="absolute inset-x-0 top-0 flex items-center justify-between gap-x-3 p-3">
                  <div className="flex items-center gap-x-1.5 text-gray-400 text-xs font-semibold">
                    {tk("overview.weeklyAvgKeywordRank")}
                  </div>
                  <div className="flex items-center gap-x-2">
                    <div className="p-1 text-lg text-gray-600 font-bold">
                      {typeof brandCompetitionOverview?.avg_rank === "number"
                        ? brandCompetitionOverview.avg_rank.toLocaleString(undefined, { maximumFractionDigits: 0 })
                        : "0"
                      }
                    </div>
                    {typeof brandCompetitionOverview?.avg_rank_change === "number" &&
                      <div
                        className={cn(
                          "flex items-center justify-center gap-x-1",
                          brandCompetitionOverview?.avg_rank_change === 0
                            ? "text-gray-400"
                            : brandCompetitionOverview?.avg_rank_change < 0
                              ? "text-red-400"
                              : "text-blue-400"
                        )}
                      >
                        <ArrowDownCircleIcon
                          className={cn(
                            "h-4 w-4 inline-block",
                            brandCompetitionOverview?.avg_rank_change < 0
                              ? "transform rotate-180"
                              : ""
                          )}
                        />
                        <span className="text-xs">
                          {brandCompetitionOverview?.avg_rank_change === 0
                            ? '0%'
                            : brandCompetitionOverview?.avg_rank
                              ? `${Math.abs((brandCompetitionOverview.avg_rank_change / (brandCompetitionOverview.avg_rank - brandCompetitionOverview.avg_rank_change)) * 100).toFixed(0)}%`
                              : '0%'
                          }
                        </span>
                      </div>
                    }
                  </div>
                </div>
              </div>
              <div className="relative w-full h-1/3 pt-[50px] pb-1 rounded-md bg-white shadow overflow-hidden">
                <TrendGraph
                  key={`rank-${JSON.stringify(brandCompetitionOverview?.daily?.map((item: any) => item.avg_relative_rank) || [])}`}
                  data={brandCompetitionOverview?.daily.sort((a: any, b: any) => new Date(a.date).getTime() - new Date(b.date).getTime()).map((item: any) => ({
                    date: item.date,
                    value: item.avg_relative_rank
                  })) || []}
                />
                <div className="absolute inset-x-0 top-0 flex items-center justify-between gap-x-3 p-3">
                  <div className="flex items-center gap-x-1.5 text-gray-400 text-xs font-semibold">
                    {tk("overview.weeklyAvgRelKeywordRank")}
                  </div>
                  <div className="flex items-center gap-x-2">
                    <div className="p-1 text-lg text-gray-600 font-bold">
                      {((brandCompetitionOverview?.avg_relative_rank ?? 0) * 100).toFixed(0)}%
                    </div>
                    {typeof brandCompetitionOverview?.avg_relative_rank_change === "number" &&
                      <div
                        className={cn(
                          "flex items-center justify-center gap-x-1",
                          brandCompetitionOverview?.avg_relative_rank_change === 0
                            ? "text-gray-400"
                            : brandCompetitionOverview?.avg_relative_rank_change < 0
                              ? "text-red-400"
                              : "text-blue-400"
                        )}
                      >
                        <ArrowDownCircleIcon
                          className={cn(
                            "h-4 w-4 inline-block",
                            brandCompetitionOverview?.avg_relative_rank_change < 0
                              ? "transform rotate-180"
                              : ""
                          )}
                        />
                        <span className="text-xs">
                          {brandCompetitionOverview?.avg_relative_rank_change === 0
                            ? '0%'
                            : brandCompetitionOverview?.avg_relative_rank
                              ? `${Math.abs((brandCompetitionOverview.avg_relative_rank_change / (brandCompetitionOverview.avg_relative_rank - brandCompetitionOverview.avg_relative_rank_change)) * 100).toFixed(0)}%`
                              : '0%'
                          }
                        </span>
                      </div>
                    }
                  </div>
                </div>
              </div>
              <div className="relative w-full h-1/3 pt-[50px] pb-1 rounded-md bg-white shadow overflow-hidden">
                <TrendGraph
                  key={`rank-${JSON.stringify(brandCompetitionOverview?.daily?.map((item: any) => item.avg_top3_click_prop) || [])}`}
                  data={brandCompetitionOverview?.daily.sort((a: any, b: any) => new Date(a.date).getTime() - new Date(b.date).getTime()).map((item: any) => ({
                    date: item.date,
                    value: item.avg_top3_click_prop
                  })) || []}
                />
                <div className="absolute inset-x-0 top-0 flex items-center justify-between gap-x-3 p-3">
                  <div className="flex items-center gap-x-1.5 text-gray-400 text-xs font-semibold">
                    {tk("overview.weeklyBrandKeywordLeadershipRate")}
                  </div>
                  <div className="flex items-center gap-x-2">
                    <div className="p-1 text-lg text-gray-600 font-bold">
                      {((brandCompetitionOverview?.avg_top3_click_prop ?? 0))?.toFixed(0)}%
                      {/* avg_rank: 167773.05714285714
                      avg_rank_change: -11566.191664285754
                      avg_relative_rank: 0.4635550688420023
                      avg_relative_rank_change: -0.033362769370987255
                      avg_top3_click_prop: 0.8023809611797333
                      avg_top3_click_prop_change: 0.03492063298111869 */}
                    </div>
                    {typeof brandCompetitionOverview?.avg_top3_click_prop_change === "number" &&
                      <div
                        className={cn(
                          "flex items-center justify-center gap-x-1",
                          brandCompetitionOverview?.avg_top3_click_prop_change === 0
                            ? "text-gray-400"
                            : brandCompetitionOverview?.avg_top3_click_prop_change > 0
                              ? "text-red-400"
                              : "text-blue-400"
                        )}
                      >
                        <ArrowDownCircleIcon
                          className={cn(
                            "h-4 w-4 inline-block",
                            brandCompetitionOverview?.avg_top3_click_prop_change < 0
                              ? ""
                              : "transform rotate-180"
                          )}
                        />
                        <span className="text-xs">
                          {brandCompetitionOverview?.avg_top3_click_prop_change === 0
                            ? '0%'
                            : `${Math.abs(brandCompetitionOverview.avg_top3_click_prop_change * 100).toFixed(0)}%`
                          }
                        </span>
                      </div>
                    }
                  </div>
                </div>
              </div>
            </div>
          }
        </div>
        <div className="relative grow flex flex-col w-full min-h-0 py-4 sm:py-6 px-8 sm:px-12 bg-white border border-gray-100">
          {/* title */}
          <div className="flex-shrink-0 flex items-center justify-between h-11">
            <h1 className="text-2xl text-gray-800 font-medium">{tk("tableTitle")}</h1>
          </div>
          {/* filters */}
          <div className="mt-3 flex-shrink-0 flex items-center gap-x-3">
            <div className="relative flex-shrink-0 z-[2]">
              <DatePicker
                selectsRange={false}
                minDate={
                  brandCompetitionClickShareChart?.chart && brandCompetitionClickShareChart.chart.length > 0
                    ? new Date(
                        brandCompetitionClickShareChart.chart
                          .map((item: any) => new Date(item.date))
                          .reduce((min: Date, curr: Date) => (curr < min ? curr : min))
                      )
                    : new Date(new Date().setDate(new Date().getDate() - 14))
                }
                maxDate={
                  brandCompetitionClickShareChart?.chart && brandCompetitionClickShareChart.chart.length > 0
                    ? new Date(
                        brandCompetitionClickShareChart.chart
                          .map((item: any) => new Date(item.date))
                          .reduce((max: Date, curr: Date) => (curr > max ? curr : max))
                      )
                    : new Date()
                }
                selected={targetDate}
                onChange={(update) => {
                  setTargetDate(update as Date)
                }}
                dateFormat="yyyy.MM.dd"
                calendarClassName="dashboard-date-range"
                // @ts-ignore
                customInput={<DateRangeInput minWidth={'110'}/>}
                locale={locale}
              />
            </div>
            {/* search box */}
            <div className="relative flex-1 rounded-lg overflow-hidden cursor-pointer">
              <MagnifyingGlassIcon
                className={cn(
                  "h-5 w-5 absolute top-1/2 left-3 transform -translate-y-1/2",
                  keywordSearchText ? "text-gray-500" : "text-gray-300",
                  isLimitedCompetition && "opacity-50"
                )}
              />
              <input
                type="text"
                className={cn(
                  "w-full pl-10 pr-4 py-2 bg-white text-sm text-gray-500 placeholder:text-gray-300 border border-gray-100 focus:border-transparent focus:outline-blue-200 rounded-lg",
                  isLimitedCompetition && "cursor-not-allowed bg-gray-100 opacity-70"
                )}
                value={keywordSearchText}
                onChange={(e) => {
                  if (isLimitedCompetition) return;
                  setKeywordSearchText(e.target.value)
                }}
                placeholder= {tk("searchBar.placeholder")}
                disabled={isLimitedCompetition}
              />
            </div>
          </div>
          {/* Product List in a Portfolio */}
          <div
            className="grow relative w-full mt-3 rounded-lg bg-white border border-gray-100 overflow-y-auto"
            ref={tableListRef}
          >
            <div className="sticky inset-x-0 top-0 z-[1] flex items-center gap-x-3 py-3 text-left text-sm text-gray-400 font-bold bg-gray-100/40 backdrop-blur-md">
              <div className="flex-shrink-0 w-[210px] pl-8">
                {tk("table.header.queryVolumeRank")}
              </div>
              <div className="grow px-8">
                {tk("table.header.myBrandKeyword")}
              </div>
              <div className="flex-shrink-0 w-[240px] px-8">
                {tk("table.header.myBrandClickShare")}
              </div>
              <div className="flex-shrink-0 w-[360px] px-8">
                {tk("table.header.leadingProducts")}
              </div>
            </div>
            <ul className="divide-y divide-gray-100">
              {tableFetchCounter.current > 0 && brandCompetitionClickShareTable.items.length === 0 ? (
                Array.from({ length: 8 }).map((_, idx) => (
                  <li
                    key={idx}
                    className="relative animate-pulse h-[112px] p-4"
                  >
                    <div className="size-full rounded-md bg-gray-100"></div>
                  </li>
                ))
              ) : (
                (() => {
                  return brandCompetitionClickShareTable?.items.length === 0
                    ? <div className="absolute inset-0 flex items-center justify-center w-full text-gray-300 text-sm font-normal">
                        No keywords found.
                      </div>
                    : <>
                        {brandCompetitionClickShareTable?.items.map((item: any, index: number) => {
                          return item && (
                            <li
                              className="relative flex items-center gap-x-3 cursor-pointer hover:bg-gray-100/50 text-center text-gray-500 text-sm"
                              key={index}
                            >
                              <div className="flex-1 flex items-center py-8 gap-x-3 overflow-hidden" onClick={() => handleKeywordItemClick(item)}>
                                <div
                                  className={cn(
                                    'absolute inset-y-0 left-0 w-1 h-full',
                                    selectedKeywordItem && item.search_term === selectedKeywordItem.search_term ? 'bg-blue-400' : 'bg-transparent'
                                  )}
                                ></div>
                                <div className="relative flex-shrink-0 flex flex-col items-start justify-center gap-x-2 gap-y-2 w-[210px] pl-8">
                                  <div className="flex items-center gap-x-2">
                                    <div className="">
                                      <span className="text-xs text-gray-500 font-semibold">
                                        {item.search_frequency_rank.toLocaleString()}
                                      </span>
                                      <span className="text-xs text-gray-400"> / </span>
                                      <span className="text-xs text-gray-400">
                                        {((1 / item.relative_search_frequency_rank) * item.search_frequency_rank).toLocaleString(undefined, { maximumFractionDigits: 0 })}
                                      </span>
                                    </div>
                                    {item.relative_search_frequency_rank && item.relative_search_frequency_rank <= 0.4 && (
                                      <div className="flex-shrink-0 py-0.25 px-1.5 border border-orange-400 text-orange-500 text-[10px] rounded-md font-semibold">
                                        Top {(item.relative_search_frequency_rank * 100).toFixed(0)}%
                                      </div>
                                    )}
                                  </div>
                                </div>
                                <div className="grow relative flex flex-col gap-y-0.5 px-8 overflow-hidden">
                                  <div className="text-base text-gray-500 text-left font-semibold truncate">
                                    {item.search_term}
                                  </div>
                                </div>
                                <div className="flex-shrink-0 relative w-[240px] flex flex-col items-center justify-start gap-y-0.5 px-8 overflow-hidden">
                                  <div className="w-full flex items-center justify-between text-xs text-gray-500 font-semibold">
                                    <div className="">My Brand</div>
                                    <div className="">
                                      {item.my_click_share
                                        ? (item.my_click_share * 100).toFixed(2)
                                        : 0
                                      }%
                                    </div>
                                  </div>
                                  <div className="relative w-full h-1 bg-gray-200 rounded-sm overflow-hidden">
                                    <div
                                      className="h-full bg-blue-400"
                                      style={{
                                        width: `${item.my_click_share
                                          ? (item.my_click_share * 100).toFixed(2)
                                          : 0}%`
                                      }}
                                    ></div>
                                  </div>
                                  <div className="w-full flex items-center justify-between text-xs">
                                    <div className="text-gray-400">0</div>
                                    <div className="text-gray-400">100%</div>
                                  </div>
                                </div>
                                <div className="flex-shrink-0 w-[360px] px-8">
                                  {item.competitor_top3 && item.competitor_top3.length > 0 && (
                                    <div className="flex flex-col gap-y-2">
                                      {item.competitor_top3.map((competitor: any, index: number) => (
                                        <div key={index} className="flex items-center gap-x-2">
                                          <div className="text-[10px] text-blue-400 font-semibold">rank {index + 1}</div>
                                          <div className="text-xs text-gray-500">
                                            {competitor.brand}
                                          </div>
                                          <div className="border-l border-gray-200 pl-2 text-xs text-gray-400">
                                            {competitor.asin}
                                          </div>
                                        </div>
                                      ))}
                                    </div>
                                  )}
                                </div>
                              </div>
                            </li>
                          )
                        })}
                        {tableFetchCounter.current > 0 && brandCompetitionClickShareTable.items.length > 0 && (
                          <li className="relative animate-pulse h-[64px] p-4">
                            <div className="size-full rounded-md bg-gray-100"></div>
                          </li>
                        )}
                        {brandCompetitionClickShareTable.items.length < brandCompetitionClickShareTable.total && tableFetchCounter.current === 0 && !isLimitedCompetition && (
                          <li className="relative h-[64px] flex items-center justify-center">
                            <button
                              className="px-4 py-2 rounded-lg bg-white text-gray-400 hover:text-gray-500 text-xs border border-gray-300 hover:border-gray-400 transition cursor-pointer"
                              onClick={() => setTablePage((prev) => prev + 1)}
                              disabled={tableFetchCounter.current > 0}
                            >
                              {tableFetchCounter.current > 0 ? "Loading..." : "Show More"}
                            </button>
                          </li>
                        )}
                      </>
                })()
              )}
            </ul>
          </div>
          {/* modal for trend graph for a keyword */}
          { isKeywordGraphModalOpen && selectedKeywordItem && selectedProfile && selectedMarketplace && brandCompetitionClickShareChart?.chart &&
          <CompetitionKeywordViewSlider
            isOpen={isKeywordGraphModalOpen}
            onClose={handleKeywordViewCloseClick}
            selectedProfile={selectedProfile}
            selectedMarketplace={selectedMarketplace}
            selectedKeywordItem={selectedKeywordItem}
            selectedDate={targetDate}
            brandCompetitionClickShareChart={brandCompetitionClickShareChart.chart}
          />
          }
        </div>
    </div>
    : ""
}
