import { ErrorResponse } from "@/app/api/_models";
import { getServerApiHostUrl } from "@/utils/host";
import { NextRequest, NextResponse } from "next/server";

export type OptimizationBudgetSensitivityDetailResponse = any;

export async function GET(
  request: NextRequest
): Promise<NextResponse<OptimizationBudgetSensitivityDetailResponse | ErrorResponse>> {
  const accessToken = request.headers.get("Authorization");
  if (!accessToken) {
    return NextResponse.json(
      { message: "Authorization header is missing" },
      { status: 400 }
    );
  }
  const optimizationId = request.nextUrl.searchParams.get("optimization_id")
  if (!optimizationId) {
    return NextResponse.json(
      { message: "optimization_id query is missing" },
      { status: 400 }
    );
  }
  const optimizationBudgetSensitivityDetailResponse = await fetch(
    `${await getServerApiHostUrl()}/api/optimization/budget-sensitivity-detail/${optimizationId}`,
    {
      method: "GET",
      headers: {
        "Authorization": `Bearer ${accessToken}`,
        "Content-Type": "application/json",
      },
      cache: "no-cache",
    }
  ).then((res) => res.json());

  return NextResponse.json(optimizationBudgetSensitivityDetailResponse, { status: 200 });
}
