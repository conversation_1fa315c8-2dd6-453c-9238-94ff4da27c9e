"use client"

import { api } from "@/utils/api"
import { useSession } from "next-auth/react"
import { Fragment, useEffect, useRef, useState } from 'react'
import { Transition } from '@headlessui/react'
import "react-datepicker/dist/react-datepicker.css"
import { cn, formatDate, formatDateTime, formatLocalDateTime } from "@/utils/msc"
import { ProfileOption } from "@/components/dashboard/profile-select"
import { MarketplaceOption } from "@/components/dashboard/marketplace-select"
import { CheckBadgeIcon, ClockIcon, ExclamationCircleIcon, PlusIcon } from "@heroicons/react/20/solid"
import { useTranslations } from "next-intl"
import DownloadAddSlider from "./download-add-slider"
import DownloadStatus from "./download-status"
import RefreshButton from "@/components/ui/refresh-button"
import Link from "next/link"

interface DownloadsLayoutProps {
  mopUserData: any;
  selectedProfile: ProfileOption;
  selectedMarketplace: MarketplaceOption;
}

export default function InCollectDownloadsLayoutComponent({
  mopUserData,
  selectedProfile,
  selectedMarketplace
}: DownloadsLayoutProps) {  
  const td = useTranslations('downloads')
  const { data: session } = useSession()
  const fetchCounter = useRef(0)
  const [isAddSliderOpen, setIsAddSliderOpen] = useState(false)
  const [downloadListItems, setDownloadListItems] = useState<any[]>([])
  const [downloadingItems, setDownloadingItems] = useState<Set<string>>(new Set())  

  // 일일 최대 요청 횟수
  const MAX_DAILY_REQUESTS = 3;
  
  // 오늘 날짜의 요청 수 계산
  const getTodayRequestCount = () => {
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    
    return downloadListItems.filter(item => {
      const requestTime = new Date(item.report_request_time);
      requestTime.setHours(0, 0, 0, 0);
      return requestTime.getTime() === today.getTime();
    }).length;
  };
  
  // 잔여 요청 횟수 계산
  const getRemainingRequests = () => {
    const todayCount = getTodayRequestCount();
    return Math.max(0, MAX_DAILY_REQUESTS - todayCount);
  };
  
  // 요청 가능 여부 확인
  const canRequestMore = () => {
    return getRemainingRequests() > 0;
  };

  const isExpired = (item: any) => {
    if (!item.presigned_url_expired_at) return false;
    
    const expiredAt = new Date(item.presigned_url_expired_at);
    const now = new Date();
    
    return now > expiredAt;
  };

  const fetchDownloadList = async (signal: AbortSignal) => {
    if (!(session?.user as any).access_token) {
      console.log('access token is missing in the session.')
    }
    console.log('AbortSignal :',AbortSignal);    
  }

  const handleDownload = async (reportId: string, report_type: string, report_by: string) => {
    if (!session?.user || !(session.user as any).access_token) {
      console.error('No access token available');
      return;
    }

    setDownloadingItems(prev => new Set(prev).add(reportId));


    const key = report_by !== "raw" && report_type === "GET_SALES_AND_TRAFFIC_REPORT" && report_by === "ASIN" ? "by_asin" : "by_date"
    

    try {      
      const response = await api.getReportPresignedUrl(
        reportId,
        report_by !== "raw" ? key : "raw",
        (session?.user as any).access_token
      );

      if (response) {        
        const link = document.createElement('a');
        link.href = response;        
        link.target = '_blank'; 
        
        document.body.appendChild(link);       
        link.click();
        document.body.removeChild(link);
      } else {
        console.error('No download URL received from server');
        alert('다운로드 URL을 받을 수 없습니다.');
      }
    } catch (error) {
      console.error('Download failed:', error);
      alert('다운로드에 실패했습니다. 다시 시도해주세요.');
    } finally {
      // Remove from downloading set
      setDownloadingItems(prev => {
        const newSet = new Set(prev);
        newSet.delete(reportId);
        return newSet;
      });
    }
  };

  const handleAddDownloadClick = () => {
    setIsAddSliderOpen(true)
  };

  const handleAddCloseClick = (refresh: boolean) => {
    const abortController = new AbortController()
    if (refresh) {
      fetchDownloadList(abortController.signal)
    }
    setIsAddSliderOpen(false)
  }

  useEffect(() => {
    const abortController = new AbortController()
    fetchDownloadList(abortController.signal,)
    return () => {
      abortController.abort()
    }
  }, [])

  const [collectionStatus, setCollectionStatus] = useState({
    overall_progress: 0
  })

  const fetchCollectionStatus = async () => {
    const collectionStatusResponse = await api.getCollectionStatus(
      selectedProfile.account_id,
      selectedMarketplace.marketplace_id,
      (session?.user as any).access_token
    )
    collectionStatusResponse && setCollectionStatus(collectionStatusResponse)
  }
  useEffect(() => {
    selectedProfile && selectedMarketplace && fetchCollectionStatus()
  }, [selectedProfile, selectedMarketplace])

  return (
    <div className="relative flex items-center justify-center w-full h-full divide-x divide-gray-200">
      <div className="flex-shrink-0 flex flex-col w-full h-full bg-white py-4 sm:py-6 px-8 sm:px-12">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-x-3">
            <h1 className="text-2xl text-gray-800 font-medium">{td("title")}</h1>
            <RefreshButton 
              onClick={() => {
                const abortController = new AbortController();
                fetchDownloadList(abortController.signal);
              }}
              className="bg-gray-100/40 hover:bg-blue-100/80 text-gray-500 hover:text-blue-500 border border-gray-200 hover:border-blue-200"
              isLoading={fetchCounter.current > 0}
              size="sm"
              aria-label="Refresh report downloads"
            />
          </div>
          <div className="flex items-center gap-x-4">
            <div className={`text-sm ${canRequestMore() ? 'text-gray-600' : 'text-red-500 font-medium'}`}>
              {td("topButton.remainingRequests")}: {getRemainingRequests()}
            </div>
            <button
              className={`flex items-center gap-x-2 p-3 cursor-pointer rounded-md focus:outline-none text-sm overflow-hidden ${
                canRequestMore() 
                  ? 'bg-blue-100 hover:bg-blue-200 text-blue-500' 
                  : 'bg-gray-100 text-gray-400 cursor-not-allowed'
              } font-semibold`}
              onClick={() => canRequestMore() && handleAddDownloadClick()}
              disabled={!canRequestMore()}>
              <PlusIcon
                className="flex-shrink-0 h-5 w-5"
                aria-hidden="true"
              />
              <div>{td("topButton.addNew")}</div>
            </button>
          </div>
        </div>
        {/* download list */}
        <div
          className={cn(
            'grow relative w-full mt-3 rounded-lg bg-white border border-gray-100 overflow-y-scroll',
          )}
        >
          {/* List */}
          <div className="sticky inset-x-0 top-0 z-[1] flex items-center gap-x-3 py-3 text-sm text-gray-400 font-bold bg-gray-100/40 backdrop-blur-md">
            <div className="w-[120px] px-8">
              {td("downloadList.header.status")}
            </div>
            <div className="w-[500px] px-8">
              {td("downloadList.header.reportType")}
            </div>
            <div className="grow px-8">
              {td("downloadList.header.reportTitle")}              
            </div>
            <div className="flex-shrink-0 w-[220px] px-8">
              {td("downloadList.header.requestTime")}
            </div>            
            <div className="flex-shrink-0 w-[200px] px-8">
              {td("downloadList.header.download")}
            </div>
          </div>
          {fetchCounter.current > 0
            ? <ul className="animate-pulse p-6 space-y-3">
                {Array.from({ length: 12 }, (_, index) => (
                  <li key={index} className="w-full flex items-center gap-x-3">
                    <div className="flex-shrink-0 w-[120px] h-20 rounded-md bg-gray-100"></div>
                    <div className="flex-shrink-0 w-[500px] h-20 rounded-md bg-gray-100"></div>
                    <div className="flex-1 h-20 rounded-md bg-gray-100"></div>
                    <div className="flex-shrink-0 w-[220px] h-20 rounded-md bg-gray-100"></div>                    
                    <div className="flex-shrink-0 w-[140px] h-20 rounded-md bg-gray-100"></div>
                  </li>
                ))}
              </ul>
            : <ul className="divide-y divide-gray-100">
                {(() => {
                  return downloadListItems.length === 0
                  ? <div className="absolute inset-0 flex items-center justify-center w-full text-gray-300 text-sm font-normal">
                      {td("downloadList.content.downloadRow.downloadNull")}
                    </div>
                  : downloadListItems.sort((a, b) => b.id - a.id).map((item, index) => (
                    <li
                      className="relative flex items-center py-8 gap-x-3 overflow-hidden cursor-pointer hover:bg-gray-100/40 text-gray-500 text-sm"
                      key={index}
                    >
                      <div className="relative flex-shrink-0 flex flex-col items-start justify-center gap-x-2 gap-y-2 w-[120px] pl-8">
                        <DownloadStatus downloadItem={item} />
                      </div>
                      <div className="relative flex-shrink-0 w-[500px] pl-8 flex flex-col items-start">
                        <div className="flex items-center gap-x-2">
                          <div className="py-0.25 px-1.5 border border-gray-400 bg-white text-gray-500 text-[10px] rounded-md">{item.api_type}</div>
                          <div className="text-base text-gray-500 text-left font-semibold truncate">{item.report_name}</div>
                        </div>
                      </div>
                      <div className="grow px-8 overflow-hidden">
                        <div className="text-gray-500 truncate">{item.report_title}</div>
                      </div>                      
                      <div className="flex-shrink-0 w-[220px] px-8">
                        {formatLocalDateTime(item.report_request_time, ".")}
                      </div>                   
                      <div className="flex-shrink-0 w-[200px] px-8">
                        {item.status === "DONE" && !isExpired(item) && item.report_type !=="GET_SALES_AND_TRAFFIC_REPORT" &&
                        <button 
                          className={`text-blue-500 hover:text-blue-700 text-xs underline ${
                            downloadingItems.has(item.id) ? 'opacity-50 cursor-not-allowed' : ''
                          }`}
                          onClick={() => handleDownload(item.id, item.report_type, "raw")}
                          disabled={downloadingItems.has(item.id)}
                        >
                          {downloadingItems.has(item.id) ? 'Downloading...' : 'Download'}
                        </button>
                        }
                        {item.status === "DONE" && !isExpired(item) && item.report_type ==="GET_SALES_AND_TRAFFIC_REPORT" &&
                        <>
                          <button 
                            className={`text-blue-500 hover:text-blue-700 text-xs mt-1 underline ${
                              downloadingItems.has(item.id) ? 'opacity-50 cursor-not-allowed' : ''
                            }`}
                            onClick={() => handleDownload(item.id, item.report_type, "ASIN")}
                            disabled={downloadingItems.has(item.id)}
                          >
                            {downloadingItems.has(item.id) ? 'Downloading...' : 'Download [by ASIN]'}
                          </button>

                          <button 
                          className={`text-blue-500 hover:text-blue-700 text-xs mt-1 underline ${
                            downloadingItems.has(item.id) ? 'opacity-50 cursor-not-allowed' : ''
                          }`}
                          onClick={() => handleDownload(item.id, item.report_type, "DATE")}
                          disabled={downloadingItems.has(item.id)}
                          >
                          {downloadingItems.has(item.id) ? 'Downloading...' : 'Download [by Date]'}
                          </button>
                        </>                          
                        }                        
                      </div>
                    </li>
                  ))
                })()}
              </ul>
          }
        </div>
      </div>
      <div className="absolute inset-0 size-full px-4 flex items-center justify-center bg-gray-100/60 backdrop-blur-sm z-[5]">
        <div className="w-full max-w-lg flex flex-col items-center">
          <div className="text-center text-gray-800 font-semibold text-lg">
            We are collecting the essential data to initialize Optapex.
            <br />
            Please wait 1 to 2 days after subscribing to the service.
            <div className="mt-8 mb-4 flex items-center justify-center gap-x-4">
              <div className="relative w-full h-3 bg-gray-300 rounded-full overflow-hidden">
                <div
                  className="absolute top-0 left-0 h-full bg-gradient-to-r from-purple-500 to-blue-500 rounded-full animate-pulse"
                  style={{
                    width: `${collectionStatus?.overall_progress * 100}%`
                  }}></div>
              </div>
              <div className="text-sm text-gray-600 text-right">
                {(collectionStatus?.overall_progress * 100).toFixed(2)}%
              </div>
            </div>
            <ul className="w-full text-left text-gray-700 divide-y divide-gray-200">
              {Object.entries(collectionStatus)
                .filter(([key]) => key !== "overall_status" && key !== "overall_progress")
                .map(([key, value]: [string, any]) => (
                <li key={key} className="py-4 w-full flex items-center justify-between">
                  <div className="">
                    <div className="text-sm font-semibold capitalize">{key.replace(/_/g, ' ')}</div>
                    <div className="mt-1 text-xs text-gray-500 font-normal">
                      {value.message}
                    </div>
                  </div>
                  {value.status === "COMPLETED"
                    ? <div className="flex items-center gap-x-1 text-xs text-gray-600">
                        <CheckBadgeIcon className="h-4 w-4 text-green-500" />
                        COMPLETED
                      </div>
                    : value.status === "IN_PROGRESS"
                      ? <div className="flex items-center gap-x-1 text-xs text-gray-600">
                          <ClockIcon className="h-4 w-4 text-blue-500" />
                          IN PROGRESS
                        </div>
                      : value.status === "PENDING"
                        ? <div className="flex items-center gap-x-1 text-xs text-gray-600">
                            <ExclamationCircleIcon className="h-4 w-4 text-gray-400" />
                            PENDING
                          </div>
                        : <div className="flex items-center gap-x-1 text-xs text-gray-600">
                            <ExclamationCircleIcon className="h-4 w-4 text-gray-400" />
                            {value.status}
                          </div>
                  }
                </li>
              ))}
            </ul>
          </div>
        </div>
      </div>
    </div>
  )
} 