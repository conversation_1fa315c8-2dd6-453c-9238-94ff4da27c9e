"use client";

import Image from "next/image"
import { useSession } from "next-auth/react"
import { redirect } from "next/navigation"
import { api } from "@/utils/api"
import { getEnvValue } from "@/utils/host";

export default function DormantPageLayout() {
  const { data: session, status } = useSession()

  const handleSubmit = async (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault();
    
    try {
      if (!(session?.user as any).access_token) {
        console.log('access token is missing in the session.')
      }
      const nextAuthUrl = await getEnvValue('NEXTAUTH_URL')
      let response = await api.reactivateAccount(
        (session?.user as any).access_token,
        `${nextAuthUrl}/dashboard?tab=reports`,
        `${nextAuthUrl}/auth/reactivate-account-failed`
      )
      if (response.status !== "success") {
        throw new Error("Network response was not ok");
      } else {
        // Redirect to the dashboard
        redirect("/dashboard?tab=reports");
      }
    } catch (error) {
      // Handle error response
      console.error("Failed to change password:", error);
    }
  };

  return (
    <div className="relative">
      <div className="w-full text-[#232F3F]">
        <div
          className="relative w-full h-screen bg-center bg-cover"
          style={{ backgroundImage: "url(/ui/auth-section-bg.png)" }}
        >
          <div className="absolute inset-0 w-full h-full flex items-center justify-center">
            <div className="max-w-full sm:max-w-[402px] p-6 mx-auto sm:border border-gray-200 bg-white rounded-lg">
              <Image
                src="/logo/optapex-logo-gray.svg"
                alt="Optapex Logo"
                className="w-auto h-4"
                width={197}
                height={60}
              />
              
              <div className="mt-8">
                <div className="text-gray-700">
                  <div className="text-2xl font-bold">Dormant account</div>
                  <div className="mt-4 text-sm font-normal">
                    Your account has become dormant due to 90 days of inactivity. Please check <span className="font-bold">{(session?.user as any).email || "your mail box"}</span> to verify and reactivate your account.
                  </div>
                </div>
                <form className="mt-8" onSubmit={handleSubmit}>
                  <div className="mt-6 flex items-center gap-x-4">
                    <button
                      type="submit"
                      className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                    >
                      Send Email
                    </button>
                  </div>
                </form>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
