/// <reference types="cypress" />

import BasePage from './BasePage';

class LoginLoginPage extends BasePage {
    INPUT_USERNAME = 'input[name="username"]';
    INPUT_PASSWORD = 'input[name="password"]';
    BUTTON_SUBMIT = 'button[type="submit"]';
    ERROR_MESSAGE = 'span[class*="awsui_error__message_"]';
    FORGOT_PASSWORD = 'a[href*="forgotPassword"]';
    CREATE_ACCOUNT = 'a[href*="signup"]';

    assertUsername(){
        cy.get(this.INPUT_USERNAME).should('be.exist');
    }

    assertPassword(){
        cy.get(this.INPUT_PASSWORD).should('be.exist');
    }

    assertSubmitButton(){
        cy.get(this.BUTTON_SUBMIT).should('be.exist');
    }

    assertErrorMessage(){
        cy.get(this.ERROR_MESSAGE).should('be.exist');
    }

    clickSubmitButton(){
        cy.get(this.BUTTON_SUBMIT).click({force:true});
    }

    clickForgotPassword(){
        cy.get(this.FORGOT_PASSWORD).click();
    }

    clickCreateAccount(){
        cy.get(this.CREATE_ACCOUNT).click();
    }

    inputUsername(username){
        cy.get(this.INPUT_USERNAME).type(username, {force:true});
    }

    inputPassword(password){
        cy.get(this.INPUT_PASSWORD).type(password);
    }

}

export default LoginLoginPage;