import Script from "next/script";
import React, { useEffect } from "react";

declare global {
  namespace JSX {
    interface IntrinsicElements {
      'stripe-pricing-table': any;
    }
  }
}
interface StripePricingTableProps extends React.HTMLAttributes<HTMLDivElement> {
  pricing_table_id: string;
  client_secret: string;
  stripe_publishable_key: string;
}
export default function StripePricingTable({
  pricing_table_id,
  client_secret,
  stripe_publishable_key
}: StripePricingTableProps) {
  return (
    <>
      <Script async strategy="lazyOnload" src="https://js.stripe.com/v3/pricing-table.js" />
      <stripe-pricing-table
        pricing-table-id={pricing_table_id}
        publishable-key={stripe_publishable_key}
        customer-session-client-secret={client_secret}
      />
    </>
  )
};
