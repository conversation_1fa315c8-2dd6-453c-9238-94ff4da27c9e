This is a [Next.js](https://nextjs.org/) project bootstrapped with [`create-next-app`](https://github.com/vercel/next.js/tree/canary/packages/create-next-app).
This project is for MOP global web page which now serves Amazon sellers and vendors to optimize their budget choices for advertisements and improve ROI of their organizations.

## Getting Started

First, run the development server:

```bash
npm run dev
# or
yarn dev
# or
pnpm dev
# or
bun dev
```

Open [http://localhost:3000](http://localhost:3000) with your browser to see the result.

You can start editing the page by modifying `app/page.tsx`. The page auto-updates as you edit the file.

This project uses [`next/font`](https://nextjs.org/docs/basic-features/font-optimization) to automatically optimize and load Inter, a custom Google Font.

## Learn More

To learn more about Next.js, take a look at the following resources:

- [Next.js Documentation](https://nextjs.org/docs) - learn about Next.js features and API.
- [Learn Next.js](https://nextjs.org/learn) - an interactive Next.js tutorial.

You can check out [the Next.js GitHub repository](https://github.com/vercel/next.js/) - your feedback and contributions are welcome!

## Deploy on Vercel (We're not using this)

The easiest way to deploy your Next.js app is to use the [Vercel Platform](https://vercel.com/new?utm_medium=default-template&filter=next.js&utm_source=create-next-app&utm_campaign=create-next-app-readme) from the creators of Next.js.

Check out our [Next.js deployment documentation](https://nextjs.org/docs/deployment) for more details.

## Deploy on AWS Amplify

This github repo is connected to AWS Amplify at California region.

## Smoke Tests

After starting the development server, you can run the minimal smoke tests with the commands below.

```bash
yarn dev &
# Once the server is up, run this in a separate terminal
yarn test:smoke
```

- Includes: root access redirection, `robots.txt` access, and verification of 400/200 responses for `POST /api/test/mock-session`
- Default base URL: `http://127.0.0.1:3000` (configured in package scripts)

## Tests

### Unit tests (Jest + RTL)
- Runner: Jest (`jsdom` environment), Transform: `babel-jest`
- Unit tests for utils/components live under `__tests__/`
- Run:
```bash
yarn test          # Run all unit tests once
yarn test:watch    # Watch mode
yarn test:ci       # CI mode (includes coverage)
```
- Examples: `__tests__/components/button.test.tsx`, `__tests__/utils/msc.test.ts`

### E2E(Module) tests (Cypress)
- Mode: Uses the module test configuration (`cypress-module.config.ts`)
- Run (requires the dev server):
```bash
yarn dev &
# After the server is up, run in a separate terminal
yarn test:module           # Run module tests
yarn test:module:default   # Same runner, default spec
```
- GUI mode:
```bash
yarn cypress:open             # Local GUI
yarn cypress:open:delayMode   # Delay mode GUI
```

### Notes
- Jest ignores `/.next/`, `/cypress/`, and `/tests/` paths to avoid conflicts with E2E.
- Framework-specific modules like `next/link` are mocked in tests as needed.
- Browser-dependent animations/portals are tested reliably by controlling props such as `open`.
