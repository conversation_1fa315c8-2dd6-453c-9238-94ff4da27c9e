"use client"

import { cn } from "@/utils/msc"
import { ProductListItem } from './ad-portfolio-layout-component'
import { useTranslations } from "next-intl"
import { Fragment, useState } from "react"
import { Dialog, DialogPanel, DialogTitle, Transition, TransitionChild } from '@headlessui/react'
import { ClipboardDocumentCheckIcon, ClipboardDocumentIcon, ExclamationTriangleIcon } from "@heroicons/react/20/solid"


interface ProductStatusProps extends React.HTMLAttributes<HTMLDivElement> {
    productItem: ProductListItem;
}
export default function ProductStatus({
  productItem
}: ProductStatusProps) {
  const t = useTranslations('component')
  const [showWarningModal, setShowWarningModal] = useState(false)
  const [showCopied, setShowCopied] = useState(false)
  return (
    <div>
      <div className="flex items-center justify-start gap-x-2 text-gray-500 text-sm">
        <span className="relative flex h-2 w-2">
          <span className={cn(
            "absolute inline-flex h-full w-full rounded-full opacity-75",
            productItem.request_status === "PAUSED"
              ? "bg-red-500"
              : productItem.request_status === "ERROR" || productItem.request_status === "ERROR_WITH_INTEGRATING" || productItem.request_status === "ERROR_WITH_RESTORING"
                ? "bg-red-500"
                : productItem.request_status === "WARNING" || productItem.request_status === "WARNING_WITH_INTEGRATING" || productItem.request_status === "WARNING_WITH_RESTORING"
                  ? "bg-yellow-500"
                  :productItem.eligibility_status === "INELIGIBLE"
                    ? "bg-red-500"
                    : productItem.request_status === "REQUESTED" || productItem.request_status === "PROCESSING" || productItem.request_status === "PROCESSING_WITH_RESTORING" || productItem.request_status === "PROCESSING_WITH_INTEGRATING"
                      ? "bg-blue-500"
                      : productItem.target_status === "AMAZON_AUTO"
                        ? "animate-ping bg-orange-400"
                        : productItem.target_status === "OPTIMIZER_BID"
                          ? "animate-ping bg-blue-500"
                          : "bg-gray-300"
          )}></span>
          <span className={cn(
            "relative inline-flex rounded-full h-2 w-2",
            productItem.request_status === "PAUSED"
              ? "bg-red-500"
              : productItem.request_status === "ERROR" || productItem.request_status === "ERROR_WITH_INTEGRATING" || productItem.request_status === "ERROR_WITH_RESTORING"
                ? "bg-red-500"
                : productItem.request_status === "WARNING" || productItem.request_status === "WARNING_WITH_INTEGRATING" || productItem.request_status === "WARNING_WITH_RESTORING"
                  ? "bg-yellow-500"
                  : productItem.eligibility_status === "INELIGIBLE"
                    ? "bg-red-500"
                    : productItem.request_status === "REQUESTED" || productItem.request_status === "PROCESSING" || productItem.request_status === "PROCESSING_WITH_RESTORING" || productItem.request_status === "PROCESSING_WITH_INTEGRATING"
                      ? "bg-blue-500"
                      : productItem.target_status === "AMAZON_AUTO"
                        ? "bg-orange-400"
                        : productItem.target_status === "OPTIMIZER_BID"
                          ? "bg-blue-500"
                          : "bg-gray-300"
          )}></span>
        </span>
        {productItem.request_status === "PAUSED"
          ? t("productStatus.paused")
          : productItem.request_status === "ERROR" || productItem.request_status === "ERROR_WITH_INTEGRATING" || productItem.request_status === "ERROR_WITH_RESTORING"
            ? t("productStatus.error")
            : productItem.request_status === "WARNING" || productItem.request_status === "WARNING_WITH_INTEGRATING" || productItem.request_status === "WARNING_WITH_RESTORING"
              ? t("productStatus.warning")
              : productItem.eligibility_status === "INELIGIBLE"
                ? t("productStatus.ineligible")
                : productItem.request_status === "REQUESTED"
                  ? t("productStatus.initializing")
                  : productItem.request_status === "PROCESSING" || productItem.request_status === "PROCESSING_WITH_RESTORING" || productItem.request_status === "PROCESSING_WITH_INTEGRATING"
                    ? t("productStatus.processing")
                    : productItem.target_status === "AMAZON_AUTO"
                      ? t("productStatus.amazonAuto")
                      : productItem.target_status === "OPTIMIZER_BID"
                        ? t("productStatus.mopAuto")
                        : productItem.request_status
        }
      </div>
      {productItem.request_status === "ERROR" || productItem.request_status === "ERROR_WITH_INTEGRATING" || productItem.request_status === "ERROR_WITH_RESTORING"
        ? <button
            className={cn(
              "flex items-center gap-x-0.5 focus:outline-none text-xs overflow-hidden text-gray-500 hover:text-gray-700 underline",
            )}
            onClick={(e) => {
              e.stopPropagation()
              setShowWarningModal(true);
            }}
          >
            <ExclamationTriangleIcon className="h-3 w-3 text-red-400" />
            error message
          </button>
        : productItem.request_status === "WARNING" || productItem.request_status === "WARNING_WITH_INTEGRATING" || productItem.request_status === "WARNING_WITH_RESTORING"
          ? <button
              className={cn(
                "flex items-center gap-x-0.5 focus:outline-none text-xs overflow-hidden text-gray-500 hover:text-gray-700 underline",
              )}
              onClick={(e) => {
                e.stopPropagation()
                setShowWarningModal(true);
              }}
            >
              <ExclamationTriangleIcon className="h-3 w-3 text-orange-400" />
              warning message
            </button>
          : ""
      }
      {showWarningModal && (
        <Transition appear show={showWarningModal} as={Fragment}>
          <Dialog as="div" className="relative z-10" onClose={() => setShowWarningModal(false)}>
            <TransitionChild
            as={Fragment}
            enter="ease-out duration-300"
            enterFrom="opacity-0"
            enterTo="opacity-100"
            leave="ease-in duration-200"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
            >
              <div className="fixed inset-0 bg-black bg-opacity-25" />
            </TransitionChild>

            <div className="fixed inset-0 overflow-y-auto">
              <div className="flex min-h-full items-center justify-center p-4 text-center">
                <TransitionChild
                as={Fragment}
                enter="ease-out duration-300"
                enterFrom="opacity-0 scale-95"
                enterTo="opacity-100 scale-100"
                leave="ease-in duration-200"
                leaveFrom="opacity-100 scale-100"
                leaveTo="opacity-0 scale-95"
                >
                <DialogPanel className="w-full max-w-md transform overflow-hidden rounded-2xl bg-white p-6 text-left align-middle shadow-xl transition-all">
                  <DialogTitle as="h3" className="text-base/7 font-medium text-gray-600">
                    {productItem.request_status === "ERROR" || productItem.request_status === "ERROR_WITH_INTEGRATING" || productItem.request_status === "ERROR_WITH_RESTORING"
                      ? "Error Message"
                      : productItem.request_status === "WARNING" || productItem.request_status === "WARNING_WITH_INTEGRATING" || productItem.request_status === "WARNING_WITH_RESTORING"
                        ? "Warning Message"
                        : ""
                    }
                  </DialogTitle>
                  <div className="mt-2">
                    {productItem.request_status === "ERROR" || productItem.request_status === "ERROR_WITH_INTEGRATING" || productItem.request_status === "ERROR_WITH_RESTORING"
                      ? <p className="text-sm text-gray-500">
                          Please copy the error message below and send it to the Optapex CS ticket.
                          <div className="mt-4 relative flex items-center justify-between p-4 bg-gray-800 text-gray-100 rounded-md">
                            {productItem?.request_id || ""}
                            <button
                              className="inline-flex items-center gap-2 rounded-md bg-gray-100/20 hover:bg-gray-100/40 p-1 focus:outline-none"
                              onClick={() => {
                                navigator.clipboard.writeText(productItem?.request_id || "");
                                setShowCopied(true);
                                setTimeout(() => setShowCopied(false), 2000);
                              }}
                            >
                              {showCopied
                                ? <ClipboardDocumentCheckIcon className="w-4 h-4 text-gray-100"/>
                                : <ClipboardDocumentIcon className="w-4 h-4 text-gray-100"/>
                              }
                            </button>
                          </div>
                        </p>
                      : productItem.request_status === "WARNING" || productItem.request_status === "WARNING_WITH_INTEGRATING" || productItem.request_status === "WARNING_WITH_RESTORING"
                        ? <p className="text-sm text-gray-500">
                            This ASIN is not eligible for Sponsored Ads. Please check <span className="text-orange-500 font-semibold">the sales availability and ad eligibility</span> of these ASINs.
                            <br/>
                            Once the issue with the ASIN is resolved, the ad campaign will be set up and go live automatically.
                          </p>
                        : ""
                    }
                  </div>

                  <div className="mt-4 flex justify-end gap-x-4">
                    <button
                      className="inline-flex items-center gap-2 rounded-md bg-white py-1.5 px-3 text-sm/6 font-semibold text-gray-500 shadow-inner shadow-white/10 focus:outline-none data-[hover]:bg-gray-100 data-[open]:bg-gray-700 data-[focus]:outline-1 data-[focus]:outline-white"
                      onClick={() => setShowWarningModal(false)}
                    >
                      Cancel
                    </button>
                    {/* <button
                      className="inline-flex items-center justify-center gap-x-2 rounded-md border border-transparent bg-blue-600 px-4 py-2 text-sm font-medium text-white hover:bg-blue-700 focus:outline-none focus-visible:ring-2 focus-visible:ring-offset-2"
                      onClick={() => setShowWarningModal(false)}
                    >
                      <div>cancel</div>
                    </button> */}
                  </div>
                </DialogPanel>
                </TransitionChild>
              </div>
            </div>
          </Dialog>
        </Transition>
      )}
    </div>
  )
}
