import { ErrorResponse } from "@/app/api/_models";
import { getServerApiHostUrl } from "@/utils/host";
import { NextRequest, NextResponse } from "next/server";

export type SendMessageResponse = any;

export async function POST(
  request: NextRequest
): Promise<NextResponse<SendMessageResponse | ErrorResponse>> {
  const accessToken = request.headers.get("Authorization");
  const body = await request.json();

  if (!accessToken) {
    return NextResponse.json(
      { message: "Authorization header is missing" },
      { status: 400 }
    );
  }
  const sendMessageResponse = await fetch(
    `${await getServerApiHostUrl()}/api/cs/messages`,
    {
      method: "POST",
      headers: {
        "Authorization": `Bearer ${accessToken}`,
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        "ticket_id": body?.ticket_id,
        "message_type": body?.message_type,
        "content": body?.content,
        "file_info": body?.file_info,
      }),
      cache: "no-cache",
    }
  ).then((res) => res.json());

  return NextResponse.json(sendMessageResponse, { status: 200 });
}
