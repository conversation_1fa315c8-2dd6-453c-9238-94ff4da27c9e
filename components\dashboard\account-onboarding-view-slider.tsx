"use client"

import { useTranslations } from 'next-intl'
import { Fragment, useEffect, useState } from 'react'
import { useSearchParams } from "next/navigation";
import { TransitionChild, Listbox, ListboxButton, ListboxOptions, ListboxOption } from '@headlessui/react'
import { cn } from "@/utils/msc"
import { api } from "@/utils/api"
import { Check } from "@/components/ui/check"
import { CircleFlag } from "react-circle-flags"
import { ArrowsRightLeftIcon, ChevronUpDownIcon, XMarkIcon } from "@heroicons/react/20/solid"
import ConnectAdButton from './connect-ad-button'


interface AccountOnboardingViewSliderProps {
  handleAccountOnboardingViewCloseClick: () => void;
  availableProfiles: any[];
  isConnectProductDataLoading: boolean;
  fetchConnectionByProfile: (profile: any) => void;
  setIsConnectProductDataModalOpen: (isOpen: boolean) => void;
  activateButton: (profile: any, className: string) => JSX.Element;
}

export default function AccountOnboardingViewSlider({
  handleAccountOnboardingViewCloseClick,
  availableProfiles,
  isConnectProductDataLoading,
  fetchConnectionByProfile,
  setIsConnectProductDataModalOpen,
  activateButton
}: AccountOnboardingViewSliderProps) {
  const searchParams = useSearchParams();

  const t = useTranslations('component')
  const tos = useTranslations('optimizationSets')
  const [selectedProfile, setSelectedProfile] = useState<any>(null)
  const [currentStep, setCurrentStep] = useState(1)

  // Calculate current step based on profile state
  const getCurrentStep = () => {
    if (!selectedProfile) return 1
    if (!selectedProfile.is_connected) return 2
    if (selectedProfile.is_connected && !selectedProfile.is_subscription_active) return 3
    return 1
  }

  useEffect(() => {
    setCurrentStep(getCurrentStep())
  }, [selectedProfile])

  useEffect(() => {
    if (availableProfiles?.length > 0 && !selectedProfile) {
      const state = searchParams.get("state") || "";
      const [country, accountType] = state.split("_");

      let connectedProfile = null;
      let unconnectedProfile = null;
      let countryMatchConnected = null;
      let countryMatchUnconnected = null;
      
      for (const adAccount of availableProfiles) {
        if (adAccount.profiles) {
          for (const profile of adAccount.profiles) {
            // Check for country match first if country exists
            if (country && profile.country_code?.toLowerCase() === country.toLowerCase()) {
              if (profile.is_connected && !countryMatchConnected) {
                countryMatchConnected = profile;
              } else if (!profile.is_connected && !countryMatchUnconnected) {
                countryMatchUnconnected = profile;
              }
            }
            
            // Fallback to any profile
            if (profile.is_connected && !connectedProfile) {
              connectedProfile = profile;
            }
            if (!profile.is_connected && !unconnectedProfile) {
              unconnectedProfile = profile;
            }
          }
        }
      }
      
      // Prioritize country matches, then fallback to any profile
      if (countryMatchConnected) {
        setSelectedProfile(countryMatchConnected);
      } else if (countryMatchUnconnected) {
        setSelectedProfile(countryMatchUnconnected);
      } else if (connectedProfile) {
        setSelectedProfile(connectedProfile);
      } else if (unconnectedProfile) {
        setSelectedProfile(unconnectedProfile);
      }
    }
  }, [availableProfiles])
  
  return (
    <>
      <TransitionChild
        as={Fragment}
        enter="ease-out duration-300"
        enterFrom="opacity-0"
        enterTo="opacity-100"
        leave="ease-in duration-200"
        leaveFrom="opacity-100"
        leaveTo="opacity-0"
      >
        <div className="w-full h-full bg-black/30" onClick={handleAccountOnboardingViewCloseClick} />
      </TransitionChild>
      <TransitionChild
        as={Fragment}
        enter="ease-out duration-300"
        enterFrom="opacity-0 w-0"
        enterTo="opacity-100 w-full"
        leave="ease-in duration-200"
        leaveFrom="opacity-100 w-full"
        leaveTo="opacity-0 w-0"
      >
        <div className="absolute inset-y-0 right-0 w-full h-full overflow-hidden shadow-md">
          <div className="flex flex-col h-full bg-white">
            {/* Header */}
            <div className="flex items-center justify-between pt-6 px-6 border-t border-gray-200">
              <div></div>
              <button
                type="button"
                className="rounded-md bg-white text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-indigo-500"
                onClick={handleAccountOnboardingViewCloseClick}
              >
                <span className="sr-only">Close</span>
                <XMarkIcon className="h-6 w-6" aria-hidden="true" />
              </button>
            </div>

            {/* Onboarding Steps */}
            <div className="flex-1 p-6 max-w-md mx-auto">
              <h1 className="text-2xl font-semibold text-gray-900">
                Account Onboarding
              </h1>
              {/* Progress Indicator */}
              <div className="pt-3 pb-6">
                <div className="flex items-center">
                  <div className="relative flex-1 flex gap-x-0.5 items-center rounded-full">
                    <div className={cn(currentStep >= 1 ? "bg-blue-200" : "bg-gray-200", "w-1/3 h-2 rounded-full")}></div>
                    <div className={cn(currentStep >= 2 ? "bg-blue-200" : "bg-gray-200", "w-1/3 h-2 rounded-full")}></div>
                    <div className={cn(currentStep >= 3 ? "bg-blue-200" : "bg-gray-200", "w-1/3 h-2 rounded-full")}></div>
                  </div>
                  <span className="ml-3 text-sm text-gray-400 font-semibold">
                    <span className="text-blue-400">{currentStep}</span>/3
                  </span>
                </div>
              </div>
              <div className="space-y-8">
                {/* Step 1: Authorize Ads */}
                <div className="flex items-start space-x-4">
                  <div className="flex-shrink-0">
                    <div
                      className={cn(
                        "flex items-center justify-center w-8 h-8 rounded-full",
                        currentStep === 1 ? "bg-blue-100 text-blue-600" : "bg-gray-100 text-gray-400"
                      )}
                    >
                      <span className="text-sm font-medium">1</span>
                    </div>
                  </div>
                  <div className="min-w-0 flex-1">
                    <h3
                      className={cn(
                        "text-base font-medium",
                        currentStep === 1 ? "text-gray-900" : "text-gray-400"
                      )}
                    >
                      Authorize Ads Account
                    </h3>
                    <p
                      className={cn(
                        "mt-1 text-sm",
                        currentStep === 1 ? "text-gray-500" : "text-gray-400"
                      )}
                    >
                      Connect your Amazon Ads account to enable campaign management and optimization.
                    </p>
                    <div className="mt-3 flex items-center gap-x-3 w-full">
                      {availableProfiles?.length > 0
                        ? (<Listbox value={selectedProfile} onChange={setSelectedProfile}>
                          {({ open }) => (
                            <>
                              <div className="relative flex-1 min-w-0">
                                <ListboxButton className="relative w-full max-w-[540px] cursor-default rounded-lg bg-white py-2 pl-3 pr-10 text-left border border-gray-200 focus:outline-none focus-visible:border-indigo-500 focus-visible:ring-2 focus-visible:ring-white focus-visible:ring-opacity-75 focus-visible:ring-offset-2 focus-visible:ring-offset-indigo-300 sm:text-sm cursor-pointer">
                                  <div className="flex items-center justify-between gap-x-2">
                                    <span className="block truncate">
                                      {selectedProfile ? selectedProfile.account_name : "Select an Ads Account"}
                                    </span>
                                    {selectedProfile && (
                                    <div className="flex items-center gap-x-1">
                                      <div className="w-4 h-4 rounded-full border-2 border-white/20">
                                        <CircleFlag countryCode={selectedProfile.country_code.toLowerCase()} />
                                      </div>
                                      <div className="text-xs text-gray-400 font-normal">{selectedProfile.country_code}</div>
                                    </div>
                                    )}
                                  </div>
                                  <span className="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-2">
                                    <ChevronUpDownIcon
                                      className="h-5 w-5 text-gray-400"
                                      aria-hidden="true"
                                    />
                                  </span>
                                </ListboxButton>
                                <ListboxOptions className="absolute mt-1 max-h-[500px] w-full divide-y divide-gray-100 overflow-auto rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none sm:text-sm z-10">
                                  {availableProfiles.map((adAccount: any) => (
                                    adAccount?.profiles.map((profile: any, profileIndex: number) => (
                                      <ListboxOption 
                                        key={`${adAccount.id}-${profileIndex}`}
                                        className="relative py-2 pl-2 pr-[60px] cursor-pointer"
                                        value={profile}
                                      >
                                        <div className={cn(
                                          "relative max-w-[540px] w-full h-full flex flex-col items-start justify-between rounded-lg overflow-hidden",
                                          "bg-purple-100/40 border border-purple-600"
                                        )}>
                                          <div className="flex-shrink-0 w-full h-9 flex items-center justify-between gap-x-2 py-2 pl-3 pr-4 bg-purple-600 text-white text-xs font-semibold">
                                            <div className="flex-shrink-0 flex items-center gap-x-2">
                                              {profile.amazon_account && (
                                                <span className="relative flex h-2 w-2">
                                                  <span className={cn(
                                                    "absolute inline-flex h-full w-full rounded-full opacity-75",
                                                    profile.amazon_account.ad_lwa_validation_yn === "N"
                                                      ? "animate-ping bg-red-500"
                                                      : profile.amazon_account.ad_lwa_validation_yn === "Y"
                                                        ? "animate-ping bg-green-500"
                                                        : "bg-gray-300"
                                                  )}></span>
                                                  <span className={cn(
                                                    "relative inline-flex rounded-full h-2 w-2",
                                                    profile.amazon_account.ad_lwa_validation_yn === "N"
                                                      ? "bg-red-500"
                                                      : profile.amazon_account.ad_lwa_validation_yn === "Y"
                                                        ? "bg-green-500"
                                                        : "bg-gray-300"
                                                  )}></span>
                                                </span>
                                              )}
                                              <div className="flex-shrink-0 flex items-center gap-x-1.5">
                                                <div className="text-[10px] py-0.5 px-1.5 bg-white/20 text-white rounded-sm">
                                                  {profile.account_type === "vendor" ? "Vendor" : "Seller"}
                                                </div>
                                                Ads
                                              </div>
                                            </div>
                                            <div className="flex items-center gap-x-1">
                                              <div className="w-4 h-4 rounded-full border-2 border-white/20">
                                                <CircleFlag countryCode={profile.country_code.toLowerCase()} />
                                              </div>
                                              <div className="text-xs text-gray-100 font-normal">{profile.country_code}</div>
                                            </div>
                                          </div>
                                          <div className="grow w-full min-h-0 flex flex-col justify-center px-4 py-3 space-y-1">
                                            <div>
                                              <div className="text-gray-400 text-[10px]">
                                                account ID
                                              </div>
                                              <div className="text-xs text-gray-500 truncate">
                                                {profile.account_id}
                                              </div>
                                            </div>
                                            <div>
                                              <div className="text-gray-400 text-[10px]">
                                                account name
                                              </div>
                                              <div className="text-xs text-gray-500 truncate">
                                                {profile.account_name}
                                              </div>
                                            </div>
                                          </div>
                                        </div>
                                        {selectedProfile?.profile_id === profile.profile_id ? (
                                          <div className="absolute inset-y-0 right-0 flex items-center pr-5">
                                            <div className="shrink-0 flex items-center justify-center w-5 h-5 rounded-full bg-blue-400">
                                              <Check className="w-4 h-4 stroke-white" aria-hidden="true" />
                                            </div>
                                          </div>
                                        ) : null}
                                      </ListboxOption>
                                    ))
                                  ))}
                                  <div 
                                    key="add-new-ad-account"
                                    className="relative py-2 px-2 cursor-pointer"
                                  >
                                    <div className={cn(
                                      "relative w-full h-full flex items-center justify-center rounded-lg overflow-hidden",
                                    )}>
                                      <ConnectAdButton
                                        buttonClassName={cn(
                                          "group relative w-full flex items-center gap-x-1 py-2 px-3 cursor-pointer rounded-md focus:outline-none text-sm overflow-hidden text-gray-500 hover:text-gray-400 font-semibold",
                                          "transition-width ease-in-out delay-150"
                                        )}
                                        isLNBExpanded={true}
                                      />
                                    </div>
                                  </div>
                                </ListboxOptions>
                              </div>
                            </>
                          )}
                          </Listbox>)
                        : <ConnectAdButton
                            buttonClassName={cn(
                              "group relative flex-shrink-0 flex items-center gap-x-1 p-3 cursor-pointer rounded-md focus:outline-none text-sm overflow-hidden bg-blue-600 hover:bg-blue-700 text-white font-semibold",
                              "transition-width ease-in-out delay-150"
                            )}
                            isLNBExpanded={true}
                          />
                      }
                    </div>
                  </div>
                </div>

                {/* Step 2: Connect SP Account */}
                <div className="flex items-start space-x-4">
                  <div className="flex-shrink-0">
                    <div
                      className={cn(
                        "flex items-center justify-center w-8 h-8 rounded-full",
                        currentStep === 2 ? "bg-blue-100 text-blue-600" : "bg-gray-100 text-gray-400"
                      )}
                    >
                      <span className="text-sm font-medium">2</span>
                    </div>
                  </div>
                  <div className="min-w-0 flex-1">
                    <h3
                      className={cn(
                        "text-base font-medium",
                        currentStep === 2 ? "text-gray-900" : "text-gray-400"
                      )}
                    >
                      Connect Selling Partner Account
                    </h3>
                    <p
                      className={cn(
                        "mt-1 text-sm",
                        currentStep === 2 ? "text-gray-500" : "text-gray-400"
                      )}
                    >
                      Link your Amazon Selling Partner account to access product and sales data.
                    </p>
                    <div className="mt-3 flex items-center gap-x-3 w-full">
                      {selectedProfile?.is_connected
                        ? (<Listbox value={selectedProfile} onChange={setSelectedProfile}>
                          {({ open }) => (
                            <>
                              <div className="relative flex-1 min-w-0">
                                <ListboxButton className="relative w-full max-w-[540px] cursor-default rounded-lg bg-white py-2 pl-3 pr-10 text-left border border-gray-200 focus:outline-none focus-visible:border-indigo-500 focus-visible:ring-2 focus-visible:ring-white focus-visible:ring-opacity-75 focus-visible:ring-offset-2 focus-visible:ring-offset-indigo-300 sm:text-sm cursor-pointer">
                                  <div className="flex items-center justify-between gap-x-2">
                                    <span className="block truncate">
                                      {selectedProfile ? selectedProfile.account_name : "Connect Product Data"}
                                    </span>
                                    {selectedProfile && (
                                    <div className="flex items-center gap-x-1">
                                      <div className="w-4 h-4 rounded-full border-2 border-white/20">
                                        <CircleFlag countryCode={selectedProfile.country_code.toLowerCase()} />
                                      </div>
                                      <div className="text-xs text-gray-400 font-normal">{selectedProfile.country_code}</div>
                                    </div>
                                    )}
                                  </div>
                                  <span className="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-2">
                                    <ChevronUpDownIcon
                                      className="h-5 w-5 text-gray-400"
                                      aria-hidden="true"
                                    />
                                  </span>
                                </ListboxButton>
                                <ListboxOptions className="absolute mt-1 max-h-60 w-full divide-y divide-gray-100 overflow-auto rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none sm:text-sm z-10">
                                  <ListboxOption 
                                    key={`${selectedProfile.profile_id}`}
                                    className="relative py-2 pl-2 pr-[60px] cursor-pointer"
                                    value={selectedProfile}
                                  >
                                    <div className={cn(
                                      "relative max-w-[540px] w-full h-full flex flex-col items-start justify-between rounded-lg overflow-hidden",
                                      "bg-blue-100/40 border border-blue-600"
                                    )}>
                                      <div className="flex-shrink-0 w-full h-9 flex items-center justify-between gap-x-2 py-2 px-4 bg-blue-600 text-white text-xs font-semibold">
                                        <div className="flex-shrink-0 flex items-center gap-x-2">
                                          {selectedProfile.amazon_account && (
                                            <span className="relative flex h-2 w-2">
                                              <span className={cn(
                                                "absolute inline-flex h-full w-full rounded-full opacity-75",
                                                selectedProfile.amazon_account.sp_lwa_validation_yn === "N"
                                                  ? "animate-ping bg-red-500"
                                                  : selectedProfile.amazon_account.sp_lwa_validation_yn === "Y"
                                                    ? "animate-ping bg-green-500"
                                                    : "bg-gray-300"
                                              )}></span>
                                              <span className={cn(
                                                "relative inline-flex rounded-full h-2 w-2",
                                                selectedProfile.amazon_account.sp_lwa_validation_yn === "N"
                                                  ? "bg-red-500"
                                                  : selectedProfile.amazon_account.sp_lwa_validation_yn === "Y"
                                                    ? "bg-green-500"
                                                    : "bg-gray-300"
                                              )}></span>
                                            </span>
                                          )}
                                          <span className="flex-shrink-0">
                                            {selectedProfile.amazon_account.sp_account_type === "vendor"
                                              ? "Vendor"
                                              : "Seller"
                                            }
                                          </span>
                                        </div>
                                        <div className="flex items-center gap-x-1">
                                          <div className="w-4 h-4 rounded-full border-2 border-white/20">
                                            <CircleFlag countryCode={selectedProfile.amazon_account.sp_country_code.toLowerCase()} />
                                          </div>
                                          <div className="text-xs text-gray-100 font-normal">{selectedProfile.amazon_account.sp_country_code}</div>
                                        </div>
                                      </div>
                                      <div className="grow w-full min-h-0 flex flex-col justify-center px-4 py-3 space-y-1">
                                        <div>
                                          <div className="text-gray-400 text-[10px]">
                                            marketplace ID
                                          </div>
                                          <div className="text-xs text-gray-500 truncate">
                                            {selectedProfile.amazon_account.sp_marketplace_id}
                                          </div>
                                        </div>
                                        <div>
                                          <div className="text-gray-400 text-[10px]">
                                            selling partner ID
                                          </div>
                                          <div className="text-xs text-gray-500 truncate">
                                            {selectedProfile.amazon_account.selling_partner_id}
                                          </div>
                                        </div>
                                      </div>
                                    </div>

                                    <div className="absolute inset-y-0 right-0 flex items-center pr-5">
                                      <div className="shrink-0 flex items-center justify-center w-5 h-5 rounded-full bg-blue-400">
                                        <Check className="w-4 h-4 stroke-white" aria-hidden="true" />
                                      </div>
                                    </div>
                                  </ListboxOption>
                                </ListboxOptions>
                              </div>
                            </>
                          )}
                          </Listbox>)
                        : <button
                            className={cn(
                              "group relative flex-shrink-0 flex items-center gap-x-1 p-3 cursor-pointer rounded-md focus:outline-none text-sm overflow-hidden bg-blue-600 hover:bg-blue-700 text-white font-semibold",
                              "transition-width ease-in-out delay-150",
                              (currentStep !== 2 || isConnectProductDataLoading) && "bg-gray-100 hover:bg-gray-100 text-gray-400 cursor-not-allowed",
                            )}
                            disabled={currentStep !== 2 || isConnectProductDataLoading || !selectedProfile}
                            onClick={(e) => {
                              e.stopPropagation()
                              fetchConnectionByProfile(selectedProfile)
                              setIsConnectProductDataModalOpen(true)
                            }}
                          >
                            {isConnectProductDataLoading
                              ? <svg className="animate-spin h-4 w-4 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                </svg>
                              : <ArrowsRightLeftIcon
                                  className="flex-shrink-0 h-4 w-4"
                                  aria-hidden="true"
                                />
                            }
                            <div className="flex-shrink-0 font-semibold">
                              {t('accounts.productData')}
                            </div>
                          </button>
                      }
                    </div>
                  </div>
                </div>

                {/* Step 3: Activate & Pay */}
                <div className="flex items-start space-x-4">
                  <div className="flex-shrink-0">
                    <div
                      className={cn(
                        "flex items-center justify-center w-8 h-8 rounded-full",
                        currentStep === 3 ? "bg-blue-100 text-blue-600" : "bg-gray-100 text-gray-400"
                      )}
                    >
                      <span className="text-sm font-medium">3</span>
                    </div>
                  </div>
                  <div className="min-w-0 flex-1">
                    <h3
                      className={cn(
                        "text-base font-medium",
                        currentStep === 3 ? "text-gray-900" : "text-gray-400"
                      )}
                    >
                      Activate Account Pair
                    </h3>
                    <p
                      className={cn(
                        "mt-1 text-sm",
                        currentStep === 3 ? "text-gray-500" : "text-gray-400"
                      )}
                    >
                      Complete payment to activate your account pair and start using optimization features.
                    </p>
                    <div className="mt-3 inline-flex">
                      {selectedProfile && selectedProfile.is_connected && selectedProfile.pricing_table_id && !selectedProfile.is_subscription_active
                        ? activateButton(selectedProfile, "group relative flex-shrink-0 flex items-center gap-x-1 p-3 cursor-pointer rounded-md focus:outline-none text-sm overflow-hidden bg-blue-600 hover:bg-blue-700 text-white font-semibold")
                        : <button
                            type="button"
                            disabled
                            className={cn(
                              "group relative flex-shrink-0 flex items-center gap-x-1 p-3 cursor-not-allowed rounded-md focus:outline-none text-sm overflow-hidden bg-gray-100 text-gray-400 font-semibold",
                            )}
                          >
                            Activate & Pay
                          </button>
                      }
                    </div>
                  </div>
                </div>
              </div>

            </div>
          </div>
        </div>
      </TransitionChild>
    </>
  )
}