import { ErrorResponse } from "@/app/api/_models";
import { getServerApiHostUrl } from "@/utils/host";
import { NextRequest, NextResponse } from "next/server";

export type SkipPasswordChangeResponse = {
  status: string;
  message: string;
};

export async function GET(
  request: NextRequest
): Promise<NextResponse<SkipPasswordChangeResponse | ErrorResponse>> {
  const accessToken = request.headers.get("Authorization");
  if (!accessToken) {
    return NextResponse.json(
      { message: "Authorization header is missing" },
      { status: 400 }
    );
  }

  const skipPasswordChangeResponse = await fetch(
    `${await getServerApiHostUrl()}/api/cognito/skip-password-change`,
    {
      method: "GET",
      headers: {
        "Authorization": `Bearer ${accessToken}`,
      },
      cache: "no-cache",
    }
  ).then((res) => res.json());

  return NextResponse.json(skipPasswordChangeResponse, { status: 200 });
}
