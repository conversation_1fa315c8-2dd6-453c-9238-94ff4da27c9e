"use client"

import { api } from "@/utils/api"
import { useSession } from "next-auth/react"
import { Fragment, useEffect, useRef, useState } from 'react'
import { Disclosure, DisclosureButton, DisclosurePanel, Transition } from '@headlessui/react'
import "react-datepicker/dist/react-datepicker.css"
import { cn, currencyFormat, formatDate, getDateDifference, integerCurrencyFormat } from "@/utils/msc"
import { ProfileOption } from "@/components/dashboard/profile-select"
import PortfolioStatus from './portfolio-status'
import { MarketplaceOption } from "@/components/dashboard/marketplace-select"
import { ChevronDownIcon, ExclamationTriangleIcon, PlusIcon, MagnifyingGlassIcon } from "@heroicons/react/20/solid"
import AdPortfolioViewSlider from "@/components/dashboard/ad-portfolio-view-slider"
import AdProductViewSlider from "@/components/dashboard/ad-product-view-slider"
import AdPortfolioEditSlider from "@/components/dashboard/ad-portfolio-edit-slider"
import VendorAdPortfolioEditSlider from "@/components/dashboard/vendor-ad-portfolio-edit-slider"
import ProductStatus from "./product-status"
import { useTranslations } from "next-intl"
import { BudgetPacingResponse } from "@/app/api/hourlyReport/new_budget_pacing/route";
import { CampaignBudgetUsageResponse } from "@/app/api/hourlyReport/campaign-budget-usage/route";
import { useCurrencyFormatter, getCurrencyCodeFromMarketplace } from "@/utils/currency"
import { useMemo } from "react"

interface AdSettingsLayoutProps {
    mopUserData: any;
    selectedProfile: ProfileOption;
    selectedMarketplace: MarketplaceOption;
}
export interface PortfolioListItem {
  id: number;
  account_id: string;
  marketplace_id: string;
  portfolio_id: string;
  created_by: number;
  creation_datetime: string;
  updated_by: number;
  last_update_datetime: string;
  ad_budget_amount: number | null;
  ad_budget_end_date: string | null;
  ad_budget_start_date: string | null;
  ad_budget_type: string;
  total_cost?: Array<{ value: number; date: string }>;
  use_yn: string; // "Y" or "N"
  bid_yn: string; // "Y" or "N"
  display_yn: string; // "Y" or "N"
  optimization_goal: string;
  optimization_name: string;
  optimization_option: string;
  optimization_range: string;
  optimization_target_type: string;
  optimization_target_value: number;
  request_status: string;
  target_products: ProductListItem[];
  prediction?: {
    budget_usage?: {
      MAX?: number;
      MIN?: number;
      TARGET?: number;
      estimated_budget_state?: string;
    };
    inventory?: Array<{
      asin: string;
      estimated_inventory_state: string;
    }>;
  };
  competition_option: string;
  target_same_sku_only_yn: string; // "Y" or "N"
  limit_cpc: number;
}
export interface ProductListItem {
  recommend_type: any
  optimization_set: any;
  account_id: string;
  creation_datetime: string;
  request_status: string;
  request_id: string;
  afn_inventory_quantity: number;
  asin: string;
  parent_asin: string;
  available_quantity: number;
  campaigns: any[];
  classification_rank: number;
  classification_rank_title: string;
  condition: string;
  currency: string;
  eligibility_status: string;
  fba_fee: number;
  fulfillment_channel: string;
  id: number;
  image: string;
  inbound_quantity: number;
  item_name: string;
  item_volume: number;
  listing_price: number | null;
  marketplace_id: string;
  merchant_inventory_quantity: number | null;
  mfn_inventory_quantity: number | null;
  product_group: string;
  product_size_tier: string;
  referral_fee: number;
  reserved_quantity: number;
  return_fee: number;
  shipping_price: number;
  shipping_weight: number;
  sku: string;
  target_status: string;
  status: string;
  unfulfillable_quantity: number;
}

export default function AdPortfolioLayoutComponent({
    mopUserData,
    selectedProfile,
    selectedMarketplace
}: AdSettingsLayoutProps) {
  const t = useTranslations('component')
  const tos = useTranslations('optimizationSets')
  const { data: session, status } = useSession()
  
  // Currency formatting
  const { formatCurrency } = useCurrencyFormatter()
  const currencyCode = useMemo(() => getCurrencyCodeFromMarketplace(selectedMarketplace), [selectedMarketplace])
  const [isPortfolioViewSliderOpen, setIsPortfolioViewSliderOpen] = useState(false)
  const optimizationsLoadingRef = useRef(0)
  const budgetPacingLoadingRef = useRef(0)
  const [isProductViewSliderOpen, setIsProductViewSliderOpen] = useState(false)
  const [isEditSliderOpen, setIsEditSliderOpen] = useState(false)
  const [portfolioListItems, setPortfolioListItems] = useState<PortfolioListItem[]>([])
  const [budgetPacingData, setBudgetPacingData] = useState<BudgetPacingResponse[]>([])
  const [isBudgetPacingLoading, setIsBudgetPacingLoading] = useState(true)
  const [selectedPortfolioItem, setSelectedPortfolioItem] = useState<PortfolioListItem | null>(null)
  const [selectedProductItem, setSelectedProductItem] = useState<ProductListItem | null>(null)
  const [parentPortfolioItem, setParentPortfolioItem] = useState<PortfolioListItem | null>(null)
  const [portfolioSearchText, setPortfolioSearchText] = useState('')
  const [campaignBudgetUsageCache, setCampaignBudgetUsageCache] = useState<Map<number, CampaignBudgetUsageResponse>>(new Map())
  const fetchingPromises = useRef<Map<number, Promise<void>>>(new Map())

  const fetchOptimizationSets = async (signal: AbortSignal) => {
    if (!(session?.user as any).access_token) {
      console.log('access token is missing in the session.')
      return
    }

    // 두 API를 병렬로 호출
    optimizationsLoadingRef.current += 1
    budgetPacingLoadingRef.current += 1
    setIsBudgetPacingLoading(true)

    try {
      const [optimizationSetsResponse, budgetPacingResponse] = await Promise.all([
        api.getListOptimizations(
          selectedProfile.account_id, 
          selectedMarketplace.marketplace_id, 
          (session?.user as any).access_token, 
          signal
        ),
        api.getNewBudgetPacing(
          {
            account_id: selectedProfile.account_id,
            marketplace_id: selectedMarketplace.marketplace_id
          },
          (session?.user as any).access_token, 
          signal
        )
      ])

      optimizationsLoadingRef.current -= 1
      budgetPacingLoadingRef.current -= 1

      if (optimizationSetsResponse && Array.isArray(optimizationSetsResponse)) {
        setPortfolioListItems(optimizationSetsResponse)
      }
      
      if (budgetPacingResponse && Array.isArray(budgetPacingResponse)) {
        setBudgetPacingData(budgetPacingResponse)
      }
      
      setIsBudgetPacingLoading(false)
    } catch (error) {
      optimizationsLoadingRef.current -= 1
      budgetPacingLoadingRef.current -= 1
      setIsBudgetPacingLoading(false)
      console.error('API error:', error)
    }
  }
  
  const fetchCampaignBudgetUsage = async (optimizationId: number) => {
    if (!(session?.user as any).access_token) {
      console.log('access token is missing in the session.')
      return
    }    
    if (campaignBudgetUsageCache.has(optimizationId)) {
      return
    }
    if (fetchingPromises.current.has(optimizationId)) {
      return fetchingPromises.current.get(optimizationId)
    }
    
    const fetchPromise = async () => {
      try {
        const campaignBudgetUsageResponse = await api.getCampaignBudgetUsage(
          optimizationId,
          (session?.user as any).access_token
        )
        if (campaignBudgetUsageResponse) {
          setCampaignBudgetUsageCache(prev => new Map(prev).set(optimizationId, campaignBudgetUsageResponse))
        }
      } catch (error) {
        console.error('Failed to fetch campaign budget usage:', error)
      } finally {
        fetchingPromises.current.delete(optimizationId)
      }
    }
    
    fetchingPromises.current.set(optimizationId, fetchPromise())
    return fetchingPromises.current.get(optimizationId)
  }
  
  const handleAddPortfolioClick = () => {
    const numberOfOptimizationSets = selectedMarketplace?.subscription_features?.number_of_optimization_sets;
    if (numberOfOptimizationSets !== -1 && portfolioListItems.length >= numberOfOptimizationSets) {
      alert(tos("addEditModal.optSetName.limitReachedMessage"));
      return;
    }
    setSelectedPortfolioItem(null)
    setIsEditSliderOpen(true)
  };
  
  const handleEditPortfolioClick = (item: any): void => {
    setSelectedPortfolioItem(item)
    setIsEditSliderOpen(true)
  };

  const handlePausePortfolioClick = async (item: any) => {
    const abortController = new AbortController()
    optimizationsLoadingRef.current += 1
    let pauseOptimizationResponse = await api.pauseOptimizationSet(
      {
        optimization_id: item.id,
        bid_yn: "N"
      },
      selectedProfile.account_id,
      selectedMarketplace.marketplace_id,
      (session?.user as any).access_token
    )
    optimizationsLoadingRef.current -= 1
    fetchOptimizationSets(abortController.signal)
  }

  const handleResumePortfolioClick = async (item: any) => {
    const abortController = new AbortController()
    optimizationsLoadingRef.current += 1
    let resumeOptimizationResponse = await api.pauseOptimizationSet(
      {
        optimization_id: item.id,
        bid_yn: "Y"
      },
      selectedProfile.account_id,
      selectedMarketplace.marketplace_id,
      (session?.user as any).access_token
    )
    optimizationsLoadingRef.current -= 1
    fetchOptimizationSets(abortController.signal)
  }

  const handleDeletePortfolioClick = async (item: any) => {
    const abortController = new AbortController()
    optimizationsLoadingRef.current += 1
    await api.archiveOptimizationSet(
      {
        optimization_id: item.id,
      },
      selectedProfile.account_id,
      selectedMarketplace.marketplace_id,
      (session?.user as any).access_token
    )
    optimizationsLoadingRef.current -= 1
    fetchOptimizationSets(abortController.signal)
  }
  
  const handlePortfolioItemClick = (item: any) => {
    setSelectedPortfolioItem(item)
    setIsPortfolioViewSliderOpen(true)
    if (item?.id) {
      fetchCampaignBudgetUsage(item.id)
    }
  };

  const handleProductItemClick = (portfolioItem: any, productItem: any) => {
    setSelectedProductItem(productItem)
    setParentPortfolioItem(portfolioItem)
    setIsProductViewSliderOpen(true)
    if (portfolioItem?.id) {
      fetchCampaignBudgetUsage(portfolioItem.id)
    }
  };

  const handlePortfolioViewCloseClick = () => {
    setSelectedPortfolioItem(null)
    setIsPortfolioViewSliderOpen(false)
  }

  const handleProductViewCloseClick = () => {
    setSelectedProductItem(null)
    setParentPortfolioItem(null)
    setIsProductViewSliderOpen(false)
  }

  const handleEditCloseClick = (refresh: boolean) => {
    const abortController = new AbortController()
    if (refresh) {
      selectedProfile && selectedMarketplace && fetchOptimizationSets(abortController.signal)
    }
    setIsEditSliderOpen(false)
  }

  useEffect(() => {
    if (selectedPortfolioItem && selectedProfile && selectedMarketplace) {
      const newSelectedPortfolioItem = portfolioListItems.find((item) => item.id === selectedPortfolioItem.id)
      newSelectedPortfolioItem && setSelectedPortfolioItem(newSelectedPortfolioItem)
    }
  }, [portfolioListItems])

  useEffect(() => {
    const abortController = new AbortController()    
    selectedProfile && selectedMarketplace && fetchOptimizationSets(abortController.signal)
    setCampaignBudgetUsageCache(new Map())
    fetchingPromises.current.clear()
    return () => {
      abortController.abort()
    }
  }, [selectedProfile, selectedMarketplace])

  return (
    <div className="relative flex items-center justify-center w-full h-full divide-x divide-gray-200">
      <div className="flex-shrink-0 flex flex-col w-full h-full bg-white py-4 sm:py-6 px-8 sm:px-12">
        <div className="flex items-center justify-between">
          <h1 className="text-2xl text-gray-800 font-medium">{tos("title")}</h1>
          <button
            className={cn(
              "flex items-center gap-x-2 p-3 cursor-pointer rounded-md focus:outline-none text-sm overflow-hidden font-semibold",
              optimizationsLoadingRef.current > 0
                ? "bg-gray-100 text-gray-400 cursor-not-allowed"
                : "bg-blue-100 hover:bg-blue-200 text-blue-500"
            )}
            onClick={handleAddPortfolioClick}
            disabled={optimizationsLoadingRef.current > 0}
          >
            <PlusIcon
              className="flex-shrink-0 h-5 w-5"
              aria-hidden="true"
            />
            <div>{tos("topButton.addNew")}</div>
          </button>
        </div>
        {/* search box */}
        <div className="relative flex-shrink-0 mt-3 rounded-lg overflow-hidden cursor-pointer">
          <MagnifyingGlassIcon
            className={cn(
              "h-5 w-5 absolute top-1/2 left-3 transform -translate-y-1/2",
              portfolioSearchText ? "text-gray-500" : "text-gray-300"
            )}
          />
          <input
            type="text"
            className="w-full pl-10 pr-4 py-2 bg-white text-sm text-gray-500 placeholder:text-gray-300 border border-gray-100 focus:border-transparent focus:outline-blue-200 rounded-lg"
            value={portfolioSearchText}
            onChange={(e) => setPortfolioSearchText(e.target.value)}
            placeholder= {tos("searchBar.placeholder")}
          />
        </div>
        {/* portfolio list */}
        <div
          className={cn(
            'grow relative w-full mt-3 rounded-lg bg-white border border-gray-100 overflow-y-scroll',
          )}
        >
          {/* List */}
          <div className="sticky inset-x-0 top-0 z-[1] flex items-center gap-x-3 py-3 text-center text-sm text-gray-400 font-bold bg-gray-100/40 backdrop-blur-md">
            <div className="w-[210px] pr-4">
              {tos("optSetList.header.status")}
            </div>
            <div className="grow px-4">
              {tos("optSetList.header.name")}
            </div>
            <div className="flex-shrink-0 w-[200px] px-4">
              {tos("optSetList.header.period")}
            </div>
            <div className="flex-shrink-0 w-[200px] px-4">
              {tos("optSetList.header.budgetPacing")}
            </div>
            <div className="flex-shrink-0 w-[160px] pr-4">
              {tos("optSetList.header.creationDate")}
            </div>
          </div>
          {optimizationsLoadingRef.current > 0
            ? <ul className="animate-pulse p-6 space-y-3">
                {Array.from({ length: 11 }, (_, index) => (
                  <li key={index} className="w-full flex items-center gap-x-3">
                    <div className="flex-shrink-0 w-40 h-20 rounded-md bg-gray-100"></div>
                    <div className="flex-1 h-20 rounded-md bg-gray-100"></div>
                    <div className="flex-shrink-0 w-[200px] h-20 rounded-md bg-gray-100"></div>
                    <div className="flex-shrink-0 w-[200px] h-20 rounded-md bg-gray-100"></div>
                    <div className="flex-shrink-0 w-40 h-20 rounded-md bg-gray-100"></div>
                  </li>
                ))}
              </ul>
            : <ul className="divide-y divide-gray-100">
                {(() => {
                  const searchResult = portfolioListItems.filter((item) => {
                    return portfolioSearchText
                      ? item.optimization_name?.toLowerCase().includes(portfolioSearchText.toLowerCase())
                      : true
                  })
                  return searchResult.length === 0
                  ? <div className="absolute inset-0 flex items-center justify-center w-full text-gray-300 text-sm font-normal">
                      { portfolioSearchText
                      ? tos("optSetList.content.optSetRow.searchResultNull")
                      : tos("optSetList.content.optSetRow.optSetNull")
                      }
                    </div>
                  : searchResult.map((item, index) => (
                    <li className="" key={index}>
                      <Disclosure defaultOpen={false}>
                        {({ open }) => (
                          <>
                            <div className="relative flex items-center cursor-pointer hover:bg-gray-100/40 text-center text-gray-500 text-sm">
                              <DisclosureButton className="flex-shrink-0 w-6 h-6 ml-6 mr-2 flex items-center justify-center rounded-full hover:bg-gray-200">
                                <ChevronDownIcon
                                  className={cn(
                                    "w-5 h-5 text-gray-400 transition duration-200 ease-in-out",
                                    open ? "rotate-180" : ""
                                  )}
                                  aria-hidden="true"
                                />
                              </DisclosureButton>
                              <div className="flex-1 flex items-center py-8 gap-x-3 overflow-hidden" onClick={() => handlePortfolioItemClick(item)}>
                                <div
                                  className={cn(
                                    'absolute inset-y-0 left-0 w-1 h-full',
                                    selectedPortfolioItem && item.id === selectedPortfolioItem.id ? 'bg-blue-400' : 'bg-transparent'
                                  )}
                                ></div>
                                <div className="relative flex-shrink-0 flex flex-col items-start justify-center gap-x-2 gap-y-2 w-[140px] pl-4">
                                  <PortfolioStatus portfolioItem={item} />
                                </div>
                                <div className="grow relative flex flex-col gap-y-0.5 px-4 overflow-hidden">
                                  <div className="w-full text-left">
                                  {(() => {
                                    const budgetPacing = budgetPacingData.find(bp => bp.optimization_id === item.id)
                                    const topProduct = item.target_products.length > 0
                                      ? item.target_products[0]
                                      : {
                                        item_name: "No Title",
                                        image: ""
                                      }
                                    return (
                                      <div className="grow relative flex items-center gap-x-4 truncate">
                                        { topProduct.image
                                        ? (<img src={topProduct.image} alt="Item Image" className="flex-shrink-0 w-14 h-14 rounded" />)
                                        : (<div className="flex-shrink-0 flex items-center justify-center w-14 h-14 bg-gray-100 rounded">
                                            <ExclamationTriangleIcon className="h-6 w-6 text-gray-300" />
                                          </div>)
                                        }
                                        <div className="flex-1 flex flex-col overflow-hidden">
                                          {(() => {
                                            const ineligibleProducts = item.target_products.filter((productItem) => productItem.eligibility_status === "INELIGIBLE")
                                            return (
                                              ineligibleProducts.length > 0 &&
                                              <div className={cn(
                                                "flex items-center gap-x-1 text-[10px] font-semibold text-red-400 rounded-md",
                                              )}>
                                                <ExclamationTriangleIcon className="h-3 w-3" />
                                                {tos("detailModal.optSet.message.ineligible")}
                                              </div>
                                            )
                                          })()}
                                          <div className="text-base text-gray-500 text-left font-semibold truncate">
                                            {item.optimization_name}
                                          </div>
                                          <div className="mt-0.5 flex items-center gap-x-2 divide-x divide-gray-100">
                                            <div className="text-[10px] text-gray-300 font-semibold rounded-full break-normal hover:no-underline">
                                              {tos("optSetList.content.optSetRow.name.topProduct")}
                                            </div>
                                            <div className="pl-2 text-gray-400 text-xs truncate">
                                              {topProduct.item_name
                                                ? topProduct.item_name
                                                : "No Title"
                                              }
                                            </div>
                                          </div>
                                        </div>
                                      </div>
                                    )
                                  })()}
                                  </div>
                                </div>
                                <div className="flex-shrink-0 w-[200px] flex flex-col gap-y-0.5 px-4 overflow-hidden">
                                  <div className="text-[10px] text-blue-400 font-semibold text-left leading-tight">
                                    {item.optimization_goal === "SALES"
                                      ? (selectedProfile.account_type === "vendor" ? tos("optSetList.content.optSetRow.period.optObjective.vendorMaxSales") : tos("optSetList.content.optSetRow.period.optObjective.maxSales"))
                                      : item.optimization_goal === "ROI"
                                        ? (selectedProfile.account_type === "vendor" ? tos("optSetList.content.optSetRow.period.optObjective.vendorMaxProfit") : tos("optSetList.content.optSetRow.period.optObjective.maxProfit"))
                                        : item.optimization_goal === "REVENUE"
                                          ? tos("optSetList.content.optSetRow.period.optObjective.maxAdSales")
                                          : tos("optSetList.content.optSetRow.period.optObjective.maxRoas")
                                    }
                                  </div>
                                  <div className="text-xs font-semibold text-left leading-tight">
                                    {item.ad_budget_type === "DATERANGE"
                                      ? item.ad_budget_end_date && item.ad_budget_start_date
                                        ? formatDate(item.ad_budget_start_date, ".") + " - " + formatDate(item.ad_budget_end_date, ".")
                                        : item.ad_budget_start_date
                                          ? "Starting from " + formatDate(item.ad_budget_start_date, ".")
                                          : ""
                                      : "Monthly recurring" + (item.ad_budget_end_date ? " until " + formatDate(item.ad_budget_end_date, ".") : "")
                                    }
                                  </div>
                                </div>
                                <div className="flex-shrink-0 relative w-[200px] flex flex-col items-center justify-start gap-y-0.5 px-4 overflow-hidden">
                                  {isBudgetPacingLoading || !budgetPacingData.find(bp => bp.optimization_id === item.id) ? (
                                    // Budget pacing loading placeholder
                                    <div className="w-full space-y-2">
                                      <div className="h-3 bg-gray-200 rounded animate-pulse"></div>
                                      <div className="h-1 bg-gray-200 rounded animate-pulse"></div>
                                      <div className="flex justify-between">
                                        <div className="h-3 w-6 bg-gray-200 rounded animate-pulse"></div>
                                        <div className="h-3 w-12 bg-gray-200 rounded animate-pulse"></div>
                                      </div>
                                    </div>
                                  ) : (
                                    <>
                                      {(() => {
                                        const budgetPacing = budgetPacingData.find(bp => bp.optimization_id === item.id)
                                        return (
                                          (budgetPacing?.budget_usage_predictions?.estimated_budget_state === "BUDGET_OVER" || budgetPacing?.budget_usage_predictions?.estimated_budget_state === "BUDGET_LACK") &&
                                          <div className={cn(
                                            "flex items-center w-full gap-x-1 text-[10px] font-semibold text-red-400 rounded-md",
                                          )}>
                                            <ExclamationTriangleIcon className="h-3 w-3" />
                                            {(budgetPacing?.budget_usage_predictions?.estimated_budget_state === "BUDGET_LACK")
                                              ? tos("detailModal.optSet.message.budgetIncrease")
                                              : tos("detailModal.optSet.message.budgetDecrease")
                                            }
                                          </div>
                                        )
                                      })()}
                                      <div className="w-full flex items-center justify-between text-xs text-gray-500 font-semibold">
                                        <div className="">{item.ad_budget_type === "DATERANGE" ? tos("optSetList.content.optSetRow.budgetPacing.totalBudget") : tos("optSetList.content.optSetRow.budgetPacing.monthlyBudget")}</div>
                                        <div className="">
                                          {(() => {
                                            const budgetPacing = budgetPacingData.find(bp => bp.optimization_id === item.id);
                                            if (budgetPacing && budgetPacing.total_budget > 0) {
                                              return (budgetPacing.spent_budget * 100 / budgetPacing.total_budget).toFixed(2);
                                            }
                                            return 0;
                                          })()}%
                                        </div>
                                      </div>
                                      <div className="relative w-full h-1 bg-gray-200 rounded-sm overflow-hidden">
                                        <div
                                          className="h-full bg-blue-400"
                                          style={{
                                            width: `${(() => {
                                              const budgetPacing = budgetPacingData.find(bp => bp.optimization_id === item.id);
                                              if (budgetPacing && budgetPacing.total_budget > 0) {
                                                return (budgetPacing.spent_budget * 100 / budgetPacing.total_budget).toFixed(2);
                                              }
                                              return 0;
                                            })()}%`
                                          }}
                                        ></div>
                                      </div>
                                      <div className="w-full flex items-center justify-between text-xs">
                                        <div className="text-gray-400">0</div>
                                        <div className="text-gray-400">{formatCurrency(budgetPacingData.find(bp => bp.optimization_id === item.id)?.total_budget || 0, currencyCode)}</div>
                                      </div>
                                    </>
                                  )}
                                </div>
                                <div className="flex-shrink-0 w-[160px] pr-4">
                                  {formatDate(item.creation_datetime, ".")}
                                </div>
                              </div>
                            </div>
                            <Transition
                              show={open}
                              enter="transition-all duration-200"
                              enterFrom="max-h-0 opacity-0"
                              enterTo="max-h-screen opacity-100"
                              leave="transition-all duration-50"
                              leaveFrom="max-h-screen opacity-100"
                              leaveTo="max-h-0 opacity-0"
                            >
                              <DisclosurePanel static>
                                <div className="grow relative w-full overflow-y-scroll">
                                  {/* List */}
                                  <ul className="divide-y divide-gray-200">
                                    {item.target_products.map((product) => {
                                      return {
                                        ...product,
                                        creation_datetime: item.creation_datetime,
                                        request_status: item.use_yn === "Y" && item.bid_yn === "N"
                                          ? "PAUSED"
                                          : product.request_status
                                      }
                                    }).map((productItem, index) => 
                                      <li
                                        className="relative flex items-center gap-x-3 py-6 cursor-pointer bg-gray-100/80 hover:bg-gray-200/60 text-center text-gray-500 text-sm"
                                        key={index}
                                        onClick={() => handleProductItemClick(item, productItem)}
                                      >
                                        <div
                                          className={cn(
                                            'absolute inset-y-0 left-0 w-1 h-full',
                                            selectedProductItem && productItem.asin === selectedProductItem.asin ? 'bg-purple-400' : 'bg-transparent'
                                          )}
                                        ></div>
                                        <div className="flex-shrink-0 w-[60px]"></div>
                                        <div className="relative flex-shrink-0 flex flex-col items-start justify-center gap-x-2 gap-y-2 w-[140px]">
                                          <ProductStatus productItem={productItem} />
                                        </div>
                                        <div className="grow relative flex items-center gap-x-4 pr-8 overflow-hidden">
                                          { productItem.image
                                          ? (<img src={productItem.image} alt="Item Image" className="flex-shrink-0 w-10 h-10 rounded" />)
                                          : (<div className="flex-shrink-0 flex items-center justify-center w-10 h-10 bg-gray-100 rounded">
                                              <ExclamationTriangleIcon className="h-5 w-5 text-gray-300" />
                                            </div>)
                                          }
                                          <div className="flex-1 flex flex-col gap-y-0.5 overflow-hidden">
                                            <div className="text-xs text-gray-500 text-left font-semibold truncate">
                                              {productItem.item_name
                                                ? productItem.item_name
                                                : "No Title"
                                              }
                                            </div>
                                            <div className="flex items-center gap-x-2">
                                              <div className="text-xs text-red-400 font-semibold">{formatCurrency(productItem.listing_price || 0, currencyCode)}</div>
                                              {productItem.eligibility_status &&
                                                productItem.eligibility_status === "ELIGIBLE"
                                                  ? <div className="border-l border-gray-200 pl-2 text-xs text-blue-400 font-semibold">{t("eligibility.eligible")}</div>
                                                  : productItem.eligibility_status === "INELIGIBLE"
                                                    ? <div className="border-l border-gray-200 pl-2 text-xs text-gray-400 font-semibold">{t("eligibility.ineligible")}</div>
                                                    : <div className="border-l border-gray-200 pl-2 text-xs text-gray-400 font-semibold">{t("eligibility.unknown")}</div>
                                              }
                                              {(() => {
                                                return (
                                                  productItem.eligibility_status === "INELIGIBLE"
                                                    ? <div className={cn(
                                                        "flex items-center gap-x-1 text-[10px] font-semibold text-red-400 rounded-md",
                                                      )}>
                                                        <ExclamationTriangleIcon className="h-3 w-3" />
                                                        {tos("detailModal.product.message.abnormal.ineligibleAlert")}
                                                      </div>
                                                      : ""
                                                )
                                              })()}
                                            </div>
                                            <div className="flex items-center gap-x-2 divide-x divide-gray-200">
                                              <div className="text-[10px] text-gray-400">{tos("optSetList.content.productRow.asin")}: {productItem.asin}</div>
                                              <div className="pl-2 text-[10px] text-gray-400">{tos("optSetList.content.productRow.sku")}: {productItem.sku}</div>
                                            </div>
                                          </div>
                                        </div>
                                      </li>
                                    )}
                                  </ul>
                                </div>
                              </DisclosurePanel>
                            </Transition>
                          </>
                        )}
                      </Disclosure>
                    </li>
                  ))
                })()}
              </ul>
          }
        </div>
        {/* right side slider for portfolio view content */}
        <Transition show={isPortfolioViewSliderOpen} as={Fragment}>
          <div className="absolute inset-0 z-[1]">
            {isPortfolioViewSliderOpen && selectedPortfolioItem &&
              <AdPortfolioViewSlider
                selectedProfile={selectedProfile}
                handlePortfolioViewCloseClick={handlePortfolioViewCloseClick}
                handleEditPortfolioClick={handleEditPortfolioClick}
                handlePausePortfolioClick={handlePausePortfolioClick}
                handleResumePortfolioClick={handleResumePortfolioClick}
                handleDeletePortfolioClick={handleDeletePortfolioClick}
                handleProductItemClick={handleProductItemClick}
                selectedPortfolioItem={selectedPortfolioItem}
                isOptSetsLoading={optimizationsLoadingRef.current > 0}
                budgetSensitivityStatus={selectedMarketplace?.subscription_features?.budget_sensitivity}
                budgetPacingData={budgetPacingData.find(bp => bp.optimization_id === selectedPortfolioItem.id) || null}
                campaignBudgetUsage={campaignBudgetUsageCache.get(selectedPortfolioItem.id) || null}
                fetchCampaignBudgetUsage={fetchCampaignBudgetUsage}
              />
            }
          </div>
        </Transition>

        {/* right side slider for product view content */}
        <Transition show={isProductViewSliderOpen} as={Fragment}>
          <div className="absolute inset-0 z-[1]">
            {isProductViewSliderOpen && selectedProductItem && parentPortfolioItem &&
              <AdProductViewSlider
                handleProductViewCloseClick={handleProductViewCloseClick}
                selectedProductItem={selectedProductItem}
                parentPortfolioItem={parentPortfolioItem}
                budgetPacingData={budgetPacingData.find(bp => bp.optimization_id === parentPortfolioItem.id) || null}
                campaignBudgetUsage={campaignBudgetUsageCache.get(parentPortfolioItem.id) || null}
                fetchCampaignBudgetUsage={fetchCampaignBudgetUsage}
              />
            }
          </div>
        </Transition>
        
        {/* right side slider for add & edit content */}
        <Transition show={isEditSliderOpen} as={Fragment}>
          <div className="absolute inset-0 z-[1]">
            {isEditSliderOpen &&
              (
                selectedProfile.account_type === "vendor"
                ? <VendorAdPortfolioEditSlider
                  selectedProfile={selectedProfile}
                  selectedMarketplace={selectedMarketplace}
                  optimizationSetNames={portfolioListItems.map((item) => item.optimization_name)}
                  handleEditCloseClick={handleEditCloseClick}
                  selectedPortfolioItem={selectedPortfolioItem}
                />
                : <AdPortfolioEditSlider
                  selectedProfile={selectedProfile}
                  selectedMarketplace={selectedMarketplace}
                  optimizationSetNames={portfolioListItems.map((item) => item.optimization_name)}
                  handleEditCloseClick={handleEditCloseClick}
                  selectedPortfolioItem={selectedPortfolioItem}
                />
              )
            }
          </div>
        </Transition>
      </div>
    </div>
  )
}
