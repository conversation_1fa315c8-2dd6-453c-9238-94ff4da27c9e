import { ErrorResponse } from "@/app/api/_models";
import { getServerApiHostUrl } from "@/utils/host";
import { NextRequest, NextResponse } from "next/server";

export type ListNewCampaignsAndPausedAdsResponse = any;

export async function POST(
  request: NextRequest
): Promise<NextResponse<ListNewCampaignsAndPausedAdsResponse | ErrorResponse>> {
  const accessToken = request.headers.get("Authorization");
  const body = await request.json();

  if (!accessToken) {
    return NextResponse.json(
      { message: "Authorization header is missing" },
      { status: 400 }
    );
  }
  const accountId = request.nextUrl.searchParams.get("account_id")
  if (!accountId) {
    return NextResponse.json(
      { message: "account_id query is missing" },
      { status: 400 }
    );
  }
  const marketplaceId = request.nextUrl.searchParams.get("marketplace_id")
  if (!marketplaceId) {
    return NextResponse.json(
      { message: "marketplace_id query is missing" },
      { status: 400 }
    );
  }
  const listNewCampaignsAndPausedAdsResponse = await fetch(
    `${await getServerApiHostUrl()}/api/optimization/list_new_campaigns_and_paused_ads?account_id=${accountId}&marketplace_id=${marketplaceId}`,
    {
      method: "POST",
      headers: {
        "Authorization": `Bearer ${accessToken}`,
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        "target_products": body?.target_products,
      }),
      cache: "no-cache",
    }
  ).then((res) => res.json());

  return NextResponse.json(listNewCampaignsAndPausedAdsResponse, { status: 200 });
}
