import { signIn } from "auth"
import { But<PERSON> } from "./button"

export function SignInButton({
  provider,
  ...props
}: { provider?: string } & React.ComponentPropsWithRef<typeof Button>) {
  return (
    <form
      action={async () => {
        "use server"
        await signIn(provider ? provider : 'cognito', { callbackUrl: "/dashboard" })
      }}
    >
      <Button {...props}>Sign In</Button>
    </form>
  )
}
