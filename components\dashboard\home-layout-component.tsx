"use client"

import { forwardRef, useEffect, useState } from "react"
import { useSession } from "next-auth/react"
import { useLocale, useTranslations } from 'next-intl'
import { Tab, TabGroup, TabList, TabPanel, TabPanels } from '@headlessui/react'
import DatePicker, { registerLocale } from "react-datepicker"
import { ko } from "date-fns/locale"
import "react-datepicker/dist/react-datepicker.css"
import { cn, currencyFormat, integerCurrencyFormat } from "@/utils/msc"
import { api } from "@/utils/api"
import { ArrowDownCircleIcon, ExclamationTriangleIcon } from "@heroicons/react/20/solid"
import ComparisonSelect, { comparisonOptions } from "@/components/dashboard/comparison-select"
import WorldMapChart from "./world-map-chart"
import { ProfileOption } from "./profile-select"
import { MarketplaceOption } from "./marketplace-select"
import Link from "next/link"
import { ArrowRight } from "lucide-react"
import ProductStatus from "./product-status"
import { PortfolioListItem, ProductListItem } from "./ad-portfolio-layout-component"
registerLocale('ko', ko)

interface HomeLayoutProps {
  mopUserData: any;
	selectedProfile: ProfileOption;
	selectedMarketplace: MarketplaceOption;
}

export default function HomeLayoutComponent({
  mopUserData,
	selectedProfile,
	selectedMarketplace
}: HomeLayoutProps) {
	const t = useTranslations('component')
	const tos = useTranslations('optimizationSets')
	const locale = useLocale()
  const { data: session, status } = useSession()
	const [selectedNewProfile, setSelectedNewProfile] = useState<any>(null)
	const fetchNewProfile = async () => {
		const memberResponse = await api.getExMember((session?.user as any).access_token)
		setSelectedNewProfile(memberResponse)
	}
	useEffect(() => {
		if (session?.user) {
			fetchNewProfile()
		}
	}, [session])

	const [isLoading, setIsLoading] = useState(false)
	const [selectedComparison, setSelectedComparison] = useState(comparisonOptions[0])
	const [portfolioListItems, setPortfolioListItems] = useState<PortfolioListItem[]>([])
	const [selectedPortfolioItem, setSelectedPortfolioItem] = useState<PortfolioListItem | null>(null)
	const [dateRange, setDateRange] = useState<Date[]>([new Date(new Date().getFullYear(), new Date().getMonth(), 1), new Date()])
  const [startDate, endDate] = dateRange
	// endDate of comparedDateRange is determined by the time span between startDate and endDate
  const [comparedDateRange, setComparedDateRange] = useState<Date[]>([new Date(new Date().getFullYear(), new Date().getMonth() - 1, 1), new Date()])
  const [comparedStartDate, comparedEndDate] = comparedDateRange
  const DateRangeInput = forwardRef(({ value, onClick }: { value: string, onClick: () => void }, ref) => (
    // @ts-ignore
    <button className="w-auto mt-1 rounded-lg bg-white shadow-md overflow-hidden cursor-pointer text-sm text-gray-600 focus:outline-none" onClick={onClick} ref={ref}>
      <div className="min-w-[200px] inline-flex py-2 px-4 bg-white hover:bg-gray-100/20">
        {value}
      </div>
    </button>
  ));
  DateRangeInput.displayName = 'DateRangeInput'
	
	const fetchOptimizationSets = async (signal: AbortSignal) => {
    if (!(session?.user as any).access_token) {
      console.log('access token is missing in the session.')
    }
    setIsLoading(true)
    let optimizationSetsResponse = await api.getListOptimizations(selectedProfile.account_id, selectedMarketplace.marketplace_id, (session?.user as any).access_token, signal)
    setIsLoading(false)
    optimizationSetsResponse && setPortfolioListItems(optimizationSetsResponse)
  }
	useEffect(() => {
		setSelectedPortfolioItem(portfolioListItems[0])
	}, [portfolioListItems])
	useEffect(() => {
		const controller = new AbortController()
		fetchOptimizationSets(controller.signal)
		return () => controller.abort()
	}, [selectedProfile, selectedMarketplace])

	const productListItemCard = (item: ProductListItem, mode: string) => {
    return (
      <div className="grow relative flex items-center gap-x-4 px-4 overflow-hidden">
        { item.image
        ? (<img src={item.image} alt="Item Image" className="flex-shrink-0 w-10 h-10 rounded" />)
        : (<div className="flex-shrink-0 flex items-center justify-center w-10 h-10 bg-gray-100 rounded">
            <ExclamationTriangleIcon className="h-5 w-5 text-gray-300" />
          </div>)
        }
        <div className="flex-1 flex flex-col gap-y-0.5 overflow-hidden">
          <div className={cn(
            "text-xs text-left font-semibold truncate",
            mode === "dark" ? "text-gray-200" : "text-gray-500"
          )}>
            {item.item_name
              ? item.item_name
              : "No Title"
            }
          </div>
          <div className={cn(
            "flex items-center gap-x-2 divide-x divide-gray-100",
            mode === "dark" ? "divide-gray-600" : "divide-gray-100"
          )}>
            <div className="text-xs text-red-400 font-semibold">{currencyFormat.format(item.listing_price || 0)}</div>
            {item.eligibility_status &&
              item.eligibility_status === "ELIGIBLE"
                ? <div className="pl-2 text-xs text-blue-400 font-semibold">{t("eligibility.eligible")}</div>
                : item.eligibility_status === "INELIGIBLE"
                  ? <div className="pl-2 text-xs text-gray-400 font-semibold">{t("eligibility.ineligible")}</div>
                  : <div className="pl-2 text-xs text-gray-400 font-semibold">{t("eligibility.unknown")}</div>
            }
          </div>
          <div className={cn(
            "flex items-center gap-x-2 divide-x",
            mode === "dark" ? "divide-gray-600" : "divide-gray-100"
          )}>
            <div className="text-[10px] text-gray-400 truncate">{tos("detailModal.optSet.budgetTab.table.content.productInfo.asin")}: {item.asin}</div>
            <div className="pl-2 text-[10px] text-gray-400 truncate">{tos("detailModal.optSet.budgetTab.table.content.productInfo.sku")}: {item.sku}</div>
          </div>
        </div>
      </div>
    )
  }

  return selectedNewProfile && selectedMarketplace
		? <div className="relative size-full flex items-center justify-center gap-x-4 py-4 sm:py-6 px-8 sm:px-12 bg-gray-100">
			<div className="w-3/5 h-full relative rounded-lg border border-gray-100">
				<div className="relative size-full flex flex-col gap-y-4">
					<div className="relative flex-shrink-0">
						<div className="flex items-center gap-x-3">
							<div>
								<div className="text-xs text-gray-400 font-semibold">{t("filter.dateRange.label")} *</div>
								<DatePicker
									selectsRange={true}
									minDate={new Date('2023-01-01')}
									maxDate={new Date()}
									startDate={startDate}
									endDate={endDate}
									onChange={(update) => {
										setDateRange(update as Date[])
									}}
									dateFormat="yyyy.MM.dd"
									calendarClassName="dashboard-date-range"
									// @ts-ignore
									customInput={<DateRangeInput />}
									locale={locale}
								/>
							</div>
							<div>
								<div className="text-xs text-gray-400 font-semibold">{t("filter.compareWith.label")}</div>
								<ComparisonSelect
									className="mt-1"
									selected={selectedComparison}
									setSelected={setSelectedComparison}
								/>
							</div>
						</div>
						<div className="mt-4 relative flex-shrink-0 rounded-lg p-4 bg-gray-900/90">
							<div className="size-full max-w-[680px] mx-auto">
								<WorldMapChart countryCode={selectedMarketplace.country_code}/>
							</div>
							<div className="absolute inset-x-0 bottom-0 p-6">
								<div className="flex items-center justify-between gap-x-4">
									<div className="p-2 rounded-md bg-gray-100/10 shadow-sm backdrop-blur-sm">
										<div className="text-gray-400 text-base font-semibold">
											Sales
										</div>
										<div className="mt-1 text-2xl text-gray-100 font-semibold">
											{integerCurrencyFormat.format(1234)}
										</div>
									</div>
									<div className="p-2 rounded-md bg-gray-100/10 shadow-sm backdrop-blur-sm">
										<div className="text-gray-400 text-base font-semibold">
											Ad Sales
										</div>
										<div className="mt-1 text-2xl text-gray-100 font-semibold">
											{integerCurrencyFormat.format(1234)}
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>
					<div className="relative grow w-full min-h-0 rounded-lg bg-white p-6 border border-gray-100">
						<TabGroup className="flex flex-col h-full">
							<div className="flex-shrink-0">
								<TabList className="inline-flex gap-x-1 bg-gray-100 p-1 rounded-lg">
									<Tab
										className="flex items-center gap-x-2 py-2 px-4 text-lg font-bold text-gray-400 focus:outline-none data-[selected]:text-gray-500 data-[selected]:bg-white data-[selected]:shadow-md data-[selected]:data-[hover]:bg-white data-[hover]:bg-white/40 data-[focus]:outline-1 data-[focus]:outline-white rounded-lg"
									>
										{({ selected }) => (
											<>
												<span>Movers</span>
												{(() => {
													return (selectedPortfolioItem && selectedPortfolioItem?.target_products?.length > 0
														? (
															<div className={cn(
																"flex items-center justify-center text-xs font-semibold w-5 h-5 rounded-full",
																selected ? "text-blue-500 bg-blue-100" : "text-gray-400 bg-gray-200"
																)}>
																{selectedPortfolioItem?.target_products?.length}
															</div>
														)
														: ""
													)
												})()}
											</>
										)}
									</Tab>
									<Tab
										className="flex items-center gap-x-2 py-2 px-4 text-lg font-bold text-gray-400 focus:outline-none data-[selected]:text-gray-500 data-[selected]:bg-white data-[selected]:shadow-md data-[selected]:data-[hover]:bg-white data-[hover]:bg-white/40 data-[focus]:outline-1 data-[focus]:outline-white rounded-lg"
									>
										{({ selected }) => (
											<>
												<span>Shakers</span>
												{(() => {
													return (selectedPortfolioItem && selectedPortfolioItem?.target_products?.length > 0
														? (
															<div className={cn(
																"flex items-center justify-center text-xs font-semibold w-5 h-5 rounded-full",
																selected ? "text-blue-500 bg-blue-100" : "text-gray-400 bg-gray-200"
																)}>
																{selectedPortfolioItem?.target_products?.length}
															</div>
														)
														: ""
													)
												})()}
											</>
										)}
									</Tab>
								</TabList>
							</div>
							<TabPanels className="mt-4 grow relative flex flex-col min-h-0 rounded-lg bg-white">
								<TabPanel className="flex flex-col w-full h-full">
									<div className="relative grow w-full min-h-0">
										<div className="relative size-full flex flex-col">
											{/* Product List in a Portfolio */}
											<div className="grow relative w-full rounded-lg bg-white border border-gray-100 overflow-y-scroll">
												<div className="sticky inset-x-0 top-0 z-[1] flex items-center gap-x-3 py-3 text-center text-sm text-gray-400 font-bold bg-gray-100/40 backdrop-blur-md">
													<div className="flex-shrink-0 w-[140px] pl-8">
														{tos("detailModal.optSet.budgetTab.table.header.status")}
													</div>
													<div className="grow px-8">
														{tos("detailModal.optSet.budgetTab.table.header.productInfo")}
													</div>
													<div className="flex-shrink-0 w-[180px] pr-6">
														Sales
													</div>
												</div>
												<ul className="divide-y divide-gray-100">
													{(() => {
														return selectedPortfolioItem?.target_products?.length === 0
															? <div className="absolute inset-0 flex items-center justify-center w-full text-gray-300 text-sm font-normal">
																	No movers found.
																</div>
															: selectedPortfolioItem?.target_products.map((item, index) => {
																const total_product_usage = item.campaigns.reduce((acc, campaign) => {
																	return acc + (campaign.campaign_total_cost?.reduce((sum: any, current: { value: any }) => { return (sum + current.value) }, 0) ?? 0)
																}, 0)
																const total_product_percent = selectedPortfolioItem.ad_budget_amount
																	? total_product_usage / selectedPortfolioItem.ad_budget_amount * 100
																	: 0
																return item && (
																	<li
																		className="relative flex items-center gap-x-3 py-6 cursor-pointer hover:bg-gray-100/50 text-center text-gray-500 text-sm"
																		key={index}
																		onClick={() => {}}
																	>
																		<div className="relative flex-shrink-0 flex flex-col items-start justify-center gap-x-2 gap-y-2 w-[140px] pl-8">
																			{(() => {
																				const targetProductInventory = selectedPortfolioItem.prediction?.inventory?.find((inventory: any) => inventory.asin === item.asin)
																				const eligibilityAbnormality = targetProductInventory
																					? targetProductInventory.estimated_inventory_state === "DANGER"
																					: false
																				return (
																					item.eligibility_status === "INELIGIBLE"
																						? <div className={cn(
																								"absolute pl-4 left-3.5 -top-4 flex items-center gap-x-1 text-[10px] font-semibold text-red-400 rounded-md",
																							)}>
																								<ExclamationTriangleIcon className="h-3 w-3" />
																								{tos("detailModal.optSet.budgetTab.table.content.statusAlert.abnormal")}
																							</div>
																						: eligibilityAbnormality
																							? <div className={cn(
																									"absolute pl-4 left-3.5 -top-4 flex items-center gap-x-1 text-[10px] font-semibold text-orange-400 rounded-md",
																								)}>
																								<ExclamationTriangleIcon className="h-3 w-3" />
																								{tos("detailModal.optSet.budgetTab.table.content.statusAlert.warning")}
																							</div>
																							: ""
																				)
																			})()}
																			<ProductStatus productItem={item} />
																		</div>
																		{productListItemCard(item, "light")}
																		<div className="flex-shrink-0 w-[180px] pr-6">
																			<div className="flex items-start justify-center w-full gap-x-3">
																				<div className="">
																					<div className="flex-shrink-0 text-xs text-gray-500 font-semibold">
																						{currencyFormat.format(total_product_usage || 0)}
																					</div>
																					<div
																						className={cn(
																							"mt-1 flex items-center justify-center gap-x-1",
																							"text-red-400"
																						)}
																					>
																						<ArrowDownCircleIcon
																							className={cn(
																								"h-4 w-4 inline-block",
																								"transform rotate-180"
																							)}
																						/>
																						<span className="text-xs font-semibold">
																							{total_product_percent.toFixed(2)}%
																						</span>
																					</div>
																				</div>
																			</div>
																		</div>
																	</li>
																)
															})
													})()}
												</ul>
											</div>
										</div>
									</div>
								</TabPanel>
								<TabPanel className="flex flex-col w-full h-full">
									<div className="relative grow w-full min-h-0">
										<div className="relative size-full flex flex-col">
											{/* Product List in a Portfolio */}
											<div className="grow relative w-full rounded-lg bg-white border border-gray-100 overflow-y-scroll">
												<div className="sticky inset-x-0 top-0 z-[1] flex items-center gap-x-3 py-3 text-center text-sm text-gray-400 font-bold bg-gray-100/40 backdrop-blur-md">
													<div className="flex-shrink-0 w-[140px] pl-8">
														{tos("detailModal.optSet.budgetTab.table.header.status")}
													</div>
													<div className="grow px-8">
														{tos("detailModal.optSet.budgetTab.table.header.productInfo")}
													</div>
													<div className="flex-shrink-0 w-[180px] pr-6">
														Sales
													</div>
												</div>
												<ul className="divide-y divide-gray-100">
													{(() => {
														return selectedPortfolioItem?.target_products?.length === 0
															? <div className="absolute inset-0 flex items-center justify-center w-full text-gray-300 text-sm font-normal">
																	No movers found.
																</div>
															: selectedPortfolioItem?.target_products.map((item, index) => {
																const total_product_usage = item.campaigns.reduce((acc, campaign) => {
																	return acc + (campaign.campaign_total_cost?.reduce((sum: any, current: { value: any }) => { return (sum + current.value) }, 0) ?? 0)
																}, 0)
																const total_product_percent = selectedPortfolioItem.ad_budget_amount
																	? total_product_usage / selectedPortfolioItem.ad_budget_amount * 100
																	: 0
																return item && (
																	<li
																		className="relative flex items-center gap-x-3 py-6 cursor-pointer hover:bg-gray-100/50 text-center text-gray-500 text-sm"
																		key={index}
																		onClick={() => {}}
																	>
																		<div className="relative flex-shrink-0 flex flex-col items-start justify-center gap-x-2 gap-y-2 w-[140px] pl-8">
																			{(() => {
																				const targetProductInventory = selectedPortfolioItem.prediction?.inventory?.find((inventory: any) => inventory.asin === item.asin)
																				const eligibilityAbnormality = targetProductInventory
																					? targetProductInventory.estimated_inventory_state === "DANGER"
																					: false
																				return (
																					item.eligibility_status === "INELIGIBLE"
																						? <div className={cn(
																								"absolute pl-4 left-3.5 -top-4 flex items-center gap-x-1 text-[10px] font-semibold text-red-400 rounded-md",
																							)}>
																								<ExclamationTriangleIcon className="h-3 w-3" />
																								{tos("detailModal.optSet.budgetTab.table.content.statusAlert.abnormal")}
																							</div>
																						: eligibilityAbnormality
																							? <div className={cn(
																									"absolute pl-4 left-3.5 -top-4 flex items-center gap-x-1 text-[10px] font-semibold text-orange-400 rounded-md",
																								)}>
																								<ExclamationTriangleIcon className="h-3 w-3" />
																								{tos("detailModal.optSet.budgetTab.table.content.statusAlert.warning")}
																							</div>
																							: ""
																				)
																			})()}
																			<ProductStatus productItem={item} />
																		</div>
																		{productListItemCard(item, "light")}
																		<div className="flex-shrink-0 w-[180px] pr-6">
																			<div className="flex items-start justify-center w-full gap-x-3">
																				<div className="">
																					<div className="flex-shrink-0 text-xs text-gray-500 font-semibold">
																						{currencyFormat.format(total_product_usage || 0)}
																					</div>
																					<div
																						className={cn(
																							"mt-1 flex items-center justify-center gap-x-1",
																							"text-blue-400"
																						)}
																					>
																						<ArrowDownCircleIcon
																							className={cn(
																								"h-4 w-4 inline-block",
																							)}
																						/>
																						<span className="text-xs font-semibold">
																							{total_product_percent.toFixed(2)}%
																						</span>
																					</div>
																				</div>
																			</div>
																		</div>
																	</li>
																)
															})
													})()}
												</ul>
											</div>
										</div>
									</div>
								</TabPanel>
							</TabPanels>
						</TabGroup>
					</div>
				</div>
			</div>
			<div className="flex flex-col w-2/5 h-full gap-y-4 relative">
				<div className="relative flex flex-col w-full h-1/2 rounded-lg bg-white p-6 overflow-hidden">
					<div className="flex-shrink-0 flex items-center justify-between">
						<div className="flex items-center gap-x-2 text-gray-500 text-lg font-bold">
							<span>Alerts</span>
							{(() => {
								const alerts = [{id: 1}, {id: 2}, {id: 3}]
								return (alerts && alerts.length > 0
									? (
										<div className="flex items-center justify-center text-xs font-semibold text-orange-500 w-5 h-5 rounded-full bg-orange-100">
											{alerts.length}
										</div>
									)
									: ""
								)
							})()}
						</div>
						<Link href="/dashboard/alerts" className="text-blue-500 hover:text-blue-700 text-xs underline">
							View all
						</Link>
					</div>
					<div className="grow relative w-full min-h-0 mt-2 overflow-y-scroll">
						{ true
							? <div className="size-full space-y-3">
									{[{id: 1}, {id: 2}, {id: 3}].map((item) => {
										return (
											<div key={item.id} className="group relative flex-shrink-0 w-full rounded-md border border-gray-100 hover:border-orange-500 cursor-pointer overflow-hidden">
												<div className="absolute inset-y-0 left-0 w-1 h-full bg-orange-500"></div>
												<div className="grow relative w-full px-4 gap-x-2 divide-y divide-gray-100">
													<div className="flex items-center justify-between gap-x-2 py-2">
														<div className="text-sm text-gray-500 font-semibold">
															Collecting your data
														</div>
														<button className="flex items-center justify-center py-1 text-gray-400 group-hover:text-orange-500">
															<ArrowRight	className="h-4 w-4" />
														</button>
													</div>
													<div className="py-2 text-xs text-gray-500">
														<span><EMAIL> has been connected.</span>
														<span className="text-gray-400 text-[10px]">&nbsp;&#183;&nbsp;8 hours ago</span>
													</div>
												</div>
											</div>
										)
									})}
								</div>
							: <div className="size-full flex items-center justify-center text-gray-400 text-sm">You have no alerts.</div>
						}
					</div>
				</div>
				<div className="relative flex flex-col w-full h-1/2 rounded-lg bg-white p-6 overflow-hidden">
					<div className="flex-shrink-0 flex items-center justify-between">
						<div className="flex items-center gap-x-2 text-gray-500 text-lg font-bold">
							<span>Abnormalities</span>
							{(() => {
								const abnormalities = [{id: 1}, {id: 2}, {id: 3}]
								return (abnormalities && abnormalities.length > 0
									? (
										<div className="flex items-center justify-center text-xs font-semibold text-red-500 w-5 h-5 rounded-full bg-red-100">
											{abnormalities.length}
										</div>
									)
									: ""
								)
							})()}
						</div>
						<Link href="/dashboard/alerts" className="text-blue-500 hover:text-blue-700 text-xs underline">
							View all
						</Link>
					</div>
					<div className="grow relative w-full min-h-0 mt-2 overflow-y-scroll">
						{ true
							? <div className="size-full space-y-3">
									{[{id: 1}, {id: 2}, {id: 3}].map((item) => {
										return (
											<div key={item.id} className="group relative flex-shrink-0 w-full rounded-md border border-gray-100 hover:border-red-500 cursor-pointer overflow-hidden">
												<div className="absolute inset-y-0 left-0 w-1 h-full bg-red-500"></div>
												<div className="grow relative w-full px-4 gap-x-2 divide-y divide-gray-100">
													<div className="flex items-center justify-between gap-x-2 py-2">
														<div className="text-sm text-gray-500 font-semibold">
															Collecting your data
														</div>
														<button className="flex items-center justify-center py-1 text-gray-400 group-hover:text-red-500">
															<ArrowRight	className="h-4 w-4" />
														</button>
													</div>
													<div className="py-2 text-xs text-gray-500">
														<span><EMAIL> has been connected.</span>
														<span className="text-gray-400 text-[10px]">&nbsp;&#183;&nbsp;8 hours ago</span>
													</div>
												</div>
											</div>
										)
									})}
								</div>
							: <div className="size-full flex items-center justify-center text-gray-400 text-sm">You have no abnormalities.</div>
						}
					</div>
				</div>
			</div>
		</div>
		: ""
}
