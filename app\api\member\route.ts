import { ErrorResponse } from "@/app/api/_models";
import { getServerApiHostUrl } from "@/utils/host";
import { NextRequest, NextResponse } from "next/server";

export type MemberResponse = {
  cognito_sub: string,
  id: number,
  profiles: any[]
};

export async function GET(
  request: NextRequest
): Promise<NextResponse<MemberResponse | ErrorResponse>> {
  const accessToken = request.headers.get("Authorization");
  if (!accessToken) {
    return NextResponse.json(
      { message: "Authorization header is missing" },
      { status: 400 }
    );
  }

  const memberResponse = await fetch(
    `${await getServerApiHostUrl()}/api/member`,
    {
      method: "GET",
      headers: {
        "Authorization": `Bearer ${accessToken}`,
      },
      cache: "no-cache",
    }
  ).then((res) => res.json());

  return NextResponse.json(memberResponse, { status: 200 });
}
