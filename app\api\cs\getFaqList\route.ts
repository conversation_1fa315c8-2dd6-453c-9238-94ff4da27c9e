import { ErrorResponse } from "@/app/api/_models";
import { getServerApiHostUrl } from "@/utils/host";
import { NextRequest, NextResponse } from "next/server";

export type ListFaqsResponse = any;

export async function GET(
  request: NextRequest
): Promise<NextResponse<ListFaqsResponse | ErrorResponse>> {
  const accessToken = request.headers.get("Authorization");
  const locale = request.nextUrl.searchParams.get("locale");

  if (!accessToken) {
    return NextResponse.json(
      { message: "Authorization header is missing" },
      { status: 400 }
    );
  }
  const listFaqsResponse = await fetch(
    `${await getServerApiHostUrl()}/api/cs/faq?locale=${locale}`,
    {
      method: "GET",
      headers: {
        "Authorization": `Bearer ${accessToken}`,
        "Content-Type": "application/json",
      },
      cache: "no-cache",
    }
  ).then((res) => res.json());

  return NextResponse.json(listFaqsResponse, { status: 200 });
}
