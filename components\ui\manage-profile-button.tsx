'use client';

import { useTranslations } from "next-intl"
import { useSearchParams } from "next/navigation"
import Link from "next/link"
import { cn } from "@/utils/msc"
import { Button } from "./button"
import { UserPlusIcon } from "@heroicons/react/20/solid"

export function ManageProfileButton(props: React.ComponentPropsWithRef<typeof Button>) {
  const t = useTranslations('component')
  const searchParams = useSearchParams()
  const tab = searchParams.get('tab')
  return (
    <Link
      href={{
        pathname: '/dashboard',
        search: '?tab=manage-profile'
      }}
      className={cn(
        "inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",
        "hover:bg-accent hover:text-accent-foreground",
        "w-full h-6 py-4 px-4 justify-start text-gray-600 text-xs",
      )}
    >
      <UserPlusIcon
        className={cn(
          "flex-shrink-0 h-4 w-4 mr-2",
          tab === 'manage-profile' ? "font-bold" : ""
        )}
        aria-hidden="true"
      />
      <span
        className={cn(
          "flex-shrink-0 block",
          tab === 'manage-profile' ? "font-bold" : ""
        )}
      >
        {t("lnb.menu.manageAccounts")} 
      </span>
    </Link>
  )
}