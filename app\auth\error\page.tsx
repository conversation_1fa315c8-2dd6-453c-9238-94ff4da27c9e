"use client";

import { useEffect } from "react";
import { useSearchParams, useRouter } from "next/navigation";

export default function AuthErrorPage() {
  const searchParams = useSearchParams();
  const router = useRouter();
  const error = searchParams.get("error");

  useEffect(() => {
    if (error === "InvalidCheck") {
      router.replace("/auth/login");
    }
  }, [error, router]);

  return (
    <div>
      {error !== "InvalidCheck" ? (
        <div>
          <h1>Authentication Error</h1>
          <p>{error}</p>
        </div>
      ) : null}
    </div>
  );
}