// utils/currency.ts

import { useFormatter } from 'next-intl';

/**
 * 통화 포맷팅 훅 - next-intl의 useFormatter 사용
 * 자동으로 지역화된 통화 기호와 포맷팅을 제공
 */
export function useCurrencyFormatter() {
  const format = useFormatter();
  
  return {
    formatCurrency: (
      value: number | string, 
      currencyCode: string = 'USD',
      options?: { 
        maximumFractionDigits?: number;
        minimumFractionDigits?: number;
      }
    ): string => {
      const numValue = typeof value === 'string' ? parseFloat(value) : value;
      
      if (isNaN(numValue)) {
        return format.number(0, {
          style: 'currency',
          currency: currencyCode,
          maximumFractionDigits: options?.maximumFractionDigits ?? 2,
          minimumFractionDigits: options?.minimumFractionDigits ?? 2
        });
      }

      return format.number(numValue, {
        style: 'currency',
        currency: currencyCode,
        maximumFractionDigits: options?.maximumFractionDigits ?? 2,
        minimumFractionDigits: options?.minimumFractionDigits ?? 2
      });
    }
  };
}

/**
 * 통화 기호 없이 숫자만 포맷팅하는 함수
 */
export function formatNumber(value: number | string): string {
  const numValue = typeof value === 'string' ? parseFloat(value) : value;
  
  if (isNaN(numValue)) {
    return '0.00';
  }

  return numValue.toLocaleString(undefined, {
    maximumFractionDigits: 2,
    minimumFractionDigits: 2
  });
}

/**
 * 통화 코드에서 통화 기호만 추출하는 함수 (Plotly 차트 포맷팅용)
 */
export function getCurrencySymbol(currencyCode: string): string {
  // 입력값 검증
  if (!currencyCode || typeof currencyCode !== 'string') {
    return '$'
  }

  const upperCurrencyCode = currencyCode.toUpperCase()
  
  // Plotly.js에서 안정적으로 표시되는 통화 기호 매핑 (ASCII 기반 우선)
  const currencySymbolMap: Record<string, string> = {
    'USD': '$',
    'EUR': 'EUR',
    'GBP': 'GBP', 
    'JPY': 'JPY',
    'KRW': 'KRW',
    'CAD': 'CAD',
    'AUD': 'AUD',
    'CNY': 'CNY',
    'MXN': 'MXN',
    'SEK': 'SEK',
    'PLN': 'PLN',
    'TRY': 'TRY',
    'AED': 'AED',
    'INR': 'INR',
    'SGD': 'SGD',
    'BRL': 'BRL'
  }
  
  // 매핑 테이블에서 바로 반환 (Plotly.js 호환성 우선)
  const symbol = currencySymbolMap[upperCurrencyCode]
  if (symbol) {
    return symbol
  }
  
  return '$'
}

/**
 * Plotly.js에서 사용할 통화 기호 (유니코드 버전)
 * tickprefix로 사용할 때를 위한 함수
 */
export function getCurrencySymbolUnicode(currencyCode: string): string {
  // 입력값 검증
  if (!currencyCode || typeof currencyCode !== 'string') {
    return '$'
  }

  const upperCurrencyCode = currencyCode.toUpperCase()
  
  // 유니코드 통화 기호 매핑
  const currencySymbolMap: Record<string, string> = {
    'USD': '$',
    'EUR': '€',       // € 유니코드
    'GBP': '£',       // £ 유니코드
    'JPY': '¥',       // ¥ 유니코드
    'KRW': '₩',       // ₩ 유니코드
    'CAD': 'C$',
    'AUD': 'A$',
    'CNY': '¥',       // ¥ 유니코드
    'MXN': 'MX$',
    'SEK': 'kr',
    'PLN': 'zł',      // 폴란드 즐로티
    'TRY': '₺',       // 터키 리라
    'AED': 'د.إ',     // UAE 디르함
    'INR': '₹',       // 인도 루피
    'SGD': 'S$',
    'BRL': 'R$'
  }
  
  try {
    const formatter = new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: upperCurrencyCode,
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    })
    
    const parts = formatter.formatToParts(0)
    const currencyPart = parts.find(part => part.type === 'currency')
    const symbol = currencyPart?.value
    
    if (symbol && symbol !== upperCurrencyCode) {
      return symbol
    }
    
    // Intl API가 실패하면 매핑 테이블 사용
    const fallbackSymbol = currencySymbolMap[upperCurrencyCode]
    if (fallbackSymbol) {
      return fallbackSymbol
    }
    
    return '$'
    
  } catch (error) {
    // 에러 발생 시 매핑 테이블 사용
    const fallbackSymbol = currencySymbolMap[upperCurrencyCode]
    if (fallbackSymbol) {
      return fallbackSymbol
    }
    
    return '$'
  }
}

/**
 * 마켓플레이스 정보를 기반으로 통화 코드를 추출하는 함수
 */
export function getCurrencyCodeFromMarketplace(marketplace: any): string {
  if (!marketplace) {
    return 'USD'; // 기본값
  }

  // 먼저 default_currency_code가 있으면 그것을 사용
  if (marketplace.default_currency_code) {
    return marketplace.default_currency_code;
  }

  // 마켓플레이스 ID를 기반으로 통화 코드 결정
  const marketplaceId = marketplace.marketplace_id;
  
  switch (marketplaceId) {
    case 'ATVPDKIKX0DER': // US
      return 'USD';
    case 'A2EUQ1WTGCTBG2': // CA
      return 'CAD';
    case 'A1AM78C64UM0Y8': // MX
      return 'MXN';
    case 'A1VC38T7YXB528': // JP
      return 'JPY';
    case 'AAHKV2X7AFYLW': // CN
      return 'CNY';
    case 'A1F83G8C2ARO7P': // UK
      return 'GBP';
    case 'A1PA6795UKMFR9': // DE
      return 'EUR';
    case 'A13V1IB3VIYZZH': // FR
      return 'EUR';
    case 'APJ6JRA9NG5V4': // IT
      return 'EUR';
    case 'A1RKKUPIHCS9HS': // ES
      return 'EUR';
    case 'A1805IZSGTT6HS': // NL
      return 'EUR';
    case 'AMEN7PMS3EDWL': // BE
      return 'EUR';
    case 'A2NODRKZP88ZB9': // SE
      return 'SEK';
    case 'A1C3SOZRARQ6R3': // PL
      return 'PLN';
    case 'A33AVAJ2PDY3EV': // TR
      return 'TRY';
    case 'A2VIGQ35RCS4UG': // AE
      return 'AED';
    case 'A1AT7YVPFBWXBL': // IN
      return 'INR';
    case 'A19VAU5U5O7RUS': // SG
      return 'SGD';
    case 'A39IBJ37TRP1C6': // AU
      return 'AUD';
    case 'A2Q3Y263D00KWC': // BR
      return 'BRL';
    default:
      return 'USD'; // 기본값
  }
}
