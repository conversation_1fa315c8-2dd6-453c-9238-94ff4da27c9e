import { ErrorResponse } from "@/app/api/_models";
import { getServerApiHostUrl } from "@/utils/host";
import { NextRequest, NextResponse } from "next/server";

export type ListAsinHistoryResponse = any[];

export async function GET(
  request: NextRequest
): Promise<NextResponse<ListAsinHistoryResponse | ErrorResponse>> {
  const accessToken = request.headers.get("Authorization");
  if (!accessToken) {
    return NextResponse.json(
      { message: "Authorization header is missing" },
      { status: 400 }
    );
  }
  const optimizationId = request.nextUrl.searchParams.get("optimization_id")
  if (!optimizationId) {
    return NextResponse.json(
      { message: "optimization_id query is missing" },
      { status: 400 }
    );
  }
	const asin = request.nextUrl.searchParams.get("asin")
  if (!asin) {
    return NextResponse.json(
      { message: "asin query is missing" },
      { status: 400 }
    );
  }
  const startDate = request.nextUrl.searchParams.get("start_date")
  if (!startDate) {
    return NextResponse.json(
      { message: "start_date query is missing" },
      { status: 400 }
    );
  }
	const endDate = request.nextUrl.searchParams.get("end_date")
  if (!endDate) {
    return NextResponse.json(
      { message: "end_date query is missing" },
      { status: 400 }
    );
  }
	const type = request.nextUrl.searchParams.get("type")
  const listAsinHistoryResponse = await fetch(
    `${await getServerApiHostUrl()}/api/optimization/list_asin_history?optimization_id=${optimizationId}&asin=${asin}&start_date=${startDate}&end_date=${endDate}&type=${type}`,
    {
      method: "GET",
      headers: {
        "Authorization": `Bearer ${accessToken}`,
        "Content-Type": "application/json",
      },
      cache: "no-cache",
    }
  ).then((res) => res.json());

  return NextResponse.json(listAsinHistoryResponse, { status: 200 });
}
