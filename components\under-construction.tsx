"use client"

import Image from "next/image"
import Link from "next/link"
import { useTranslations } from "next-intl"
import { ArrowRightIcon, CheckIcon, ExclamationTriangleIcon } from "@heroicons/react/20/solid"
import { useCallback, useEffect, useRef, useState } from "react"
import CustomLink from "@/components/custom-link"
import { cn } from "@/utils/msc"
import ShiftingCountdown from "../components/ui/shifting-countdown"

export default function Index() {
  return (
    <div className="relative">
      <div className="w-full text-[#232F3F]">
        <div
          className="relative w-full h-screen bg-center bg-cover"
          style={{ backgroundImage: "url(/ui/footer-section-bg.jpg)" }}
        >
          <div className="absolute inset-0 w-full h-full flex items-center justify-center">
            <div className="px-8">
              <Image
                src="/logo/optapex-logo-white.svg"
                alt="Optapex Logo"
                className="w-auto h-14 md:h-16 mx-auto"
                style={{
                  filter: "drop-shadow(rgba(255, 255, 255, 0.8) 0px 0px 4px)"
                }}
                width={459}
                height={80}
              />
              <div
                className="hidden sm:flex w-[500px] h-auto mx-auto mt-8"
                style={{
                  flexDirection: "column",
                  boxShadow: "0 0 10px rgba(0, 0, 0, 0.4)",
                  borderRadius: "5px",
                }}>
                <div
                  style={{
                    height: "35px",
                    background: "rgb(244,244,244)",
                    borderBottom: "0.5px solid rgba(162,163,164,.4)",
                    borderTopLeftRadius: "5px",
                    borderTopRightRadius: "5px",
                    display: "flex",
                    alignItems: "center",
                    position: "relative",
                  }}>
                  <div
                    style={{
                      display: "flex",
                      alignItems: "center",
                      zIndex: 9,
                    }}>
                    <div
                      style={{
                        width: "12px",
                        height: "12px",
                        marginLeft: "6px",
                        borderRadius: "50%",

                        background: "rgb(231,69,70)",
                        border: "0.5px solid rgb(208,67,67)",
                      }}></div>
                    <div
                      style={{
                        width: "12px",
                        height: "12px",
                        marginLeft: "6px",
                        borderRadius: "50%",

                        background: "rgb(246,180,39)",
                        border: "0.5px solid rgb(212,154,58)",
                      }}></div>
                    <div
                      style={{
                        width: "12px",
                        height: "12px",
                        marginLeft: "6px",
                        borderRadius: "50%",

                        background: "rgb(86,202,53)",
                        border: "0.5px solid rgb(169,169,169)",
                      }}></div>
                  </div>
                  <div className="absolute w-full flex items-center justify-center gap-x-1 text-base text-gray-500">
                    <ExclamationTriangleIcon className="mt-1 w-5 h-5 text-orange-500" />
                    system upgrade
                  </div>
                </div>
                <div
                  className="flex flex-col gap-y-6 items-center justify-center p-6 "
                  style={{
                    height: "100%",
                    background: "rgb(231,231,231)",
                    borderBottomLeftRadius: "5px",
                    borderBottomRightRadius: "5px",
                    lineHeight: "300px",
                    textAlign: "center",
                  }}>
                  <ShiftingCountdown />
                </div>
              </div>
              <div className="mt-6 md:mt-8 text-xl text-center text-white">
                Optapex is currently under system <br className="hidden sm:block md:hidden"/>upgrade to serve you better. <br className="hidden sm:block"/>Your account and data are safe and <br className="hidden sm:block md:hidden"/>will be available shortly.
              </div>
            </div>
          </div>
        </div>
      </div>
      {/* footer section */}
      <div
        className="hidden relative w-full pt-[100px] pb-[360px] px-4 bg-center bg-cover"
        style={{ backgroundImage: "url(/ui/footer-section-bg.jpg)" }}
      >
        <div className="max-w-5xl px-4 sm:px-6 mx-auto text-white w-full h-full flex items-center justify-center">
          <div className="px-4">
            <h1 className="mt-4 text-4xl md:text-6xl font-semibold !leading-tight text-center">
              LG&apos;s Proven Technology
            </h1>
            <div className="mt-8 text-base sm:text-xl text-center">
              Harness the power of LG&apos;s proven technology - LG Optapex leverages LG&apos;s world-class expertise in Big Data, AI, and Decision Algorithm to supercharge your commerce business. Join us and experience the unparalleled precision and proven excellence of LG&apos;s technology.
            </div>
            <Link href="/contact-us">
              <Image
                src="/ui/earlybird-cta.svg"
                alt="earlybird cta"
                className="w-auto h-[60px] mx-auto mt-8 hover:opacity-80"
                width={323}
                height={78}
              />
            </Link>
          </div>
        </div>
        <footer
          className="absolute inset-x-0 bottom-0 w-full px-4 sm:px-6 bg-black/40 border-t border-white/10 text-gray-400 overflow-hidden"
        >
          <div className="w-full max-w-4xl flex flex-col px-4 pt-10 mx-auto text-sm">
            <div className="flex-col md:flex-row gap-4 flex items-center justify-between">
              <div className="flex items-center gap-x-4 md:block">
                <Image
                  src="/logo/optapex-logo-white.svg"
                  alt="Optapex Logo"
                  className="w-auto h-4"
                  width={157}
                  height={27}
                />
                <div className="md:mt-1">
                  <a href="mailto:<EMAIL>"><EMAIL></a>
                </div>
              </div>
              <div className="flex items-center sm:justify-center gap-x-2">
                <CustomLink className="border border-gray-400 hover:border-gray-200 text-gray-400 hover:text-gray-200 rounded-full py-2 px-8 mb-2 sm:mb-4" href="/eula">EULA</CustomLink>
                <CustomLink className="border border-gray-400 hover:border-gray-200 text-gray-400 hover:text-gray-200 rounded-full py-2 px-8 mb-2 sm:mb-4" href="/privacy-policy">Policy</CustomLink>
              </div>
            </div>
            <div className="mt-2 md:mt-0 flex-col md:flex-row flex items-center justify-between">
              <div className="">Copyright 2024. LG CNS Co., Ltd. All rights reserved.</div>
              <div className="flex items-center sm:justify-center gap-x-2 sm:ml-auto text-white">
                Powered by
                <Image
                  src="/lg-cns.svg"
                  alt="LG CNS Logo"
                  width={100}
                  height={24}
                  priority
                />
              </div>
            </div>
          </div>
        </footer>
      </div>
    </div>
  )
}