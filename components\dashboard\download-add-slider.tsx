"use client"

import { api } from "@/utils/api"
import { useSession } from "next-auth/react"
import { forwardRef, Fragment, useEffect, useState } from 'react'
import DatePicker, { registerLocale } from "react-datepicker"
import { ko, zhCN } from "date-fns/locale"
import { Listbox, RadioGroup, Transition, Popover } from '@headlessui/react'
import { ProfileOption } from "@/components/dashboard/profile-select"
import { MarketplaceOption } from "@/components/dashboard/marketplace-select"
import { cn, formatLocalDateAsISO } from "@/utils/msc"
import { CloudArrowUpIcon, ChevronUpDownIcon, XMarkIcon, InformationCircleIcon, QuestionMarkCircleIcon, RocketLaunchIcon } from "@heroicons/react/20/solid"
import { Check } from "@/components/ui/check"
import { useLocale, useTranslations } from 'next-intl'
registerLocale('ko', ko)
registerLocale('cn', zhCN)

interface DownloadAddSliderProps {
  selectedProfile: ProfileOption;
  selectedMarketplace: MarketplaceOption;
  handleAddCloseClick: (refresh: boolean) => void;
}
const stepOptions = [
  {
    type: 1,
    name: 'addModal.reportType.label'
  },
  {
    type: 2,
    name: 'addModal.reportName.label'
  },
  {
    type: 3,
    name: 'addModal.advancedSetting.label'
  }
]
const reportTypeOptions = [
  {
    type: 'OPTAPEX',
    name: 'addModal.reportType.options.optapex.label'
  },
  {
    type: 'SP',
    name: 'addModal.reportType.options.sp.label'
  },
  {
    type: 'VP',
    name: 'addModal.reportType.options.vp.label'
  },
  {
    type: 'AD',
    name: 'addModal.reportType.options.ad.label'
  },
]
const reportCategoryOptions = [
  {
    id: 'LONG_TERM_REPORT',
    name: 'Consolidated Report',
    type: 'OPTAPEX'
  },
  {
    id: 'analytics_report',
    name: 'Analytics Report',
    type: 'SP'
  }, 
  {
    id: 'fba_sales_report',
    name: 'FBA Sales Report',
    type: 'SP'
  },
  {
    id: 'fba_inventory_report',
    name: 'FBA Inventory Report',
    type: 'SP'
  },
  {
    id: 'fba_payment_report',
    name: 'FBA Payment Report',
    type: 'SP'
  },
  {
    id: 'fba_concessions_report',
    name: 'FBA Concessions Report',
    type: 'SP'
  },
  {
    id: 'fba_removal_report',
    name: 'FBA Removal Report',
    type: 'SP'
  },
  {
    id: 'fba_subscribe_and_save_report',
    name: 'FBA Subscribe and Save Report',
    type: 'SP'
  },
  {
    id: 'search_term_report',
    name: 'Search Term Report',
    type: 'AD'
  },
  {
    id: 'targeting_report',
    name: 'Targeting Report',
    type: 'AD'
  },
  {
    id: 'advertised_product_report',
    name: 'Advertised Product Report',
    type: 'AD'
  },
  {
    id: 'vendor_retail_analytics_report',
    name: 'Vendor Retail Analytics Report',
    type: 'VP'
  }
]
const reportNameOptions = [
  // OptApex Consolidated Reports
  {
    id: 'DAILY_CONSOLIDATED_SELLER_REPORT',
    name: 'Consolidated Report (Daily)',
    category: 'LONG_TERM_REPORT',
    type: 'OPTAPEX'
  },
  {
    id: 'HOURLY_CONSOLIDATED_SELLER_REPORT',
    name: 'Consolidated Report (Hourly)',
    category: 'LONG_TERM_REPORT',
    type: 'OPTAPEX'
  },
  {
    id: 'DAILY_CONSOLIDATED_VENDOR_REPORT',
    name: 'Consolidated Report (Daily)',
    category: 'LONG_TERM_REPORT',
    type: 'OPTAPEX'
  },
  {
    id: 'HOURLY_CONSOLIDATED_VENDOR_REPORT',
    name: 'Consolidated Report (Hourly)',
    category: 'LONG_TERM_REPORT',
    type: 'OPTAPEX'
  },
  // SP/AD/VP existing options
  {
    id: 'GET_SALES_AND_TRAFFIC_REPORT',
    name: 'Sales and Traffic Business Report',
    category: 'analytics_report',
    type: 'SP'
  },      
  {
    id: 'GET_AMAZON_FULFILLED_SHIPMENTS_DATA_GENERAL',
    name: 'FBA Amazon Fulfilled Shipments Report',
    category: 'fba_sales_report',
    type: 'SP'
  },
  {
    id: 'GET_FLAT_FILE_ALL_ORDERS_DATA_BY_ORDER_DATE_GENERAL',
    name: 'Flat File All Orders Report by Order Date',
    category: 'fba_sales_report',
    type: 'SP'
  },
  {
    id: 'GET_LEDGER_SUMMARY_VIEW_DATA',
    name: 'Inventory Ledger Report - Summary View',
    category: 'fba_inventory_report',
    type: 'SP'
  },
  {
    id: 'GET_LEDGER_DETAIL_VIEW_DATA',
    name: 'Inventory Ledger Report - Detailed View',
    category: 'fba_inventory_report',
    type: 'SP'
  },
  {
    id: 'GET_FBA_REIMBURSEMENTS_DATA',
    name: 'FBA Reimbursements Report',
    category: 'fba_payment_report',
    type: 'SP'
  },
  {
    id: 'GET_FBA_FULFILLMENT_CUSTOMER_RETURNS_DATA',
    name: 'FBA Returns Report',
    category: 'fba_concessions_report',
    type: 'SP'
  },
  {
    id: 'GET_FBA_FULFILLMENT_REMOVAL_ORDER_DETAIL_DATA',
    name: 'FBA Removal Order Detail Report',
    category: 'fba_removal_report',
    type: 'SP'
  },
  {
    id: 'GET_FBA_SNS_PERFORMANCE_DATA',
    name: 'Subscribe and Save Performance Report',
    category: 'fba_subscribe_and_save_report',
    type: 'SP'
  },
  {
    id: 'spSearchTerm',
    name: 'Sponsored Product Search Term Report',
    category: 'search_term_report',
    type: 'AD',
    groubBy: 'searchTerm'
  },
  {
    id: 'spTargeting',
    name: 'Sponsored Product Targeting Report',
    category: 'targeting_report',
    type: 'AD',
    groubBy: 'targeting'
  },
  {
    id: 'sdTargeting',
    name: 'Sponsored Display Targeting Report',
    category: 'targeting_report',
    type: 'AD',
    groubBy: 'targeting'
  },
  {
    id: 'sdTargeting_matched',
    name: 'Sponsored Display Matched Targeting Report',
    category: 'targeting_report',
    type: 'AD',
    groubBy: 'matchedTarget'
  },
  {
    id: 'spAdvertisedProduct',
    name: 'Sponsored Product Advertised Product Report',
    category: 'advertised_product_report',
    type: 'AD',
    groubBy: 'advertiser'
  },
  {
    id: 'sdAdvertisedProduct',
    name: 'Sponsored Display Advertised Product Report',
    category: 'advertised_product_report',
    type: 'AD',
    groubBy: 'advertiser'
  },
  {
    id: 'GET_VENDOR_SALES_REPORT',
    name: 'Vendor Sales Report',
    category: 'vendor_retail_analytics_report',
    type: 'VP'
  },
  {
    id: 'GET_VENDOR_TRAFFIC_REPORT',
    name: 'Vendor Traffic Report',
    category: 'vendor_retail_analytics_report',
    type: 'VP'
  },
  {
    id: 'GET_VENDOR_INVENTORY_REPORT',
    name: 'Vendor Inventory Report',
    category: 'vendor_retail_analytics_report',
    type: 'VP'
  },
  {
    id: 'GET_VENDOR_REAL_TIME_SALES_REPORT',
    name: 'Rapid Retail Analytics Sales Report',
    category: 'vendor_retail_analytics_report',
    type: 'VP'
  },
  {
    id: 'GET_VENDOR_REAL_TIME_INVENTORY_REPORT',
    name: 'Rapid Retail Analytics Inventory Report',
    category: 'vendor_retail_analytics_report',
    type: 'VP'
  },
  {
    id: 'GET_VENDOR_REAL_TIME_TRAFFIC_REPORT',
    name: 'Rapid Retail Analytics Traffic Report',
    category: 'vendor_retail_analytics_report',
    type: 'VP'
  },
  {
    id: 'GET_VENDOR_NET_PURE_PRODUCT_MARGIN_REPORT',
    name: 'Net Pure Product Margin Report',
    category: 'vendor_retail_analytics_report',
    type: 'VP'
  }
]
/* advanced filter options */
const dateAggregationFilterOptions = [
  {
    type: 'DAILY',
    name: 'addModal.advancedSetting.filter.dateAggregationFilter.options.daily'
  },
  {
    type: 'WEEKLY',
    name: 'addModal.advancedSetting.filter.dateAggregationFilter.options.weekly'
  },
  {
    type: 'MONTHLY',
    name: 'addModal.advancedSetting.filter.dateAggregationFilter.options.monthly'
  }
]
const asinAggregationFilterOptions = [
  {
    type: 'CHILD',
    name: 'addModal.advancedSetting.filter.asinAggregationFilter.options.child'
  },  
]
const locationAggregationFilterOptions = [
  {
    type: 'Country',
    name: 'addModal.advancedSetting.filter.locationAggregationFilter.options.country'
  },
  {
    type: 'FC',
    name: 'addModal.advancedSetting.filter.locationAggregationFilter.options.fc'
  }
]
const eventTypeFilterOptions = [
  {
    type: 'Adjustments',
    name: 'addModal.advancedSetting.filter.eventTypeFilter.options.adjustments'
  },
  {
    type: 'CustomerReturns',
    name: 'addModal.advancedSetting.filter.eventTypeFilter.options.customerReturns'
  },
  {
    type: 'Receipts',
    name: 'addModal.advancedSetting.filter.eventTypeFilter.options.receipts'
  },
  {
    type: 'Shipments',
    name: 'addModal.advancedSetting.filter.eventTypeFilter.options.shipments'
  },
  {
    type: 'VendorReturns',
    name: 'addModal.advancedSetting.filter.eventTypeFilter.options.vendorReturns'
  },
  {
    type: 'WhseTransfers',
    name: 'addModal.advancedSetting.filter.eventTypeFilter.options.whseTransfers'
  }
]
const timeUnitFilterOptions = [
  {
    type: 'DAILY',
    name: 'addModal.advancedSetting.filter.timeUnitFilter.options.daily'
  },
  {
    type: 'SUMMARY',
    name: 'addModal.advancedSetting.filter.timeUnitFilter.options.summary'
  }
]
const groupByFilterOptions = [
  {
    type: 'searchTerm',
    name: 'addModal.advancedSetting.filter.groupByFilter.options.searchTerm'
  },  
  {
    type: 'targeting',
    name: 'addModal.advancedSetting.filter.groupByFilter.options.targeting'
  },
  {
    type: 'matchedTarget',
    name: 'addModal.advancedSetting.filter.groupByFilter.options.matchedTarget'
  },
  {
    type: 'advertiser',
    name: 'addModal.advancedSetting.filter.groupByFilter.options.advertiser'
  }
]

/* VP report options */
const reportPeriodOptions = [
  {
    type: 'DAY',
    name: 'addModal.advancedSetting.filter.reportPeriodFilter.options.day',
    maxFromNow: 1460, // max days from now
    maxRange: 15 // max range in days
  },
  {
    type: 'WEEK',
    name: 'addModal.advancedSetting.filter.reportPeriodFilter.options.week',
    maxFromNow: 49, // 7 weeks in days
    maxRange: 49 // 7 weeks in days
  },
  {
    type: 'MONTH',
    name: 'addModal.advancedSetting.filter.reportPeriodFilter.options.month',
    maxFromNow: 1095, // ~36 months in days
    maxRange: 456 // ~15 months in days
  },
  {
    type: 'QUARTER',
    name: 'addModal.advancedSetting.filter.reportPeriodFilter.options.quarter',
    maxFromNow: 730, // ~8 quarters in days
    maxRange: 365 // ~4 quarters in days
  },
  {
    type: 'YEAR',
    name: 'addModal.advancedSetting.filter.reportPeriodFilter.options.year',
    maxFromNow: 1095, // 3 years in days
    maxRange: 730 // 2 years in days
  }
]

const distributorViewOptions = [
  {
    type: 'MANUFACTURING',
    name: 'addModal.advancedSetting.filter.distributorViewFilter.options.manufacturing'
  },
  {
    type: 'SOURCING',
    name: 'addModal.advancedSetting.filter.distributorViewFilter.options.sourcing'
  }
]

const sellingProgramOptions = [
  {
    type: 'RETAIL',
    name: 'addModal.advancedSetting.filter.sellingProgramFilter.options.retail'
  },
  {
    type: 'BUSINESS',
    name: 'addModal.advancedSetting.filter.sellingProgramFilter.options.business'
  },
  {
    type: 'FRESH',
    name: 'addModal.advancedSetting.filter.sellingProgramFilter.options.fresh'
  }
]

// OptApex specific options
const insightLevelOptions = [
  { type: 'SIMPLE', name: 'Simple' },
  { type: 'COMPREHENSIVE', name: 'Comprehensive' }
]
const aggregationLevelOptions = [
  { type: 'DAILY', name: 'Daily' },
  { type: 'SUMMARY', name: 'Summary' }
]

export default function DownloadAddSlider({
  selectedProfile,
  selectedMarketplace,
  handleAddCloseClick
}: DownloadAddSliderProps) {
  const t = useTranslations("component")
  const td = useTranslations('downloads')
  const locale = useLocale()
  const { data: session, status } = useSession()
  const [isLoading, setIsLoading] = useState(false)
  const [isSaveLoading, setIsSaveLoading] = useState(false)
  const [selectedStepOption, setSelectedStepOption] = useState(stepOptions[0])
  const [selectedReportTypeOption, setSelectedReportTypeOption] = useState(reportTypeOptions[0])
  const [selectedReportCategoryOption, setSelectedReportCategoryOption] = useState(reportCategoryOptions[0])
  const [selectedReportNameOption, setSelectedReportNameOption] = useState(reportNameOptions[0])
  const [reportTitle, setReportTitle] = useState('');
  /* advanced filter options */
  const [selectedDateAggregationFilterOption, setSelectedDateAggregationFilterOption] = useState(dateAggregationFilterOptions[0])
  const [selectedAsinAggregationFilterOption, setSelectedAsinAggregationFilterOption] = useState(asinAggregationFilterOptions[0])
  const [selectedLocationAggregationFilterOption, setSelectedLocationAggregationFilterOption] = useState(locationAggregationFilterOptions[0])
  const [selectedEventTypeFilterOption, setSelectedEventTypeFilterOption] = useState(eventTypeFilterOptions[0])
  const [selectedTimeUnitFilterOption, setSelectedTimeUnitFilterOption] = useState(timeUnitFilterOptions[0])
  const [selectedGroupByFilterOption, setSelectedGroupByFilterOption] = useState(groupByFilterOptions[0]);
  const [selectedReportPeriodOption, setSelectedReportPeriodOption] = useState(reportPeriodOptions[0])
  const [selectedDistributorViewOption, setSelectedDistributorViewOption] = useState(distributorViewOptions[0])
  const [selectedSellingProgramOption, setSelectedSellingProgramOption] = useState(sellingProgramOptions[0])
  const [selectedInsightLevelOption, setSelectedInsightLevelOption] = useState(insightLevelOptions[0])
  const [selectedAggregationLevelOption, setSelectedAggregationLevelOption] = useState(aggregationLevelOptions[0])
  
  const getMinDate = () => {
    const { subscription_start_date, subscription_active_date, created_datetime } = selectedMarketplace;

    const dateStr = subscription_start_date || subscription_active_date || created_datetime;

    if (dateStr) {
      const date = new Date(dateStr);
      date.setDate(date.getDate() - 90);
      return date;
    }

    return new Date('2023-01-01');
  };

  const [dateRange, setDateRange] = useState<Date[]>([
    new Date(new Date().getFullYear(), new Date().getMonth(), 1), 
    (() => {
      const maxDate = new Date();
      maxDate.setDate(maxDate.getDate());
      return maxDate;
    })()
  ])
  const [startDate, endDate] = dateRange
  
  const [dateValidationError, setDateValidationError] = useState<string | null>(null);
  
  const DateRangeInput = forwardRef(({ value, onClick, minWidth }: { value: string, onClick: () => void, minWidth: string }, ref) => (
    // @ts-ignore
    <button className="w-auto mt-1 bg-white overflow-hidden cursor-pointer text-sm text-gray-600 focus:outline-none" onClick={onClick} ref={ref}>
      <div className={`min-w-[${minWidth}px] inline-flex py-2 px-4 rounded-lg bg-white hover:bg-gray-100/20 border border-gray-100 hover:border0-gray-200 text-gray-500`}>
        {value}
      </div>
    </button>
  ));
  DateRangeInput.displayName = 'DateRangeInput'


  const formatTimePeriod = (days: number, periodType?: string): string => {
    if (periodType === 'DAY' || !periodType) {
      return `${days}${td("addModal.error.timeUnits.days")}`;
    } else if (periodType === 'WEEK') {
      const weeks = Math.ceil(days / 7);
      return `${weeks}${td("addModal.error.timeUnits.weeks")}`;
    } else if (periodType === 'MONTH') {
      const months = Math.ceil(days / 30);
      return `${months}${td("addModal.error.timeUnits.months")}`;
    } else if (periodType === 'QUARTER') {
      const quarters = Math.ceil(days / 90);
      return `${quarters}${td("addModal.error.timeUnits.quarters")}`;
    } else if (periodType === 'YEAR') {
      const years = Math.ceil(days / 365);
      return `${years}${td("addModal.error.timeUnits.years")}`;
    }
    return `${days}${td("addModal.error.timeUnits.days")}`;
  };

  const checkDateRangeValidity = (): boolean => {
    // Validate for vendor traffic and inventory reports
    if (selectedReportNameOption.id === "GET_VENDOR_SALES_REPORT" || 
        selectedReportNameOption.id === "GET_VENDOR_TRAFFIC_REPORT" || 
        selectedReportNameOption.id === "GET_VENDOR_INVENTORY_REPORT"||
        selectedReportNameOption.id === "GET_VENDOR_NET_PURE_PRODUCT_MARGIN_REPORT") {
      if (!startDate || !endDate) {
        setDateValidationError(td("addModal.error.dateRangeRequired"));
        return false;
      }
      
      const today = new Date();
      const selectedPeriod = reportPeriodOptions.find(option => option.type === selectedReportPeriodOption.type);
      
      if (!selectedPeriod) {
        return true;
      }
      
      // Check if start date is too far in the past
      const maxPastDate = new Date();
      maxPastDate.setDate(today.getDate() - selectedPeriod.maxFromNow);
      if (startDate < maxPastDate) {
        setDateValidationError(td("addModal.error.startDateTooFar") + ` (${td("addModal.error.recent")} ${formatTimePeriod(selectedPeriod.maxFromNow, selectedPeriod.type)})`);
        return false;
      }
      
      // Check if date range is too wide
      const diffTime = Math.abs(endDate.getTime() - startDate.getTime());
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
      if (diffDays > selectedPeriod.maxRange) {
        setDateValidationError(td("addModal.error.dateRangeTooWide") + ` (${td("addModal.error.maximum")} ${formatTimePeriod(selectedPeriod.maxRange, selectedPeriod.type)})`);
        return false;
      }
      
      setDateValidationError(null);
      return true;
    }
    
    // Validate for RealTime reports
    if (selectedReportNameOption.id.includes("REAL_TIME")) {
      if (!startDate || !endDate) {
        setDateValidationError(td("addModal.error.dateRangeRequired"));
        return false;
      }
      
      const today = new Date();
      
      // Different validation for RealTime Inventory vs other RealTime reports
      if (selectedReportNameOption.id === "GET_VENDOR_REAL_TIME_INVENTORY_REPORT") {
        // RealTime Inventory: 30 days lookback, 7 days max range
        const maxPastDate = new Date();
        maxPastDate.setDate(today.getDate() - 30);
        if (startDate < maxPastDate) {
          setDateValidationError(td("addModal.error.startDateTooFar") + ` (${td("addModal.error.recent")} ${formatTimePeriod(30)})`);
          return false;
        }
        
        const diffTime = Math.abs(endDate.getTime() - startDate.getTime());
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
        if (diffDays > 7) {
          setDateValidationError(td("addModal.error.dateRangeTooWide") + ` (${td("addModal.error.maximum")} ${formatTimePeriod(7)})`);
          return false;
        }
      } else {
        // Other RealTime reports: 28 days lookback, 14 days max range
        const maxPastDate = new Date();
        maxPastDate.setDate(today.getDate() - 28);
        if (startDate < maxPastDate) {
          setDateValidationError(td("addModal.error.startDateTooFar") + ` (${td("addModal.error.recent")} ${formatTimePeriod(28)})`);
          return false;
        }
        
        const diffTime = Math.abs(endDate.getTime() - startDate.getTime());
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
        if (diffDays > 14) {
          setDateValidationError(td("addModal.error.dateRangeTooWide") + ` (${td("addModal.error.maximum")} ${formatTimePeriod(14)})`);
          return false;
        }
      }
      
      setDateValidationError(null);
      return true;
    }
    
    setDateValidationError(null);
    return true;
  };

  const requestReportDownload = async () => {
    if (!(session?.user as any).access_token) {
      console.error('Access token is missing in the session.')
      return
    }
    
    // Validate date range for vendor reports
    if (!checkDateRangeValidity()) {
      return;
    }
    
    try {
      setIsSaveLoading(true)
      
      const formattedStartDate = startDate ? formatLocalDateAsISO(startDate) : null;
      const formattedEndDate = endDate ? formatLocalDateAsISO(endDate) : null;         
      
      const reportOptions: Record<string, string> = {};
      
      if (selectedReportNameOption.id === "GET_SALES_AND_TRAFFIC_REPORT") {
        reportOptions.asinGranularity = selectedAsinAggregationFilterOption.type;
      } else if (selectedReportNameOption.id === "GET_LEDGER_SUMMARY_VIEW_DATA") {
        reportOptions.aggregateByLocation = selectedLocationAggregationFilterOption.type;
        reportOptions.aggregatedByTimePeriod = selectedDateAggregationFilterOption.type;
      } else if (selectedReportNameOption.id === "GET_LEDGER_DETAIL_VIEW_DATA") {
        reportOptions.eventType = selectedEventTypeFilterOption.type;
      } else if (selectedReportNameOption.type === "VP" && !selectedReportNameOption.id.includes("REAL_TIME")) {
        reportOptions.reportPeriod = selectedReportPeriodOption.type;
        
        if (selectedReportNameOption.id === "GET_VENDOR_SALES_REPORT") {
          reportOptions.distributorView = selectedDistributorViewOption.type;
          reportOptions.sellingProgram = selectedSellingProgramOption.type;
        } else if (selectedReportNameOption.id === "GET_VENDOR_INVENTORY_REPORT") {
          reportOptions.distributorView = selectedDistributorViewOption.type;
          reportOptions.sellingProgram = selectedSellingProgramOption.type;
        }
      }

      const requestData: any = {
        seller_type: selectedReportNameOption.type === "VP" ? "SP" : selectedReportNameOption.type,
        account_id: selectedProfile.account_id,
        marketplace_id: selectedMarketplace.marketplace_id,
        report_type: selectedReportNameOption.type === "OPTAPEX" 
          ? selectedReportCategoryOption.id 
          : selectedReportNameOption.id === 'sdTargeting_matched' ? 'sdTargeting' : selectedReportNameOption.id,
        report_name: selectedReportNameOption.type === "OPTAPEX" 
          ? selectedReportNameOption.id 
          : selectedReportNameOption.name,
        report_title: reportTitle || selectedReportNameOption.name,
        report_options: reportOptions,
        start_date: formattedStartDate,
        end_date: formattedEndDate,
        group_by: selectedReportTypeOption.type === "AD"?  selectedGroupByFilterOption.type : "",
        time_unit: selectedReportTypeOption.type === "AD"?  selectedTimeUnitFilterOption.type : "",
      };

      if (selectedReportNameOption.type === 'OPTAPEX') {
        requestData.insight_level = selectedInsightLevelOption.type;
        requestData.aggregation_level = selectedAggregationLevelOption.type;
      }
      
      let requestReportDownloadResponse = await api.requestReportDownload(
        requestData,
        (session?.user as any).access_token
      );
      
      if (!requestReportDownloadResponse) {
        throw new Error('No response from server');
      }            
      
      handleAddCloseClick(true);
      
      return requestReportDownloadResponse;
    } catch (error) {
      console.error('Error requesting report download:', error);
      
      if (error instanceof Error) {
        console.error('Error message:', error.message);
        console.error('Error stack:', error.stack);
      } else {
        console.error('Error message: Failed to request report download: No response');
      }
      
      alert(td('addModal.error.requestFailed'));
     
      setIsSaveLoading(false);    

      throw error;
    }
  }

  const step1Label = () => {
    return (
      <div className="flex items-center gap-x-2 divide-x divide-gray-100 text-xs text-gray-400 font-normal">
        <div className="flex-shrink-0">{td(selectedReportTypeOption.name)}</div>
      </div>
    )
  }
  const step2Label = () => {
    return (
      <div className="flex items-center gap-x-2 divide-x divide-gray-100 text-xs text-gray-400 font-normal">
        <div className="flex-shrink-0">{selectedReportCategoryOption.name}</div>
        <div className="pl-2 flex-shrink-0">
          <span>{selectedReportNameOption.name}</span>
        </div>
      </div>
    )
  }
  const step3Label = () => {
    return (
      <div className="flex items-center gap-x-2 divide-x divide-gray-100 text-xs text-gray-400 font-normal">
        <div className="flex-shrink-0">{td(selectedReportTypeOption.name)}</div>
      </div>
    )
  }
  const step1Content = () => {
    return (
      <div className="py-3">
        <div className="w-full">
          <div className="text-gray-400 text-xs">
            {td("addModal.reportType.subLabel")} <span className="text-red-500 text-sm">*</span>
          </div>
          <RadioGroup className="w-full" value={selectedReportTypeOption} onChange={setSelectedReportTypeOption}>
            <div className="mt-1 space-y-2">
              {reportTypeOptions.filter(option => 
                selectedProfile.account_type === 'vendor' 
                  ? (option.type === 'VP' || option.type === 'AD' || option.type === 'OPTAPEX')
                  : (option.type === 'SP' || option.type === 'AD' || option.type === 'OPTAPEX')
              ).map((option) => (
                <RadioGroup.Option
                  key={option.name}
                  value={option}
                  className={({ active, checked }) =>
                    `${checked ? 'text-gray-500 border-blue-300' : 'border-gray-100 hover:border-blue-100 hover:bg-blue-100/40'}
                      peer relative flex w-full cursor-pointer rounded-lg px-3 py-4 bg-white border-2 focus:outline-none`
                  }
                >
                  {({ active, checked }) => (
                    <>
                      <div className="flex w-full items-center justify-between gap-x-4">
                        <div className="flex w-full items-center">
                          <div className="w-full text-xs">
                            <RadioGroup.Label as="p">
                              <div
                                className={`flex items-center text-sm font-semibold ${
                                  checked ? '' : 'peer-hover:text-gray-500 text-gray-400'
                                }`}
                              >
                                {td(option.name)}
                                {option.type === "OPTAPEX" && (
                                  <span
                                    className={`pl-3 flex items-center gap-x-1 text-xs font-bold ${
                                      checked ? 'text-red-400' : 'text-red-300'
                                    }`}
                                  >
                                    <RocketLaunchIcon className="h-4 w-4" />
                                    {td("addModal.reportType.options.optapex.featured")}
                                  </span>
                                )}
                              </div>
                              <div
                                className={`mt-2 text-xs  ${
                                  checked ? 'text-gray-400' : 'text-gray-300'
                                }`}
                              >
                                {option.type === "SP"
                                  ? td("addModal.reportType.options.sp.details")
                                  : option.type === "VP"
                                    ? td("addModal.reportType.options.vp.details")
                                    : option.type === "AD"
                                      ? td("addModal.reportType.options.ad.details")
                                      : td("addModal.reportType.options.optapex.details")
                                }
                              </div>
                            </RadioGroup.Label>
                          </div>
                        </div>
                        {checked
                          ? <div className="shrink-0 flex items-center justify-center w-5 h-5 bg-blue-100 rounded-full">
                              <Check className="w-4 h-4 stroke-blue-500" />
                            </div>
                          : <div className="shrink-0 w-5 h-5"></div>
                        }
                      </div>
                    </>
                  )}
                </RadioGroup.Option>
              ))}
            </div>
          </RadioGroup>
        </div>
      </div>
    )
  }
  const step2Content = () => {
    return (
      <div className="py-3">
        <div className="w-full">
          <div className="text-gray-400 text-xs">
            {td("addModal.reportName.category.subLabel")} <span className="text-red-500 text-sm">*</span>
          </div>
          <div
            className={cn(
              "relative w-full",
              "mt-1"
            )}
          >
            <Listbox value={selectedReportCategoryOption} onChange={setSelectedReportCategoryOption}>
              {({ open }) => (
              <div className="relative">
                <Listbox.Button
                  className={cn(
                    "group relative w-full h-9 py-2 pl-3 pr-6 cursor-pointer rounded-lg text-left focus:outline-none text-sm border border-gray-200 hover:border-gray-300",
                    open
                      ? "bg-gray-100"
                      : ""
                  )}
                >
                  <span className="block truncate text-gray-500 group-hover:text-gray-600">
                    {selectedReportCategoryOption.name}
                  </span>
                  <span className="pointer-events-none absolute right-0 inset-y-0 flex items-center px-2">
                    <ChevronUpDownIcon
                      className="h-5 w-5 text-gray-300 group-hover:text-gray-400"
                      aria-hidden="true"
                    />
                  </span>
                </Listbox.Button>
                <Transition
                  as={Fragment}
                  leave="transition ease-in duration-100"
                  leaveFrom="opacity-100"
                  leaveTo="opacity-0"
                >
                  <Listbox.Options
                    className={cn(
                      "absolute mt-1 max-h-60 w-full overflow-auto rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black/5 focus:outline-none sm:text-sm z-[1]",
                    )}
                  >
                    {reportCategoryOptions.filter(option => {
                      // 기본 타입 필터링
                      if (option.type !== selectedReportTypeOption.type) {
                        return false;
                      }
                      
                      // OptApex는 Consolidated 카테고리만 노출
                      if (selectedReportTypeOption.type === 'OPTAPEX') {
                        return option.id === 'LONG_TERM_REPORT';
                      }
                      
                      // account_type에 따른 추가 필터링
                      if (selectedProfile.account_type === 'vendor' && option.id === 'SELLER_LONG_TERM_REPORT') {
                        return false;
                      }
                      
                      if (selectedProfile.account_type !== 'vendor' && option.id === 'VENDOR_LONG_TERM_REPORT') {
                        return false;
                      }
                      
                      return true;
                    }).map((option, optionIdx) => (
                      <Listbox.Option
                        key={optionIdx}
                        className={({ active }) =>
                          `relative cursor-pointer select-none py-2 pl-4 pr-10 ${
                            active ? 'bg-blue-100/40 text-blue-900' : 'text-gray-600'
                          }`
                        }
                        value={option}
                      >
                        {({ selected }) => (
                          <>
                            <span
                              className={`block truncate ${
                                selected ? 'font-medium' : 'font-normal'
                              }`}
                            >
                              {option.name}
                            </span>
                            {selected ? (
                              <div className="absolute inset-y-0 right-0 flex items-center pr-3">
                                <div className="shrink-0 flex items-center justify-center w-5 h-5 rounded-full">
                                  <Check className="w-4 h-4 stroke-blue-400" aria-hidden="true" />
                                </div>
                              </div>
                            ) : null}
                          </>
                        )}
                      </Listbox.Option>
                    ))}
                  </Listbox.Options>
                </Transition>
              </div>
              )}
            </Listbox>
          </div>
        </div>
        <div className="mt-3 w-full">
          <div className="text-gray-400 text-xs">
            {td("addModal.reportName.name.subLabel")} <span className="text-red-500 text-sm">*</span>
          </div>
          <div
            className={cn(
              "relative w-full",
              "mt-1"
            )}
          >
            <Listbox value={selectedReportNameOption} onChange={setSelectedReportNameOption}>
              {({ open }) => (
              <div className="relative">
                <Listbox.Button
                  className={cn(
                    "group relative w-full h-9 py-2 pl-3 pr-6 cursor-pointer rounded-lg text-left focus:outline-none text-sm border border-gray-200 hover:border-gray-300",
                    open
                      ? "bg-gray-100"
                      : ""
                  )}
                >
                  <span className="block truncate text-gray-500 group-hover:text-gray-600">
                    {selectedReportNameOption.name}
                  </span>
                  <span className="pointer-events-none absolute right-0 inset-y-0 flex items-center px-2">
                    <ChevronUpDownIcon
                      className="h-5 w-5 text-gray-300 group-hover:text-gray-400"
                      aria-hidden="true"
                    />
                  </span>
                </Listbox.Button>
                <Transition
                  as={Fragment}
                  leave="transition ease-in duration-100"
                  leaveFrom="opacity-100"
                  leaveTo="opacity-0"
                >
                  <Listbox.Options
                    className={cn(
                      "absolute mt-1 max-h-60 w-full overflow-auto rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black/5 focus:outline-none sm:text-sm z-[1]",
                    )}
                  >
                    {reportNameOptions
                      .filter(option => option.category === selectedReportCategoryOption.id)
                      .filter(option => selectedReportTypeOption.type === 'OPTAPEX'
                        ? (selectedProfile.account_type === 'vendor' ? option.id.includes('VENDOR') : option.id.includes('SELLER'))
                        : true
                      )
                      .map((option, optionIdx) => (
                        <Listbox.Option
                          key={optionIdx}
                          className={({ active }) =>
                            `relative cursor-pointer select-none py-2 pl-4 pr-10 ${
                              active ? 'bg-blue-100/40 text-blue-900' : 'text-gray-600'
                            }`
                          }
                          value={option}
                        >
                          {({ selected }) => (
                            <>
                              <span
                                className={`block truncate ${
                                  selected ? 'font-medium' : 'font-normal'
                                }`}
                              >
                                {option.name}
                              </span>
                              {selected ? (
                                <div className="absolute inset-y-0 right-0 flex items-center pr-3">
                                  <div className="shrink-0 flex items-center justify-center w-5 h-5 rounded-full">
                                    <Check className="w-4 h-4 stroke-blue-400" aria-hidden="true" />
                                  </div>
                                </div>
                              ) : null}
                            </>
                          )}
                        </Listbox.Option>
                      ))}
                  </Listbox.Options>
                </Transition>
              </div>
              )}
            </Listbox>
          </div>
        </div>
        
        <div className="mt-3 w-full">
          <div className="text-gray-400 text-xs">
            {td("addModal.reportTitle.subLabel")}
          </div>
          <div className="mt-1">
            <input
              type="text"
              className="w-full h-9 py-2 px-3 rounded-lg border border-gray-200 hover:border-gray-300 focus:outline-none focus:border-blue-300 text-sm text-gray-500"
              placeholder={selectedReportNameOption?.name || td("addModal.reportTitle.subLabel")}
              value={reportTitle}
              onChange={(e) => setReportTitle(e.target.value)}
              onKeyDown={(e) => {
                if (e.key === ' ' || e.code === 'Space') {
                  e.stopPropagation();
                }
              }}
            />
          </div>
        </div>
      </div>
    )
  }
  const step3Content = () => {
    return (
      <div className="py-3">
        <div className="w-full">
          <div className="flex items-center text-xs text-gray-400">
            {t("filter.dateRange.label")} <span className="text-red-500 text-sm">*</span>
            {(selectedReportNameOption.id === "GET_VENDOR_SALES_REPORT" || 
              selectedReportNameOption.id === "GET_VENDOR_TRAFFIC_REPORT" || 
              selectedReportNameOption.id === "GET_VENDOR_INVENTORY_REPORT"||
              selectedReportNameOption.id === "GET_VENDOR_NET_PURE_PRODUCT_MARGIN_REPORT") && (
              <Popover className="relative ml-2 flex items-center justify-center">
                {({ open }) => (
                  <>
                    <Popover.Button
                      className={cn(
                        "inline-flex items-center justify-center rounded-full text-gray-300 hover:text-gray-400 focus:outline-none"
                      )}
                    >
                      <QuestionMarkCircleIcon className="w-4 h-4"/>
                    </Popover.Button>
                    <Transition
                      as={Fragment}
                      enter="transition ease-out duration-200"
                      enterFrom="opacity-0 translate-y-1"
                      enterTo="opacity-100 translate-y-0"
                      leave="transition ease-in duration-150"
                      leaveFrom="opacity-100 translate-y-0"
                      leaveTo="opacity-0 translate-y-1"
                    >
                      <Popover.Panel className="absolute left-full top-0 z-10 ml-2 w-screen max-w-xs translate-y-0 transform px-4 sm:px-0">
                        <div className="overflow-hidden rounded-lg shadow-lg">
                          <div className="relative p-4 bg-gray-900/90 text-xs text-white font-normal text-left">                            
                            <div className="mt-1 font-medium text-white">{td("addModal.advancedSetting.dateRestrictions.maxFromNow")}:</div>
                            <ul className="ml-4 list-disc space-y-0.5 mt-1 text-white">
                              <li>DAY: 1,460 days</li>
                              <li>WEEK: 7 weeks</li>
                              <li>MONTH: 36 months</li>
                              <li>QUARTER: 8 quarters</li>
                              <li>YEAR: 3 years</li>
                            </ul>
                            <div className="mt-2 font-medium text-white">{td("addModal.advancedSetting.dateRestrictions.maxRange")}:</div>
                            <ul className="ml-4 list-disc space-y-0.5 mt-1 text-white">
                              <li>DAY: 15 days</li>
                              <li>WEEK: 7 weeks</li>
                              <li>MONTH: 15 months</li>
                              <li>QUARTER: 4 quarters</li>
                              <li>YEAR: 2 years</li>
                            </ul>
                          </div>
                        </div>
                      </Popover.Panel>
                    </Transition>
                  </>
                )}
              </Popover>
            )}
          </div>
          <DatePicker
            selectsRange={true}
            minDate={getMinDate()}
            maxDate={(() => {
              // vp 일부 리포트는 72시간(3일) 전까지만 날짜 선택 가능하도록 (FATAL 방지)           
              if (selectedReportNameOption.id === "GET_VENDOR_SALES_REPORT" || 
                selectedReportNameOption.id === "GET_VENDOR_TRAFFIC_REPORT" ||
                selectedReportNameOption.id === "GET_VENDOR_INVENTORY_REPORT" ||
                selectedReportNameOption.id === "GET_VENDOR_NET_PURE_PRODUCT_MARGIN_REPORT") {
                const maxDate = new Date();
                maxDate.setDate(maxDate.getDate() - 4);
                return maxDate;
              } else{                
                return new Date();                
              }
            })()}
            startDate={startDate}
            endDate={endDate}
            onChange={(update) => {
              setDateRange(update as Date[])
            }}
            dateFormat="yyyy.MM.dd"
            calendarClassName="dashboard-date-range"
            // @ts-ignore
            customInput={<DateRangeInput />}
            locale={locale}
          />
          {dateValidationError && (
            <div className="mt-1 text-xs text-red-500">
              {dateValidationError}
            </div>
          )}
        </div>
        {selectedReportNameOption.id === "GET_SALES_AND_TRAFFIC_REPORT" && (
          <>            
            <div className="mt-3 w-full">
              <div className="text-gray-400 text-xs">
                {td("addModal.advancedSetting.filter.asinAggregationFilter.label")}
              </div>
              <div
                className={cn(
                  "relative w-full",
                  "mt-1"
                )}
              >
                <Listbox value={selectedAsinAggregationFilterOption} onChange={setSelectedAsinAggregationFilterOption}>
                  {({ open }) => (
                  <div className="relative">
                    <Listbox.Button
                      className={cn(
                        "group relative w-full h-9 py-2 pl-3 pr-6 cursor-pointer rounded-lg text-left focus:outline-none text-sm border border-gray-200 hover:border-gray-300",
                        open
                          ? "bg-gray-100"
                          : ""
                      )}
                    >
                      <span className="block truncate text-gray-500 group-hover:text-gray-600">
                        {td(selectedAsinAggregationFilterOption.name)}
                      </span>
                      <span className="pointer-events-none absolute right-0 inset-y-0 flex items-center px-2">
                        <ChevronUpDownIcon
                          className="h-5 w-5 text-gray-300 group-hover:text-gray-400"
                          aria-hidden="true"
                        />
                      </span>
                    </Listbox.Button>
                    <Transition
                      as={Fragment}
                      leave="transition ease-in duration-100"
                      leaveFrom="opacity-100"
                      leaveTo="opacity-0"
                    >
                      <Listbox.Options
                        className={cn(
                          "absolute mt-1 max-h-60 w-full overflow-auto rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black/5 focus:outline-none sm:text-sm z-[1]",
                        )}
                      >
                        {asinAggregationFilterOptions.map((option, optionIdx) => (
                          <Listbox.Option
                            key={optionIdx}
                            className={({ active }) =>
                              `relative cursor-pointer select-none py-2 pl-4 pr-10 ${
                                active ? 'bg-blue-100/40 text-blue-900' : 'text-gray-600'
                              }`
                            }
                            value={option}
                          >
                            {({ selected }) => (
                              <>
                                <span
                                  className={`block truncate ${
                                    selected ? 'font-medium' : 'font-normal'
                                  }`}
                                >
                                  {td(option.name)}
                                </span>
                                {selected ? (
                                  <div className="absolute inset-y-0 right-0 flex items-center pr-3">
                                    <div className="shrink-0 flex items-center justify-center w-5 h-5 rounded-full">
                                      <Check className="w-4 h-4 stroke-blue-400" aria-hidden="true" />
                                    </div>
                                  </div>
                                ) : null}
                              </>
                            )}
                          </Listbox.Option>
                        ))}
                      </Listbox.Options>
                    </Transition>
                  </div>
                  )}
                </Listbox>
              </div>
            </div>
          </>
        )}
        {selectedReportNameOption.id === "GET_LEDGER_SUMMARY_VIEW_DATA" && (
          <>
            <div className="mt-3 w-full">
              <div className="text-gray-400 text-xs">
                {td("addModal.advancedSetting.filter.locationAggregationFilter.label")}
              </div>
              <div
                className={cn(
                  "relative w-full",
                  "mt-1"
                )}
              >
                <Listbox value={selectedLocationAggregationFilterOption} onChange={setSelectedLocationAggregationFilterOption}>
                  {({ open }) => (
                  <div className="relative">
                    <Listbox.Button
                      className={cn(
                        "group relative w-full h-9 py-2 pl-3 pr-6 cursor-pointer rounded-lg text-left focus:outline-none text-sm border border-gray-200 hover:border-gray-300",
                        open
                          ? "bg-gray-100"
                          : ""
                      )}
                    >
                      <span className="block truncate text-gray-500 group-hover:text-gray-600">
                        {td(selectedLocationAggregationFilterOption.name)}
                      </span>
                      <span className="pointer-events-none absolute right-0 inset-y-0 flex items-center px-2">
                        <ChevronUpDownIcon
                          className="h-5 w-5 text-gray-300 group-hover:text-gray-400"
                          aria-hidden="true"
                        />
                      </span>
                    </Listbox.Button>
                    <Transition
                      as={Fragment}
                      leave="transition ease-in duration-100"
                      leaveFrom="opacity-100"
                      leaveTo="opacity-0"
                    >
                      <Listbox.Options
                        className={cn(
                          "absolute mt-1 max-h-60 w-full overflow-auto rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black/5 focus:outline-none sm:text-sm z-[1]",
                        )}
                      >
                        {locationAggregationFilterOptions.map((option, optionIdx) => (
                          <Listbox.Option
                            key={optionIdx}
                            className={({ active }) =>
                              `relative cursor-pointer select-none py-2 pl-4 pr-10 ${
                                active ? 'bg-blue-100/40 text-blue-900' : 'text-gray-600'
                              }`
                            }
                            value={option}
                          >
                            {({ selected }) => (
                              <>
                                <span
                                  className={`block truncate ${
                                    selected ? 'font-medium' : 'font-normal'
                                  }`}
                                >
                                  {td(option.name)}
                                </span>
                                {selected ? (
                                  <div className="absolute inset-y-0 right-0 flex items-center pr-3">
                                    <div className="shrink-0 flex items-center justify-center w-5 h-5 rounded-full">
                                      <Check className="w-4 h-4 stroke-blue-400" aria-hidden="true" />
                                    </div>
                                  </div>
                                ) : null}
                              </>
                            )}
                          </Listbox.Option>
                        ))}
                      </Listbox.Options>
                    </Transition>
                  </div>
                  )}
                </Listbox>
              </div>
            </div>
            <div className="mt-3 w-full">
              <div className="text-gray-400 text-xs">
                {td("addModal.advancedSetting.filter.dateAggregationFilter.label")}
              </div>
              <div
                className={cn(
                  "relative w-full",
                  "mt-1"
                )}
              >
                <Listbox value={selectedDateAggregationFilterOption} onChange={setSelectedDateAggregationFilterOption}>
                  {({ open }) => (
                  <div className="relative">
                    <Listbox.Button
                      className={cn(
                        "group relative w-full h-9 py-2 pl-3 pr-6 cursor-pointer rounded-lg text-left focus:outline-none text-sm border border-gray-200 hover:border-gray-300",
                        open
                          ? "bg-gray-100"
                          : ""
                      )}
                    >
                      <span className="block truncate text-gray-500 group-hover:text-gray-600">
                        {td(selectedDateAggregationFilterOption.name)}
                      </span>
                      <span className="pointer-events-none absolute right-0 inset-y-0 flex items-center px-2">
                        <ChevronUpDownIcon
                          className="h-5 w-5 text-gray-300 group-hover:text-gray-400"
                          aria-hidden="true"
                        />
                      </span>
                    </Listbox.Button>
                    <Transition
                      as={Fragment}
                      leave="transition ease-in duration-100"
                      leaveFrom="opacity-100"
                      leaveTo="opacity-0"
                    >
                      <Listbox.Options
                        className={cn(
                          "absolute mt-1 max-h-60 w-full overflow-auto rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black/5 focus:outline-none sm:text-sm z-[1]",
                        )}
                      >
                        {dateAggregationFilterOptions.map((option, optionIdx) => (
                          <Listbox.Option
                            key={optionIdx}
                            className={({ active }) =>
                              `relative cursor-pointer select-none py-2 pl-4 pr-10 ${
                                active ? 'bg-blue-100/40 text-blue-900' : 'text-gray-600'
                              }`
                            }
                            value={option}
                          >
                            {({ selected }) => (
                              <>
                                <span
                                  className={`block truncate ${
                                    selected ? 'font-medium' : 'font-normal'
                                  }`}
                                >
                                  {td(option.name)}
                                </span>
                                {selected ? (
                                  <div className="absolute inset-y-0 right-0 flex items-center pr-3">
                                    <div className="shrink-0 flex items-center justify-center w-5 h-5 rounded-full">
                                      <Check className="w-4 h-4 stroke-blue-400" aria-hidden="true" />
                                    </div>
                                  </div>
                                ) : null}
                              </>
                            )}
                          </Listbox.Option>
                        ))}
                      </Listbox.Options>
                    </Transition>
                  </div>
                  )}
                </Listbox>
              </div>
            </div>
          </>
        )}
        {selectedReportNameOption.id === "GET_LEDGER_DETAIL_VIEW_DATA" && (
          <div className="mt-3 w-full">
            <div className="text-gray-400 text-xs">
              {td("addModal.advancedSetting.filter.eventTypeFilter.label")}
            </div>
            <div
              className={cn(
                "relative w-full",
                "mt-1"
              )}
            >
              <Listbox value={selectedEventTypeFilterOption} onChange={setSelectedEventTypeFilterOption}>
                {({ open }) => (
                <div className="relative">
                  <Listbox.Button
                    className={cn(
                      "group relative w-full h-9 py-2 pl-3 pr-6 cursor-pointer rounded-lg text-left focus:outline-none text-sm border border-gray-200 hover:border-gray-300",
                      open
                        ? "bg-gray-100"
                        : ""
                    )}
                  >
                    <span className="block truncate text-gray-500 group-hover:text-gray-600">
                      {td(selectedEventTypeFilterOption.name)}
                    </span>
                    <span className="pointer-events-none absolute right-0 inset-y-0 flex items-center px-2">
                      <ChevronUpDownIcon
                        className="h-5 w-5 text-gray-300 group-hover:text-gray-400"
                        aria-hidden="true"
                      />
                    </span>
                  </Listbox.Button>
                  <Transition
                    as={Fragment}
                    leave="transition ease-in duration-100"
                    leaveFrom="opacity-100"
                    leaveTo="opacity-0"
                  >
                    <Listbox.Options
                      className={cn(
                        "absolute mt-1 max-h-60 w-full overflow-auto rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black/5 focus:outline-none sm:text-sm z-[1]",
                      )}
                    >
                      {eventTypeFilterOptions.map((option, optionIdx) => (
                        <Listbox.Option
                          key={optionIdx}
                          className={({ active }) =>
                            `relative cursor-pointer select-none py-2 pl-4 pr-10 ${
                              active ? 'bg-blue-100/40 text-blue-900' : 'text-gray-600'
                            }`
                          }
                          value={option}
                        >
                          {({ selected }) => (
                            <>
                              <span
                                className={`block truncate ${
                                  selected ? 'font-medium' : 'font-normal'
                                }`}
                              >
                                {td(option.name)}
                              </span>
                              {selected ? (
                                <div className="absolute inset-y-0 right-0 flex items-center pr-3">
                                  <div className="shrink-0 flex items-center justify-center w-5 h-5 rounded-full">
                                    <Check className="w-4 h-4 stroke-blue-400" aria-hidden="true" />
                                  </div>
                                </div>
                              ) : null}
                            </>
                          )}
                        </Listbox.Option>
                      ))}
                    </Listbox.Options>
                  </Transition>
                </div>
                )}
              </Listbox>
            </div>
          </div>
        )}
        {selectedReportTypeOption.type === "AD" && (
          <div className="mt-3 w-full">
            <div className="text-gray-400 text-xs">
              {td("addModal.advancedSetting.filter.timeUnitFilter.label")}
            </div>
            <div
              className={cn(
                "relative w-full",
                "mt-1"
              )}
            >
              <Listbox value={selectedTimeUnitFilterOption} onChange={setSelectedTimeUnitFilterOption}>
                {({ open }) => (
                <div className="relative">
                  <Listbox.Button
                    className={cn(
                      "group relative w-full h-9 py-2 pl-3 pr-6 cursor-pointer rounded-lg text-left focus:outline-none text-sm border border-gray-200 hover:border-gray-300",
                      open
                        ? "bg-gray-100"
                        : ""
                    )}
                  >
                    <span className="block truncate text-gray-500 group-hover:text-gray-600">
                      {td(selectedTimeUnitFilterOption.name)}
                    </span>
                    <span className="pointer-events-none absolute right-0 inset-y-0 flex items-center px-2">
                      <ChevronUpDownIcon
                        className="h-5 w-5 text-gray-300 group-hover:text-gray-400"
                        aria-hidden="true"
                      />
                    </span>
                  </Listbox.Button>
                  <Transition
                    as={Fragment}
                    leave="transition ease-in duration-100"
                    leaveFrom="opacity-100"
                    leaveTo="opacity-0"
                  >
                    <Listbox.Options
                      className={cn(
                        "absolute mt-1 max-h-60 w-full overflow-auto rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black/5 focus:outline-none sm:text-sm z-[1]",
                      )}
                    >
                      {timeUnitFilterOptions.map((option, optionIdx) => (
                        <Listbox.Option
                          key={optionIdx}
                          className={({ active }) =>
                            `relative cursor-pointer select-none py-2 pl-4 pr-10 ${
                              active ? 'bg-blue-100/40 text-blue-900' : 'text-gray-600'
                            }`
                          }
                          value={option}
                        >
                          {({ selected }) => (
                            <>
                              <span
                                className={`block truncate ${
                                  selected ? 'font-medium' : 'font-normal'
                                }`}
                              >
                                {td(option.name)}
                              </span>
                              {selected ? (
                                <div className="absolute inset-y-0 right-0 flex items-center pr-3">
                                  <div className="shrink-0 flex items-center justify-center w-5 h-5 rounded-full">
                                    <Check className="w-4 h-4 stroke-blue-400" aria-hidden="true" />
                                  </div>
                                </div>
                              ) : null}
                            </>
                          )}
                        </Listbox.Option>
                      ))}
                    </Listbox.Options>
                  </Transition>
                </div>
                )}
              </Listbox>
            </div>
            <div className="mt-3 w-full">
              <div className="text-gray-400 text-xs">
                {td("addModal.advancedSetting.filter.groupByFilter.label")}
              </div>
              <div
                className={cn(
                  "relative w-full",
                  "mt-1"
                )}
              >
                <Listbox value={selectedGroupByFilterOption} onChange={setSelectedGroupByFilterOption}>
                  {({ open }) => (
                    <div className="relative">
                      <Listbox.Button
                        className={cn(
                          "group relative w-full h-9 py-2 pl-3 pr-6 cursor-pointer rounded-lg text-left focus:outline-none text-sm border border-gray-200 hover:border-gray-300",
                          open
                            ? "bg-gray-100"
                            : ""
                        )}
                      >
                        <span className="block truncate text-gray-500 group-hover:text-gray-600">
                          {td(selectedGroupByFilterOption.name)}
                        </span>
                        <span className="pointer-events-none absolute right-0 inset-y-0 flex items-center px-2">
                          <ChevronUpDownIcon
                            className="h-5 w-5 text-gray-300 group-hover:text-gray-400"
                            aria-hidden="true"
                          />
                        </span>
                      </Listbox.Button>
                      <Transition
                        as={Fragment}
                        leave="transition ease-in duration-100"
                        leaveFrom="opacity-100"
                        leaveTo="opacity-0"
                      >
                        <Listbox.Options
                          className={cn(
                            "absolute mt-1 max-h-60 w-full overflow-auto rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black/5 focus:outline-none sm:text-sm z-[1]",
                          )}
                        >
                          {groupByFilterOptions.filter(option=>
                              option.type===selectedReportNameOption.groubBy
                          ).map((option, optionIdx) => (
                            <Listbox.Option
                              key={optionIdx}
                              className={({ active }) =>
                                `relative cursor-pointer select-none py-2 pl-4 pr-10 ${
                                  active ? 'bg-blue-100/40 text-blue-900' : 'text-gray-600'
                                }`
                              }
                              value={option}
                            >
                              {({ selected }) => (
                                <>
                                  <span
                                    className={`block truncate ${
                                      selected ? 'font-medium' : 'font-normal'
                                    }`}
                                  >
                                    {td(option.name)}
                                  </span>
                                </>
                              )}
                            </Listbox.Option>
                          ))}
                        </Listbox.Options>
                      </Transition>
                    </div>
                  )}
                </Listbox>
              </div>           
            </div>
          </div>
          
        )}
        {selectedReportTypeOption.type === "VP" && !selectedReportNameOption.id.includes("REAL_TIME") && (
          <>
            <div className="mt-3 w-full">
              <div className="text-gray-400 text-xs">
                {td("addModal.advancedSetting.filter.reportPeriodFilter.label")}
              </div>
              <div
                className={cn(
                  "relative w-full",
                  "mt-1"
                )}
              >
                <Listbox value={selectedReportPeriodOption} onChange={setSelectedReportPeriodOption}>
                  {({ open }) => (
                  <div className="relative">
                    <Listbox.Button
                      className={cn(
                        "group relative w-full h-9 py-2 pl-3 pr-6 cursor-pointer rounded-lg text-left focus:outline-none text-sm border border-gray-200 hover:border-gray-300",
                        open
                          ? "bg-gray-100"
                          : ""
                      )}
                    >
                      <span className="block truncate text-gray-500 group-hover:text-gray-600">
                        {td(selectedReportPeriodOption.name)}
                      </span>
                      <span className="pointer-events-none absolute right-0 inset-y-0 flex items-center px-2">
                        <ChevronUpDownIcon
                          className="h-5 w-5 text-gray-300 group-hover:text-gray-400"
                          aria-hidden="true"
                        />
                      </span>
                    </Listbox.Button>
                    <Transition
                      as={Fragment}
                      leave="transition ease-in duration-100"
                      leaveFrom="opacity-100"
                      leaveTo="opacity-0"
                    >
                      <Listbox.Options
                        className={cn(
                          "absolute mt-1 max-h-60 w-full overflow-auto rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black/5 focus:outline-none sm:text-sm z-[1]",
                        )}
                      >
                        {reportPeriodOptions.map((option, optionIdx) => (
                          <Listbox.Option
                            key={optionIdx}
                            className={({ active }) =>
                              `relative cursor-pointer select-none py-2 pl-4 pr-10 ${
                                active ? 'bg-blue-100/40 text-blue-900' : 'text-gray-600'
                              }`
                            }
                            value={option}
                          >
                            {({ selected }) => (
                              <>
                                <span
                                  className={`block truncate ${
                                    selected ? 'font-medium' : 'font-normal'
                                  }`}
                                >
                                  {td(option.name)}
                                </span>
                                {selected ? (
                                  <div className="absolute inset-y-0 right-0 flex items-center pr-3">
                                    <div className="shrink-0 flex items-center justify-center w-5 h-5 rounded-full">
                                      <Check className="w-4 h-4 stroke-blue-400" aria-hidden="true" />
                                    </div>
                                  </div>
                                ) : null}
                              </>
                            )}
                          </Listbox.Option>
                        ))}
                      </Listbox.Options>
                    </Transition>
                  </div>
                  )}
                </Listbox>
              </div>
            </div>
            
            {(selectedReportNameOption.id === "GET_VENDOR_SALES_REPORT" || 
              selectedReportNameOption.id === "GET_VENDOR_INVENTORY_REPORT") && (
              <>
                <div className="mt-3 w-full">
                  <div className="text-gray-400 text-xs">
                    {td("addModal.advancedSetting.filter.distributorViewFilter.label")}
                  </div>
                  <div
                    className={cn(
                      "relative w-full",
                      "mt-1"
                    )}
                  >
                    <Listbox value={selectedDistributorViewOption} onChange={setSelectedDistributorViewOption}>
                      {({ open }) => (
                      <div className="relative">
                        <Listbox.Button
                          className={cn(
                            "group relative w-full h-9 py-2 pl-3 pr-6 cursor-pointer rounded-lg text-left focus:outline-none text-sm border border-gray-200 hover:border-gray-300",
                            open
                              ? "bg-gray-100"
                              : ""
                          )}
                        >
                          <span className="block truncate text-gray-500 group-hover:text-gray-600">
                            {td(selectedDistributorViewOption.name)}
                          </span>
                          <span className="pointer-events-none absolute right-0 inset-y-0 flex items-center px-2">
                            <ChevronUpDownIcon
                              className="h-5 w-5 text-gray-300 group-hover:text-gray-400"
                              aria-hidden="true"
                            />
                          </span>
                        </Listbox.Button>
                        <Transition
                          as={Fragment}
                          leave="transition ease-in duration-100"
                          leaveFrom="opacity-100"
                          leaveTo="opacity-0"
                        >
                          <Listbox.Options
                            className={cn(
                              "absolute mt-1 max-h-60 w-full overflow-auto rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black/5 focus:outline-none sm:text-sm z-[1]",
                            )}
                          >
                            {distributorViewOptions.map((option, optionIdx) => (
                              <Listbox.Option
                                key={optionIdx}
                                className={({ active }) =>
                                  `relative cursor-pointer select-none py-2 pl-4 pr-10 ${
                                    active ? 'bg-blue-100/40 text-blue-900' : 'text-gray-600'
                                  }`
                                }
                                value={option}
                              >
                                {({ selected }) => (
                                  <>
                                    <span
                                      className={`block truncate ${
                                        selected ? 'font-medium' : 'font-normal'
                                      }`}
                                    >
                                      {td(option.name)}
                                    </span>
                                    {selected ? (
                                      <div className="absolute inset-y-0 right-0 flex items-center pr-3">
                                        <div className="shrink-0 flex items-center justify-center w-5 h-5 rounded-full">
                                          <Check className="w-4 h-4 stroke-blue-400" aria-hidden="true" />
                                        </div>
                                      </div>
                                    ) : null}
                                  </>
                                )}
                              </Listbox.Option>
                            ))}
                          </Listbox.Options>
                        </Transition>
                      </div>
                      )}
                    </Listbox>
                  </div>
                </div>
                
                <div className="mt-3 w-full">
                  <div className="text-gray-400 text-xs">
                    {td("addModal.advancedSetting.filter.sellingProgramFilter.label")}
                  </div>
                  <div
                    className={cn(
                      "relative w-full",
                      "mt-1"
                    )}
                  >
                    <Listbox value={selectedSellingProgramOption} onChange={setSelectedSellingProgramOption}>
                      {({ open }) => (
                      <div className="relative">
                        <Listbox.Button
                          className={cn(
                            "group relative w-full h-9 py-2 pl-3 pr-6 cursor-pointer rounded-lg text-left focus:outline-none text-sm border border-gray-200 hover:border-gray-300",
                            open
                              ? "bg-gray-100"
                              : ""
                          )}
                        >
                          <span className="block truncate text-gray-500 group-hover:text-gray-600">
                            {td(selectedSellingProgramOption.name)}
                          </span>
                          <span className="pointer-events-none absolute right-0 inset-y-0 flex items-center px-2">
                            <ChevronUpDownIcon
                              className="h-5 w-5 text-gray-300 group-hover:text-gray-400"
                              aria-hidden="true"
                            />
                          </span>
                        </Listbox.Button>
                        <Transition
                          as={Fragment}
                          leave="transition ease-in duration-100"
                          leaveFrom="opacity-100"
                          leaveTo="opacity-0"
                        >
                          <Listbox.Options
                            className={cn(
                              "absolute mt-1 max-h-60 w-full overflow-auto rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black/5 focus:outline-none sm:text-sm z-[1]",
                            )}
                          >
                            {sellingProgramOptions
                              .filter(
                                (option) =>
                                  !(
                                    selectedReportNameOption.id === "GET_VENDOR_INVENTORY_REPORT" &&
                                    option.type === "BUSINESS"
                                  )
                              )
                              .map((option, optionIdx) => (
                                <Listbox.Option
                                  key={optionIdx}
                                  className={({ active }) =>
                                    `relative cursor-pointer select-none py-2 pl-4 pr-10 ${
                                      active ? "bg-blue-100/40 text-blue-900" : "text-gray-600"
                                    }`
                                  }
                                  value={option}
                                >
                                  {({ selected }) => (
                                    <>
                                      <span
                                        className={`block truncate ${
                                          selected ? "font-medium" : "font-normal"
                                        }`}
                                      >
                                        {td(option.name)}
                                      </span>
                                      {selected ? (
                                        <div className="absolute inset-y-0 right-0 flex items-center pr-3">
                                          <div className="shrink-0 flex items-center justify-center w-5 h-5 rounded-full">
                                            <Check className="w-4 h-4 stroke-blue-400" aria-hidden="true" />
                                          </div>
                                        </div>
                                      ) : null}
                                    </>
                                  )}
                                </Listbox.Option>
                              ))}
                          </Listbox.Options>
                        </Transition>
                      </div>
                      )}
                    </Listbox>
                  </div>
                </div>
              </>
            )}
          </>
        )}
        {selectedReportTypeOption.type === 'OPTAPEX' && (
          <>
            <div className="mt-3 w-full">
              <div className="text-gray-400 text-xs">Report Type</div>
              <div className={cn("relative w-full","mt-1")}>
                <Listbox value={selectedInsightLevelOption} onChange={setSelectedInsightLevelOption}>
                  {({ open }) => (
                    <div className="relative">
                      <Listbox.Button className={cn(
                        "group relative w-full h-9 py-2 pl-3 pr-6 cursor-pointer rounded-lg text-left focus:outline-none text-sm border border-gray-200 hover:border-gray-300",
                        open ? "bg-gray-100" : ""
                      )}>
                        <span className="block truncate text-gray-500 group-hover:text-gray-600">{selectedInsightLevelOption.name}</span>
                        <span className="pointer-events-none absolute right-0 inset-y-0 flex items-center px-2">
                          <ChevronUpDownIcon className="h-5 w-5 text-gray-300 group-hover:text-gray-400" aria-hidden="true" />
                        </span>
                      </Listbox.Button>
                      <Transition as={Fragment} leave="transition ease-in duration-100" leaveFrom="opacity-100" leaveTo="opacity-0">
                        <Listbox.Options className={cn("absolute mt-1 max-h-60 w-full overflow-auto rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black/5 focus:outline-none sm:text-sm z-[1]")}> 
                          {insightLevelOptions.map((option, optionIdx) => (
                            <Listbox.Option key={optionIdx} className={({ active }) => `relative cursor-pointer select-none py-2 pl-4 pr-10 ${active ? 'bg-blue-100/40 text-blue-900' : 'text-gray-600'}`} value={option}>
                              {({ selected }) => (<span className={`block truncate ${selected ? 'font-medium' : 'font-normal'}`}>{option.name}</span>)}
                            </Listbox.Option>
                          ))}
                        </Listbox.Options>
                      </Transition>
                    </div>
                  )}
                </Listbox>
              </div>
            </div>

            {selectedReportNameOption.id.includes('HOURLY') && (
              <div className="mt-3 w-full">
                <div className="text-gray-400 text-xs">Aggregation Method</div>
                <div className={cn("relative w-full","mt-1")}>
                  <Listbox value={selectedAggregationLevelOption} onChange={setSelectedAggregationLevelOption}>
                    {({ open }) => (
                      <div className="relative">
                        <Listbox.Button className={cn(
                          "group relative w-full h-9 py-2 pl-3 pr-6 cursor-pointer rounded-lg text-left focus:outline-none text-sm border border-gray-200 hover:border-gray-300",
                          open ? "bg-gray-100" : ""
                        )}>
                          <span className="block truncate text-gray-500 group-hover:text-gray-600">{selectedAggregationLevelOption.name}</span>
                          <span className="pointer-events-none absolute right-0 inset-y-0 flex items-center px-2">
                            <ChevronUpDownIcon className="h-5 w-5 text-gray-300 group-hover:text-gray-400" aria-hidden="true" />
                          </span>
                        </Listbox.Button>
                        <Transition as={Fragment} leave="transition ease-in duration-100" leaveFrom="opacity-100" leaveTo="opacity-0">
                          <Listbox.Options className={cn("absolute mt-1 max-h-60 w-full overflow-auto rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black/5 focus:outline-none sm:text-sm z-[1]")}> 
                            {aggregationLevelOptions.map((option, optionIdx) => (
                              <Listbox.Option key={optionIdx} className={({ active }) => `relative cursor-pointer select-none py-2 pl-4 pr-10 ${active ? 'bg-blue-100/40 text-blue-900' : 'text-gray-600'}`} value={option}>
                                {({ selected }) => (<span className={`block truncate ${selected ? 'font-medium' : 'font-normal'}`}>{option.name}</span>)}
                              </Listbox.Option>
                            ))}
                          </Listbox.Options>
                        </Transition>
                      </div>
                    )}
                  </Listbox>
                </div>
              </div>
            )}
          </>
        )}
      </div>
    )
  }

  useEffect(() => {
    const filteredOptions = reportCategoryOptions.filter(option => {
      // 기본 타입 필터링
      if (option.type !== selectedReportTypeOption.type) {
        return false;
      }
      
      // OptApex는 Consolidated 카테고리만 유지
      if (selectedReportTypeOption.type === 'OPTAPEX') {
        return option.id === 'LONG_TERM_REPORT';
      }
      
      // account_type에 따른 추가 필터링
      if (selectedProfile.account_type === 'vendor' && option.id === 'SELLER_LONG_TERM_REPORT') {
        return false;
      }
      
      if (selectedProfile.account_type !== 'vendor' && option.id === 'VENDOR_LONG_TERM_REPORT') {
        return false;
      }
      
      return true;
    });
    
    setSelectedReportCategoryOption(filteredOptions[0])
  }, [selectedReportTypeOption, selectedProfile.account_type])
  useEffect(() => {
    const candidates = reportNameOptions
      .filter(option => option.category === selectedReportCategoryOption.id)
      .filter(option => selectedReportTypeOption.type === 'OPTAPEX'
        ? (selectedProfile.account_type === 'vendor' ? option.id.includes('VENDOR') : option.id.includes('SELLER'))
        : true
      );
    if (candidates.length > 0) {
      setSelectedReportNameOption(candidates[0]);
    }
  }, [selectedReportCategoryOption, selectedReportTypeOption, selectedProfile.account_type])
  useEffect(() => {
    if(selectedReportNameOption.groubBy) {
      setSelectedGroupByFilterOption(groupByFilterOptions.find(option => option.type === selectedReportNameOption.groubBy) || groupByFilterOptions[0])
    }
  }, [selectedReportNameOption])

  useEffect(() => {
    // When switching to Vendor Inventory Report, if 'BUSINESS' was selected, reset to a valid option.
    if (
      selectedReportNameOption.id === "GET_VENDOR_INVENTORY_REPORT" &&
      selectedSellingProgramOption.type === "BUSINESS"
    ) {
      setSelectedSellingProgramOption(sellingProgramOptions[0]); // Reset to default (RETAIL)
    }
  }, [selectedReportNameOption]);
    
  useEffect(() => {
    // Check date validity when report type, report name, or date range changes
    if (
        selectedReportNameOption.id === "GET_VENDOR_SALES_REPORT" || 
        selectedReportNameOption.id === "GET_VENDOR_TRAFFIC_REPORT" || 
        selectedReportNameOption.id === "GET_VENDOR_INVENTORY_REPORT" ||
        selectedReportNameOption.id === "GET_VENDOR_NET_PURE_PRODUCT_MARGIN_REPORT" ||
        selectedReportNameOption.id.includes("REAL_TIME")) {
      checkDateRangeValidity();
    } else {
      setDateValidationError(null);
    }
  }, [selectedReportNameOption, selectedReportPeriodOption, dateRange]);
  
  // REAL_TIME 리포트의 기본 날짜 범위 설정
  useEffect(() => {
    if (!selectedReportNameOption || !selectedReportNameOption.id) return;
    if (selectedReportNameOption.id.includes("REAL_TIME")) {
      const now = new Date();
      
      // 리포트별 데이터 처리 지연 시간 설정
      const endTime = new Date(now);
      if (selectedReportNameOption.id === "GET_VENDOR_REAL_TIME_TRAFFIC_REPORT") {
        // Traffic Report: 115분 지연
        endTime.setMinutes(now.getMinutes() - 115);
      } else {
        // 다른 REAL_TIME 리포트들: 5분 지연
        endTime.setMinutes(now.getMinutes() - 5);
      }
      
      let defaultStartDate: Date;
      
      if (selectedReportNameOption.id === "GET_VENDOR_REAL_TIME_INVENTORY_REPORT") {
        // RealTime Inventory: 7일 전부터
        defaultStartDate = new Date(endTime);
        defaultStartDate.setDate(endTime.getDate() - 7);
      } else {
        // Other RealTime reports: 14일 전부터  
        defaultStartDate = new Date(endTime);
        defaultStartDate.setDate(endTime.getDate() - 14);
      }
      
      // 시간 보정 없이 실제 시각 그대로 설정
      setDateRange([defaultStartDate, endTime]);
    }
  }, [selectedReportNameOption?.id]);
  
  useEffect(() => {
    // 리포트가 변경될 때 적절한 endDate로 업데이트
    if (!selectedReportNameOption || !selectedReportNameOption.id) {
      return;
    }
    if (selectedReportNameOption.id.includes("REAL_TIME")) {
      return; // REAL_TIME 리포트는 별도의 useEffect에서 날짜를 관리합니다.
    }

    const isVendorReport =
      selectedReportNameOption.id === "GET_VENDOR_SALES_REPORT" ||
      selectedReportNameOption.id === "GET_VENDOR_INVENTORY_REPORT" ||
      selectedReportNameOption.id === "GET_VENDOR_TRAFFIC_REPORT" ||
      selectedReportNameOption.id === "GET_VENDOR_NET_PURE_PRODUCT_MARGIN_REPORT";

    const maxAllowedDate = new Date();
    if (isVendorReport) {
      maxAllowedDate.setDate(maxAllowedDate.getDate() - 4);
    }
    maxAllowedDate.setHours(0, 0, 0, 0);

    if (endDate) {
      const currentEndDate = new Date(endDate);
      currentEndDate.setHours(0, 0, 0, 0);

      if (currentEndDate.getTime() > maxAllowedDate.getTime()) {
        setDateRange([startDate, maxAllowedDate]);
      }
    }
  }, [selectedReportNameOption, startDate, endDate]);
  
  useEffect(() => {
    const minDate = getMinDate();
    if (startDate && startDate < minDate) {
      setDateRange([minDate, endDate]);
    }
  }, [selectedMarketplace]);
  
  useEffect(() => {
    // Set default report type based on account type
    if (selectedProfile.account_type === 'vendor') {
      const vendorDefault = reportTypeOptions.find(option => option.type === 'VP');
      if (vendorDefault) setSelectedReportTypeOption(vendorDefault);
    } else {
      const sellerDefault = reportTypeOptions.find(option => option.type === 'SP');
      if (sellerDefault) setSelectedReportTypeOption(sellerDefault);
    }
  }, [selectedProfile.account_type]);
  
  useEffect(() => {
    // Set default report type to OPTAPEX on open/profile change
    const optapexDefault = reportTypeOptions.find(option => option.type === 'OPTAPEX');
    if (optapexDefault) setSelectedReportTypeOption(optapexDefault);
  }, [selectedProfile.account_type]);
  
  return (
    <>
      <Transition.Child
        as={Fragment}
        enter="ease-out duration-300"
        enterFrom="opacity-0"
        enterTo="opacity-100"
        leave="ease-in duration-200"
        leaveFrom="opacity-100"
        leaveTo="opacity-0"
      >
        <div className="w-full h-full bg-black/30" onClick={() => handleAddCloseClick(false)} />
      </Transition.Child>
      <Transition.Child
        as={Fragment}
        enter="ease-out duration-300"
        enterFrom="opacity-0 w-[0vw]"
        enterTo="opacity-100 w-[40vw]"
        leave="ease-in duration-200"
        leaveFrom="opacity-100 w-[40vw]"
        leaveTo="opacity-0 w-[0vw]"
      >
        <div className="absolute inset-y-0 right-0 w-[40vw] h-full overflow-hidden shadow-md">
          {/* add download report item */}
          <div className="flex-shrink-0 flex flex-col w-[40vw] h-full p-6 bg-white">
              <div className="flex-shrink-0 w-full flex items-center justify-between">
                <button onClick={() => handleAddCloseClick(false)}>
                  <XMarkIcon className="h-5 w-5 text-gray-500 hover:text-gray-800" />
                </button>
                <div className="flex items-center gap-x-4">
                  <button
                    className={cn(
                      "flex items-center gap-x-2 p-3 cursor-pointer rounded-md focus:outline-none text-sm overflow-hidden font-semibold",
                      isLoading ? "bg-gray-200 text-gray-500" : "bg-blue-100 hover:bg-blue-200 text-blue-500",
                      (isSaveLoading || isLoading) && "cursor-not-allowed"
                      )}
                    disabled={isSaveLoading || isLoading}
                    onClick={
                      async () => {
                        try {
                          await requestReportDownload();
                        } catch (error) {
                          console.error("Error in request button click handler:", error);
                          // Error is already handled in requestReportDownload function
                        }
                      }
                    }>
                      {isSaveLoading
                        ? <svg className="animate-spin h-4 w-4 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                          </svg>
                        : <CloudArrowUpIcon
                            className="flex-shrink-0 h-4 w-4"
                            aria-hidden="true"
                          />
                      }
                    <div>{td("addModal.topButton.request")}</div>
                  </button>
                </div>
              </div>
              <div className="grow relative flex flex-col w-full pl-3 pt-3">
                {/* setting for a report download */}
                <RadioGroup className="flex flex-col w-full h-full" value={selectedStepOption} onChange={setSelectedStepOption}>
                  {stepOptions.map((option) => (
                    <RadioGroup.Option
                      key={option.name}
                      value={option}
                      className={({ active, checked }) =>
                        cn(
                          "group relative flex w-full cursor-pointer border-l-2 bg-white focus:outline-none",
                          stepOptions.length === option.type ? 'min-h-[40px]' : 'min-h-[80px]',
                          option.type < stepOptions.length ? 'border-gray-200' : 'border-transparent',
                          selectedStepOption.type > option.type ? 'border-blue-400' : '',
                          checked ? 'grow' : ''
                        )
                      }
                    >
                      {({ active, checked }) => (
                        <>
                          <div className="flex w-full items-start gap-x-6">
                            <div
                              className={cn(
                                "shrink-0 flex items-center justify-center w-5 h-5 ml-[-11px] bg-white rounded-full border-2 border-gray-200 group-hover:border-gray-500",
                                selectedStepOption.type > option.type ? 'bg-blue-400 border-blue-400 group-hover:bg-blue-500 group-hover:border-blue-500' : '',
                                checked ? 'border-blue-400 group-hover:border-blue-400 bg-white' : ''
                              )}
                            >
                              { selectedStepOption.type > option.type
                                ? <Check className="w-4 h-4 stroke-white" />
                                : <div
                                    className={cn(
                                      "w-2 h-2 bg-white rounded-full",
                                      selectedStepOption.type > option.type ? 'group-hover:bg-blue-500' : 'group-hover:bg-gray-500',
                                      checked ? 'bg-blue-400 group-hover:bg-blue-400': ''
                                    )}
                                  />
                              }
                            </div>
                            <div className="flex items-center w-full">
                              <div className="w-full text-xs text-gray-400">
                                <RadioGroup.Label
                                  as="p"
                                  className={cn(
                                    "mt-[-2px] text-base font-bold",
                                    checked ? 'text-blue-400' : 'group-hover:text-gray-500',
                                    selectedStepOption.type > option.type ? 'text-blue-400 group-hover:text-blue-500' : ''
                                  )}
                                >
                                  {td(option.name)}
                                  { !checked &&
                                    (<div className="mt-1">
                                      {option.type === 1
                                        ? step1Label()
                                        : option.type === 2
                                          ? step2Label()
                                          : step3Label()
                                      }
                                    </div>)
                                  }
                                  
                                </RadioGroup.Label>
                                <Transition
                                  show={option.type === 1 && checked}
                                  enter="transition-all duration-100"
                                  enterFrom="max-h-0 opacity-0"
                                  enterTo="max-h-screen opacity-100"
                                  leave="transition-all duration-100"
                                  leaveFrom="max-h-screen opacity-100"
                                  leaveTo="max-h-0 opacity-0"
                                >
                                  {/* report type section */}
                                  {step1Content()}
                                </Transition>
                                <Transition
                                  show={option.type === 2 && checked}
                                  enter="transition-all duration-100"
                                  enterFrom="max-h-0 opacity-0"
                                  enterTo="max-h-screen opacity-100"
                                  leave="transition-all duration-100"
                                  leaveFrom="max-h-screen opacity-100"
                                  leaveTo="max-h-0 opacity-0"
                                >
                                  {/* detail report name section */}
                                  {step2Content()}
                                </Transition>
                                <Transition
                                  show={option.type === 3 && checked}
                                  enter="transition-all duration-100"
                                  enterFrom="max-h-0 opacity-0"
                                  enterTo="max-h-screen opacity-100"
                                  leave="transition-all duration-100"
                                  leaveFrom="max-h-screen opacity-100"
                                  leaveTo="max-h-0 opacity-0"
                                >
                                  {/* advanced setting section */}
                                  {step3Content()}
                                </Transition>
                              </div>
                            </div>
                          </div>
                        </>
                      )}
                    </RadioGroup.Option>
                  ))}
                </RadioGroup>
              </div>
          </div>
        </div>
      </Transition.Child>
    </>
  )
}
