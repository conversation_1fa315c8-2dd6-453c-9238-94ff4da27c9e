"use client";

import { useSession } from "next-auth/react";
import { useSearchParams } from "next/navigation";
import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import LNBComponent from "@/components/dashboard/lnb-component";
import SellerReportsLayoutComponent from "@/components/dashboard/seller-reports-layout-component";
import VendorReportsLayoutComponent from "@/components/dashboard/vendor-reports-layout-component";
import AdPortfolioLayoutComponent from "@/components/dashboard/ad-portfolio-layout-component";
import AddProfileLayoutComponent from "@/components/dashboard/add-profile-layout-component";
import { ProfileOption } from "@/components/dashboard/profile-select";
import { MarketplaceOption } from "@/components/dashboard/marketplace-select";
import { CheckBadgeIcon } from "@heroicons/react/20/solid"
import actions from "@/actions";
import ConnectProfileLayoutComponent from "@/components/dashboard/connect-profile-layout-component";
import ManageProfileLayoutComponent from "@/components/dashboard/manage-profile-layout-component";
import ManageBillingLayoutComponent from "@/components/dashboard/manage-billing-layout-component";
import HomeLayoutComponent from "@/components/dashboard/home-layout-component";
import CompetitionLayoutComponent from "@/components/dashboard/competition-layout-component";
import { api } from "@/utils/api";
import TNBComponent from "./tnb-component";
import CSButton from "./cs-button";
import BackdropReportsLayoutComponent from "./backdrop-reports-layout-component";
import BackdropAdPortfolioLayoutComponent from "./backdrop-ad-portfolio-layout-component";
import InCollectReportsLayoutComponent from "./in-collect-reports-layout-component";
import InCollectAdPortfolioLayoutComponent from "./in-collect-ad-portfolio-layout-component";
import DownloadsLayoutComponent from "./downloads-layout-component";
import BackdropCompetitionLayoutComponent from "./backdrop-competition-layout-component";
import BackdropDownloadsLayoutComponent from "./backdrop-downloads-layout-component"
import RealtimeLayoutComponent from "./realtime-layout-component";
import InCollectCompetitionLayoutComponent from "./in-collect-competition-layout-component";
import InCollectDownloadsLayoutComponent from "./in-collect-downloads-layout-component";
import BackdropRealtimeLayoutComponent from "./backdrop-realtime-layout-component";
import InCollectRealtimeLayoutComponent from "./in-collect-realtime-layout-component";


export default function DashboardLayoutComponent({
  mopUserData: initialMopUserData,
  accountButton
}: {
  mopUserData: any;
  accountButton: JSX.Element;
}) {
  const { data: session, status } = useSession();
  const router = useRouter();
  const searchParams = useSearchParams();
  const tab = searchParams.get("tab");
  const [selectedProfile, setSelectedProfile] = useState<ProfileOption | null>(
    null
  );
  const [selectedMarketplace, setSelectedMarketplace] =
    useState<MarketplaceOption | null>(null);

  // Ensure client-side fetch fills data when SSR data is missing
  const [mopUserData, setMopUserData] = useState<any>(initialMopUserData);
  useEffect(() => {
    const noProfiles = !initialMopUserData?.lwa_accounts || initialMopUserData?.lwa_accounts?.length === 0;
    if (session?.user && noProfiles) {
      api.getMember((session?.user as any).access_token).then((res) => {
        if (res) setMopUserData(res);
      }).catch(console.error);
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [session]);

  // Set sensible defaults for profile/marketplace when user data becomes available
  useEffect(() => {
    if (!selectedProfile && mopUserData?.lwa_accounts && mopUserData.lwa_accounts.length > 0) {
      setSelectedProfile(mopUserData.lwa_accounts[0]);
    }
  }, [mopUserData, selectedProfile]);
  useEffect(() => {
    if (!selectedMarketplace && selectedProfile?.marketplaces && selectedProfile.marketplaces.length > 0) {
      const us = selectedProfile.marketplaces.find((m: any) => m.country_code === 'US');
      setSelectedMarketplace(us || selectedProfile.marketplaces[0]);
    }
  }, [selectedProfile, selectedMarketplace]);

  useEffect(() => {
    if (status === "unauthenticated") {
      // Redirect to logout if session is expired
      router.push("/auth/logout");
    }
  }, [status, router]);

  // fetch mopUserData when LWA login is done(integrating Lwa user)
  useEffect(() => {
    const fetchDataFromLwaAdLogin = async () => {
      const code = searchParams.get("code");
      if (code) {
        if (!(session?.user as any).access_token) {
          console.log("access token is missing in the session.");
          return;
        }
        const integratedUserData = await actions.oauth.integrateLwaAd({
          accessToken: (session?.user as any).access_token,
          lwaCode: code,
        });
        window.location.reload();
      } else {
        console.log("LwA code is missing in the query params.");
        return;
      }
    };
    const fetchDataFromExLwaAdLogin = async () => {
      const code = searchParams.get("code");
      if (code) {
        if (!(session?.user as any).access_token) {
          console.log("access token is missing in the session.");
          return;
        }
        const integratedUserData = await actions.oauth.integrateExLwaAd({
          accessToken: (session?.user as any).access_token,
          lwaCode: code
        });
        window.location.href = "/dashboard?tab=reports"
      } else {
        console.log("LwA code is missing in the query params.");
        return;
      }
    };

    const fetchDataFromLwaSpLogin = async () => {
      const code = searchParams.get("spapi_oauth_code");
      const sellingPartnerId = searchParams.get("selling_partner_id");
      const state = searchParams.get("state");
      if (code && sellingPartnerId && state) {
        if (!(session?.user as any).access_token) {
          console.log("access token is missing in the session.");
          return;
        }
        const [country, accountType] = state.split("_");

        if (accountType === "seller") {
          const integratedUserData = await actions.oauth.integrateLwaSp({
            country,
            accessToken: (session?.user as any).access_token,
            lwaOAuthCode: code,
            sellingPartnerId: sellingPartnerId,
          });
        } else if (accountType === "vendor") {
          const integratedUserData = await actions.oauth.integrateLwaSpVendor({
            country,
            accessToken: (session?.user as any).access_token,
            lwaOAuthCode: code,
            sellingPartnerId: sellingPartnerId,
          });
        } else {
          console.error("Invalid state value.");
          return;
        }
        
        window.location.reload();
      } else {
        console.log(
          "spapi_oauth_code or selling_partner_id is missing in the query params."
        );
        return;
      }
    };
    const fetchDataFromExLwaSpLogin = async () => {
      const code = searchParams.get("spapi_oauth_code");
      const sellingPartnerId = searchParams.get("selling_partner_id");
      const state = searchParams.get("state");
      if (code && sellingPartnerId && state) {
        if (!(session?.user as any).access_token) {
          console.log("access token is missing in the session.");
          return;
        }
        const [country, accountType] = state.split("_");

        if (accountType === "seller") {
          const integratedUserData = await actions.oauth.integrateExLwaSp({
            country,
            accessToken: (session?.user as any).access_token,
            lwaOAuthCode: code,
            sellingPartnerId: sellingPartnerId,
          });
        } else if (accountType === "vendor") {
          const integratedUserData = await actions.oauth.integrateExLwaSpVendor({
            country,
            accessToken: (session?.user as any).access_token,
            lwaOAuthCode: code,
            sellingPartnerId: sellingPartnerId,
          });
        } else {
          console.error("Invalid state value.");
          return;
        }
        window.location.href = "/dashboard?tab=manage-profile"
      } else {
        console.log(
          "spapi_oauth_code or selling_partner_id is missing in the query params."
        );
        return;
      }
    };
    if (tab === "manage-profile" || tab === "connect-profile") {
      fetchDataFromExLwaAdLogin()
      fetchDataFromExLwaSpLogin()
    } else if(tab === "add-profile") {
      fetchDataFromLwaAdLogin()
      fetchDataFromLwaSpLogin()
    }
  }, [searchParams]);

  // fetch new profile data when user is new and redirect to manage-profile tab
  useEffect(() => {
    if (mopUserData && mopUserData.role === "NEW" && tab !== "manage-profile") {
      if (session?.user) {
        fetchNewProfile()
      }
    }
  }, [mopUserData])
  const [selectedNewProfile, setSelectedNewProfile] = useState<any>(null)
	const fetchNewProfile = async () => {
		const memberResponse = await api.getExMember((session?.user as any).access_token)
		setSelectedNewProfile(memberResponse)
	}
  useEffect(() => {
    const validUser = selectedNewProfile?.amazon_accounts
			&& selectedNewProfile.amazon_accounts.length > 0
    if (selectedNewProfile && !validUser && tab !== "add-profile") {
      window.location.href = "/dashboard?tab=manage-profile"
    }
  }, [selectedNewProfile])

  return (
    <div className="relative size-full flex items-center justify-center">
      {/* left column */}
      <LNBComponent
        mopUserData={mopUserData}
        selectedProfile={selectedProfile}
        selectedMarketplace={selectedMarketplace}
        accountButton={accountButton}
      />
      {/* right content */}
      <div className="relative flex flex-col justify-between grow h-full overflow-hidden">
        <TNBComponent
          mopUserData={mopUserData}
          selectedProfile={selectedProfile}
          setSelectedProfile={setSelectedProfile}
          selectedMarketplace={selectedMarketplace}
          setSelectedMarketplace={setSelectedMarketplace}
        />
        <div className="pt-16 flex-auto size-full mx-auto">
          <div className="relative hidden sm:block grow h-full overflow-y-scroll bg-gray-100">
            {tab === "home" && selectedProfile && selectedMarketplace
              ? (
                <HomeLayoutComponent
                  mopUserData={mopUserData}
                  selectedProfile={selectedProfile}
                  selectedMarketplace={selectedMarketplace}
                />
              )
              : tab === "competition"
                ? selectedProfile && selectedMarketplace && selectedMarketplace?.subscription_yn === "Y"
                  ? selectedMarketplace.status === "IN_COLLECT"
                    ? <InCollectCompetitionLayoutComponent
                          mopUserData={mopUserData}
                          selectedProfile={selectedProfile}
                          selectedMarketplace={selectedMarketplace}
                        />
                    : <CompetitionLayoutComponent
                        mopUserData={mopUserData}
                        selectedProfile={selectedProfile}
                        selectedMarketplace={selectedMarketplace}
                      />
                  : <BackdropCompetitionLayoutComponent
                      mopUserData={mopUserData}
                    />
                : tab === "reports"
                  ? selectedProfile && selectedMarketplace && selectedMarketplace?.subscription_yn === "Y"
                    ? selectedMarketplace.status === "IN_COLLECT"
                      ? <InCollectReportsLayoutComponent
                          mopUserData={mopUserData}
                          selectedProfile={selectedProfile}
                          selectedMarketplace={selectedMarketplace}
                        />
                      : selectedProfile.account_type === "vendor"
                        ? <VendorReportsLayoutComponent
                            mopUserData={mopUserData}
                            selectedProfile={selectedProfile}
                            selectedMarketplace={selectedMarketplace}
                          />
                        : <SellerReportsLayoutComponent
                            mopUserData={mopUserData}
                            selectedProfile={selectedProfile}
                            selectedMarketplace={selectedMarketplace}
                          />
                    : <BackdropReportsLayoutComponent
                        mopUserData={mopUserData}
                      />
                  : tab === "ad-portfolio"
                    ? selectedProfile && selectedMarketplace && selectedMarketplace?.subscription_yn === "Y"
                      ? selectedMarketplace.status === "IN_COLLECT"
                        ? <InCollectAdPortfolioLayoutComponent
                            mopUserData={mopUserData}
                            selectedProfile={selectedProfile}
                            selectedMarketplace={selectedMarketplace}
                          />
                        : <AdPortfolioLayoutComponent
                            mopUserData={mopUserData}
                            selectedProfile={selectedProfile}
                            selectedMarketplace={selectedMarketplace}
                          />
                      : <BackdropAdPortfolioLayoutComponent
                          mopUserData={mopUserData}
                        />
                    : tab === "realtime"
                     ? selectedProfile && selectedMarketplace && selectedMarketplace?.subscription_yn === "Y" 
                      ? selectedMarketplace.status === "IN_COLLECT"
                        ? <InCollectRealtimeLayoutComponent
                          mopUserData={mopUserData}
                          selectedProfile={selectedProfile}
                          selectedMarketplace={selectedMarketplace}
                        />
                        : <RealtimeLayoutComponent
                            mopUserData={mopUserData}
                            selectedProfile={selectedProfile}
                            selectedMarketplace={selectedMarketplace}
                          />
                        : <BackdropRealtimeLayoutComponent
                            mopUserData={mopUserData}
                          />
                      : tab === "downloads"
                        ? selectedProfile && selectedMarketplace && selectedMarketplace?.subscription_yn === "Y" 
                          ? selectedMarketplace.status === "IN_COLLECT"
                            ? <InCollectDownloadsLayoutComponent
                              mopUserData={mopUserData}
                              selectedProfile={selectedProfile}
                              selectedMarketplace={selectedMarketplace}
                            />
                            : <DownloadsLayoutComponent
                                mopUserData={mopUserData}
                                selectedProfile={selectedProfile}
                                selectedMarketplace={selectedMarketplace}
                              />
                          : <BackdropDownloadsLayoutComponent
                              mopUserData={mopUserData}
                            />
                        : tab === "add-profile"
                          ? (
                            <AddProfileLayoutComponent
                              mopUserData={mopUserData}
                            />
                          )
                          : tab === "connect-profile"
                            ? (
                              <ConnectProfileLayoutComponent
                                mopUserData={mopUserData}
                              />
                            )
                            : tab === "manage-profile"
                              ? (
                                <ManageProfileLayoutComponent
                                  mopUserData={mopUserData}
                                />
                              )
                              : tab === "manage-billing"
                                ? (
                                  <ManageBillingLayoutComponent
                                    mopUserData={mopUserData}
                                  />
                                )
                                : ""
            }
          </div>
        </div>
        <CSButton 
          mopUserData={mopUserData}
          selectedProfile={selectedProfile}
          setSelectedProfile={setSelectedProfile}
          selectedMarketplace={selectedMarketplace}
          setSelectedMarketplace={setSelectedMarketplace}
        />
      </div>
    </div>
  );
}
