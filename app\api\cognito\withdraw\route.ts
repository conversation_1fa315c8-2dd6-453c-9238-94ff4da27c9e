import { ErrorResponse } from "@/app/api/_models";
import { getServerApiHostUrl } from "@/utils/host";
import { NextRequest, NextResponse } from "next/server";

export type WithDrawAccountResponse = {
  status: string;
  message: string;
};

export async function POST(
  request: NextRequest
): Promise<NextResponse<WithDrawAccountResponse | ErrorResponse>> {
  const accessToken = request.headers.get("Authorization");

  if (!accessToken) {
    return NextResponse.json(
      { message: "Authorization header is missing" },
      { status: 400 }
    );
  }
  const withDrawAccountResponse = await fetch(
    `${await getServerApiHostUrl()}/api/cognito/withdraw`,
    {
      method: "POST",
      headers: {
        "Authorization": `Bearer ${accessToken}`,
        "Content-Type": "application/json",
      },
      cache: "no-cache",
    }
  ).then((res) => res.json());

  return NextResponse.json(withDrawAccountResponse, { status: 200 });
}
