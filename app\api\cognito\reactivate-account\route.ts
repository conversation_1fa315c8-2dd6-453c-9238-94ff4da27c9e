import { ErrorResponse } from "@/app/api/_models";
import { getServerApiHostUrl } from "@/utils/host";
import { NextRequest, NextResponse } from "next/server";

export type ReactivateAccountResponse = {
  status: string;
  message: string;
};

export async function POST(
  request: NextRequest
): Promise<NextResponse<ReactivateAccountResponse | ErrorResponse>> {
  const accessToken = request.headers.get("Authorization");
  const body = await request.json();

  if (!accessToken) {
    return NextResponse.json(
      { message: "Authorization header is missing" },
      { status: 400 }
    );
  }
  const reactivateAccountResponse = await fetch(
    `${await getServerApiHostUrl()}/api/cognito/reactivate-account`,
    {
      method: "POST",
      headers: {
        "Authorization": `Bearer ${accessToken}`,
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        "success_url": body?.success_url,
        "fail_url": body?.fail_url,
      }),
      cache: "no-cache",
    }
  ).then((res) => res.json());

  return NextResponse.json(reactivateAccountResponse, { status: 200 });
}
