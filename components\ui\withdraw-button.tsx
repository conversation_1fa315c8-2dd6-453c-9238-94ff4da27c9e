'use client';

import { useTranslations } from "next-intl"
import { signOut, useSession } from "next-auth/react"
import { useState } from 'react'
import { Button, Dialog, DialogPanel, DialogTitle, Transition, TransitionChild } from '@headlessui/react'
import { api } from "@/utils/api"
import { cn } from "@/utils/msc"
import { getEnvValue } from "@/utils/host"

export function WithdrawButton() {
  const t = useTranslations('component')
  const { data: session, status } = useSession()
  const [isWithdrawModalOpen, setIsWithdrawModalOpen] = useState(false)
  const [isWithdrawLoading, setIsWithdrawLoading] = useState(false)
  return (
    <div className="w-full">
      <button
        className="w-full pb-2 px-4 justify-start text-gray-400 hover:text-gray-600 text-[10px] underline text-left"
        onClick={() => setIsWithdrawModalOpen(true)}
      >
        {t("gnb.account.withdraw")}
      </button>
      <Transition appear show={isWithdrawModalOpen}>
        <Dialog as="div" className="relative z-20 focus:outline-none pointer-events-auto" onClose={() => setIsWithdrawModalOpen(false)}>
          <div className="fixed inset-0 bg-black/30 backdrop-blur-sm" aria-hidden="true" />
          <div className="fixed inset-0 z-20 w-screen overflow-y-auto">
            <div className="flex min-h-full items-center justify-center p-4">
              <TransitionChild
                enter="ease-out duration-300"
                enterFrom="opacity-0 transform-[scale(95%)]"
                enterTo="opacity-100 transform-[scale(100%)]"
                leave="ease-in duration-200"
                leaveFrom="opacity-100 transform-[scale(100%)]"
                leaveTo="opacity-0 transform-[scale(95%)]"
              >
                <DialogPanel className="w-full max-w-md rounded-xl bg-white p-6">
                  <DialogTitle as="h3" className="text-base/7 font-medium text-gray-600">
                    Delete Your Account
                  </DialogTitle>
                  <p className="mt-2 text-sm/6 text-gray-500">
                    Are you sure you want to delete your account?
                    <br/>
                    All of your related information will be permanently deleted and cannot be reverted.
                  </p>
                  <div className="mt-4 flex items-center justify-end gap-x-4">
                    <Button
                      className="inline-flex items-center gap-2 rounded-md bg-white py-1.5 px-3 text-sm/6 font-semibold text-gray-500 shadow-inner shadow-white/10 focus:outline-none hover:bg-gray-100 open:bg-gray-700 focus:outline-1 focus:outline-white"
                      onClick={() => setIsWithdrawModalOpen(false)}
                    >
                      Cancel
                    </Button>
                    <Button
                      onClick={async () => {
                        if (!(session?.user as any).access_token) {
                          console.log('access token is missing in the session.')
                        }
                        setIsWithdrawLoading(true)
                        let response = await api.withdrawAccount(
                          (session?.user as any).access_token,
                        )
                        setIsWithdrawLoading(false)
                        setIsWithdrawModalOpen(false)
                        if (response.status !== "success") {
                          throw new Error("Network response was not ok");
                        } else {
                          const authDomain = await getEnvValue('AUTH_DOMAIN')
                          const cognitoClientId = await getEnvValue('COGNITO_CLIENT_ID')
                          const nextAuthUrl = await getEnvValue('NEXTAUTH_URL')
                          await signOut({
                            redirect: false
                          }).then(() => {
                            window.location.href = `https://${authDomain}/logout?client_id=${cognitoClientId}&logout_uri=${nextAuthUrl}/auth/login&redirect_uri=${nextAuthUrl}/auth/login&response_type=code`
                          });
                        }
                      }}
                      className={cn(
                        "inline-flex items-center gap-2 rounded-md bg-red-600 py-1.5 px-3 text-sm/6 font-semibold text-white shadow-inner shadow-white/10 focus:outline-none data-[hover]:bg-red-700 data-[open]:bg-gray-700 data-[focus]:outline-1 data-[focus]:outline-white cursor-pointer",
                        isWithdrawLoading && "cursor-not-allowed"
                      )}
                      disabled={isWithdrawLoading}
                    >
                      {isWithdrawLoading
                        ? <svg className="animate-spin h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                          </svg>
                        : ""
                      }
                      <div>{t("gnb.account.withdraw")}</div>
                    </Button>
                  </div>
                </DialogPanel>
              </TransitionChild>
            </div>
          </div>
        </Dialog>
      </Transition>
    </div>
  )
}