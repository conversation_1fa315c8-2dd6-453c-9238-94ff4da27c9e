{"AppLayout": {"home": "首页", "logout": "退出登录", "profile": "个人资料"}, "HomePage": {"title": "首页"}, "LocaleSwitcher": {"ko": "한국어", "en": "English", "cn": "中文", "label": "语言选择"}, "LoginPage": {"description": "请输入您的登录信息", "email": "电子邮箱", "invalidCredentials": "请检查您的登录凭证", "invalidEmail": "请输入有效的邮箱地址", "invalidPassword": "请输入密码", "login": "登录", "password": "密码", "title": "欢迎回来"}, "component": {"gnb": {"logo": "OptApex", "account": {"logout": "退出", "withdraw": "注销账户"}}, "accounts": {"title": "管理您的亚马逊账户", "description": "您需要两个亚马逊账户才能完成数据关联。两个账户可以是SP和Ads的账户组合，也可以是Vendor和Ads的账户组合。", "noProductData": "产品数据未连接", "productData": "产品数据", "authorizeAds": "授权广告", "authorizeSP": "授权 SP", "authorizeVendor": "授权 Vendor", "selectRegion": "选择地区", "selectRegionDescription": "请选择站点区域来授权您的亚马逊广告账户。", "chooseProductData": "请选择一个产品数据进行连接", "productDataAvailable": "个产品数据可用", "type": "类型", "actions": "操作", "cancel": "取消", "confirm": "确认", "chooseProductType": "选择要授权产品中心数据的账号类型", "authorizationDescription": "SP 或 Vendor 产品中心数据授权成功后，该账号下每个区域站点的产品中心与广告数据均将进行关联，无需逐个授权操作。", "areSeller": "您是个体卖家?", "areVendor": "您是供应商?", "notConnected": "未连接"}, "lnb": {"profile": {"region": "运营区域", "profileEmail": "账户邮箱", "profileName": "账户名称", "profileId": "账户ID"}, "marketplace": {"country": {"usa": "美国", "canada": "加拿大", "mexico": "墨西哥", "japan": "日本", "china": "中国"}, "currency": {"USD": "美元", "CAD": "加元", "MXN": "墨西哥比索", "JPY": "日元", "CNY": "人民币"}, "title": {"usMarketTitle": "{{marketplace.country.usa}} {{marketplace.currency.USD}}", "caMarketTitle": "{{marketplace.country.canada}} {{marketplace.currency.CAD}}", "mxMarketTitle": "{{marketplace.country.mexico}} {{marketplace.currency.MXN}}", "jpMarketTitle": "{{marketplace.country.japan}} {{marketplace.currency.JPY}}", "cnMarketTitle": "{{marketplace.country.china}} {{marketplace.currency.CNY}}"}, "marketplaceName": {"label": "电商平台名称", "content": {"usMarketplace": "Amazon.com", "caMarketplace": "Amazon.ca", "mxMarketplace": "Amazon.com.mx", "jpMarketplace": "Amazon.co.jp", "cnMarketplace": "Amazon.cn"}}, "marketplaceId": {"label": "平台ID"}}, "menu": {"accounts": "账户管理", "home": "首页", "competition": "品牌保护", "keywords": "关键词", "reports": "数据报告", "optimizationSets": "优化集管理", "realtime": "实时监控", "downloads": "报告下载", "notifications": "系统通知", "manageAccounts": "账户设置", "manageBillings": "账单管理"}}, "calendar": {"month": ["一月", "二月", "三月", "四月", "五月", "六月", "七月", "八月", "九月", "十月", "十一月", "十二月"], "days": ["日", "一", "二", "三", "四", "五", "六"]}, "eligibility": {"eligible": "符合条件", "ineligible": "不符合条件", "unknown": "状态未知"}, "productStatus": {"mopAuto": "优化中", "amazonAuto": "Amazon Auto", "ineligible": "不可用", "initializing": "初始化中", "processing": "处理中", "paused": "已暂停", "warning": "警告", "error": "错误"}, "campaignType": {"auto": "自动模式", "manual": "手动模式"}, "portfolioStatus": {"initializing": "初始化中", "processing": "处理中", "paused": "已暂停", "active": "运行中", "expired": "已过期", "warning": "警告"}, "downloadStatus": {"creating": "创建中", "created": "已创建", "error": "错误", "expired": "已过期"}, "adStatus": {"enabled": "启用中", "paused": "已暂停"}, "accountStatus": {"initializing": "初始化中", "processing": "处理中", "paused": "已暂停", "active": "运行中"}, "filter": {"dateRange": {"label": "日期范围"}, "compareWith": {"label": "对比", "content": {"null": "None", "previousMonth": "上月同期", "previousYear": "去年同期", "customPeriod": "自定义"}}}, "status": {"notAvailable": "不可用", "connected": "已连接", "activated": "已激活", "activate": "激活"}, "billing": {"title": "管理您的账单", "table": {"header": {"status": "状态", "accountsPair": "账户配对", "updatedDate": "更新日期", "pairActions": "配对操作", "billing": "账单"}}}}, "CompetitionPage": {"daily": "每日", "weekly": "每周", "topBannerTitle": "品牌保护分数", "tableTitle": "我的品牌关键词保护状态", "weeklyFunnel": {"topButton": "周报漏斗", "modalTitle": "周报漏斗", "targetPeriod": "目标时间段", "targetKeywords": "目标关键词", "myBrandShare": "我的品牌份额", "competitorShare": "竞争对手份额", "table": {"header": {"keyword": "关键词", "impressionShare": "展示份额", "leadingProducts": "领先产品"}}}, "keywordModalTitle": "关键词竞争状态", "overview": {"weeklyAvgKeywordRank": "每周平均关键词排名", "weeklyAvgRelKeywordRank": "每周平均相对关键词排名", "weeklyBrandKeywordLeadershipRate": "每周品牌关键词领导力比率"}, "searchBar": {"placeholder": "按关键词搜索"}, "tooltip": {"brandProtectionScore": "品牌保护分数可帮助您了解品牌关键词在竞争中的保护程度。分数范围为0到1, 分数越高表示保护越强。<enter></enter><enter></enter>该分数基于LG Optapex分类算法选取的品牌关键词点击份额及竞争对手数量计算得出。", "keywordModalTitle": "您可以查看您的品牌在主要竞争对手中的点击份额领先趋势。<enter></enter><enter></enter>主要竞争对手是基于LG Optapex算法,根据产品排名、价格、Listing信息以及关键词与我品牌产品的关系选取的。"}, "table": {"header": {"queryVolumeRank": "搜索量排名", "myBrandKeyword": "我的品牌关键词", "myBrandClickShare": "我的品牌点击份额", "leadingProducts": "领先产品"}}, "keywordModal": {"myBrandTotalClickShare": "我的品牌总点击份额", "keyCompetitorsTotalClickShare": "主要竞争对手总点击份额"}}, "DashboardPage": {"title": "亚马逊运营看板", "filter": {"dateRange": {"label": "日期范围"}, "attributionWindow": {"label": "归因周期", "content": {"1d": "1天", "7d": "7天", "14d": "14天", "30d": "30天"}}, "optimizationSet": {"label": "优化集设置", "content": {"optSet": "优化集设置", "null": "无"}}, "productFilter": {"label": "商品筛选"}, "compareWith": {"label": "对比", "content": {"null": "None", "previousMonth": "上月同期", "previousYear": "去年同期", "customPeriod": "自定义"}}, "customPeriod": {"label": "自定义时间段"}, "advancedFilter": {"label": "高级筛选", "subLabel": {"lowInventory": "库存不足", "excessInventory": "库存过剩", "recommendedAsin": "推荐ASIN"}, "content": {"null": "未选择", "low30d": "商品供应天数 < 30天", "low60d": "商品供应天数 < 60天", "low90d": "商品供应天数 < 90天", "excess180d": "商品供应天数 > 180天", "excess270d": "商品供应天数 > 270天", "excess365d": "商品供应天数 > 365天", "growth": "增长潜力", "efficiency": "效率化潜力"}}}, "keyMetrics": {"totalMetrics": {"totalSales": "总销售额", "totalRevenue": "Revenue", "totalCogsRevenue": "Cogs Revenue", "estFee": "预估费用", "estMargin": "预估利润率"}, "adMetrics": {"adSales": "广告销售额", "adSpend": "广告花费", "roas": "ROAS"}, "metricsTooltip": {"estimatedMargin": "(总销售额 - 退货金额 - 预估费用 - 广告花费) / 总销售额 * 100", "vendorEstimatedMargin": "(cogs revenue - 退货金额 - 预估费用 - 广告花费) / cogs revenue * 100", "totalSales": "<first>${value}的退货金额未被计入。</first>", "totalCogsRevenue": "<first>Cogs revenue 指亚马逊向供应商支付的货款金额。 {value} 的 shipped revenue 指亚马逊实际销售总和。</first>", "totalRevenue": "Revenue 即客户为商品实际支付金额总和。", "estFee": "FBA费用 + 销售佣金 + 仓储费 (根据时间跨度)", "roas": "(SP sales + SD sales) / (SP spend + SD spend) * 100"}}, "graph": {"tab": {"daily": "每日", "hourly": "每小时", "tabTooltip": {"hourly": "显示查询期间特定小时的平均值。"}}, "totalMetrics": {"absolute": {"totalSales": "总销售额", "estMargin": "预估利润率", "pageViews": "PV"}, "compared": {"totalSales": "总销售额 (对比)", "estMargin": "预估利润率 (对比)", "pageViews": "PV (对比)"}}, "adMetrics": {"absolute": {"adSales": "广告销售额", "roas": "ROAS", "adClicks": "广告点击量"}, "compared": {"adSales": "广告销售额 (对比)", "roas": "ROAS (对比)", "adClicks": "广告点击量 (对比)"}}, "legendSelect": {"sales": "总销售额", "adSales": "广告销售额", "adSalesSameSku": "广告直接订单金额", "sdSalesPromotedClick": "SD Sales Promoted Clicks", "pageViews": "PV", "clicks": "广告点击量", "impressions": "曝光量", "sessions": "会话量", "roas": "ROAS", "estimatedMargin": "预估利润率", "comparedSales": "总销售额 (对比)", "comparedAdSales": "广告销售额 (对比)", "comparedAdSalesSameSku": "广告直接订单金额 (对比)", "comparedSdSalesPromotedClick": "SD Sales Promoted Clicks (对比)", "comparedPageViews": "PV (对比)", "comparedClicks": "广告点击量 (对比)", "comparedImpressions": "曝光量 (对比)", "comparedSessions": "会话量 (对比)", "comparedRoas": "ROAS (对比)", "comparedEstimatedMargin": "预估利润率 (对比)", "revenue": "Revenue", "cogsRevenue": "Cogs Revenue", "adCost": "广告花费", "glanceViews": "Glance Views", "comparedRevenue": "Revenue (对比)", "comparedCogsRevenue": "Cogs Revenue (对比)", "comparedAdCost": "广告花费 (对比)", "comparedGlanceViews": "Glance Views (对比)", "orderedUnits": "订单数量", "adOrders": "Ad Orders"}}, "table": {"header": {"products": "商品列表", "totalPerformance": "整体表现", "adPerformance": "广告表现"}, "subHeader": {"productsAdInfo": "商品与广告信息"}, "content": {"products": {"rank": "<first><second>#{value1}</second> 在 {value2} 中排名</first>", "fee": "FEE", "points": "积分", "inventory": "库存状态", "inventoryType": {"available": "可售库存", "unfulfillable": "不可售库存", "reserved": "预留库存", "inbound": "在途库存", "openPurchase": "待采购库存"}, "asin": "ASIN", "sku": "SKU"}, "totalMetrics": {"cogsRevenue": "Cogs Revenue", "totalRevenue": "Shipped Revenue", "sales": "销售额", "estFee": "预估费用", "shippedUnits": "销售件数", "unitsSold": "销售件数", "unitsReturned": "退货件数", "salesReturned": "退货金额", "sessions": "会话量", "buyBoxPercentage": "Buy Box %", "glanceViews": "Glance Views", "pageViews": "PV", "cvrTotal": "CVR (Total)"}, "adMetrics": {"adSpend": "广告花费", "adSales": "广告销售额", "adUnitsSold": "SP 销售件数", "attributedSalesSameSku": "SP 直接订单金额", "unitsSoldSameSku": "SP 直接订单销售件数", "tacos": "TACOS", "profit": "利润", "acos": "ACOS", "spAcos": "SP ACOS", "roas": "ROAS", "impressions": "曝光量", "spImpressions": "SP 曝光量", "clicks": "点击量", "spClicks": "SP 点击量", "ctr": "CTR", "spCtr": "SP CTR", "avgCPC": "Avg.CPC", "spAvgCPC": "SP Avg.CPC", "cvrAds": "CVR (Ad)", "spCvr": "SP CVR", "spCost": "SP 花费", "spSales": "SP 销售额", "spRoas": "SP ROAS", "sdSales": "SD 销售额", "sdSalesPromotedClicks": "SD Promoted Clicks", "sdCost": "SD 花费", "sdUnitsSold": "SD 销售件数", "sdClicks": "SD 点击量", "sdImpressions": "SD 曝光量", "sdImpressionsViews": "SD 可视曝光量", "sdCumulativeReach": "SD 累计触达人数", "sdDetailPageViews": "SD 详情页浏览量", "sdNewToBrandDetailPageViews": "SD 新客详情页浏览量", "sdNewToBrandSales": "SD 品牌新客销售额", "sdRoas": "SD ROAS", "sdAcos": "SD ACOS", "sdConvRate": "SD CVR", "sdViewClickThroughRate": "SD View CTR", "sdNewToBrandSalesRate": "SD 新客销售占比", "sdImpressionFrequencyAverage": "SD 广告平均曝光频次", "sdViewabilityRate": "SD 可视曝光率", "sdNewToBrandDetailPageViewRate": "SD 新客详情页浏览量占比"}}, "adDetailRow": {"header": {"status": "状态", "runBy": "运营", "campaign": "广告活动"}, "content": {"status": {"enabled": "启用中", "paused": "已暂停", "archived": "已归档"}, "runBy": {"amazon": "Amazon", "mop": "OptApex"}, "campaign": {"id": "活动ID", "budget": "日预算", "type": {"auto": "自动", "manual": "手动"}}}}}, "viewSetting": {"rowsPerPage": "每页显示条数", "currentRow": "当前第{value1}条/共{value2}条"}}, "optimizationSets": {"title": "优化集设置", "topButton": {"addNew": "新建优化集"}, "searchBar": {"placeholder": "按优化集名称搜索"}, "optSetList": {"header": {"status": "状态", "name": "名称", "period": "投放周期", "budgetPacing": "预算使用率", "creationDate": "创建时间"}, "content": {"optSetRow": {"status": {"active": "运行中", "paused": "已暂停"}, "statusAlert": {"abnormal": "异常", "warning": "警告"}, "name": {"optSet": "优化集设置", "topProduct": "主推商品"}, "period": {"optObjective": {"maxSales": "销售额最大化", "vendorMaxSales": "vendor销售额最大化", "maxProfit": "利润最大化", "vendorMaxProfit": "vendor利润最大化", "maxAdSales": "广告销售额最大化", "maxRoas": "ROAS最大化"}}, "budgetPacing": {"totalBudget": "总预算", "monthlyBudget": "月度预算"}, "searchResultNull": "未找到相关记录", "optSetNull": "尚未创建优化集"}, "productRow": {"statusAlert": {"abnormal": "异常", "warning": "警告"}, "asin": "ASIN", "sku": "SKU"}}}, "detailModal": {"accountPair": {"message": {"dateDifference": "<first>{value}</first> 天前已完成账户关联", "initialWarning": "通常需要1~2周完成数据同步"}}, "optSet": {"topButton": {"edit": "编辑", "resume": "恢复运行", "pause": "暂停", "delete": "删除"}, "topStatus": {"active": "运行中", "paused": "已暂停"}, "message": {"ineligibleDetails": "<first>{value}</first> <second><red>存在不符合条件的商品</red></second>", "totalBudget": "<first>{value}</first> 的预算分配", "expectedUsage": "<first>{value}</first> 的预计消耗", "feasibleBudgetRange": "<second>可行的预算范围</second> <first>{value}</first>", "and": " 与 ", "ineligible": "存在不符合条件的商品", "budgetDecrease": "需调低预算", "budgetIncrease": "需调高预算"}, "budgetSensitivityGraph": {"title": "预算灵敏度分析", "cta": "查找您的最佳预算", "tooltip": {"budgetGuideline": "预算指导是根据您当前优化集的状态，分析近期表现和趋势，预测并建议最佳预算区间。"}, "warning": "查看您未来7天的预计表现。", "maximum": "最大化", "minimum": "最小化", "optimalBudget": "最优预算", "optimizationTarget": "优化目标", "optimizationNotStarted": "开始模拟以查找您的最优预算。", "startSimulation": "开始模拟", "totalProfit": {"label": "总利润"}, "totalSales": {"label": "总销售额"}, "totalCost": {"label": "总成本"}, "adSpend": {"label": "广告花费"}, "adSales": {"label": "广告销售额"}, "adSalesSameSku": {"label": "广告直接订单金额"}}, "budgetPaceGraph": {"title": {"daterangeBudget": "预算使用进度", "monthlyBudget": "月度预算进度"}, "budgetType": {"noTracking": {"label": "未跟踪", "details": "尚未完成最终核算"}, "abnormal": {"label": "异常", "underAbnormal": {"details": "消耗速度过慢", "action": "调低预算"}, "overAbnormal": {"details": "消耗速度过快", "action": "调高预算"}}, "normal": {"label": "正常", "details": "预算使用符合预期"}}, "metricType": {"usage": {"daterange": "预算消耗量", "monthly": "预算消耗量"}, "usageExpected": "预计总消耗", "budget": {"daterange": "总预算额度", "monthly": "总预算额度"}}}, "optProfile": {"label": "优化集设置", "optSetName": "优化集名称 {value}", "optSetObjective": {"label": "优化目标", "objectiveType": {"maxSales": "销售额最大化", "vendorMaxSales": "vendor销售额最大化", "maxProfit": "利润最大化", "vendorMaxProfit": "vendor利润最大化", "maxAdSales": "广告销售额最大化", "maxRoas": "ROAS最大化"}, "option": {"default": "未选择加速选项", "minInventory": "库存最小化", "boostImpression": "曝光增强"}}, "optSetPeriod": {"label": "投放周期", "policy": {"daterange": "自定义", "monthly": "每月自动续期"}, "createdDate": "创建时间"}}, "budgetTab": {"tabLabel": "预算优化", "searchBar": {"placeholder": "按商品标题或ASIN搜索"}, "table": {"header": {"status": "状态", "productInfo": "商品信息", "totalBudgetUsage": "预算消耗量"}, "content": {"statusAlert": {"warning": "警告", "abnormal": "异常"}, "productInfo": {"asin": "ASIN", "sku": "SKU"}, "searchResultNull": "未找到相关记录", "optSetNull": "尚未创建优化集"}}}, "historyTab": {"tabLabel": "历史", "filter": {"dateRange": {"label": "日期范围"}}, "table": {"header": {"actionType": "操作类型", "actionDetails": "操作详情", "dateTime": "操作时间"}, "content": {"actionType": {"create": "创建优化集", "edit": "编辑优化集", "pauseResume": "暂停/恢复运行"}, "actionDetails": {"budgetPolicy": "预算策略", "budgetAmount": "预算金额", "budgetStartDate": "开始时间", "budgetEndDate": "结束时间"}, "null": "无操作记录"}}}}, "product": {"message": {"abnormal": {"ineligibleAlert": "此商品不符合条件", "action": "<first>请前往 <red>亚马逊卖家中心</red> 进行调整</first>"}, "warning": {"lowStockAlert": "库存不足", "estimatedSold": "<orange1>{value}</orange1> <first>预计每日售出</first>", "available": "<orange1>{value}</orange1> <first>当前可用库存</first>", "estimatedDaysLeft": "<orange1>{value}</orange1> <first>预计售罄天数</first>"}}, "productProfile": {"label": {"optSet": "优化集设置", "asin": "ASIN", "sku": "SKU"}}, "campaignTab": {"tabLabel": "广告活动", "tabTooltip": "亚马逊自动投放广告（前2周）→ OptApex智能优化（后续）", "cumulatedBudgetUsage": "累计预算消耗", "table": {"header": {"status": "状态", "campaignInfo": "活动信息", "dailyBudgetUsage": "日预算消耗"}, "content": {"status": {"enabled": "启用中", "paused": "已暂停"}, "campaignInfo": {"id": "活动ID", "startDate": "启动时间"}}}, "donutChart": {"dailyBudgetUsage": "日预算消耗占比"}}, "productHistoryTab": {"tabLabel": "操作历史", "filter": {"history": {"label": "筛选条件", "type": {"operation": "操作类型", "budget": "预算调整"}}, "date": {"label": "日期范围"}}, "table": {"header": {"actionType": "操作类型", "from": "起始值", "to": "结束值", "datetime": "操作时间"}, "content": {"operation": {}, "budget": {}, "null": "无记录"}}}, "targetTab": {"tabLabel": "Target", "filter": {"date": {"label": "日期范围"}}, "table": {"header": {"actionType": "操作类型", "from": "起始值", "to": "结束值", "datetime": "操作时间"}, "content": {"actionType": {}, "itemChanged": {}, "null": "无记录"}}}, "bidHistoryTab": {"tabLabel": "竞价历史", "filter": {"date": {"label": "日期范围"}}, "table": {"header": {"actionType": "操作类型", "from": "起始值", "to": "终止值", "datetime": "操作时间"}, "content": {"actionType": {}, "itemChanged": {}, "null": "无历史记录"}}}}, "addEditModal": {"topButton": {"save": "保存配置"}, "bulkSelection": {"openModal": "批量选择", "description": "粘贴包含ASIN或SKU的csv或txt文件以批量选择产品。", "select": "选择", "textarea": {"placeholder": "请输入ASIN，使用逗号、空格或换行分隔。"}, "import": {"placeholder": "导入CSV或TXT文件", "tooltip": "您可以点击此处选择文件。可下载示例文件。", "sampleFile": "下载示例文件"}, "matchedProducts": "匹配的产品"}, "limitReachedMessage": "LITE版本最多只能创建1个优化集。", "objective": {"label": "优化目标", "subLabel": "优化策略", "optObjectiveType": {"maxSales": {"label": "销售额最大化", "vendorLabel": "vendor销售额最大化", "dataSource": "SP数据+广告数据", "vendorDataSource": "vendor数据 + 广告数据", "details": "综合自然流量与广告数据实现销量最大化", "vendorDetails": "结合vendor数据与广告数据达成目标", "option": "库存最小化"}, "maxProfit": {"label": "利润最大化", "vendorLabel": "vendor利润最大化", "dataSource": "SP数据+广告数据", "vendorDataSource": "vendor数据 + 广告数据", "details": "综合自然流量与广告数据实现利润最大化", "vendorDetails": "结合vendor数据与广告数据达成目标", "option": "库存最小化"}, "maxAdSales": {"label": "广告销售额最大化", "dataSource": "广告数据", "details": "基于广告数据提升广告销售额", "option": "曝光增强"}, "maxRoas": {"label": "ROAS最大化", "dataSource": "广告数据", "details": "优化广告投资回报率", "option": "曝光增强"}}, "advancedOptions": {"label": "高级选项", "competitionTactic": {"label": "竞争策略", "option": {"none": "无", "brandDefense": "品牌保护", "competitorConquesting": "竞品抢占"}}, "salesTargeting": {"label": "销售目标策略", "option": "仅针对直接转化SKU"}, "tooltip": {"competitionTactic": "竞争策略为您提供基于营销目标的高级优化方案:<enter></enter>品牌保护侧重于提升LG Optapex专有算法选取的品牌关键词点击份额。<enter></enter>竞品抢占则侧重于提升LG Optapex专有算法选取的主要竞品关键词点击份额。"}}}, "adType": {"label": "广告类型", "subLabel": "广告范围", "optAdType": {"displayN": {"label": "Sponsored Product", "target": "仅SP广告", "details": "仅优化SP广告"}, "displayY": {"label": "Sponsored Product + Sponsored Display", "target": "SP+SD", "details": "同时优化两种广告类型"}}}, "scheduleBudget": {"label": "排期与预算", "creationDateLabel": "创建时间", "schedule": {"label": "优化排期", "overview": {"monthlyInfinite": "按月循环", "monthlyEnd": "按月循环至{value}", "daterangeInfinite": "自{value}起持续"}, "checkbox": {"daterange": {"label": "指定时间段", "details": "设置特定时间段预算"}, "monthly": {"label": "按月自动续费", "details": "每月初自动续约预算", "toggle": {"label": "设置截止日期"}}}}, "budget": {"daterange": {"label": "总预算额", "input": "请输入总预算"}, "monthly": {"label": "月度预算", "input": "请输入月度预算"}}, "limitCpc": {"label": "智能CPC保护", "input": "请输入智能CPC保护值。", "tooltip": "系统会根据您设定的保护值，尽量将CPC控制在期望范围内，但不会强制进行限制。"}}}}, "addEditModal": {"optSetName": {"label": "优化集名称", "placeholder": "请输入优化集名称", "duplicateError": "该优化集名称已存在", "limitReachedMessage": "LITE版本最多只能创建1个优化集。"}, "objective": {"label": "优化目标", "subLabel": "优化策略", "optObjectiveType": {"maxSales": {"label": "销售额最大化", "vendorLabel": "vendor销售额最大化", "dataSource": "SP数据+广告数据", "vendorDataSource": "vendor数据 + 广告数据", "details": "综合自然流量与广告数据实现销量最大化", "vendorDetails": "结合vendor数据与广告数据达成目标", "option": "库存最小化"}, "maxProfit": {"label": "利润最大化", "vendorLabel": "vendor利润最大化", "dataSource": "SP数据+广告数据", "vendorDataSource": "vendor数据 + 广告数据", "details": "综合自然流量与广告数据实现利润最大化", "vendorDetails": "结合vendor数据与广告数据达成目标", "option": "库存最小化"}, "maxAdSales": {"label": "广告销售额最大化", "dataSource": "广告数据", "details": "基于广告数据提升广告销售额", "option": "曝光增强"}, "maxRoas": {"label": "ROAS最大化", "dataSource": "广告数据", "details": "优化广告投资回报率", "option": "曝光增强"}}, "advancedOptions": {"label": "高级选项", "competitionTactic": {"label": "竞争策略", "option": {"none": "无", "brandDefense": "品牌保护", "competitorConquesting": "竞品抢占"}}, "salesTargeting": {"label": "销售目标策略", "option": "仅针对直接转化SKU"}, "tooltip": {"competitionTactic": "竞争策略为您提供基于营销目标的高级优化方案:<enter></enter>品牌保护侧重于提升LG Optapex专有算法选取的品牌关键词点击份额。<enter></enter>竞品抢占则侧重于提升LG Optapex专有算法选取的主要竞品关键词点击份额。"}}}, "adType": {"label": "广告类型", "subLabel": "广告范围", "optAdType": {"displayN": {"label": "Sponsored Product", "target": "仅SP广告", "details": "仅优化SP广告"}, "displayY": {"label": "Sponsored Product + Sponsored Display", "target": "SP+SD", "details": "同时优化两种广告类型"}}}, "scheduleBudget": {"label": "排期与预算", "creationDateLabel": "创建时间", "schedule": {"label": "优化排期", "overview": {"monthlyInfinite": "按月循环", "monthlyEnd": "按月循环至{value}", "daterangeInfinite": "自{value}起持续"}, "checkbox": {"daterange": {"label": "指定时间段", "details": "设置特定时间段预算"}, "monthly": {"label": "按月自动续费", "details": "每月初自动续约预算", "toggle": {"label": "设置截止日期"}}}}, "budget": {"daterange": {"label": "总预算额", "input": "请输入总预算"}, "monthly": {"label": "月度预算", "input": "请输入月度预算"}}, "limitCpc": {"label": "智能CPC保护", "input": "请输入智能CPC保护值。", "tooltip": "系统会根据您设定的保护值，尽量将CPC控制在期望范围内，但不会强制进行限制。"}}, "confirmModal": {"titleCreate": "创建优化集", "titleEdit": "编辑优化集", "descriptionCreate": "确定要创建此优化集吗？", "descriptionEdit": "确定要保存对此优化集的更改吗？", "cancel": "取消", "save": "保存"}, "closeConfirmModal": {"title": "放弃未保存的更改", "description": "您有未保存的更改。确定要在不保存的情况下关闭吗？", "cancel": "取消", "discard": "放弃更改"}, "topButton": {"save": "保存配置"}, "product": {"searchBar": {"placeholder": "按商品标题或ASIN搜索"}, "targetCandidates": {"label": "备选商品", "asin": "ASIN", "null": "未找到匹配商品"}, "targetProducts": {"label": "已选商品", "asin": "ASIN", "null": "未选择商品"}}}}, "RealtimePage": {"title": {"todayOverview": "今天的概览", "realTimePerformance": "实时表现", "optsetMonitoring": "优化集监控"}, "filter": {"attributionWindow": {"label": "归因周期", "content": {"1d": "1天", "7d": "7天", "14d": "14天", "30d": "30天"}}, "optimizationSet": {"label": "优化集", "content": {"optSet": "优化集", "null": "无"}}}, "keyMetrics": {"totalMetrics": {"totalSales": "总销售额", "totalRevenue": "收入", "totalCogsRevenue": "Cogs收入", "estFee": "预估费用", "estMargin": "预估利润率"}, "adMetrics": {"adSales": "广告销售额", "adSpend": "广告花费", "roas": "ROAS"}, "metricsTooltip": {"estimatedMargin": "(总销售额 - 退货金额 - 预估费用 - 广告花费) / 总销售额 * 100", "vendorEstimatedMargin": "(Cogs收入 - 退货金额 - 预估费用 - 广告花费) / Cogs收入 * 100", "totalSales": "<first>${value}的退货金额未被计入。</first>", "totalCogsRevenue": "<first>Cogs收入指亚马逊向供应商支付的货款金额。{value}的shipped revenue指实际销售总和。</first>", "totalRevenue": "收入即客户为商品实际支付金额总和。", "estFee": "FBA费用 + 销售佣金 + 仓储费（根据时间跨度）", "roas": "(SP销售额 + SD销售额) / (SP花费 + SD花费) * 100"}}, "productShare": {"title": "商品份额", "searchBar": {"placeholder": "按商品标题或ASIN搜索"}, "table": {"header": {"rank": "排名", "productInfo": "商品", "share": "份额"}, "content": {"statusAlert": {"warning": "警告", "abnormal": "异常"}, "productInfo": {"asin": "ASIN", "sku": "SKU"}, "searchResultNull": "未找到搜索结果", "optSetNull": "尚未创建优化集"}}}, "graph": {"legendSelect": {"sales": "Total Sales", "adSales": "Ad Sales", "adSalesSameSku": "Ad Sales for Same SKU", "sdSalesPromotedClick": "SD Sales Promoted Clicks", "pageViews": "Pageviews", "clicks": "<PERSON>", "impressions": "Impressions", "sessions": "Sessions", "roas": "ROAS", "estimatedMargin": "Est. Profit", "comparedSales": "Total Sales (compared)", "comparedAdSales": "Ad Sales (compared)", "comparedAdSalesSameSku": "Ad Sales for Same SKU (compared)", "comparedSdSalesPromotedClick": "SD Sales Promoted Clicks (compared)", "comparedPageViews": "Pageviews (compared)", "comparedClicks": "<PERSON> (compared)", "comparedImpressions": "Impressions (compared)", "comparedSessions": "Sessions (compared)", "comparedRoas": "ROAS (compared)", "comparedEstimatedMargin": "Est. <PERSON>it (compared)", "revenue": "Revenue", "cogsRevenue": "Cogs Revenue", "adCost": "Ad Spend", "glanceViews": "Glance Views", "comparedRevenue": "Revenue (compared)", "comparedCogsRevenue": "Cogs Revenue (compared)", "comparedAdCost": "<PERSON>pend (compared)", "comparedGlanceViews": "Glance Views (compared)", "orderedUnits": "Ordered Units", "adOrders": "Ad Orders"}}}, "downloads": {"title": "报告下载", "topButton": {"addNew": "新建下载", "remainingRequests": "今日剩余请求次数"}, "downloadList": {"header": {"status": "状态", "reportTitle": "报告标题", "reportType": "报告类型", "requestTime": "请求时间", "fileCreationTime": "文件创建时间", "download": "下载"}, "content": {"downloadRow": {"downloadNull": "未找到下载记录"}}}, "addModal": {"topButton": {"request": "请求报告"}, "reportType": {"label": "报告类型", "subLabel": "类型", "options": {"sp": {"label": "卖家中心报告", "details": "卖家中心报告是从卖家中心生成的报告。它包含与您的产品相关的所有数据，包括销售、库存和绩效指标。"}, "vp": {"label": "供应商零售分析报告", "details": "供应商零售分析报告是从供应商零售分析生成的报告。它包含与您的产品相关的所有数据，包括销售、库存和绩效指标。"}, "ad": {"label": "广告控制台报告", "details": "广告控制台报告是从广告控制台生成的报告。它包含与您的广告系列相关的所有数据，包括展示次数、点击次数和销售额。"}, "optapex": {"label": "Optapex Report", "details": "创建和下载仅在Optapex中提供的报告。", "featured": "特色功能"}}}, "reportName": {"label": "报告名称", "category": {"subLabel": "类别"}, "name": {"subLabel": "名称"}}, "reportTitle": {"label": "报告标题", "subLabel": "标题"}, "advancedSetting": {"label": "高级设置", "filter": {"dateAggregationFilter": {"label": "日期聚合筛选", "options": {"daily": "按日", "weekly": "按周", "monthly": "按月"}}, "asinAggregationFilter": {"label": "ASIN聚合筛选", "options": {"parent": "按父ASIN", "child": "按子ASIN", "sku": "按SKU"}}, "locationAggregationFilter": {"label": "位置聚合筛选", "options": {"country": "按国家", "fc": "按配送中心"}}, "eventTypeFilter": {"label": "事件类型筛选", "options": {"adjustments": "调整", "customerReturns": "客户退货", "receipts": "收据", "shipments": "发货", "vendorReturns": "供应商退货", "whseTransfers": "仓库转移"}}, "timeUnitFilter": {"label": "时间单位筛选", "options": {"daily": "每日", "summary": "摘要"}}, "groupByFilter": {"label": "分组筛选", "options": {"searchTerm": "搜索词", "targeting": "定位", "matchedTarget": "匹配目标", "advertiser": "广告主"}}, "distributorViewFilter": {"label": "分销商视图筛选", "options": {"manufacturing": "制造", "sourcing": "采购"}}, "sellingProgramFilter": {"label": "销售项目筛选", "options": {"retail": "零售", "business": "商业", "fresh": "生鲜"}}, "reportPeriodFilter": {"label": "报告周期筛选", "options": {"day": "日", "week": "周", "month": "月", "quarter": "季度", "year": "年"}}}, "dateRestrictions": {"title": "日期限制", "maxFromNow": "距今最大查询期限", "maxRange": "最大日期范围"}}, "error": {"dateRangeRequired": "日期范围为必填项", "startDateTooFar": "开始日期距今太远", "dateRangeTooWide": "日期范围太宽", "recent": "最近", "maximum": "最大", "timeUnits": {"days": "日", "weeks": "周", "months": "个月", "quarters": "季度", "years": "年"}}}}}