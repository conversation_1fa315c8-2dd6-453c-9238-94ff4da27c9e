import { NextRequest, NextResponse } from "next/server"
import { getServerApiHostUrl } from "@/utils/host"

export interface BudgetPacingRequest {
  optimization_ids: number[]
}

export interface BudgetPacingResponse {
  optimization_id: number
  total_budget: number
  start_date: string
  end_date: string
  spent_budget: number
  remaining_budget: number
  remaining_days: number
  daily_recommended_budget: number
  daily_spending_history: Record<string, number>
  is_ended: boolean
  custom_date_range: boolean
  ad_budget_type: string
}

export async function POST(request: NextRequest) {
  try {
    const { optimization_ids }: BudgetPacingRequest = await request.json()
    const accessToken = request.headers.get("Authorization")

    if (!accessToken) {
      return NextResponse.json(
        { error: "Access token is required" },
        { status: 401 }
      )
    }

    if (!optimization_ids || !Array.isArray(optimization_ids) || optimization_ids.length === 0) {
      return NextResponse.json(
        { error: "optimization_ids array is required" },
        { status: 400 }
      )
    }

    const response = await fetch(`${await getServerApiHostUrl()}/api/hourly_report/budget_pacing`, {
      method: "POST",
      headers: {
        "Authorization": `Bearer ${accessToken}`,
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ optimization_ids }),
      cache: "no-store", // Disable caching to avoid the 2MB limit issue
    })

    if (!response.ok) {
      return NextResponse.json(
        { error: "Failed to fetch budget pacing data" },
        { status: response.status }
      )
    }

    const data: BudgetPacingResponse[] = await response.json()
    return NextResponse.json(data)
  } catch (error) {
    console.error("Budget pacing API error:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
} 