"use client"

import { api } from "@/utils/api"
import { useSession } from "next-auth/react"
import { useLocale, useTranslations } from 'next-intl'
import { ChangeEvent, forwardRef, Fragment, useEffect, useMemo, useRef, useState } from 'react'
import { Dialog, DialogPanel, DialogTitle, Disclosure, DisclosureButton, DisclosurePanel, Popover, PopoverButton, PopoverPanel, Tab, TabGroup, TabList, TabPanel, TabPanels, Transition, TransitionChild } from '@headlessui/react'
import { PortfolioListItem, ProductListItem } from './ad-portfolio-layout-component'
import { CampaignBudgetUsageResponse } from "@/app/api/hourlyReport/campaign-budget-usage/route"
import { BudgetPacingResponse } from "@/app/api/hourlyReport/new_budget_pacing/route"
import DatePicker, { registerLocale } from "react-datepicker"
import { ko } from "date-fns/locale"
import ProductStatus from './product-status'
import ProductHistoryTypeSelect, { productHistoryTypeOptions } from "@/components/dashboard/product-history-type-select"
import { colorSetList, cn, formatDate, formatDateTime, integerCurrencyFormat, getDateDifference } from "@/utils/msc"
import { useCurrencyFormatter, getCurrencyCodeFromMarketplace, getCurrencySymbol } from "@/utils/currency"
import { ChevronDownIcon, ChevronDoubleLeftIcon, ChevronLeftIcon, ChevronRightIcon, ChevronDoubleRightIcon, ExclamationTriangleIcon, PresentationChartLineIcon, QuestionMarkCircleIcon, XMarkIcon, PlusIcon } from "@heroicons/react/20/solid"
import dynamic from "next/dynamic"
const DailyBudgetPieChart = dynamic(() => import('@/components/dashboard/daily-budget-pie-chart'), { ssr: false })
import Plot from "react-plotly.js"
import { TargetsResponse, TargetState } from "@/app/api/optimization/list_targets/route"
import { Box, CircularProgress, Switch } from "@mui/material"
import AddTargetModal from './add-target-modal'
registerLocale('ko', ko)


interface AdProductViewSliderProps {
  handleProductViewCloseClick: () => void;
  selectedProductItem: ProductListItem;
  parentPortfolioItem: PortfolioListItem;
  budgetPacingData: BudgetPacingResponse | null;
  campaignBudgetUsage: CampaignBudgetUsageResponse | null;
  fetchCampaignBudgetUsage: (optimizationId: number) => Promise<void>;
}

export default function AdProductViewSlider({
  handleProductViewCloseClick,
  selectedProductItem,
  parentPortfolioItem,
  budgetPacingData,
  campaignBudgetUsage,
  fetchCampaignBudgetUsage,
}: AdProductViewSliderProps) {
  const t = useTranslations('component')
  const tos = useTranslations('optimizationSets')
  const locale = useLocale()
  
  // Currency formatting - 마켓플레이스 기반 동적 통화 코드 설정
  const { formatCurrency } = useCurrencyFormatter()
  const currencyCode = getCurrencyCodeFromMarketplace({ marketplace_id: parentPortfolioItem?.marketplace_id }) || 'USD'
  const { data: session, status } = useSession()
  const reversedColorSetList = [...colorSetList].reverse()
  const [selectedTabIndex, setSelectedTabIndex] = useState(0)
  const [productHistory, setProductHistory] = useState<any>([])
  const [targetHistory, setTargetHistory] = useState<any>([])
  const [bidHistory, setBidHistory] = useState<any>([])
  const [isProductHistoryLoading, setIsProductHistoryLoading] = useState(false)
  const [isTargetHistoryLoading, setIsTargetHistoryLoading] = useState(false)
  const [isBidHistoryLoading, setIsBidHistoryLoading] = useState(false)
  const [selectedProductHistoryType, setSelectedProductHistoryType] = useState(productHistoryTypeOptions[0])
  const [productHistoryDateRange, setProductHistoryDateRange] = useState<Date[]>([new Date(new Date().getFullYear(), new Date().getMonth(), 1), new Date()])
  const [productHistoryStartDate, productHistoryEndDate] = productHistoryDateRange
  const [targetHistoryDateRange, setTargetHistoryDateRange] = useState<Date[]>([new Date(new Date().getFullYear(), new Date().getMonth(), 1), new Date()])
  const [targetHistoryStartDate, targetHistoryEndDate] = targetHistoryDateRange
  const [bidHistoryDateRange, setBidHistoryDateRange] = useState<Date[]>([new Date(new Date().getFullYear(), new Date().getMonth(), 1), new Date()])
  const [bidHistoryStartDate, bidHistoryEndDate] = bidHistoryDateRange
  
  // 선택된 AdGroup 및 타겟 관련 상태
  const [selectedAdGroup, setSelectedAdGroup] = useState<any>(null)
  const [adGroupTargets, setAdGroupTargets] = useState<TargetsResponse[]>([])
  const [isTargetsLoading, setIsTargetsLoading] = useState(false)
  const [selectedTargetType, setSelectedTargetType] = useState("PRODUCT")
  const [searchTerm, setSearchTerm] = useState("");
  const [targetSwitchLoading, setTargetSwitchLoading] = useState<string[]>([])
  const selectedMarketplaceId = useRef('');
  
  // 차트 모달 관련 상태
  const [isChartModalOpen, setIsChartModalOpen] = useState(false)
  const [selectedCampaignForChart, setSelectedCampaignForChart] = useState<any>(null)
  const [campaignHistory, setCampaignHistory] = useState<any>(null)
  const [isCampaignHistoryLoading, setIsCampaignHistoryLoading] = useState(false)
  
  // 타겟 차트 모달 관련 상태
  const [isTargetChartModalOpen, setIsTargetChartModalOpen] = useState(false)
  const [selectedTargetForChart, setSelectedTargetForChart] = useState<TargetsResponse | null>(null)
  const [targetBidHistory, setTargetBidHistory] = useState<any>(null)
  const [isTargetBidHistoryLoading, setIsTargetBidHistoryLoading] = useState(false)
  const [currentHistoryPage, setCurrentHistoryPage] = useState(1)
  const itemsPerPage = 5 // 페이지당 표시할 아이템 수
  const [openCampaignDisclosureId, setOpenCampaignDisclosureId] = useState<string | null>(null);
  const [currentBudgetHistoryPage, setCurrentBudgetHistoryPage] = useState(1); // 예산 변경 내역 페이지 상태

  // Add Target Modal 관련 상태
  const [isAddTargetModalOpen, setIsAddTargetModalOpen] = useState(false)
  
  const DateRangeInput = forwardRef(({ value, onClick }: { value: string, onClick: () => void }, ref) => (
    // @ts-ignore
    <button className="w-auto mt-1 rounded-lg bg-white border border-gray-100 overflow-hidden cursor-pointer text-sm text-gray-600 focus:outline-none" onClick={onClick} ref={ref}>
      <div className="min-w-[200px] inline-flex py-2 px-4 bg-white hover:bg-gray-100/20">
        {value}
      </div>
    </button>
  ));
  DateRangeInput.displayName = 'DateRangeInput'
  
  const targetProductInventory = parentPortfolioItem.prediction?.inventory?.find((inventory: any) => inventory.asin === selectedProductItem.asin)
  const eligibilityAbnormality = targetProductInventory
    ? targetProductInventory.estimated_inventory_state === "DANGER"
    : false
  const estimatedDaysToSoldOut = targetProductInventory && 'available_quantity' in targetProductInventory && 'estimated_daily_units_sold' in targetProductInventory
    ? (targetProductInventory as any).available_quantity / (targetProductInventory as any).estimated_daily_units_sold
    : 0

  const calculateTotalProductUsage = useMemo(() => {
    return (asin: string): number => {
      if (!campaignBudgetUsage || !campaignBudgetUsage.asins) {
        return 0
      }      
      const asinData = campaignBudgetUsage.asins.find((asinItem) => asinItem.asin === asin)
      if (!asinData || !asinData.campaigns) {
        return 0
      }
      return asinData.campaigns.reduce((acc, campaign) => {
        return acc + (campaign.total_budget_usage || 0)
      }, 0)
    }
  }, [campaignBudgetUsage])

  const getCampaignTodayUsage = useMemo(() => {
    return (asin: string, campaignId: string): number => {
      if (!campaignBudgetUsage || !campaignBudgetUsage.asins) {
        return 0
      }
      const asinData = campaignBudgetUsage.asins.find((asinItem) => asinItem.asin === asin)
      if (!asinData || !asinData.campaigns) {
        return 0
      }
      const campaign = asinData.campaigns.find((c) => c.campaign_id === campaignId)
      return campaign ? (campaign.today_budget_usage || 0) : 0
    }
  }, [campaignBudgetUsage])
  
  useEffect(() => {
    if (parentPortfolioItem?.id) {
      fetchCampaignBudgetUsage(parentPortfolioItem.id)
    }
  }, [parentPortfolioItem?.id, session])
  
  const fetchHistory = async (
    optimizationId: string,
    asin: string,
    targetStartDate: Date,
    targetEndDate: Date,
    type: string,
    loadingStateSetter: (arg0: boolean) => void,
    historySetter: (arg0: any) => void
  ) => {
    if (!(session?.user as any).access_token) {
      console.log('access token is missing in the session.')
    }
    if (targetStartDate === null || targetEndDate === null) {
      return
    }
    if (selectedTabIndex === 1 && type !== "OPERATION" ||
      selectedTabIndex === 2 && type !== "TARGET" ||
      selectedTabIndex === 3 && type !== "BID") {
        return
    }

    loadingStateSetter(true)
    let historyResponse = await api.getListAsinHistory(optimizationId, asin, formatDate(targetStartDate), formatDate(targetEndDate), type, (session?.user as any).access_token)
    loadingStateSetter(false)
    historyResponse?.length >= 0 && historySetter(historyResponse || [])
  }
  useEffect(() => {
    if (!parentPortfolioItem || !selectedProductItem || selectedTabIndex !== 1) {
      return
    }
    if (productHistoryStartDate && productHistoryEndDate) {
      fetchHistory(
        parentPortfolioItem.id.toString(),
        selectedProductItem.asin,
        productHistoryStartDate,
        productHistoryEndDate,
        selectedProductHistoryType.type,
        setIsProductHistoryLoading,
        setProductHistory
      )
    }
  }, [selectedTabIndex, productHistoryDateRange, selectedProductHistoryType])
  useEffect(() => {
    if (!parentPortfolioItem || !selectedProductItem || selectedTabIndex !== 2) {
      return
    }
    if (targetHistoryStartDate && targetHistoryEndDate) {
      fetchHistory(
        parentPortfolioItem.id.toString(),
        selectedProductItem.asin,
        targetHistoryStartDate,
        targetHistoryEndDate,
        "TARGET",
        setIsTargetHistoryLoading,
        setTargetHistory
      )
    }
  }, [selectedTabIndex, targetHistoryDateRange])
  useEffect(() => {
    if (!parentPortfolioItem || !selectedProductItem || selectedTabIndex !== 3) {
      return
    }
    if (bidHistoryStartDate && bidHistoryEndDate) {
      fetchHistory(
        parentPortfolioItem.id.toString(),
        selectedProductItem.asin,
        bidHistoryStartDate,
        bidHistoryEndDate,
        "BID",
        setIsBidHistoryLoading,
        setBidHistory
      )
    }
  }, [selectedTabIndex, bidHistoryDateRange])

  const handleGetListTarget = async (marketplaceId: string, campaignId: string, adGroupId: string) => {
    const targetsResponse = await api.getListTargets(
      parentPortfolioItem.account_id,
      marketplaceId,
      campaignId,
      adGroupId,
      (session?.user as any).access_token
    )
    if (targetsResponse && Array.isArray(targetsResponse)) {
      setAdGroupTargets(targetsResponse)
    }
  }
  
  // AdGroup 클릭 핸들러 및 타겟 불러오기 함수
  const handleAdGroupClick = async (adGroup: any, marketplaceId: string) => {
    if (!session?.user || !adGroup || !adGroup.campaign_id || !adGroup.ad_group_id) return
    
    selectedMarketplaceId.current = marketplaceId
    setSelectedAdGroup(adGroup)
    setAdGroupTargets([])
    try {
      setIsTargetsLoading(true)
      await handleGetListTarget(
        marketplaceId,
        adGroup.campaign_id,
        adGroup.ad_group_id
      );
    } catch (error) {
      console.error("Error fetching targets:", error)
    } finally {
      setIsTargetsLoading(false)
    }
  }
  
  // 1. 각 탭별 타겟 카운트 계산
  const productTargetsCount = useMemo(() => adGroupTargets.filter(t => t.target_type === "PRODUCT" && !t.negative).length, [adGroupTargets]);
  const keywordTargetsCount = useMemo(() => adGroupTargets.filter(t => t.target_type === "KEYWORD" && !t.negative).length, [adGroupTargets]);
  const otherTargetsCount = useMemo(() => adGroupTargets.filter(t => t.target_type !== "PRODUCT" && t.target_type !== "KEYWORD" && !t.negative).length, [adGroupTargets]);
  const negativeProductTargetsCount = useMemo(() => adGroupTargets.filter(t => t.negative && t.target_type === "PRODUCT").length, [adGroupTargets]);
  const negativeKeywordTargetsCount = useMemo(() => adGroupTargets.filter(t => t.negative && t.target_type === "KEYWORD").length, [adGroupTargets]);

  // 2. 실제로 표시될 탭의 총 개수 계산
  const visibleTabsCount = useMemo(() => {
    let count = 0;
    if (productTargetsCount > 0) count++;
    if (keywordTargetsCount > 0) count++;
    if (otherTargetsCount > 0) count++;
    if (negativeProductTargetsCount > 0) count++;
    if (negativeKeywordTargetsCount > 0) count++;
    return count;
  }, [
    productTargetsCount,
    keywordTargetsCount,
    otherTargetsCount,
    negativeProductTargetsCount,
    negativeKeywordTargetsCount,
  ]);

  // 3. 현재 선택된 탭 기준으로 필터링된 타겟 (검색 전)
  const filteredTargets = useMemo(() => {
    if (selectedTargetType === "PRODUCT") 
      return adGroupTargets.filter(target => target.target_type === "PRODUCT" && !target.negative);
    if (selectedTargetType === "KEYWORD")
      return adGroupTargets.filter(target => target.target_type === "KEYWORD" && !target.negative);
    if (selectedTargetType === "OTHERS")
      return adGroupTargets.filter(target => target.target_type !== "PRODUCT" && target.target_type !== "KEYWORD" && !target.negative);
    if (selectedTargetType === "NEGATIVE_PRODUCT")
      return adGroupTargets.filter(target => target.negative && target.target_type === "PRODUCT");
    if (selectedTargetType === "NEGATIVE_KEYWORD")
      return adGroupTargets.filter(target => target.negative && target.target_type === "KEYWORD");
    return [];
  }, [adGroupTargets, selectedTargetType]);

  // 4. 검색어 기반으로 최종 필터링된 타겟
  const searchedAndFilteredTargets = useMemo(() => {
    if (!searchTerm.trim()) {
      return filteredTargets;
    }
    return filteredTargets.filter(target =>
      (target.target || '').toLowerCase().includes(searchTerm.toLowerCase())
    );
  }, [filteredTargets, searchTerm]);
  
  useEffect(() => {
    // 현재 선택된 탭이 실제로 렌더링되고 아이템을 가지고 있는지 확인
    const isCurrentTabRenderedAndNotEmpty =
      (selectedTargetType === "PRODUCT" && productTargetsCount > 0) ||
      (selectedTargetType === "KEYWORD" && keywordTargetsCount > 0) ||
      (selectedTargetType === "OTHERS" && otherTargetsCount > 0) ||
      (selectedTargetType === "NEGATIVE_PRODUCT" && negativeProductTargetsCount > 0) ||
      (selectedTargetType === "NEGATIVE_KEYWORD" && negativeKeywordTargetsCount > 0);

    // 현재 선택된 탭이 유효하지 않거나 아이템이 없다면, 표시 가능한 첫 번째 탭으로 변경
    if (!isCurrentTabRenderedAndNotEmpty) {
      let newSelectedTab = "PRODUCT"; // 기본값
      if (productTargetsCount > 0) {
        newSelectedTab = "PRODUCT";
      } else if (keywordTargetsCount > 0) {
        newSelectedTab = "KEYWORD";
      } else if (otherTargetsCount > 0) {
        newSelectedTab = "OTHERS";
      } else if (negativeProductTargetsCount > 0) {
        newSelectedTab = "NEGATIVE_PRODUCT";
      } else if (negativeKeywordTargetsCount > 0) {
        newSelectedTab = "NEGATIVE_KEYWORD";
      }
      // 현재 선택된 탭과 변경하려는 탭이 다를 경우에만 업데이트
      if (selectedTargetType !== newSelectedTab) {
        setSelectedTargetType(newSelectedTab);
      }
    }
  }, [
    productTargetsCount,
    keywordTargetsCount,
    otherTargetsCount,
    negativeProductTargetsCount,
    negativeKeywordTargetsCount,
    adGroupTargets, // adGroupTargets가 변경될 때 이 로직이 실행되도록 함
  ]);

  const handleSwitchKeyword = async (
    _: ChangeEvent<HTMLInputElement>,
    checked: boolean,
    targetId: string
  ) => {
    try {
      setTargetSwitchLoading(prev => {
        const temp = [...prev];
        temp.push(targetId)        
        return temp;
      }
      );
      const response = await api.changeStatusOfTarget(
        targetId,
        checked ? TargetState.ENABLED : TargetState.PAUSED,
        (session?.user as any).access_token
      );
      if (response.status) {
        await handleGetListTarget(
          selectedMarketplaceId.current,
          selectedAdGroup.campaign_id,
          selectedAdGroup.ad_group_id
        );
      }
    } catch (error) {
      console.error('Call API error:', error);
    } finally {
        setTargetSwitchLoading(prev => {
          const newTargetLoading =  prev.filter(item => item !== targetId);
          return newTargetLoading;    
        }
      );
    }
  };

  const handleAddTarget = async (targetData: any) => {
    try {
      if (!selectedAdGroup || !session?.user) {
        console.error('Missing required data for adding target')
        return
      }

      console.log('Adding target:', targetData)

      const response = await api.addAdGroupTarget(
        parentPortfolioItem.account_id,
        selectedMarketplaceId.current,
        selectedAdGroup.campaign_id,
        selectedAdGroup.ad_group_id,
        targetData,
        (session?.user as any).access_token
      );

      if (response?.success) {
        await handleGetListTarget(
          selectedMarketplaceId.current,
          selectedAdGroup.campaign_id,
          selectedAdGroup.ad_group_id
        );
      }
    } catch (error) {
      console.error('Error adding target:', error)
    }
  }

  const getHighlightedText = (text: string | undefined, highlight: string) => {
    const currentText = text || '';
    if (!highlight.trim()) {
      return <span>{currentText}</span>;
    }
    const parts = currentText.split(new RegExp(`(${highlight.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')})`, 'gi'));
    return (
      <span>
        {parts.map((part, i) =>
          part.toLowerCase() === highlight.toLowerCase() ? (
            <span key={i} className="bg-yellow-300">
              {part}
            </span>
          ) : (
            part
          )
        )}
      </span>
    );
  };
  
  // 차트 모달 오픈 핸들러
  const handleChartButtonClick = async (e: React.MouseEvent, campaign: any) => {
    e.stopPropagation() // 이벤트 전파 방지
    setSelectedCampaignForChart(campaign)
    setIsChartModalOpen(true)
    setCampaignHistory(null)
    setIsCampaignHistoryLoading(true)
    
    try {
      // 날짜 범위 설정 (최근 14일 또는 만료된 경우 마지막 날짜부터 14일)
      const endDate = new Date()
      const startDate = new Date()
      
      // parentPortfolioItem이 활성화 상태인지 확인
      const isOptSetActive = parentPortfolioItem.ad_budget_end_date && 
                            new Date(parentPortfolioItem.ad_budget_end_date) > new Date()
      
      if (isOptSetActive) {
        // 최근 14일 데이터 조회
        startDate.setDate(startDate.getDate() - 14)
      } else {
        // opt set이 만료된 경우, 마지막 날짜부터 14일 이전 데이터
        const lastDate = budgetPacingData && budgetPacingData.daily_spending_history && Object.keys(budgetPacingData.daily_spending_history).length > 0
          ? new Date(Math.max(...Object.keys(budgetPacingData.daily_spending_history).map(date => new Date(date).getTime())))
          : new Date()
          
        endDate.setTime(lastDate.getTime())
        startDate.setTime(lastDate.getTime())
        startDate.setDate(startDate.getDate() - 14)
      }
      
      const historyResponse = await api.getListCampaignHistory(
        parentPortfolioItem.account_id,
        parentPortfolioItem.marketplace_id,
        campaign.campaign_id,
        formatDate(startDate),
        formatDate(endDate),
        (session?.user as any).access_token
      )
      
      console.log("API 응답:", historyResponse) // 응답 확인을 위한 로깅
      
      // 응답 구조에 따라 데이터 설정
      setCampaignHistory(historyResponse)
    } catch (error) {
      console.error("Error fetching campaign history:", error)
    } finally {
      setIsCampaignHistoryLoading(false)
    }
  }
  
  // 타겟 차트 모달 오픈 핸들러
  const handleTargetChartButtonClick = async (e: React.MouseEvent, target: TargetsResponse) => {
    e.stopPropagation() // 이벤트 전파 방지
    setSelectedTargetForChart(target)
    setIsTargetChartModalOpen(true)
    setTargetBidHistory(null)
    setIsTargetBidHistoryLoading(true)
    setCurrentHistoryPage(1) // 페이지 번호 리셋
    
    try {
      // 날짜 범위 설정 (최근 14일 또는 만료된 경우 마지막 날짜부터 14일)
      const endDate = new Date()
      const startDate = new Date()
      
      // parentPortfolioItem이 활성화 상태인지 확인
      const isOptSetActive = parentPortfolioItem.ad_budget_end_date && 
                            new Date(parentPortfolioItem.ad_budget_end_date) > new Date()
      
      if (isOptSetActive) {
        // 최근 14일 데이터 조회
        startDate.setDate(startDate.getDate() - 14)
      } else {
        // opt set이 만료된 경우, 마지막 날짜부터 14일 이전 데이터
        const lastDate = budgetPacingData && budgetPacingData.daily_spending_history && Object.keys(budgetPacingData.daily_spending_history).length > 0
          ? new Date(Math.max(...Object.keys(budgetPacingData.daily_spending_history).map(date => new Date(date).getTime())))
          : new Date()
          
        endDate.setTime(lastDate.getTime())
        startDate.setTime(lastDate.getTime())
        startDate.setDate(startDate.getDate() - 14)
      }
      
      // 타겟 ID 확인 및 콘솔에 로그 출력
      const targetId = target.target_id || target.id
      console.log("타겟 ID:", targetId, "타겟 객체:", target)
      
      if (!targetId) {
        console.error("타겟 ID가 없습니다:", target)
        setIsTargetBidHistoryLoading(false)
        return
      }
      
      const historyResponse = await api.getListTargetHistory(
        parentPortfolioItem.account_id,
        parentPortfolioItem.marketplace_id,
        targetId,
        formatDate(startDate),
        formatDate(endDate),
        (session?.user as any).access_token
      )
      
      console.log("타겟 API 응답:", historyResponse) // 응답 확인을 위한 로깅
      
      // 응답 구조에 따라 데이터 설정
      setTargetBidHistory(historyResponse)
    } catch (error) {
      console.error("Error fetching target bid history:", error)
    } finally {
      setIsTargetBidHistoryLoading(false)
    }
  }
  
  // 차트 데이터 변환
  const campaignHistoryDataset = useMemo(() => {
    if (!campaignHistory) {
      return []
    }
    
    try {
      console.log("차트 데이터 생성:", campaignHistory) // 데이터 확인을 위한 로깅
      
      // API 응답 구조 확인 (histories 배열이 있거나, 전체가 배열이거나)
      let historyData: any[] = [];
      
      if (campaignHistory.histories && Array.isArray(campaignHistory.histories)) {
        historyData = campaignHistory.histories;
      } else if (Array.isArray(campaignHistory)) {
        historyData = campaignHistory;
      } else if (typeof campaignHistory === 'object') {
        // 객체인 경우 직접 배열에 추가
        historyData = [campaignHistory];
      }
      
      console.log("처리할 히스토리 데이터:", historyData);
      
      if (historyData.length === 0) {
        return []
      }
      
      // 일자별 예산 사용량 데이터 생성
      const dataset = []
      
      // 예산 그래프
      dataset.push({
        x: historyData.map((item: any) => new Date(item.history_created_at)),
        y: historyData.map((item: any) => Number(item.budget_amount) || 0),
        type: 'scatter',
        mode: 'lines+markers',
        marker: {
          color: '#3B82F6',
          size: 6
        },
        name: 'Budget',
      })
      
      // 누적 예산 사용량 그래프 제거
      
      console.log("생성된 데이터셋:", dataset);
      return dataset
    } catch (error) {
      console.error('Error processing campaign history data:', error)
      return []
    }
  }, [campaignHistory])
  
  // 타겟 차트 데이터 변환
  const targetBidHistoryDataset = useMemo(() => {
    if (!targetBidHistory) {
      return []
    }
    
    try {
      console.log("타겟 차트 데이터 생성:", targetBidHistory) // 데이터 확인을 위한 로깅
      
      // 히스토리가 비어 있는지 확인 (total이 0이거나 histories가 빈 배열인 경우)
      if (targetBidHistory.total === 0 || !targetBidHistory?.histories?.length) {
        console.log("타겟 히스토리 데이터가 없습니다.");
        return [];
      }
      
      // API 응답 구조 확인
      let historyData: any[] = [];
      
      if (targetBidHistory?.histories?.length > 0) {
        historyData = targetBidHistory.histories;
      } else if (Array.isArray(targetBidHistory)) {
        historyData = targetBidHistory;
      } else if (typeof targetBidHistory === 'object') {
        // 객체인 경우 직접 배열에 추가
        historyData = [targetBidHistory];
      }
      
      console.log("처리할 타겟 히스토리 데이터:", historyData);
      
      if (historyData.length === 0) {
        return []
      }
      
      // 입찰가 데이터 생성
      const dataset = []
      
      // 입찰가 그래프
      dataset.push({
        x: historyData.map((item: any) => new Date(item.history_created_at)),
        y: historyData.map((item: any) => {
          // bid_amount 또는 bid를 확인
          const bidAmount = item.bid_amount !== undefined ? item.bid_amount : 
                            item.bid !== undefined ? item.bid : 
                            item.value && item.value.bid ? item.value.bid : 0;
          return Number(bidAmount) || 0;
        }),
        type: 'scatter',
        mode: 'lines+markers',
        marker: {
          color: '#3B82F6',
          size: 6
        },
        name: 'Bid Amount',
      })
      
      console.log("생성된 타겟 데이터셋:", dataset);
      return dataset
    } catch (error) {
      console.error('Error processing target bid history data:', error)
      return []
    }
  }, [targetBidHistory])
  
  const campaignContent = (selectedProductItem: ProductListItem, parentPortfolioItem: PortfolioListItem) => {
    const campaignList = selectedProductItem.campaigns?.filter((campaign) => campaign.campaign_mop_yn === "Y").sort((a, b) => {
      if (a.campaign_targeting_settings === "AUTO" && b.campaign_targeting_settings !== "AUTO") {
        return -1
      } else if (a.campaign_targeting_settings !== "AUTO" && b.campaign_targeting_settings === "AUTO") {
        return 1
      }
      return (new Date(a.campaign_start_date).getTime() - new Date(b.campaign_start_date).getTime())
    })
    const target_product_usage = calculateTotalProductUsage(selectedProductItem.asin)
    const total_daily_campaign_usage = campaignList.reduce((acc, campaign) => {
      return acc + getCampaignTodayUsage(selectedProductItem.asin, campaign.campaign_id)
    }, 0)
    return (
      <div className="flex flex-col h-full gap-y-4 items-start justify-start overflow-hidden">
        <div className="flex-shrink-0 w-full pl-5">
          <div className="">
            <div className="text-gray-400 text-xs font-semibold">
              {tos("detailModal.product.campaignTab.cumulatedBudgetUsage")}
            </div>
            <div className="mt-1 text-lg text-gray-500 font-semibold">
              {!campaignBudgetUsage ? (
                <div className="h-6 bg-gray-200 rounded animate-pulse"></div>
              ) : (
                formatCurrency(target_product_usage || 0, currencyCode)
              )}
            </div>
          </div>
          <div className="mt-4 flex-shrink-0 relative w-full flex flex-col items-center justify-start">
            <div className="flex items-start w-full gap-x-3">
              <div className="grow">
                {!campaignBudgetUsage ? (
                  <div className="w-full h-8 bg-gray-200 rounded-sm animate-pulse"></div>
                ) : (
                  <div className="relative flex items-center justify-start w-full h-8 bg-gray-100 rounded-sm gap-x-0.5">
                    {parentPortfolioItem.target_products.map((item, index) => {
                      const total_product_usage = calculateTotalProductUsage(item.asin)
                      const total_product_percent = parentPortfolioItem.ad_budget_amount
                        ? total_product_usage / parentPortfolioItem.ad_budget_amount * 100
                        : 0
                      return item && (
                        <div
                          className={cn(
                            "group relative flex items-center justify-end min-w-[48px] h-full text-xs border font-semibold rounded-sm cursor-pointer",
                            selectedProductItem.asin !== item.asin ? "opacity-20" : "",
                            `${colorSetList[index % colorSetList.length].border} ${colorSetList[index % colorSetList.length].bg} ${colorSetList[index % colorSetList.length].text}`
                          )}
                          style={{ width: `${total_product_percent}%` }}
                          key={index}
                        >
                          <div className="pl-1">{total_product_percent.toFixed(2)}%</div>
                          <div className="shrink w-2 h-full"></div>
                        </div>
                    )})}
                  </div>
                )}
                <div className="mt-1 w-full flex items-center justify-between text-xs">
                  <div className="text-gray-400">0</div>
                  <div className="text-gray-400">{formatCurrency(parentPortfolioItem.ad_budget_amount || 0, currencyCode, { maximumFractionDigits: 0, minimumFractionDigits: 0 })}</div>
                </div>
              </div>
              <div className="mt-2 flex-shrink-0 text-xs text-gray-500 font-semibold">
                {!campaignBudgetUsage ? (
                  <div className="h-4 w-16 bg-gray-200 rounded animate-pulse"></div>
                ) : (
                  formatCurrency(target_product_usage || 0, currencyCode)
                )}
              </div>
            </div>
          </div>
        </div>
        <div className="grow relative w-full min-h-0 grid grid-cols-3 gap-x-6">
          <div className="col-span-2 relative w-full min-h-0 rounded-lg bg-white border border-gray-100 overflow-y-scroll">
            {/* List */}
            <div className="sticky inset-x-0 top-0 z-[1] flex items-center gap-x-3 py-3 text-center text-sm text-gray-400 font-bold bg-gray-100/40 backdrop-blur-md">
              <div className="flex-shrink-0 w-[120px] pl-12 text-left">
                {tos("detailModal.product.campaignTab.table.header.status")}
              </div>
              <div className="grow px-4">
                {tos("detailModal.product.campaignTab.table.header.campaignInfo")}
              </div>
              <div className="flex-shrink-0 w-[180px] pr-6">
                {tos("detailModal.product.campaignTab.table.header.dailyBudgetUsage")}
              </div>
            </div>
            <ul className="divide-y divide-gray-100">
              {campaignList.length > 0 ? (
                campaignList.map((campaign, index) => {
                  const target_campaign_usage = getCampaignTodayUsage(selectedProductItem.asin, campaign.campaign_id);
                  const target_campaign_percent = total_daily_campaign_usage
                    ? target_campaign_usage / total_daily_campaign_usage * 100
                    : 0;
                  const ad_groups = campaign.ad_groups || [];
                  
                  return (
                    <Disclosure as="li"
                      className="relative hover:bg-gray-100/50 text-center text-gray-500 text-sm"
                      key={campaign.campaign_id}
                    >
                      {({ open }) => (
                        <>
                          <DisclosureButton 
                            className="w-full flex items-center gap-x-3 py-6 cursor-pointer"
                            onClick={() => {
                              if (openCampaignDisclosureId === campaign.campaign_id) {
                                setOpenCampaignDisclosureId(null); // 이미 열려있으면 닫기
                              } else {
                                setOpenCampaignDisclosureId(campaign.campaign_id); // 새로 열기
                              }
                            }}
                          >
                            <div className="flex-shrink-0 flex items-center justify-start gap-x-2 w-[120px] pl-8">
                              <div
                                className={cn(
                                  "h-2 w-2 rounded-full",
                                  campaign.campaign_state === "PAUSED"
                                    ? "bg-red-500"
                                    : campaign.campaign_state === "ENABLED"
                                      ? "bg-green-500" 
                                      : "bg-gray-300"
                                )}
                              />
                              {campaign.campaign_state === "PAUSED"
                                ? tos("detailModal.product.campaignTab.table.content.status.paused")
                                : campaign.campaign_state === "ENABLED"
                                  ? tos("detailModal.product.campaignTab.table.content.status.enabled")
                                  : campaign.campaign_state
                              }
                            </div>
                            <div className="grow relative flex flex-col px-4 overflow-hidden text-left">
                              <div className="text-xs text-gray-500 font-semibold text-left break-words leading-relaxed">
                                <span className="mr-2 py-0.5 px-1.5 bg-gray-200 text-[10px] text-gray-500 font-normal rounded-full break-normal">
                                  {campaign.campaign_targeting_settings}
                                </span>
                                {campaign.campaign_name}
                              </div>
                              <div className="mt-0.5 text-[10px] text-gray-400 leading-snug">{tos("detailModal.product.campaignTab.table.content.campaignInfo.id")}: {campaign.campaign_id}</div>
                              <div className="text-[10px] text-gray-400 leading-snug">{tos("detailModal.product.campaignTab.table.content.campaignInfo.startDate")}: {formatDate(campaign.campaign_start_date, ".")}</div>
                            </div>
                            <div className="flex-shrink-0 w-[180px] pr-6">
                              <div className="flex items-start justify-center w-full gap-x-3">
                                <div
                                  className={cn(
                                    "inline-flex items-center justify-end w-[70px] h-8 px-3 text-xs border font-semibold rounded-sm",
                                    `${reversedColorSetList[index % reversedColorSetList.length].border} ${reversedColorSetList[index % reversedColorSetList.length].bg} ${reversedColorSetList[index % reversedColorSetList.length].text}`
                                  )}
                                >
                                  {target_campaign_percent.toFixed(2)}%
                                </div>
                                <div className="mt-2 flex-shrink-0 text-xs text-gray-500 font-semibold">
                                  {formatCurrency(target_campaign_usage, currencyCode)}
                                </div>
                              </div>
                            </div>
                            <div className="flex items-center gap-x-2 pr-4">
                              <button
                                onClick={(e) => {
                                  e.stopPropagation(); // DisclosureButton의 onClick 이벤트 전파 방지
                                  handleChartButtonClick(e, campaign);
                                }}
                                className="p-1 rounded-full hover:bg-blue-100 transition-colors duration-200"
                              >
                                <PresentationChartLineIcon className="h-5 w-5 text-blue-500" />
                              </button>
                              <ChevronDownIcon 
                                className={cn(
                                  "h-5 w-5 text-gray-400 transition-transform duration-200",
                                  openCampaignDisclosureId === campaign.campaign_id ? "transform rotate-180" : ""
                                )} 
                              />
                            </div>
                          </DisclosureButton>

                          {openCampaignDisclosureId === campaign.campaign_id && (
                            <DisclosurePanel static className="pb-4 px-8 ml-8 border-l-2 border-gray-200">
                              {ad_groups.length > 0 ? (
                                <ul className="space-y-3">
                                  <li className="flex items-center gap-x-3 py-2 text-center text-xs text-gray-400 font-semibold bg-gray-50/80 rounded-md px-4">
                                    <div className="w-[120px] text-left">State</div>
                                    <div className="grow text-left">Ad Group</div>
                                    <div className="w-[100px] text-right">Type</div>
                                  </li>
                                  {ad_groups.map((adGroup: any) => (
                                    <li 
                                      key={adGroup.ad_group_id}
                                      className={cn(
                                        "flex items-center gap-x-3 py-3 text-xs text-gray-500 bg-white rounded-md shadow-sm px-4 cursor-pointer hover:bg-blue-50",
                                        selectedAdGroup?.ad_group_id === adGroup.ad_group_id && "bg-blue-100 border border-blue-300 ring-1 ring-blue-300"
                                      )}
                                      onClick={() => {
                                        handleAdGroupClick({...adGroup, campaign_id: campaign.campaign_id}, parentPortfolioItem.marketplace_id);
                                      }}
                                    >
                                      <div className="flex items-center gap-x-2 w-[120px]">
                                        <div
                                          className={cn(
                                            "h-2 w-2 rounded-full",
                                            adGroup.ad_group_state === "PAUSED"
                                              ? "bg-red-500"
                                              : adGroup.ad_group_state === "ENABLED"
                                                ? "bg-green-500" 
                                                : "bg-gray-300"
                                          )}
                                        />
                                        {adGroup.ad_group_state === "PAUSED"
                                          ? tos("detailModal.product.campaignTab.table.content.status.paused")
                                          : adGroup.ad_group_state === "ENABLED"
                                            ? tos("detailModal.product.campaignTab.table.content.status.enabled")
                                            : adGroup.ad_group_state
                                        }
                                      </div>
                                      <div className="grow relative flex flex-col overflow-hidden text-left">
                                        <div className="font-semibold break-words leading-relaxed">
                                          {adGroup.ad_group_name}
                                        </div>
                                        <div className="mt-0.5 text-[10px] text-gray-400 leading-snug">ID: {adGroup.ad_group_id}</div>
                                        <div className="text-[10px] text-gray-400 leading-snug">Started At: {formatDate(adGroup.ad_group_creation_date_time, ".")}</div>
                                      </div>
                                      <div className="w-[100px] text-center px-2 py-1 bg-gray-100 rounded-full">
                                        {adGroup.ad_group_type}
                                      </div>
                                    </li>
                                  ))}
                                </ul>
                              ) : (
                                <div className="py-6 text-center text-gray-400 text-sm">
                                  등록된 광고 그룹이 없습니다
                                </div>
                              )}
                            </DisclosurePanel>
                          )}
                        </>
                      )}
                    </Disclosure>
                  );
                })
              ) : (
                <li className="flex items-center justify-center h-24 text-gray-400 text-sm">
                  No campaigns running
                </li>
              )}
            </ul>
          </div>
          <div className="col-span-1 relative w-full min-h-0 rounded-lg border border-gray-100 overflow-hidden">
            {selectedAdGroup ? (
              <div className="flex flex-col h-full">
                <div className="bg-gray-50 border-b border-gray-100">
                  {/* 상태 아이콘 삭제됨 */}
                  
                  {/* Tabs (헤더 영역을 꽉 채움) */}
                  {visibleTabsCount > 0 && (
                      <TabGroup>
                        <TabList className="flex w-full space-x-1 rounded-lg bg-gray-100 p-1">
                          {productTargetsCount > 0 && (
                            <Tab 
                              className={({ selected }) => cn(
                                "w-full py-2 text-sm font-medium leading-5 text-gray-500 rounded-lg",
                                "ring-white ring-opacity-60 ring-offset-2 focus:outline-none",
                                selected 
                                  ? "bg-white shadow text-blue-600" 
                                  : "hover:bg-white/[0.5] hover:text-gray-700"
                              )}
                              onClick={() => setSelectedTargetType("PRODUCT")}
                            >
                              Products
                              <span className="ml-2 px-2 py-0.5 bg-gray-100 rounded-full text-xs">
                                {productTargetsCount}
                              </span>
                            </Tab>
                          )}
                          {keywordTargetsCount > 0 && (
                            <Tab 
                              className={({ selected }) => cn(
                                "w-full py-2 text-sm font-medium leading-5 text-gray-500 rounded-lg",
                                "ring-white ring-opacity-60 ring-offset-2 focus:outline-none",
                                selected 
                                  ? "bg-white shadow text-blue-600" 
                                  : "hover:bg-white/[0.5] hover:text-gray-700"
                              )}
                              onClick={() => setSelectedTargetType("KEYWORD")}
                            >
                              Keywords
                              <span className="ml-2 px-2 py-0.5 bg-gray-100 rounded-full text-xs">
                                {keywordTargetsCount}
                              </span>
                            </Tab>
                          )}
                          {otherTargetsCount > 0 && (
                            <Tab 
                              className={({ selected }) => cn(
                                "w-full py-2 text-sm font-medium leading-5 text-gray-500 rounded-lg",
                                "ring-white ring-opacity-60 ring-offset-2 focus:outline-none",
                                selected 
                                  ? "bg-white shadow text-blue-600" 
                                  : "hover:bg-white/[0.5] hover:text-gray-700"
                              )}
                              onClick={() => setSelectedTargetType("OTHERS")}
                            >
                              Others
                              <span className="ml-2 px-2 py-0.5 bg-gray-100 rounded-full text-xs">
                                {otherTargetsCount}
                              </span>
                            </Tab>
                          )}
                          {negativeProductTargetsCount > 0 && (
                            <Tab 
                              className={({ selected }) => cn(
                                "w-full py-2 text-sm font-medium leading-5 text-gray-500 rounded-lg",
                                "ring-white ring-opacity-60 ring-offset-2 focus:outline-none",
                                selected 
                                  ? "bg-white shadow text-blue-600" 
                                  : "hover:bg-white/[0.5] hover:text-gray-700"
                              )}
                              onClick={() => setSelectedTargetType("NEGATIVE_PRODUCT")}
                            >
                              Negative Products
                              <span className="ml-2 px-2 py-0.5 bg-gray-100 rounded-full text-xs">
                                {negativeProductTargetsCount}
                              </span>
                            </Tab>
                          )}
                          {negativeKeywordTargetsCount > 0 && (
                            <Tab 
                              className={({ selected }) => cn(
                                "w-full py-2 text-sm font-medium leading-5 text-gray-500 rounded-lg",
                                "ring-white ring-opacity-60 ring-offset-2 focus:outline-none",
                                selected 
                                  ? "bg-white shadow text-blue-600" 
                                  : "hover:bg-white/[0.5] hover:text-gray-700"
                              )}
                              onClick={() => setSelectedTargetType("NEGATIVE_KEYWORD")}
                            >
                              Negative Keywords
                              <span className="ml-2 px-2 py-0.5 bg-gray-100 rounded-full text-xs">
                                {negativeKeywordTargetsCount}
                              </span>
                            </Tab>
                          )}
                        </TabList>
                      </TabGroup>
                  )}
                </div>
                
                {/* 검색창 추가: 표시될 타겟이 하나라도 있을 때 검색창을 보여줌 */}
                { (productTargetsCount > 0 || keywordTargetsCount > 0 || otherTargetsCount > 0 || negativeProductTargetsCount > 0 || negativeKeywordTargetsCount > 0) && (
                  <div className="flex gap-4 items-center p-4">
                    <div className="border-b border-t border-gray-100 flex-1">
                      <input
                        type="text"
                        placeholder={`Search in ${selectedTargetType.replace("_", " ").toLowerCase()} targets...`}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                      />
                    </div>
                    {selectedTargetType !== 'OTHERS' && (
                      <button
                        className='flex items-center gap-x-2 px-3 py-[0.55rem] cursor-pointer rounded-md focus:outline-none text-sm overflow-hidden font-semibold bg-blue-100 hover:bg-blue-200 text-blue-500'
                        onClick={() => setIsAddTargetModalOpen(true)}
                      >
                        <PlusIcon
                          className='flex-shrink-0 h-5 w-5'
                          aria-hidden='true'
                        />
                        <div>{tos('topButton.addNew')}</div>
                      </button>
                    )}
                  </div> 
                )}
                
                <div className="flex-1 overflow-y-auto">
                  {renderTargetList()}
                </div>
              </div>
            ) : (
              <div className="flex items-center justify-center h-full text-gray-400 text-sm">
                <div className="text-center">
                  <div className="mb-2">To see the target list</div>
                  <div>Please select an ad group</div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    )
  }

  const historyTypeMapper = {
    "EDIT_CAMPAIGN_BUDGETS": "Campaign budget edited",
    "MODIFY_AUTO_CAMPAIGN_BUDGETS": "Auto campaign budget modified",
    "MODIFY_MANUAL_CAMPAIGN_BUDGETS": "Manual campaign budget modified",
    "EDIT_OPTIMIZED_AUTO_TARGETS": "Auto targets edited",
    "EDIT_OPTIMIZED_KEYWORDS": "Keywords edited",
    "EDIT_OPTIMIZED_PRODUCT_TARGETS": "Product targets edited",
    "CREATE_PRODUCT_TARGETS": "Product targets created",
    "CREATE_NEGATIVE_PRODUCT_TARGETS": "Negative product targets created",
    "CREATE_KEYWORDS": "Keywords created",
    "CREATE_NEGATIVE_KEYWORDS": "Negative keywords created",
    "CREATE_AUTO_CAMPAIGNS": "Auto campaigns created",
    "CREATE_AUTO_AD_GROUPS": "Auto ad groups created",
    "CREATE_AUTO_PRODUCT_ADS": "Auto product ads created",
    "CREATE_MANUAL_CAMPAIGNS": "Manual campaigns created",
    "CREATE_MANUAL_AD_GROUPS": "Manual ad groups created",
    "CREATE_MANUAL_PRODUCT_ADS": "Manual product ads created",
    "EDIT_AUTO_CAMPAIGN_BUDGETS": "Auto campaign budget edited",
    "EDIT_AUTO_TARGETS": "Auto targets edited",
    "EDIT_KEYWORDS": "Keywords edited",
    "EDIT_PRODUCT_TARGETS": "Product targets edited",
    "APPLY_TARGET_BID_RECOMMENDATIONS": "Target bid recommendations applied",
  }
  const historyKeyMapper = {
    "ad_budget_type": "budget policy",
    "ad_budget_amount": "budget amount",
    "ad_budget_start_date": "budget start date",
    "ad_budget_end_date": "budget end date",
    "bid_yn": "bid status",
  }
  const productHistoryContent = (selectedProductItem: ProductListItem) => {
    const campaignList = selectedProductItem.campaigns?.filter((campaign) => campaign.campaign_mop_yn === "Y").sort((a, b) => {
      if (a.campaign_targeting_settings === "AUTO" && b.campaign_targeting_settings !== "AUTO") {
        return -1
      } else if (a.campaign_targeting_settings !== "AUTO" && b.campaign_targeting_settings === "AUTO") {
        return 1
      }
      return (new Date(a.campaign_start_date).getTime() - new Date(b.campaign_start_date).getTime())
    })
    return (
      <div className="flex flex-col h-full gap-y-4 items-start justify-start">
        <div className="flex-shrink-0 flex items-center gap-x-3">
          <div>
            <div className="text-xs text-gray-400 font-semibold">{tos("detailModal.product.productHistoryTab.filter.history.label")}</div>
            {isProductHistoryLoading
              ? <div className="animate-pulse pt-1">
                  <div className="w-[160px] h-9 rounded-md bg-gray-100"/>
                </div>
              : <ProductHistoryTypeSelect
                  className="mt-1 z-[2]"
                  listboxClassName="absolute mt-1 max-h-60 w-full overflow-auto rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black/5 focus:outline-none sm:text-sm"
                  selected={selectedProductHistoryType}
                  setSelected={setSelectedProductHistoryType}
                />
            }
          </div>
          <div className="history-date-range">
            <div className="text-xs text-gray-400 font-semibold">{tos("detailModal.product.productHistoryTab.filter.date.label")} *</div>
            {isProductHistoryLoading
              ? <div className="animate-pulse pt-1">
                  <div className="w-[200px] h-9 rounded-md bg-gray-100"/>
                </div>
              : <DatePicker
                  selectsRange={true}
                  minDate={new Date('2023-01-01')}
                  maxDate={new Date()}
                  startDate={productHistoryStartDate}
                  endDate={productHistoryEndDate}
                  onChange={(update) => {
                    setProductHistoryDateRange(update as Date[])
                  }}
                  dateFormat="yyyy.MM.dd"
                  calendarClassName="dashboard-date-range"
                  // @ts-ignore
                  customInput={<DateRangeInput />}
                  locale={locale}
                />
            }
          </div>
        </div>
        <div className="grow relative w-full rounded-lg bg-white border border-gray-100 overflow-y-scroll">
          {/* List */}
          <div className="sticky inset-x-0 top-0 z-[1] flex items-center gap-x-3 py-3 text-center text-sm text-gray-400 font-bold bg-gray-100/40 backdrop-blur-md">
          <div className="flex-shrink-0 w-[160px] px-4">
              {tos("detailModal.product.productHistoryTab.table.header.actionType")}
            </div>
            <div className="flex-1 px-4">
              {tos("detailModal.product.productHistoryTab.table.header.from")}
            </div>
            <div className="flex-1 px-4">
              {tos("detailModal.product.productHistoryTab.table.header.to")}
            </div>
            <div className="flex-shrink-0 w-[200px] px-4">
              {tos("detailModal.product.productHistoryTab.table.header.datetime")}
            </div>
          </div>
          { isProductHistoryLoading
            ? <ul className="animate-pulse p-6 space-y-3">
                <li className="w-full flex items-center gap-x-3">
                  <div className="flex-shrink-0 w-[160px] h-16 rounded-md bg-gray-100"></div>
                  <div className="flex-1 h-16 rounded-md bg-gray-100"></div>
                  <div className="flex-1 h-16 rounded-md bg-gray-100"></div>
                  <div className="flex-shrink-0 w-[200px] h-16 rounded-md bg-gray-100"></div>
                </li>
                <li className="w-full flex items-center gap-x-3">
                  <div className="flex-shrink-0 w-[160px] h-16 rounded-md bg-gray-100"></div>
                  <div className="flex-1 h-16 rounded-md bg-gray-100"></div>
                  <div className="flex-1 h-16 rounded-md bg-gray-100"></div>
                  <div className="flex-shrink-0 w-[200px] h-16 rounded-md bg-gray-100"></div>
                </li>
                <li className="w-full flex items-center gap-x-3">
                  <div className="flex-shrink-0 w-[160px] h-16 rounded-md bg-gray-100"></div>
                  <div className="flex-1 h-16 rounded-md bg-gray-100"></div>
                  <div className="flex-1 h-16 rounded-md bg-gray-100"></div>
                  <div className="flex-shrink-0 w-[200px] h-16 rounded-md bg-gray-100"></div>
                </li>
                <li className="w-full flex items-center gap-x-3">
                  <div className="flex-shrink-0 w-[160px] h-16 rounded-md bg-gray-100"></div>
                  <div className="flex-1 h-16 rounded-md bg-gray-100"></div>
                  <div className="flex-1 h-16 rounded-md bg-gray-100"></div>
                  <div className="flex-shrink-0 w-[200px] h-16 rounded-md bg-gray-100"></div>
                </li>
                <li className="w-full flex items-center gap-x-3">
                  <div className="flex-shrink-0 w-[160px] h-16 rounded-md bg-gray-100"></div>
                  <div className="flex-1 h-16 rounded-md bg-gray-100"></div>
                  <div className="flex-1 h-16 rounded-md bg-gray-100"></div>
                  <div className="flex-shrink-0 w-[200px] h-16 rounded-md bg-gray-100"></div>
                </li>
                <li className="w-full flex items-center gap-x-3">
                  <div className="flex-shrink-0 w-[160px] h-16 rounded-md bg-gray-100"></div>
                  <div className="flex-1 h-16 rounded-md bg-gray-100"></div>
                  <div className="flex-1 h-16 rounded-md bg-gray-100"></div>
                  <div className="flex-shrink-0 w-[200px] h-16 rounded-md bg-gray-100"></div>
                </li>
                <li className="w-full flex items-center gap-x-3">
                  <div className="flex-shrink-0 w-[160px] h-16 rounded-md bg-gray-100"></div>
                  <div className="flex-1 h-16 rounded-md bg-gray-100"></div>
                  <div className="flex-1 h-16 rounded-md bg-gray-100"></div>
                  <div className="flex-shrink-0 w-[200px] h-16 rounded-md bg-gray-100"></div>
                </li>
                <li className="w-full flex items-center gap-x-3">
                  <div className="flex-shrink-0 w-[160px] h-16 rounded-md bg-gray-100"></div>
                  <div className="flex-1 h-16 rounded-md bg-gray-100"></div>
                  <div className="flex-1 h-16 rounded-md bg-gray-100"></div>
                  <div className="flex-shrink-0 w-[200px] h-16 rounded-md bg-gray-100"></div>
                </li>
              </ul>
            : <ul className="divide-y divide-gray-100">
              {
                productHistory.length === 0
                  ? <div className="absolute inset-0 flex items-center justify-center w-full text-gray-300 text-sm font-normal">
                      {tos("detailModal.product.productHistoryTab.table.content.null")}
                    </div>
                  : productHistory.map((historyItem: any, index: number) => {
                      return (
                        <li
                          className="relative flex items-center gap-x-3 py-6 hover:bg-gray-100/50 text-center text-gray-500 text-sm"
                          key={index}
                        >
                          <div className="flex-shrink-0 w-[160px] px-4 text-xs font-semibold">
                            {historyTypeMapper[historyItem.operation_type as keyof typeof historyTypeMapper] || historyItem.operation_type}
                          </div>
                          {historyItem.old_value
                            ? <div className="flex-1 relative flex flex-col px-4 overflow-hidden text-left">
                                <div className="space-y-2 text-xs text-left break-words">
                                  {Object.entries(historyItem.old_value).map(([key, value], index) => (
                                    <div key={key + "-" + index}>
                                      {key === "campaignId"
                                        ? <>
                                          <div className="text-gray-400 text-[10px]">
                                            campaign
                                          </div>
                                          {(() => {
                                            const campaign = campaignList.find(campaign => campaign.campaign_id === value)
                                            return campaign ? (
                                              <div className="mt-1 px-4 py-2 space-y-2 bg-blue-100/40 text-xs text-left break-words rounded-md">
                                                <div className="grow relative flex flex-col overflow-hidden text-left">
                                                  <div className="text-xs text-gray-500 font-semibold text-left break-words leading-relaxed">
                                                    <span className="mr-2 py-0.5 px-1.5 bg-gray-200 text-[10px] text-gray-500 font-normal rounded-full break-normal">
                                                      {campaign.campaign_targeting_settings}
                                                    </span>
                                                    {campaign.campaign_name}
                                                  </div>
                                                  <div className="mt-0.5 text-[10px] text-gray-400 leading-snug">ID: {campaign.campaign_id}</div>
                                                  <div className="text-[10px] text-gray-400 leading-snug">Started At: {formatDate(campaign.campaign_start_date, ".")}</div>
                                                </div>
                                              </div>
                                            ) : null
                                          })()}
                                        </>
                                        : <>
                                          <div className="text-gray-400 text-[10px]">
                                            {historyKeyMapper[key as keyof typeof historyKeyMapper] || key}
                                          </div>
                                          <div className="text-xs text-gray-500">
                                            {typeof value === "object"
                                              ? (
                                                <div className="mt-1 px-4 py-2 space-y-2 bg-gray-100/40 text-xs text-left break-words rounded-md">
                                                  {value && Object.entries(value).map(([subkey, subvalue], index) => (
                                                    <div key={subkey + "-" + index}>
                                                      {subkey !== "0" &&
                                                      <div className="text-gray-400 text-[10px]">
                                                        {subkey === "budget"
                                                          ? "budgetAmount"
                                                          : subkey
                                                        }
                                                      </div>
                                                      }
                                                      <div className="text-xs text-gray-500">
                                                        {JSON.parse(JSON.stringify(subvalue))}
                                                      </div>
                                                    </div>
                                                  ))}
                                                </div>
                                              )
                                              : value as React.ReactNode
                                            }
                                          </div>
                                        </>
                                      }
                                    </div>
                                  ))}
                                  {historyItem.target_details && typeof historyItem.target_details === "object" &&
                                    <div>
                                      <div className="text-gray-400 text-[10px]">
                                        target details
                                      </div>
                                      <div className="text-xs text-gray-500">
                                        <div className="mt-1 px-4 py-2 space-y-2 bg-gray-100/40 text-xs text-left break-words rounded-md">
                                          {Object.entries(historyItem.target_details).map(([key, value], index) => (
                                            <div key={key + "-" + index}>
                                              <div className="text-gray-400 text-[10px]">
                                                {key}
                                              </div>
                                              <div className="text-xs text-gray-500">
                                                {JSON.parse(JSON.stringify(value))}
                                              </div>
                                            </div>
                                          ))}
                                        </div>
                                      </div>
                                    </div>
                                  }
                                </div>
                              </div>
                            : <div className="flex-1 relative flex flex-col px-4 overflow-hidden text-left"></div>
                          }
                          {historyItem.value &&
                          <div className="flex-1 relative flex flex-col px-4 overflow-hidden text-left">
                            <div className="space-y-2 text-xs text-left break-words">
                              {Object.entries(historyItem.value).map(([key, value], index) => (
                                <div key={key + "-" + index}>
                                  {key === "campaignId"
                                    ? <>
                                      <div className="text-gray-400 text-[10px]">
                                        campaign
                                      </div>
                                      {(() => {
                                        const campaign = campaignList.find(campaign => campaign.campaign_id === value)
                                        return campaign ? (
                                          <div className="mt-1 px-4 py-2 space-y-2 bg-blue-100/40 text-xs text-left break-words rounded-md">
                                            <div className="grow relative flex flex-col overflow-hidden text-left">
                                              <div className="text-xs text-gray-500 font-semibold text-left break-words leading-relaxed">
                                                <span className="mr-2 py-0.5 px-1.5 bg-gray-200 text-[10px] text-gray-500 font-normal rounded-full break-normal">
                                                  {campaign.campaign_targeting_settings}
                                                </span>
                                                {campaign.campaign_name}
                                              </div>
                                              <div className="mt-0.5 text-[10px] text-gray-400 leading-snug">ID: {campaign.campaign_id}</div>
                                              <div className="text-[10px] text-gray-400 leading-snug">Started At: {formatDate(campaign.campaign_start_date, ".")}</div>
                                            </div>
                                          </div>
                                        ) : null
                                      })()}
                                    </>
                                    : <>
                                      <div className="text-gray-400 text-[10px]">
                                        {historyKeyMapper[key as keyof typeof historyKeyMapper] || key}
                                      </div>
                                      <div className="text-xs text-gray-500">
                                        {typeof value === "object"
                                          ? (
                                            <div className="mt-1 px-4 py-2 space-y-2 bg-blue-100/40 text-xs text-left break-words rounded-md">
                                              {value && Object.entries(value).map(([subkey, subvalue], index) => (
                                                <div key={subkey + "-" + index}>
                                                  {subkey !== "0" &&
                                                  <div className="text-gray-400 text-[10px]">
                                                    {subkey === "budget"
                                                      ? "budgetAmount"
                                                      : subkey
                                                    }
                                                  </div>
                                                  }
                                                  <div className="text-xs text-gray-500">
                                                    {typeof subvalue === "object"
                                                      ? (
                                                        subvalue && Object.entries(subvalue).map(([sub2key, sub2value], index) => (
                                                          <div key={sub2key + "-" + index}>
                                                            {sub2key !== "0" &&
                                                            <div className="text-gray-400 text-[10px]">
                                                              {sub2key}
                                                            </div>
                                                            }
                                                            <div className="mt-1 text-xs text-gray-500">
                                                              {JSON.parse(JSON.stringify(sub2value))}
                                                            </div>
                                                          </div>
                                                        ))
                                                      )
                                                      : subvalue as React.ReactNode
                                                    }
                                                  </div>
                                                </div>
                                              ))}
                                            </div>
                                          )
                                          : value as React.ReactNode
                                        }
                                      </div>
                                    </>
                                  }
                                </div>
                              ))}
                              {historyItem.target_details && typeof historyItem.target_details === "object" &&
                                <div>
                                  <div className="text-gray-400 text-[10px]">
                                    target details
                                  </div>
                                  <div className="text-xs text-gray-500">
                                    <div className="mt-1 px-4 py-2 space-y-2 bg-blue-100/40 text-xs text-left break-words rounded-md">
                                      {Object.entries(historyItem.target_details).map(([key, value], index) => (
                                        <div key={key + "-" + index}>
                                          <div className="text-gray-400 text-[10px]">
                                            {key}
                                          </div>
                                          <div className="text-xs text-gray-500">
                                            {JSON.parse(JSON.stringify(value))}
                                          </div>
                                        </div>
                                      ))}
                                    </div>
                                  </div>
                                </div>
                              }
                            </div>
                          </div>
                          }
                          <div className="flex-shrink-0 w-[200px] px-4">
                            {formatDateTime(historyItem.datetime, ".")}
                          </div>
                        </li>
                      )
                    })
              }
              </ul>
          }
        </div>
      </div>
    )
  }

  const targetContent = (selectedProductItem: ProductListItem) => {
    const campaignList = selectedProductItem.campaigns?.filter((campaign) => campaign.campaign_mop_yn === "Y").sort((a, b) => {
      if (a.campaign_targeting_settings === "AUTO" && b.campaign_targeting_settings !== "AUTO") {
        return -1
      } else if (a.campaign_targeting_settings !== "AUTO" && b.campaign_targeting_settings === "AUTO") {
        return 1
      }
      return (new Date(a.campaign_start_date).getTime() - new Date(b.campaign_start_date).getTime())
    })
    return (
      <div className="flex flex-col h-full gap-y-4 items-start justify-start">
        <div className="flex-shrink-0 flex items-center gap-x-3">
          <div className="history-date-range">
            <div className="text-xs text-gray-400 font-semibold">{tos("detailModal.product.targetTab.filter.date.label")} *</div>
            {isTargetHistoryLoading
              ? <div className="animate-pulse pt-1">
                  <div className="w-[200px] h-9 rounded-md bg-gray-100"/>
                </div>
              : <DatePicker
                  selectsRange={true}
                  minDate={new Date('2023-01-01')}
                  maxDate={new Date()}
                  startDate={targetHistoryStartDate}
                  endDate={targetHistoryEndDate}
                  onChange={(update) => {
                    setTargetHistoryDateRange(update as Date[])
                  }}
                  dateFormat="yyyy.MM.dd"
                  calendarClassName="dashboard-date-range"
                  // @ts-ignore
                  customInput={<DateRangeInput />}
                  locale={locale}
                />
            }
          </div>
        </div>
        <div className="grow relative w-full rounded-lg bg-white border border-gray-100 overflow-y-scroll">
          {/* List */}
          <div className="sticky inset-x-0 top-0 z-[1] flex items-center gap-x-3 py-3 text-center text-sm text-gray-400 font-bold bg-gray-100/40 backdrop-blur-md">
          <div className="flex-shrink-0 w-[160px] px-4">
              {tos("detailModal.product.targetTab.table.header.actionType")}
            </div>
            <div className="flex-1 px-4">
              {tos("detailModal.product.targetTab.table.header.from")}
            </div>
            <div className="flex-1 px-4">
              {tos("detailModal.product.targetTab.table.header.to")}
            </div>
            <div className="flex-shrink-0 w-[200px] px-4">
              {tos("detailModal.product.targetTab.table.header.datetime")}
            </div>
          </div>
          { isTargetHistoryLoading
            ? <ul className="animate-pulse p-6 space-y-3">
                <li className="w-full flex items-center gap-x-3">
                  <div className="flex-shrink-0 w-[160px] h-16 rounded-md bg-gray-100"></div>
                  <div className="flex-1 h-16 rounded-md bg-gray-100"></div>
                  <div className="flex-1 h-16 rounded-md bg-gray-100"></div>
                  <div className="flex-shrink-0 w-[200px] h-16 rounded-md bg-gray-100"></div>
                </li>
                <li className="w-full flex items-center gap-x-3">
                  <div className="flex-shrink-0 w-[160px] h-16 rounded-md bg-gray-100"></div>
                  <div className="flex-1 h-16 rounded-md bg-gray-100"></div>
                  <div className="flex-1 h-16 rounded-md bg-gray-100"></div>
                  <div className="flex-shrink-0 w-[200px] h-16 rounded-md bg-gray-100"></div>
                </li>
                <li className="w-full flex items-center gap-x-3">
                  <div className="flex-shrink-0 w-[160px] h-16 rounded-md bg-gray-100"></div>
                  <div className="flex-1 h-16 rounded-md bg-gray-100"></div>
                  <div className="flex-1 h-16 rounded-md bg-gray-100"></div>
                  <div className="flex-shrink-0 w-[200px] h-16 rounded-md bg-gray-100"></div>
                </li>
                <li className="w-full flex items-center gap-x-3">
                  <div className="flex-shrink-0 w-[160px] h-16 rounded-md bg-gray-100"></div>
                  <div className="flex-1 h-16 rounded-md bg-gray-100"></div>
                  <div className="flex-1 h-16 rounded-md bg-gray-100"></div>
                  <div className="flex-shrink-0 w-[200px] h-16 rounded-md bg-gray-100"></div>
                </li>
                <li className="w-full flex items-center gap-x-3">
                  <div className="flex-shrink-0 w-[160px] h-16 rounded-md bg-gray-100"></div>
                  <div className="flex-1 h-16 rounded-md bg-gray-100"></div>
                  <div className="flex-1 h-16 rounded-md bg-gray-100"></div>
                  <div className="flex-shrink-0 w-[200px] h-16 rounded-md bg-gray-100"></div>
                </li>
                <li className="w-full flex items-center gap-x-3">
                  <div className="flex-shrink-0 w-[160px] h-16 rounded-md bg-gray-100"></div>
                  <div className="flex-1 h-16 rounded-md bg-gray-100"></div>
                  <div className="flex-1 h-16 rounded-md bg-gray-100"></div>
                  <div className="flex-shrink-0 w-[200px] h-16 rounded-md bg-gray-100"></div>
                </li>
                <li className="w-full flex items-center gap-x-3">
                  <div className="flex-shrink-0 w-[160px] h-16 rounded-md bg-gray-100"></div>
                  <div className="flex-1 h-16 rounded-md bg-gray-100"></div>
                  <div className="flex-1 h-16 rounded-md bg-gray-100"></div>
                  <div className="flex-shrink-0 w-[200px] h-16 rounded-md bg-gray-100"></div>
                </li>
                <li className="w-full flex items-center gap-x-3">
                  <div className="flex-shrink-0 w-[160px] h-16 rounded-md bg-gray-100"></div>
                  <div className="flex-1 h-16 rounded-md bg-gray-100"></div>
                  <div className="flex-1 h-16 rounded-md bg-gray-100"></div>
                  <div className="flex-shrink-0 w-[200px] h-16 rounded-md bg-gray-100"></div>
                </li>
              </ul>
            : <ul className="divide-y divide-gray-100">
              {
                targetHistory.length === 0
                  ? <div className="absolute inset-0 flex items-center justify-center w-full text-gray-300 text-sm font-normal">
                      {tos("detailModal.product.targetTab.table.content.null")}
                    </div>
                  : targetHistory.map((historyItem: any, index: number) => {
                      return (
                        <li
                          className="relative flex items-center gap-x-3 py-6 hover:bg-gray-100/50 text-center text-gray-500 text-sm"
                          key={index}
                        >
                          <div className="flex-shrink-0 w-[160px] px-4 text-xs font-semibold">
                            {historyTypeMapper[historyItem.operation_type as keyof typeof historyTypeMapper] || historyItem.operation_type}
                          </div>
                          {historyItem.old_value
                            ? <div className="flex-1 relative flex flex-col px-4 overflow-hidden text-left">
                                <div className="space-y-2 text-xs text-left break-words">
                                  {Object.entries(historyItem.old_value).map(([key, value], index) => (
                                    <div key={key + "-" + index}>
                                      {key === "campaignId"
                                        ? <>
                                          <div className="text-gray-400 text-[10px]">
                                            campaign
                                          </div>
                                          {(() => {
                                            const campaign = campaignList.find(campaign => campaign.campaign_id === value)
                                            return campaign ? (
                                              <div className="mt-1 px-4 py-2 space-y-2 bg-blue-100/40 text-xs text-left break-words rounded-md">
                                                <div className="grow relative flex flex-col overflow-hidden text-left">
                                                  <div className="text-xs text-gray-500 font-semibold text-left break-words leading-relaxed">
                                                    <span className="mr-2 py-0.5 px-1.5 bg-gray-200 text-[10px] text-gray-500 font-normal rounded-full break-normal">
                                                      {campaign.campaign_targeting_settings}
                                                    </span>
                                                    {campaign.campaign_name}
                                                  </div>
                                                  <div className="mt-0.5 text-[10px] text-gray-400 leading-snug">ID: {campaign.campaign_id}</div>
                                                  <div className="text-[10px] text-gray-400 leading-snug">Started At: {formatDate(campaign.campaign_start_date, ".")}</div>
                                                </div>
                                              </div>
                                            ) : null
                                          })()}
                                        </>
                                        : <>
                                          <div className="text-gray-400 text-[10px]">
                                            {historyKeyMapper[key as keyof typeof historyKeyMapper] || key}
                                          </div>
                                          <div className="text-xs text-gray-500">
                                            {typeof value === "object"
                                              ? (
                                                <div className="mt-1 px-4 py-2 space-y-2 bg-gray-100/40 text-xs text-left break-words rounded-md">
                                                  {value && Object.entries(value).map(([subkey, subvalue], index) => (
                                                    <div key={subkey + "-" + index}>
                                                      {subkey !== "0" &&
                                                      <div className="text-gray-400 text-[10px]">
                                                        {subkey === "budget"
                                                          ? "budgetAmount"
                                                          : subkey
                                                        }
                                                      </div>
                                                      }
                                                      <div className="text-xs text-gray-500">
                                                        {JSON.parse(JSON.stringify(subvalue))}
                                                      </div>
                                                    </div>
                                                  ))}
                                                </div>
                                              )
                                              : value as React.ReactNode
                                            }
                                          </div>
                                        </>
                                      }
                                    </div>
                                  ))}
                                  {historyItem.target_details && typeof historyItem.target_details === "object" &&
                                    <div>
                                      <div className="text-gray-400 text-[10px]">
                                        target details
                                      </div>
                                      <div className="text-xs text-gray-500">
                                        <div className="mt-1 px-4 py-2 space-y-2 bg-gray-100/40 text-xs text-left break-words rounded-md">
                                          {Object.entries(historyItem.target_details).map(([key, value], index) => (
                                            <div key={key + "-" + index}>
                                              <div className="text-gray-400 text-[10px]">
                                                {key}
                                              </div>
                                              <div className="text-xs text-gray-500">
                                                {JSON.parse(JSON.stringify(value))}
                                              </div>
                                            </div>
                                          ))}
                                        </div>
                                      </div>
                                    </div>
                                  }
                                </div>
                              </div>
                            : <div className="flex-1 relative flex flex-col px-4 overflow-hidden text-left"></div>
                          }
                          {historyItem.value &&
                          <div className="flex-1 relative flex flex-col px-4 overflow-hidden text-left">
                            <div className="space-y-2 text-xs text-left break-words">
                              {Object.entries(historyItem.value).map(([key, value], index) => (
                                <div key={key + "-" + index}>
                                  {key === "campaignId"
                                    ? <>
                                      <div className="text-gray-400 text-[10px]">
                                        campaign
                                      </div>
                                      {(() => {
                                        const campaign = campaignList.find(campaign => campaign.campaign_id === value)
                                        return campaign ? (
                                          <div className="mt-1 px-4 py-2 space-y-2 bg-blue-100/40 text-xs text-left break-words rounded-md">
                                            <div className="grow relative flex flex-col overflow-hidden text-left">
                                              <div className="text-xs text-gray-500 font-semibold text-left break-words leading-relaxed">
                                                <span className="mr-2 py-0.5 px-1.5 bg-gray-200 text-[10px] text-gray-500 font-normal rounded-full break-normal">
                                                  {campaign.campaign_targeting_settings}
                                                </span>
                                                {campaign.campaign_name}
                                              </div>
                                              <div className="mt-0.5 text-[10px] text-gray-400 leading-snug">ID: {campaign.campaign_id}</div>
                                              <div className="text-[10px] text-gray-400 leading-snug">Started At: {formatDate(campaign.campaign_start_date, ".")}</div>
                                            </div>
                                          </div>
                                        ) : null
                                      })()}
                                    </>
                                    : <>
                                      <div className="text-gray-400 text-[10px]">
                                        {historyKeyMapper[key as keyof typeof historyKeyMapper] || key}
                                      </div>
                                      <div className="text-xs text-gray-500">
                                        {typeof value === "object"
                                          ? (
                                            <div className="mt-1 px-4 py-2 space-y-2 bg-blue-100/40 text-xs text-left break-words rounded-md">
                                              {value && Object.entries(value).map(([subkey, subvalue], index) => (
                                                <div key={subkey + "-" + index}>
                                                  {subkey !== "0" &&
                                                  <div className="text-gray-400 text-[10px]">
                                                    {subkey === "budget"
                                                      ? "budgetAmount"
                                                      : subkey
                                                    }
                                                  </div>
                                                  }
                                                  <div className="text-xs text-gray-500">
                                                    {typeof subvalue === "object"
                                                      ? (
                                                        subvalue && Object.entries(subvalue).map(([sub2key, sub2value], index) => (
                                                          <div key={sub2key + "-" + index}>
                                                            {sub2key !== "0" &&
                                                            <div className="text-gray-400 text-[10px]">
                                                              {sub2key}
                                                            </div>
                                                            }
                                                            <div className="mt-1 text-xs text-gray-500">
                                                              {JSON.parse(JSON.stringify(sub2value))}
                                                            </div>
                                                          </div>
                                                        ))
                                                      )
                                                      : subvalue as React.ReactNode
                                                    }
                                                  </div>
                                                </div>
                                              ))}
                                            </div>
                                          )
                                          : value as React.ReactNode
                                        }
                                      </div>
                                    </>
                                  }
                                </div>
                              ))}
                              {historyItem.target_details && typeof historyItem.target_details === "object" &&
                                <div>
                                  <div className="text-gray-400 text-[10px]">
                                    target details
                                  </div>
                                  <div className="text-xs text-gray-500">
                                    <div className="mt-1 px-4 py-2 space-y-2 bg-blue-100/40 text-xs text-left break-words rounded-md">
                                      {Object.entries(historyItem.target_details).map(([key, value], index) => (
                                        <div key={key + "-" + index}>
                                          <div className="text-gray-400 text-[10px]">
                                            {key}
                                          </div>
                                          <div className="text-xs text-gray-500">
                                            {JSON.parse(JSON.stringify(value))}
                                          </div>
                                        </div>
                                      ))}
                                    </div>
                                  </div>
                                </div>
                              }
                            </div>
                          </div>
                          }
                          <div className="flex-shrink-0 w-[200px] px-4">
                            {formatDateTime(historyItem.datetime, ".")}
                          </div>
                        </li>
                      )
                    })
              }
              </ul>
          }
        </div>
      </div>
    )
  }

  const bidContent = (selectedProductItem: ProductListItem) => {
    const campaignList = selectedProductItem.campaigns?.filter((campaign) => campaign.campaign_mop_yn === "Y").sort((a, b) => {
      if (a.campaign_targeting_settings === "AUTO" && b.campaign_targeting_settings !== "AUTO") {
        return -1
      } else if (a.campaign_targeting_settings !== "AUTO" && b.campaign_targeting_settings === "AUTO") {
        return 1
      }
      return (new Date(a.campaign_start_date).getTime() - new Date(b.campaign_start_date).getTime())
    })
    return (
      <div className="flex flex-col h-full gap-y-4 items-start justify-start">
        <div className="flex-shrink-0 flex items-center gap-x-3">
          <div className="history-date-range">
            <div className="text-xs text-gray-400 font-semibold">{tos("detailModal.product.bidHistoryTab.filter.date.label")} *</div>
            {isBidHistoryLoading
              ? <div className="animate-pulse pt-1">
                  <div className="w-[200px] h-9 rounded-md bg-gray-100"/>
                </div>
              : <DatePicker
                  selectsRange={true}
                  minDate={new Date('2023-01-01')}
                  maxDate={new Date()}
                  startDate={bidHistoryStartDate}
                  endDate={bidHistoryEndDate}
                  onChange={(update) => {
                    setBidHistoryDateRange(update as Date[])
                  }}
                  dateFormat="yyyy.MM.dd"
                  calendarClassName="dashboard-date-range"
                  // @ts-ignore
                  customInput={<DateRangeInput />}
                  locale={locale}
                />
            }
          </div>
        </div>
        <div className="grow relative w-full rounded-lg bg-white border border-gray-100 overflow-y-scroll">
          {/* List */}
          <div className="sticky inset-x-0 top-0 z-[1] flex items-center gap-x-3 py-3 text-center text-sm text-gray-400 font-bold bg-gray-100/40 backdrop-blur-md">
          <div className="flex-shrink-0 w-[160px] px-4">
              {tos("detailModal.product.bidHistoryTab.table.header.actionType")}
            </div>
            <div className="flex-1 px-4">
              {tos("detailModal.product.bidHistoryTab.table.header.from")}
            </div>
            <div className="flex-1 px-4">
              {tos("detailModal.product.bidHistoryTab.table.header.to")}
            </div>
            <div className="flex-shrink-0 w-[200px] px-4">
              {tos("detailModal.product.bidHistoryTab.table.header.datetime")}
            </div>
          </div>
          { isBidHistoryLoading
            ? <ul className="animate-pulse p-6 space-y-3">
                <li className="w-full flex items-center gap-x-3">
                  <div className="flex-shrink-0 w-[160px] h-16 rounded-md bg-gray-100"></div>
                  <div className="flex-1 h-16 rounded-md bg-gray-100"></div>
                  <div className="flex-1 h-16 rounded-md bg-gray-100"></div>
                  <div className="flex-shrink-0 w-[200px] h-16 rounded-md bg-gray-100"></div>
                </li>
                <li className="w-full flex items-center gap-x-3">
                  <div className="flex-shrink-0 w-[160px] h-16 rounded-md bg-gray-100"></div>
                  <div className="flex-1 h-16 rounded-md bg-gray-100"></div>
                  <div className="flex-1 h-16 rounded-md bg-gray-100"></div>
                  <div className="flex-shrink-0 w-[200px] h-16 rounded-md bg-gray-100"></div>
                </li>
                <li className="w-full flex items-center gap-x-3">
                  <div className="flex-shrink-0 w-[160px] h-16 rounded-md bg-gray-100"></div>
                  <div className="flex-1 h-16 rounded-md bg-gray-100"></div>
                  <div className="flex-1 h-16 rounded-md bg-gray-100"></div>
                  <div className="flex-shrink-0 w-[200px] h-16 rounded-md bg-gray-100"></div>
                </li>
                <li className="w-full flex items-center gap-x-3">
                  <div className="flex-shrink-0 w-[160px] h-16 rounded-md bg-gray-100"></div>
                  <div className="flex-1 h-16 rounded-md bg-gray-100"></div>
                  <div className="flex-1 h-16 rounded-md bg-gray-100"></div>
                  <div className="flex-shrink-0 w-[200px] h-16 rounded-md bg-gray-100"></div>
                </li>
                <li className="w-full flex items-center gap-x-3">
                  <div className="flex-shrink-0 w-[160px] h-16 rounded-md bg-gray-100"></div>
                  <div className="flex-1 h-16 rounded-md bg-gray-100"></div>
                  <div className="flex-1 h-16 rounded-md bg-gray-100"></div>
                  <div className="flex-shrink-0 w-[200px] h-16 rounded-md bg-gray-100"></div>
                </li>
                <li className="w-full flex items-center gap-x-3">
                  <div className="flex-shrink-0 w-[160px] h-16 rounded-md bg-gray-100"></div>
                  <div className="flex-1 h-16 rounded-md bg-gray-100"></div>
                  <div className="flex-1 h-16 rounded-md bg-gray-100"></div>
                  <div className="flex-shrink-0 w-[200px] h-16 rounded-md bg-gray-100"></div>
                </li>
                <li className="w-full flex items-center gap-x-3">
                  <div className="flex-shrink-0 w-[160px] h-16 rounded-md bg-gray-100"></div>
                  <div className="flex-1 h-16 rounded-md bg-gray-100"></div>
                  <div className="flex-1 h-16 rounded-md bg-gray-100"></div>
                  <div className="flex-shrink-0 w-[200px] h-16 rounded-md bg-gray-100"></div>
                </li>
                <li className="w-full flex items-center gap-x-3">
                  <div className="flex-shrink-0 w-[160px] h-16 rounded-md bg-gray-100"></div>
                  <div className="flex-1 h-16 rounded-md bg-gray-100"></div>
                  <div className="flex-1 h-16 rounded-md bg-gray-100"></div>
                  <div className="flex-shrink-0 w-[200px] h-16 rounded-md bg-gray-100"></div>
                </li>
              </ul>
            : <ul className="divide-y divide-gray-100">
              {
                bidHistory.length === 0
                  ? <div className="absolute inset-0 flex items-center justify-center w-full text-gray-300 text-sm font-normal">
                      {tos("detailModal.product.bidHistoryTab.table.content.null")}
                    </div>
                  : bidHistory.map((historyItem: any, index: number) => {
                      return (
                        <li
                          className="relative flex items-center gap-x-3 py-6 hover:bg-gray-100/50 text-center text-gray-500 text-sm"
                          key={index}
                        >
                          <div className="flex-shrink-0 w-[160px] px-4 text-xs font-semibold">
                            {historyTypeMapper[historyItem.operation_type as keyof typeof historyTypeMapper] || historyItem.operation_type}
                          </div>
                          {historyItem.old_value
                            ? <div className="flex-1 relative flex flex-col px-4 overflow-hidden text-left">
                                <div className="space-y-2 text-xs text-left break-words">
                                  {Object.entries(historyItem.old_value).map(([key, value], index) => (
                                    <div key={key + "-" + index}>
                                      {key === "campaignId"
                                        ? <>
                                          <div className="text-gray-400 text-[10px]">
                                            campaign
                                          </div>
                                          {(() => {
                                            const campaign = campaignList.find(campaign => campaign.campaign_id === value)
                                            return campaign ? (
                                              <div className="mt-1 px-4 py-2 space-y-2 bg-blue-100/40 text-xs text-left break-words rounded-md">
                                                <div className="grow relative flex flex-col overflow-hidden text-left">
                                                  <div className="text-xs text-gray-500 font-semibold text-left break-words leading-relaxed">
                                                    <span className="mr-2 py-0.5 px-1.5 bg-gray-200 text-[10px] text-gray-500 font-normal rounded-full break-normal">
                                                      {campaign.campaign_targeting_settings}
                                                    </span>
                                                    {campaign.campaign_name}
                                                  </div>
                                                  <div className="mt-0.5 text-[10px] text-gray-400 leading-snug">ID: {campaign.campaign_id}</div>
                                                  <div className="text-[10px] text-gray-400 leading-snug">Started At: {formatDate(campaign.campaign_start_date, ".")}</div>
                                                </div>
                                              </div>
                                            ) : null
                                          })()}
                                        </>
                                        : <>
                                          <div className="text-gray-400 text-[10px]">
                                            {historyKeyMapper[key as keyof typeof historyKeyMapper] || key}
                                          </div>
                                          <div className="text-xs text-gray-500">
                                            {typeof value === "object"
                                              ? (
                                                <div className="mt-1 px-4 py-2 space-y-2 bg-gray-100/40 text-xs text-left break-words rounded-md">
                                                  {value && Object.entries(value).map(([subkey, subvalue], index) => (
                                                    <div key={subkey + "-" + index}>
                                                      {subkey !== "0" &&
                                                      <div className="text-gray-400 text-[10px]">
                                                        {subkey === "budget"
                                                          ? "budgetAmount"
                                                          : subkey
                                                        }
                                                      </div>
                                                      }
                                                      <div className="text-xs text-gray-500">
                                                        {JSON.parse(JSON.stringify(subvalue))}
                                                      </div>
                                                    </div>
                                                  ))}
                                                </div>
                                              )
                                              : value as React.ReactNode
                                            }
                                          </div>
                                        </>
                                      }
                                    </div>
                                  ))}
                                  {historyItem.target_details && typeof historyItem.target_details === "object" &&
                                    <div>
                                      <div className="text-gray-400 text-[10px]">
                                        target details
                                      </div>
                                      <div className="text-xs text-gray-500">
                                        <div className="mt-1 px-4 py-2 space-y-2 bg-gray-100/40 text-xs text-left break-words rounded-md">
                                          {Object.entries(historyItem.target_details).map(([key, value], index) => (
                                            <div key={key + "-" + index}>
                                              <div className="text-gray-400 text-[10px]">
                                                {key}
                                              </div>
                                              <div className="text-xs text-gray-500">
                                                {JSON.parse(JSON.stringify(value))}
                                              </div>
                                            </div>
                                          ))}
                                        </div>
                                      </div>
                                    </div>
                                  }
                                </div>
                              </div>
                            : <div className="flex-1 relative flex flex-col px-4 overflow-hidden text-left"></div>
                          }
                          {historyItem.value &&
                          <div className="flex-1 relative flex flex-col px-4 overflow-hidden text-left">
                            <div className="space-y-2 text-xs text-left break-words">
                              {Object.entries(historyItem.value).map(([key, value], index) => (
                                <div key={key + "-" + index}>
                                  {key === "campaignId"
                                    ? <>
                                      <div className="text-gray-400 text-[10px]">
                                        campaign
                                      </div>
                                      {(() => {
                                        const campaign = campaignList.find(campaign => campaign.campaign_id === value)
                                        return campaign ? (
                                          <div className="mt-1 px-4 py-2 space-y-2 bg-blue-100/40 text-xs text-left break-words rounded-md">
                                            <div className="grow relative flex flex-col overflow-hidden text-left">
                                              <div className="text-xs text-gray-500 font-semibold text-left break-words leading-relaxed">
                                                <span className="mr-2 py-0.5 px-1.5 bg-gray-200 text-[10px] text-gray-500 font-normal rounded-full break-normal">
                                                  {campaign.campaign_targeting_settings}
                                                </span>
                                                {campaign.campaign_name}
                                              </div>
                                              <div className="mt-0.5 text-[10px] text-gray-400 leading-snug">ID: {campaign.campaign_id}</div>
                                              <div className="text-[10px] text-gray-400 leading-snug">Started At: {formatDate(campaign.campaign_start_date, ".")}</div>
                                            </div>
                                          </div>
                                        ) : null
                                      })()}
                                    </>
                                    : <>
                                      <div className="text-gray-400 text-[10px]">
                                        {historyKeyMapper[key as keyof typeof historyKeyMapper] || key}
                                      </div>
                                      <div className="text-xs text-gray-500">
                                        {typeof value === "object"
                                          ? (
                                            <div className="mt-1 px-4 py-2 space-y-2 bg-blue-100/40 text-xs text-left break-words rounded-md">
                                              {value && Object.entries(value).map(([subkey, subvalue], index) => (
                                                <div key={subkey + "-" + index}>
                                                  {subkey !== "0" &&
                                                  <div className="text-gray-400 text-[10px]">
                                                    {subkey === "budget"
                                                      ? "budgetAmount"
                                                      : subkey
                                                    }
                                                  </div>
                                                  }
                                                  <div className="text-xs text-gray-500">
                                                    {typeof subvalue === "object"
                                                      ? (
                                                        subvalue && Object.entries(subvalue).map(([sub2key, sub2value], index) => (
                                                          <div key={sub2key + "-" + index}>
                                                            {sub2key !== "0" &&
                                                            <div className="text-gray-400 text-[10px]">
                                                              {sub2key}
                                                            </div>
                                                            }
                                                            <div className="mt-1 text-xs text-gray-500">
                                                              {JSON.parse(JSON.stringify(sub2value))}
                                                            </div>
                                                          </div>
                                                        ))
                                                      )
                                                      : subvalue as React.ReactNode
                                                    }
                                                  </div>
                                                </div>
                                              ))}
                                            </div>
                                          )
                                          : value as React.ReactNode
                                        }
                                      </div>
                                    </>
                                  }
                                </div>
                              ))}
                              {historyItem.target_details && typeof historyItem.target_details === "object" &&
                                <div>
                                  <div className="text-gray-400 text-[10px]">
                                    target details
                                  </div>
                                  <div className="text-xs text-gray-500">
                                    <div className="mt-1 px-4 py-2 space-y-2 bg-blue-100/40 text-xs text-left break-words rounded-md">
                                      {Object.entries(historyItem.target_details).map(([key, value], index) => (
                                        <div key={key + "-" + index}>
                                          <div className="text-gray-400 text-[10px]">
                                            {key}
                                          </div>
                                          <div className="text-xs text-gray-500">
                                            {JSON.parse(JSON.stringify(value))}
                                          </div>
                                        </div>
                                      ))}
                                    </div>
                                  </div>
                                </div>
                              }
                            </div>
                          </div>
                          }
                          <div className="flex-shrink-0 w-[200px] px-4">
                            {formatDateTime(historyItem.datetime, ".")}
                          </div>
                        </li>
                      )
                    })
              }
              </ul>
          }
        </div>
      </div>
    )
  }

  // 타겟 목록에 차트 아이콘 추가
  const renderTargetList = () => {
    if (isTargetsLoading) {
      return (
        <div className="py-8 flex items-center justify-center">
          <div className="animate-pulse space-y-4">
            <div className="h-4 w-32 bg-gray-200 rounded"></div>
            <div className="h-4 w-48 bg-gray-200 rounded"></div>
            <div className="h-4 w-40 bg-gray-200 rounded"></div>
          </div>
        </div>
      );
    }
    
    if (searchedAndFilteredTargets.length === 0) {
      if (searchTerm.trim() && filteredTargets.length > 0) { // 검색어는 있으나 결과가 없는 경우
        return (
          <div className="py-12 text-center text-gray-400">
            No targets found for &quot;{searchTerm}&quot;.
          </div>
        );
      } 
      // 원래 타겟이 없는 경우 (기존 메시지 로직 사용)
      return (
        <div className="py-12 text-center text-gray-400">
          {selectedTargetType === "PRODUCT" 
            ? "No product targets available"
            : selectedTargetType === "KEYWORD"
              ? "No keyword targets available"
              : selectedTargetType === "OTHERS"
                ? "No other targets available"
                : selectedTargetType === "NEGATIVE_PRODUCT"
                  ? "No negative product targets available"
                  : selectedTargetType === "NEGATIVE_KEYWORD"
                    ? "No negative keyword targets available"
                    : "No targets available for this selection" 
          }
        </div>
      );
    }
    
    return (
      <ul className="divide-y divide-gray-100">
        {searchedAndFilteredTargets.map((target, index) => {
          const isLoadingItem = targetSwitchLoading.includes(target.target_id) 

          return (
           <div className='relative' key={target.target_id}>
              {isLoadingItem && (
                <Box
                  position='absolute'
                  top={0}
                  left={0}
                  width='100%'
                  height='100%'
                  display='flex'
                  justifyContent='center'
                  alignItems='center'
                  bgcolor='rgba(255,255,255,0.6)'
                >
                  <CircularProgress size={24} />
                </Box>
              )}

              <li
                key={target.target_id || index}
                className='p-4 hover:bg-gray-50'
              >
                <div className='flex items-center gap-x-4'>
                  <div className='relative inline-flex'>
                    <Switch
                      checked={target.state === TargetState.ENABLED}
                      disabled={isLoadingItem}
                      onChange={(event, checked) =>
                        handleSwitchKeyword(event, checked, target.target_id)
                      }
                    />
                  </div>
                  <div className='flex-1'>
                    <div className='flex items-center justify-between'>
                      <div className='flex flex-col'>
                        <div className='text-xs text-gray-400 mt-1'>
                          <span className='px-2 py-0.5 bg-gray-100 rounded-full'>
                            {target.target_type}
                          </span>

                          <span className='px-4 py-1 bg-cyan-700 rounded-lg text-white ml-2.5'>
                            {target.match_type}
                          </span>
                        </div>
                        <div className='font-medium text-sm mt-1'>
                          {getHighlightedText(target.target, searchTerm)}
                        </div>
                      </div>
                      <div className='flex items-center gap-x-2'>
                        {target.negative && (
                          <span className='px-2 py-1 bg-red-100 text-red-600 text-xs rounded-full'>
                            Negative
                          </span>
                        )}
                        {!target.negative && (
                          <button
                            onClick={(e) =>
                              handleTargetChartButtonClick(e, target)
                            }
                            className='p-1 rounded-full hover:bg-blue-100 transition-colors duration-200'
                          >
                            <PresentationChartLineIcon className='h-5 w-5 text-blue-500' />
                          </button>
                        )}
                      </div>
                    </div>
                    <div className='mt-2 flex items-center text-xs text-gray-500 gap-x-4'>
                      <div>
                        Added on {formatDate(target.creation_date_time, '.')}
                      </div>
                      <div className='flex items-center gap-x-1'>
                        {target.bid == null ? (
                          ''
                        ) : (
                          <>
                            <span>Bid:</span>
                            <span className='font-semibold'>
                              {formatCurrency(
                                Number(target.bid) || 0,
                                currencyCode
                              )}
                            </span>
                          </>
                        )}
                      </div>
                      <div className='flex items-center gap-x-1'>
                        <div
                          className={cn(
                            'h-2 w-2 rounded-full',
                            target.state === 'PAUSED'
                              ? 'bg-red-500'
                              : target.state === 'ENABLED'
                              ? 'bg-green-500'
                              : 'bg-gray-300'
                          )}
                        />
                        <span>
                          {target.state === 'PAUSED'
                            ? 'Paused'
                            : target.state === 'ENABLED'
                            ? 'Enabled'
                            : target.state}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </li>
            </div>
           
          );
        })}
      </ul>
    );
  }

  // 페이지네이션 관련 계산
  const renderTargetHistoryTable = () => {
    if (!targetBidHistory?.histories?.length || targetBidHistory?.total === 0) {
      return (
        <tr>
          <td colSpan={3} className="py-4 text-center text-sm text-gray-500">
            입찰가 변경 기록이 없습니다.
          </td>
        </tr>
      );
    }

    // 현재 페이지에 표시할 데이터 계산
    const indexOfLastItem = currentHistoryPage * itemsPerPage;
    const indexOfFirstItem = indexOfLastItem - itemsPerPage;
    const currentItems = targetBidHistory.histories.slice(indexOfFirstItem, indexOfLastItem);
    
    return currentItems.map((item: any, idx: number) => {
      // bid_amount 또는 bid를 확인
      const bidAmount = item.bid_amount !== undefined ? item.bid_amount : 
                        item.bid !== undefined ? item.bid : 
                        item.value && item.value.bid ? item.value.bid : 0;
      
      return (
        <tr key={idx} className="hover:bg-gray-50">
          <td className="whitespace-nowrap py-3 pl-4 pr-3 text-sm font-medium text-gray-900 sm:pl-6">
            {formatDateTime(item.history_created_at, ".")}
          </td>
          <td className="whitespace-nowrap px-3 py-3 text-sm text-gray-500">
            ${Number(bidAmount).toFixed(2)}
          </td>
          <td className="whitespace-nowrap px-3 py-3 text-sm text-gray-500">
            {item.operation_type === "MODIFY_TARGET_BID" ? "MODIFY TARGET BID" : item.operation_type}
          </td>
        </tr>
      );
    });
  };

  // 페이지네이션 컨트롤
  const renderPagination = () => {
    if (!targetBidHistory?.histories?.length || targetBidHistory?.total === 0) return null;
    
    const totalPages = Math.ceil(targetBidHistory.histories.length / itemsPerPage);
    if (totalPages <= 1) return null;
    
    // 표시할 페이지 번호 범위 계산
    let startPage = Math.max(1, currentHistoryPage - 2);
    let endPage = Math.min(totalPages, startPage + 4);
    
    // 5개 페이지를 유지하기 위한 조정
    if (endPage - startPage < 4 && startPage > 1) {
      startPage = Math.max(1, endPage - 4);
    }
    
    const pageNumbers = [];
    for (let i = startPage; i <= endPage; i++) {
      pageNumbers.push(i);
    }
    
    return (
      <div className="flex items-center justify-center mt-4 space-x-2">
        {/* 맨 처음으로 */}
        <button 
          onClick={() => setCurrentHistoryPage(1)}
          disabled={currentHistoryPage === 1}
          className={`px-2 py-1 rounded-md text-sm ${currentHistoryPage === 1 ? 'text-gray-400 cursor-not-allowed' : 'text-blue-600 hover:bg-blue-50'}`}
          title="맨 처음으로"
        >
          <ChevronDoubleLeftIcon className="h-5 w-5" />
        </button>
        
        {/* 이전 페이지 */}
        <button 
          onClick={() => setCurrentHistoryPage(prev => Math.max(prev - 1, 1))}
          disabled={currentHistoryPage === 1}
          className={`px-2 py-1 rounded-md text-sm ${currentHistoryPage === 1 ? 'text-gray-400 cursor-not-allowed' : 'text-blue-600 hover:bg-blue-50'}`}
          title="이전 페이지"
        >
          <ChevronLeftIcon className="h-5 w-5" />
        </button>
        
        {/* 페이지 번호 */}
        {pageNumbers.map(number => (
          <button
            key={number}
            onClick={() => setCurrentHistoryPage(number)}
            className={`px-3 py-1 rounded-md text-sm ${currentHistoryPage === number ? 'bg-blue-600 text-white' : 'text-gray-600 hover:bg-gray-100'}`}
          >
            {number}
          </button>
        ))}
        
        {/* 다음 페이지 */}
        <button 
          onClick={() => setCurrentHistoryPage(prev => Math.min(prev + 1, totalPages))}
          disabled={currentHistoryPage === totalPages}
          className={`px-2 py-1 rounded-md text-sm ${currentHistoryPage === totalPages ? 'text-gray-400 cursor-not-allowed' : 'text-blue-600 hover:bg-blue-50'}`}
          title="다음 페이지"
        >
          <ChevronRightIcon className="h-5 w-5" />
        </button>
        
        {/* 맨 끝으로 */}
        <button 
          onClick={() => setCurrentHistoryPage(totalPages)}
          disabled={currentHistoryPage === totalPages}
          className={`px-2 py-1 rounded-md text-sm ${currentHistoryPage === totalPages ? 'text-gray-400 cursor-not-allowed' : 'text-blue-600 hover:bg-blue-50'}`}
          title="맨 끝으로"
        >
          <ChevronDoubleRightIcon className="h-5 w-5" />
        </button>
      </div>
    );
  };

  // 예산 변경 내역 테이블 렌더링 함수
  const renderBudgetHistoryTable = () => {
    if (!campaignHistory?.histories?.length || campaignHistory?.total === 0) {
      return (
        <tr>
          <td colSpan={3} className="py-4 text-center text-sm text-gray-500">
            예산 변경 기록이 없습니다.
          </td>
        </tr>
      );
    }

    const indexOfLastItem = currentBudgetHistoryPage * itemsPerPage;
    const indexOfFirstItem = indexOfLastItem - itemsPerPage;
    const currentItems = campaignHistory.histories.slice(indexOfFirstItem, indexOfLastItem);

    return currentItems.map((item: any, idx: number) => (
      <tr key={idx} className="hover:bg-gray-50">
        <td className="whitespace-nowrap py-3 pl-4 pr-3 text-sm font-medium text-gray-900 sm:pl-6">
          {formatDateTime(item.history_created_at, ".")}
        </td>
        <td className="whitespace-nowrap px-3 py-3 text-sm text-gray-500">
          {formatCurrency(Number(item.budget_amount) || 0, currencyCode)}
        </td>
        <td className="whitespace-nowrap px-3 py-3 text-sm text-gray-500">
          {item.operation_type === "EDIT_CAMPAIGN_BUDGETS" ? "EDIT_CAMPAIGN_BUDGETS" : item.operation_type}
        </td>
      </tr>
    ));
  };

  // 예산 변경 내역 페이지네이션 컨트롤
  const renderBudgetPagination = () => {
    if (!campaignHistory?.histories?.length || campaignHistory?.total === 0) return null;

    const totalPages = Math.ceil(campaignHistory.histories.length / itemsPerPage);
    if (totalPages <= 1) return null;

    let startPage = Math.max(1, currentBudgetHistoryPage - 2);
    let endPage = Math.min(totalPages, startPage + 4);

    if (endPage - startPage < 4 && startPage > 1) {
      startPage = Math.max(1, endPage - 4);
    }

    const pageNumbers = [];
    for (let i = startPage; i <= endPage; i++) {
      pageNumbers.push(i);
    }

    return (
      <div className="flex items-center justify-center mt-4 space-x-2">
        <button
          onClick={() => setCurrentBudgetHistoryPage(1)}
          disabled={currentBudgetHistoryPage === 1}
          className={`px-2 py-1 rounded-md text-sm ${currentBudgetHistoryPage === 1 ? 'text-gray-400 cursor-not-allowed' : 'text-blue-600 hover:bg-blue-50'}`}
          title="맨 처음으로"
        >
          <ChevronDoubleLeftIcon className="h-5 w-5" />
        </button>
        <button
          onClick={() => setCurrentBudgetHistoryPage(prev => Math.max(prev - 1, 1))}
          disabled={currentBudgetHistoryPage === 1}
          className={`px-2 py-1 rounded-md text-sm ${currentBudgetHistoryPage === 1 ? 'text-gray-400 cursor-not-allowed' : 'text-blue-600 hover:bg-blue-50'}`}
          title="이전 페이지"
        >
          <ChevronLeftIcon className="h-5 w-5" />
        </button>
        {pageNumbers.map(number => (
          <button
            key={number}
            onClick={() => setCurrentBudgetHistoryPage(number)}
            className={`px-3 py-1 rounded-md text-sm ${currentBudgetHistoryPage === number ? 'bg-blue-600 text-white' : 'text-gray-600 hover:bg-gray-100'}`}
          >
            {number}
          </button>
        ))}
        <button
          onClick={() => setCurrentBudgetHistoryPage(prev => Math.min(prev + 1, totalPages))}
          disabled={currentBudgetHistoryPage === totalPages}
          className={`px-2 py-1 rounded-md text-sm ${currentBudgetHistoryPage === totalPages ? 'text-gray-400 cursor-not-allowed' : 'text-blue-600 hover:bg-blue-50'}`}
          title="다음 페이지"
        >
          <ChevronRightIcon className="h-5 w-5" />
        </button>
        <button
          onClick={() => setCurrentBudgetHistoryPage(totalPages)}
          disabled={currentBudgetHistoryPage === totalPages}
          className={`px-2 py-1 rounded-md text-sm ${currentBudgetHistoryPage === totalPages ? 'text-gray-400 cursor-not-allowed' : 'text-blue-600 hover:bg-blue-50'}`}
          title="맨 끝으로"
        >
          <ChevronDoubleRightIcon className="h-5 w-5" />
        </button>
      </div>
    );
  };

  return (
    <>
      <TransitionChild
        as={Fragment}
        enter="ease-out duration-300"
        enterFrom="opacity-0"
        enterTo="opacity-100"
        leave="ease-in duration-200"
        leaveFrom="opacity-100"
        leaveTo="opacity-0"
      >
        <div className="w-full h-full bg-black/30" onClick={handleProductViewCloseClick} />
      </TransitionChild>
      <TransitionChild
        as={Fragment}
        enter="ease-out duration-300"
        enterFrom="opacity-0 w-[0vw]"
        enterTo="opacity-100 w-[80vw]"
        leave="ease-in duration-200"
        leaveFrom="opacity-100 w-[80vw]"
        leaveTo="opacity-0 w-[0vw]"
      >
        <div className="absolute inset-y-0 right-0 w-[80vw] h-full overflow-hidden shadow-md">
          {/* item content for product item */}
          { selectedProductItem && parentPortfolioItem &&
            <div className="flex-shrink-0 flex flex-col w-[80vw] h-full px-6 pt-6 pb-10 bg-white">
              {/* right side slider header */}
              <div className="flex-shrink-0 w-full flex flex-col gap-y-3">
                {selectedProductItem.eligibility_status === "INELIGIBLE"
                  ? <div className="relative flex-shrink-0 w-full bg-red-100 rounded-md border border-red-400 overflow-hidden">
                      <div className="w-full flex items-center justify-between py-2 px-4 bg-red-400 text-xs font-semibold text-white">
                        <div className="flex items-center gap-x-1">
                          <ExclamationTriangleIcon className="h-4 w-4" />
                          <span>
                            {tos("detailModal.product.message.abnormal.ineligibleAlert")}
                          </span>
                        </div>
                      </div>
                      <div className="grow relative flex items-center w-full py-4 gap-x-4 overflow-y-scroll text-xs text-gray-500 divide-x divide-red-400">
                        <div className="pl-4 flex items-center gap-x-1">
                          {
                            tos.rich("detailModal.product.message.abnormal.action", {
                              first: (chunks) => <div className="">{chunks}</div>,
                              red: (chunks) => <span className="font-semibold text-red-500">{chunks}</span>
                            })
                          }
                        </div>
                      </div>
                    </div>
                  : eligibilityAbnormality
                    ? <div className="relative flex-shrink-0 w-full bg-orange-100 rounded-md border border-orange-400 overflow-hidden">
                        <div className="w-full flex items-center justify-between py-2 px-4 bg-orange-400 text-xs font-semibold text-white">
                          <div className="flex items-center gap-x-1">
                            <ExclamationTriangleIcon className="h-4 w-4" />
                            <span>
                              {tos("detailModal.product.message.warning.lowStockAlert")}
                            </span>
                          </div>
                        </div>
                        <div className="grow relative flex items-center w-full py-4 gap-x-4 overflow-y-scroll text-xs text-gray-500 divide-x divide-orange-400">
                          <div className="pl-4 flex items-center gap-x-1">
                            {
                              tos.rich("detailModal.product.message.warning.estimatedSold",{
                                value: targetProductInventory && 'estimated_daily_units_sold' in targetProductInventory 
                                  ? (targetProductInventory as any).estimated_daily_units_sold.toFixed(2)
                                  : "0",
                                first: (chunks) => <div className="flex items-center gap-x-1 pt-1">{chunks}</div>,
                                orange1: (chunks) => <div className="font-semibold text-lg text-orange-500">{chunks}</div>,
                                orange2: (chunks) => <span className="font-semibold text-orange-500">{chunks}</span>
                              })
                            }
                          </div>
                          <div className="pl-4 flex items-center gap-x-1">
                            {
                              tos.rich("detailModal.product.message.warning.available",{
                                value: targetProductInventory && 'available_quantity' in targetProductInventory 
                                  ? (targetProductInventory as any).available_quantity.toFixed(0)
                                  : "0",
                                first: (chunks) => <div className="flex items-center gap-x-1 pt-1">{chunks}</div>,
                                orange1: (chunks) => <div className="font-semibold text-lg text-orange-500">{chunks}</div>,
                                orange2: (chunks) => <span className="font-semibold text-orange-500">{chunks}</span>
                              })
                            }
                          </div>
                          <div className="pl-4 flex items-center gap-x-1">
                            {
                              tos.rich("detailModal.product.message.warning.estimatedDaysLeft",{
                                value: estimatedDaysToSoldOut.toFixed(0),
                                first: (chunks) => <div className="flex items-center gap-x-1 pt-1">{chunks}</div>,
                                orange1: (chunks) => <div className="font-semibold text-lg text-orange-500">{chunks}</div>,
                                orange2: (chunks) => <span className="font-semibold text-orange-500">{chunks}</span>
                              })
                            }
                          </div>
                        </div>
                      </div>
                    : ""
                }
                <div className="flex-shrink-0 w-full flex items-center justify-between">
                  <button onClick={handleProductViewCloseClick}>
                    <XMarkIcon className="h-5 w-5 text-gray-500 hover:text-gray-800" />
                  </button>
                  <div className="flex items-center gap-x-4 py-3">
                    <ProductStatus productItem={selectedProductItem} />
                    <div></div>
                    {/* { selectedProductItem && selectedProductItem.request_status === "Paused"
                      ? (<button
                          className="flex items-center gap-x-2 p-3 cursor-pointer rounded-md focus:outline-none text-sm overflow-hidden bg-purple-100 hover:bg-purple-200 text-purple-500 font-semibold"
                          onClick={() => {}}>
                          <PlayIcon
                            className="flex-shrink-0 h-3 w-3"
                            aria-hidden="true"
                          />
                          <div>Automate</div>
                        </button>)
                      : (<button
                          className="flex items-center gap-x-2 p-3 cursor-pointer rounded-md focus:outline-none text-sm overflow-hidden bg-purple-100 hover:bg-purple-200 text-purple-500 font-semibold"
                          onClick={() => {}}>
                          <PauseIcon
                            className="flex-shrink-0 h-3 w-3"
                            aria-hidden="true"
                          />
                          <div>Pause</div>
                        </button>)
                    } */}
                  </div>
                </div>
              </div>
              <div className="grow relative flex flex-col w-full min-h-0">
                <div className="flex-shrink-0">
                  <div className="flex items-center pb-6 gap-x-6 border-b border-gray-100">
                    <div className="flex-shrink-0 p-4">
                      { selectedProductItem.image
                        ? (<img src={selectedProductItem.image} alt="Item Image" className="w-20 h-20 rounded" />)
                        : (<div className="flex items-center justify-center w-20 h-20 bg-gray-100 rounded">
                            <ExclamationTriangleIcon className="h-6 w-6 text-gray-300" />
                          </div>)
                      }
                    </div>
                    <div className="grow flex flex-col gap-y-0.5">
                      <a
                        href={`https://www.amazon.com/dp/${selectedProductItem.asin}`}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="group text-sm text-gray-500 text-left font-semibold focus:outline-none break-words leading-relaxed"
                      >
                        <span className="mr-2 py-0.5 px-1.5 bg-gray-200 text-xs text-gray-500 font-normal rounded-full break-normal no-underline">
                          {selectedProductItem.optimization_set.optimization_name}
                        </span>
                        <span className="group-hover:underline">
                          {selectedProductItem.item_name
                            ? selectedProductItem.item_name
                            : "No Title"
                          }
                        </span>
                      </a>
                      <div className="flex items-center gap-x-2 divide-x divide-gray-100">
                        <div className="text-sm text-red-400 font-semibold">{formatCurrency(selectedProductItem.listing_price || 0, currencyCode)}</div>
                        {selectedProductItem.eligibility_status &&
                          selectedProductItem.eligibility_status === "ELIGIBLE"
                            ? <div className="pl-2 text-xs text-blue-400 font-semibold">{t("eligibility.eligible")}</div>
                            : selectedProductItem.eligibility_status === "INELIGIBLE"
                              ? <div className="pl-2 text-xs text-gray-400 font-semibold">{t("eligibility.ineligible")}</div>
                              : <div className="pl-2 text-xs text-gray-400 font-semibold">{t("eligibility.unknown")}</div>
                        }
                      </div>
                      <div className="mt-1 flex items-center gap-x-2 divide-x divide-gray-100">
                        <div className="text-xs text-gray-400">{tos("detailModal.product.productProfile.label.asin")}: {selectedProductItem.asin}</div>
                        <div className="pl-2 text-xs text-gray-400">{tos("detailModal.product.productProfile.label.sku")}: {selectedProductItem.sku}</div>
                      </div>
                    </div>
                  </div>
                </div>
                <div className="grow relative flex flex-col min-h-0">
                  <TabGroup
                    className="flex flex-col h-full"
                    selectedIndex={selectedTabIndex}
                    onChange={setSelectedTabIndex}
                  >
                    <div className="flex-shrink-0">
                      <TabList className="hidden inline-flex gap-x-1 bg-gray-100 p-1 rounded-lg">
                        <Tab
                          className="flex items-center gap-x-1 py-2 px-4 text-sm font-semibold text-gray-400 focus:outline-none data-[selected]:text-gray-500 data-[selected]:bg-white data-[selected]:shadow-md data-[selected]:data-[hover]:bg-white data-[hover]:bg-white/40 data-[focus]:outline-1 data-[focus]:outline-white rounded-lg"
                        >
                          {tos("detailModal.product.campaignTab.tabLabel")}
                          <Popover className="relative flex items-center justify-center">
                            {({ open }) => (
                              <>
                                <PopoverButton
                                  className={cn(
                                    "inline-flex items-center justify-center rounded-full text-gray-300 hover:text-gray-400 focus:outline-none"
                                  )}
                                >
                                  <QuestionMarkCircleIcon className="w-5 h-5"/>
                                </PopoverButton>
                                <Transition
                                  as={Fragment}
                                  enter="transition ease-out duration-200"
                                  enterFrom="opacity-0 translate-y-1"
                                  enterTo="opacity-100 translate-y-0"
                                  leave="transition ease-in duration-150"
                                  leaveFrom="opacity-100 translate-y-0"
                                  leaveTo="opacity-0 translate-y-1"
                                >
                                  <PopoverPanel className="absolute left-full top-0 z-10 ml-2 w-screen max-w-xs translate-y-0 transform px-4 sm:px-0">
                                    <div className="overflow-hidden rounded-lg shadow-lg">
                                      <div className="relative p-4 bg-gray-900/90 text-xs text-white font-normal text-left">
                                        {tos.rich("detailModal.product.campaignTab.tabTooltip",{
                                          enter: () =>  <br/>
                                        })}
                                        {/* Run campaigns automated by Amazon for the first 2 weeks to soft land the product.
                                        <br/><br/>
                                        Based on the performance of campaigns run by Amazon for the last 2 weeks, the MOP system will automatically optimize the bid to reach your objective within the budget. */}
                                      </div>
                                    </div>
                                  </PopoverPanel>
                                </Transition>
                              </>
                            )}
                          </Popover>
                        </Tab>
                        <Tab
                          className="flex items-center gap-x-1 py-2 px-4 text-sm font-semibold text-gray-400 focus:outline-none data-[selected]:text-gray-500 data-[selected]:bg-white data-[selected]:shadow-md data-[selected]:data-[hover]:bg-white data-[hover]:bg-white/40 data-[focus]:outline-1 data-[focus]:outline-white rounded-lg"
                        >
                          {tos("detailModal.product.productHistoryTab.tabLabel")}
                        </Tab>
                        <Tab
                          className="flex items-center gap-x-1 py-2 px-4 text-sm font-semibold text-gray-400 focus:outline-none data-[selected]:text-gray-500 data-[selected]:bg-white data-[selected]:shadow-md data-[selected]:data-[hover]:bg-white data-[hover]:bg-white/40 data-[focus]:outline-1 data-[focus]:outline-white rounded-lg"
                        >
                          {tos("detailModal.product.targetTab.tabLabel")}
                        </Tab>
                        <Tab
                          className="flex items-center gap-x-1 py-2 px-4 text-sm font-semibold text-gray-400 focus:outline-none data-[selected]:text-gray-500 data-[selected]:bg-white data-[selected]:shadow-md data-[selected]:data-[hover]:bg-white data-[hover]:bg-white/40 data-[focus]:outline-1 data-[focus]:outline-white rounded-lg"
                        >
                          {tos("detailModal.product.bidHistoryTab.tabLabel")}
                        </Tab>
                      </TabList>
                    </div>
                    <TabPanels className="mt-6 grow relative flex flex-col min-h-0">
                      <TabPanel className="flex flex-col w-full h-full">
                        <div className="relative grow w-full min-h-0">
                          {campaignContent(selectedProductItem, parentPortfolioItem)}
                        </div>
                      </TabPanel>
                      <TabPanel className="flex flex-col w-full h-full">
                        <div className="relative grow w-full min-h-0">
                          {productHistoryContent(selectedProductItem)}
                        </div>
                      </TabPanel>
                      <TabPanel className="flex flex-col w-full h-full">
                        <div className="relative grow w-full min-h-0">
                          {targetContent(selectedProductItem)}
                        </div>
                      </TabPanel>
                      <TabPanel className="flex flex-col w-full h-full">
                        <div className="relative grow w-full min-h-0">
                          {bidContent(selectedProductItem)}
                        </div>
                      </TabPanel>
                    </TabPanels>
                  </TabGroup>
                </div>
              </div>
            </div>
          }
        </div>
      </TransitionChild>
      
      {/* 차트 모달 */}
      <Transition appear show={isChartModalOpen} as={Fragment}>
        <Dialog as="div" className="relative z-20" onClose={() => setIsChartModalOpen(false)}>
          <div className="fixed inset-0 bg-black/30 backdrop-blur-sm" />
          <div className="fixed inset-0 overflow-y-auto">
            <div className="flex min-h-full items-center justify-center p-4">
              <TransitionChild
                as={Fragment}
                enter="ease-out duration-300"
                enterFrom="opacity-0 scale-95"
                enterTo="opacity-100 scale-100"
                leave="ease-in duration-200"
                leaveFrom="opacity-100 scale-100"
                leaveTo="opacity-0 scale-95"
              >
                <DialogPanel className="w-full max-w-4xl rounded-lg bg-white p-6 shadow-xl">
                  <div className="flex items-center justify-between">
                    <DialogTitle as="h3" className="text-lg font-medium text-gray-700">
                      {selectedCampaignForChart?.campaign_name || '캠페인 분석'}
                    </DialogTitle>
                    <button
                      type="button"
                      className="text-gray-400 hover:text-gray-500"
                      onClick={() => setIsChartModalOpen(false)}
                    >
                      <XMarkIcon className="h-6 w-6" />
                    </button>
                  </div>
                  <div className="mt-4">
                    {selectedCampaignForChart ? (
                      <div className="space-y-4">
                        <div className="flex flex-col gap-2 p-4 bg-gray-50 rounded-md">
                          <div className="text-sm text-gray-500">Campaign Info</div>
                          <div className="grid grid-cols-2 gap-4">
                            <div>
                              <div className="text-xs text-gray-400">ID</div>
                              <div className="text-sm font-medium">{selectedCampaignForChart.campaign_id}</div>
                            </div>
                            <div>
                              <div className="text-xs text-gray-400">Type</div>
                              <div className="text-sm font-medium">{selectedCampaignForChart.campaign_targeting_settings}</div>
                            </div>
                            <div>
                              <div className="text-xs text-gray-400">Status</div>
                              <div className="text-sm font-medium">
                                {selectedCampaignForChart.campaign_state === "PAUSED"
                                  ? "PAUSED"
                                  : selectedCampaignForChart.campaign_state === "ENABLED"
                                    ? "ENABLED"
                                    : selectedCampaignForChart.campaign_state
                                }
                              </div>
                            </div>
                            <div>
                              <div className="text-xs text-gray-400">Start Date</div>
                              <div className="text-sm font-medium">{formatDate(selectedCampaignForChart.campaign_start_date, ".")}</div>
                            </div>
                          </div>
                        </div>
                        
                        <div className="border-t border-gray-200 pt-4">
                          <div className="text-sm font-medium text-gray-700 mb-2">Campaign Budget</div>
                          <div className="h-64 bg-gray-50 rounded-md flex items-center justify-center">
                            {isCampaignHistoryLoading ? (
                              <div className="animate-pulse space-y-4">
                                <div className="h-4 w-32 bg-gray-200 rounded"></div>
                                <div className="h-4 w-48 bg-gray-200 rounded"></div>
                                <div className="h-4 w-40 bg-gray-200 rounded"></div>
                              </div>
                            ) : campaignHistoryDataset && campaignHistoryDataset.length > 0 ? (
                              <Plot
                                data={campaignHistoryDataset as any}
                                layout={{
                                  margin: {
                                    l: 40,
                                    r: 20,
                                    b: 40,
                                    t: 10,
                                    pad: 0
                                  },
                                  paper_bgcolor: 'rgba(0,0,0,0)',
                                  plot_bgcolor: 'rgba(0,0,0,0)',
                                  autosize: true,
                                  showlegend: false,
                                  xaxis: {
                                    tickfont: {
                                      size: 10,
                                      color: '#9CA3AF'
                                    },
                                    tickformat: '%y.%m.%d',
                                    zerolinecolor: '#e5e7eb',
                                    tickmode: 'auto',
                                    nticks: 7,
                                  },
                                  yaxis: {
                                    tickformat: `${getCurrencySymbol(currencyCode)},`,
                                    tickfont: {
                                      size: 10,
                                      color: '#9CA3AF'
                                    },
                                    gridcolor: '#f3f4f6',
                                    zerolinecolor: '#e5e7eb',
                                    automargin: true,
                                    rangemode: 'tozero', // y축 최소값을 0으로 설정
                                  },
                                  hovermode: 'x unified',
                                  hoverlabel: {
                                    bgcolor: 'rgba(17, 24, 39, 0.9)',
                                    font: {
                                      size: 10,
                                      color: '#e5e7eb'
                                    },
                                  },
                                  dragmode: false,
                                }}
                                config={{
                                  displayModeBar: false,
                                }}
                                useResizeHandler={true}
                                className="w-full h-full"
                              />
                            ) : (
                              <div className="text-gray-400 text-sm">
                                No Campaign Budget Data
                              </div>
                            )}
                          </div>
                        </div>
                        
                        <div className="border-t border-gray-200 pt-4">
                          { /** budget history table with pagination */ }
                          <div className="text-sm font-medium text-gray-700 mb-2">Budget Change History</div>
                          <div className="overflow-hidden bg-white shadow ring-1 ring-black ring-opacity-5 md:rounded-lg">
                            <table className="min-w-full divide-y divide-gray-300">
                              <thead className="bg-gray-50">
                                <tr>
                                  <th scope="col" className="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-6">Date</th>
                                  <th scope="col" className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">Budget Amount</th>
                                  <th scope="col" className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">Action Type</th>
                                </tr>
                              </thead>
                              <tbody className="divide-y divide-gray-200 bg-white">
                                {renderBudgetHistoryTable()}
                              </tbody>
                            </table>
                          </div>
                          {renderBudgetPagination()}
                        </div>
                      </div>
                    ) : (
                      <div className="flex items-center justify-center h-64">
                        <div className="text-gray-400">No Campaign Info</div>
                      </div>
                    )}
                  </div>
                </DialogPanel>
              </TransitionChild>
            </div>
          </div>
        </Dialog>
      </Transition>
      
      {/* 타겟 차트 모달 */}
      <Transition appear show={isTargetChartModalOpen} as={Fragment}>
        <Dialog 
          as="div" 
          className="relative z-20" 
          onClose={() => {
            setIsTargetChartModalOpen(false);
            setCurrentHistoryPage(1); // 모달이 닫힐 때 페이지 번호 초기화
            setCurrentBudgetHistoryPage(1); // 예산 히스토리 페이지 번호 리셋
          }}
        >
          <div className="fixed inset-0 bg-black/30 backdrop-blur-sm" />
          <div className="fixed inset-0 overflow-y-auto">
            <div className="flex min-h-full items-center justify-center p-4">
              <TransitionChild
                as={Fragment}
                enter="ease-out duration-300"
                enterFrom="opacity-0 scale-95"
                enterTo="opacity-100 scale-100"
                leave="ease-in duration-200"
                leaveFrom="opacity-100 scale-100"
                leaveTo="opacity-0 scale-95"
              >
                <DialogPanel className="w-full max-w-4xl rounded-lg bg-white p-6 shadow-xl">
                  <div className="flex items-center justify-between">
                    <DialogTitle as="h3" className="text-lg font-medium text-gray-700">
                      {selectedTargetForChart?.target || '타겟 입찰가 변화 추이'}
                    </DialogTitle>
                    <button
                      type="button"
                      className="text-gray-400 hover:text-gray-500"
                      onClick={() => {
                        setIsTargetChartModalOpen(false)
                        setCurrentHistoryPage(1) // 모달이 닫힐 때 페이지 번호 초기화
                        setCurrentBudgetHistoryPage(1); // 예산 히스토리 페이지 번호 리셋
                      }}
                    >
                      <XMarkIcon className="h-6 w-6" />
                    </button>
                  </div>
                  <div className="mt-4">
                    {selectedTargetForChart ? (
                      <div className="space-y-4">
                        <div className="flex flex-col gap-2 p-4 bg-gray-50 rounded-md">
                          <div className="text-sm text-gray-500">Target Info</div>
                          <div className="grid grid-cols-3 gap-4">
                            <div>
                              <div className="text-xs text-gray-400">Target ID</div>
                              <div className="text-sm font-medium">{selectedTargetForChart.target_id}</div>
                            </div>
                            <div>
                              <div className="text-xs text-gray-400">Type</div>
                              <div className="text-sm font-medium">{selectedTargetForChart.target_type}</div>
                            </div>
                             <div>
                              <div className="text-xs text-gray-400">Match Type</div>
                              <div className="text-sm font-medium">{selectedTargetForChart.match_type}</div>
                            </div>
                            <div>
                              <div className="text-xs text-gray-400">Status</div>
                              <div className="text-sm font-medium">
                                {selectedTargetForChart.state === "PAUSED"
                                  ? "PAUSED"
                                  : selectedTargetForChart.state === "ENABLED"
                                    ? "ENABLED"
                                    : selectedTargetForChart.state
                                }
                              </div>
                            </div>
                            <div>
                              <div className="text-xs text-gray-400">Current Bid</div>
                              <div className="text-sm font-medium">${selectedTargetForChart.bid}</div>
                            </div>
                          </div>
                        </div>
                        
                        <div className="border-t border-gray-200 pt-4">
                          <div className="text-sm font-medium text-gray-700 mb-2">Bid Change History</div>
                          <div className="h-64 bg-gray-50 rounded-md flex items-center justify-center">
                            {isTargetBidHistoryLoading ? (
                              <div className="animate-pulse space-y-4">
                                <div className="h-4 w-32 bg-gray-200 rounded"></div>
                                <div className="h-4 w-48 bg-gray-200 rounded"></div>
                                <div className="h-4 w-40 bg-gray-200 rounded"></div>
                              </div>
                            ) : (targetBidHistory?.total > 0 && targetBidHistoryDataset && targetBidHistoryDataset.length > 0) ? (
                              <Plot
                                data={targetBidHistoryDataset as any}
                                layout={{
                                  margin: {
                                    l: 40,
                                    r: 20,
                                    b: 40,
                                    t: 10,
                                    pad: 0
                                  },
                                  paper_bgcolor: 'rgba(0,0,0,0)',
                                  plot_bgcolor: 'rgba(0,0,0,0)',
                                  autosize: true,
                                  showlegend: false,
                                  xaxis: {
                                    tickfont: {
                                      size: 10,
                                      color: '#9CA3AF'
                                    },
                                    tickformat: '%y.%m.%d',
                                    zerolinecolor: '#e5e7eb',
                                    tickmode: 'auto',
                                    nticks: 7,
                                  },
                                  yaxis: {
                                    tickformat: `${getCurrencySymbol(currencyCode)},.2f`,
                                    tickfont: {
                                      size: 10,
                                      color: '#9CA3AF'
                                    },
                                    gridcolor: '#f3f4f6',
                                    zerolinecolor: '#e5e7eb',
                                    automargin: true,
                                    rangemode: 'tozero', // y축 최소값을 0으로 설정
                                  },
                                  hovermode: 'x unified',
                                  hoverlabel: {
                                    bgcolor: 'rgba(17, 24, 39, 0.9)',
                                    font: {
                                      size: 10,
                                      color: '#e5e7eb'
                                    },
                                  },
                                  dragmode: false,
                                }}
                                config={{
                                  displayModeBar: false,
                                }}
                                useResizeHandler={true}
                                className="w-full h-full"
                              />
                            ) : (
                              <div className="text-gray-400 text-sm font-medium px-8 py-4 text-center">
                                No Bid Change History
                              </div>
                            )}
                          </div>
                        </div>
                        
                        {/* 입찰가 변경 히스토리 테이블 */}
                        <div className="border-t border-gray-200 pt-4">
                          <div className="text-sm font-medium text-gray-700 mb-2">Bid Change History</div>
                          <div className="overflow-hidden bg-white shadow ring-1 ring-black ring-opacity-5 md:rounded-lg">
                            <table className="min-w-full divide-y divide-gray-300">
                              <thead className="bg-gray-50">
                                <tr>
                                  <th scope="col" className="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-6">Date</th>
                                  <th scope="col" className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">Bid Amount</th>
                                  <th scope="col" className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">History Type</th>
                                </tr>
                              </thead>
                              <tbody className="divide-y divide-gray-200 bg-white">
                                {renderTargetHistoryTable()}
                              </tbody>
                            </table>
                          </div>
                          {renderPagination()}
                        </div>
                      </div>
                    ) : (
                      <div className="flex items-center justify-center h-64">
                        <div className="text-gray-400">No Target Info</div>
                      </div>
                    )}
                  </div>
                </DialogPanel>
              </TransitionChild>
            </div>
          </div>
        </Dialog>
      </Transition>

      <AddTargetModal
        isOpen={isAddTargetModalOpen}
        onClose={() => setIsAddTargetModalOpen(false)}
        selectedTargetType={selectedTargetType}
        onAdd={handleAddTarget}
      />
    </>
  )
}
